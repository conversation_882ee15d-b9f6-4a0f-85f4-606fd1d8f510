﻿<%@ Page Language="C#" AutoEventWireup="true" CodeFile="ApplicationPrintOfCandidate.aspx.cs" Inherits="candreg_ApplicationPrintOfCandidate" %>

<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml">
<head runat="server">
    <title>Candidate Application</title>
    <meta content="" name="description" />
    <meta content="" name="keywords" />
    <link href="assets/images/favicon.png" rel="icon" />
    <link href="assets/images/apple-touch-icon.png" rel="apple-touch-icon" />
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Montserrat:300,400,500,700" rel="stylesheet" />
    <%--<link href="css/bootstrap.min.css" type="text/css" rel="stylesheet">--%>
    <link href="<%=ResolveClientUrl("~/css/bootstrap4.min.css") %>" rel="stylesheet" />
    <%--<link rel="stylesheet" type="text/css" href="https://getbootstrap.com/docs/4.1/dist/css/bootstrap.min.css" />--%>
    <link href="css/bootstrap4.min.css" rel="stylesheet" />
    <link href="css/bootstrap-icons.css" rel="stylesheet" />
    <link href="css/glightbox.min.css" rel="stylesheet" />
    <link href="css/swiper-bundle.min.css" rel="stylesheet" />
    <link href="css/stylesheet.css" rel="stylesheet" />
    <link href="css/CustomSheet.css" rel="stylesheet" />
    <%--<style id="table_style">
        .caption{
             font-family:'Times New Roman', Times, serif;
             font-size:20px;
             font-weight:bold;
                }

        .tablestyle{
        

        }

         @media print {
    body {
      
        transform: scale(0.96);
        
    }
}
    </style>--%>
</head>
<body>
    <form id="form1" runat="server">

        <div class="container">
            <div class="srow print-aplication">
                <div class="srow border-bottom pb-3 mb-3">
                    <div class="row">
                        <%-- <div class="col-md-2 offset-8">
                            <button class="srow btn btn-danger btn-sm" onclick="history.go(-1); return false;">back</button>
                        </div>--%>
                        <div class="col-md-2">
                            <asp:Button CssClass="srow btn btn-danger btn-sm" runat="server" ID="btnBack" OnClick="btnBack_Click" Text="Back" />
                        </div>
                        <div class="col-md-2">
                            <button class="srow btn btn-success btn-sm" onclick="return PrintPanel()">Print</button>
                        </div>
                    </div>
                </div>

                <div class="srow" id="PrintArea" runat="server">
                    <div class="srow heading-logo" style="text-align: center;">
                        <h2 style="font-weight: 700; font-size: 23px; margin-bottom: 0px; color: #036;">
                            <asp:Image ID="Image1" ImageUrl="images/CggovtLogo.jpeg" runat="server" Style="width: 6%; margin-right: 5px;" />
                            उच्च शिक्षा संचालनालय, द्वितीय/तृतीय तल, ब्लॉक-3, इन्द्रावती भवन, नवा रायपुर  </h2>
                        <h3 style="font-weight: 700; font-size: 18px; display: inline-block; border-radius: 5px;">एप्लीकेशन फॉर्म </h3>

                    </div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="srow clrleft">
                                <div class="row">
                                    <div class="col-12">
                                        <label><strong>एप्लीकेशन नंबर:</strong></label>
                                        <asp:Label ID="lblRegistrationNo" runat="server" Text=""></asp:Label>

                                        <label><strong>दिनांक:</strong></label>
                                        <asp:Label ID="lblformfilldate" runat="server" Text=""></asp:Label>
                                    </div>
                                    <div class="col-lg-12">
                                        <hr style="margin-bottom: 30px; border-bottom: 1px solid #000; margin-top: 5px;" />
                                    </div>
                                </div>
                            </div>
                            <div class="srow clrleft">
                                <h3 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: #1665b5; padding-bottom: 5px; border-bottom: 1px dashed #1665b5;">व्यक्तिगत जानकारी</h3>
                                <div class="row">
                                    <div class="col-9">
                                        <div class="srow clrleft typer_details" style="font-size: 14px;">
                                            <div class="row">
                                                <dfn class="col-12" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">कैंडिडेट का नाम:</label>
                                                    <asp:Label ID="lblCandidateName" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>
                                                <dfn class="col-5" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">लिंग:</label>
                                                    <asp:Label ID="LblGender" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>
                                                <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">जन्म तारीख:</label>
                                                    <asp:Label ID="LblDOB" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>

                                                <dfn class="col-4" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">आयु (as on 01.01.2023):</label>
                                                    <asp:Label ID="lblAge" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>


                                                <dfn class="col-5" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">माता का नाम:</label>
                                                    <asp:Label ID="LblMotherName" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>

                                                <dfn class="col-6" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">पिता/पति का नाम:</label>
                                                    <asp:Label ID="LblFatherName" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>

                                                <dfn class="col-6" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">वैवाहिक स्थिति:</label>
                                                    <asp:Label ID="lblmarritalstatus" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>

                                                <dfn class="col-6" style="font-style: normal; margin-bottom: 10px;">
                                                    <label style="float: left; font-weight: 700;">जाति:</label>
                                                    <asp:Label ID="LblCast" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                                </dfn>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-3">
                                        <div class="srow clrleft" style="width: 100%; text-align: right; padding-right: 10px;">
                                            <dfn style="font-style: normal; border: 1px solid #dbdbdb; display: inline-block;">
                                                <asp:Image ID="imgphoto" Height="120" Width="150" runat="server" Style="border: 1px solid #dbdbdb;" />
                                                <span style="width: 100%; display: block; font-weight: 700; margin-top: 5px; color: #1665b5; font-size: 14px; text-align: center;">फोटो</span>
                                            </dfn>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="srow clrleft" style="align-content: center; margin-top: 10px;">

                                <div id="divcastifstscobc" runat="server" visible="true">
                                    <h3 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: #1665b5; padding-bottom: 5px; border-bottom: 1px dashed #1665b5;">ST/SC/OBC:-</h3>
                                    <div class="srow clrleft typer_details" style="font-size: 14px;">
                                        <div class="row">
                                            <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                                <label style="float: left; font-weight: 700;">प्रमाण पत्र क्रमांक</label>
                                                <asp:Label ID="lblcastesrno" runat="server" Text="" Style="width: 100%; float: left; margin-left: 0px;"></asp:Label>
                                            </dfn>
                                            <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                                <label style="float: left; font-weight: 700;">प्रमाण पत्र जारी करने वाले जिला का नाम</label>
                                                <asp:Label ID="lblcastdistname" runat="server" Text="" Style="width: 100%; float: left; margin-left: 0px;"></asp:Label>
                                            </dfn>
                                            <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                                <label style="float: left; font-weight: 700;">प्रमाण पत्र किसके द्वारा जारी किया गया</label>
                                                <asp:Label ID="lblcastename" runat="server" Text="" Style="width: 100%; float: left; margin-left: 0px;"></asp:Label>
                                            </dfn>

                                            <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                                <label style="float: left; font-weight: 700;">प्रमाण पत्र जारी करने की तिथि</label>
                                                <asp:Label ID="lblcastedate" runat="server" Text="" Style="width: 100%; float: left; margin-left: 0px;"></asp:Label>
                                            </dfn>

                                        </div>
                                    </div>
                                </div>
                                <%--  <h3 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: #1665b5; padding-bottom: 5px; border-bottom: 1px dashed #1665b5;">परीक्षा का नाम जिसके लिए आवेदन कर रहे है</h3>
                                <div class="srow clrleft typer_details" style="font-size: 14px;">
                                    <div class="row">
                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">परीक्षा का नाम:</label>
                                            <asp:Label ID="LblExamName" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                    </div>
                                </div>--%>

                                <h3 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: #1665b5; padding-bottom: 5px; border-bottom: 1px dashed #1665b5;">संपर्क जानकारी</h3>
                                <div class="srow clrleft typer_details" style="font-size: 14px;">
                                    <div class="row">
                                        <dfn class="col-5" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">मोबाइल नंबर :</label>
                                            <asp:Label ID="LblMobileNo" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-7" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">ईमेल :</label>
                                            <asp:Label ID="lblEmail" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-4" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">वर्तमान पता:</label>
                                            <asp:Label ID="lblpresentAddress" runat="server" Text="" Style="width: 75%; float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">जिला:</label>
                                            <asp:Label ID="lbldistpresent" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">राज्य:</label>
                                            <asp:Label ID="lblpresentstate" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-2" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">पिनकोड :</label>
                                            <asp:Label ID="lblPresentPincode" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-4" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">स्थायी पता:</label>
                                            <asp:Label ID="LblPermanentAddress" runat="server" Text="" Style="width: 75%; float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">जिला:</label>
                                            <asp:Label ID="lbldistparmanet" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">राज्य:</label>
                                            <asp:Label ID="lblpermanentState" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-2" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">पिनकोड :</label>
                                            <asp:Label ID="LblpermanentPincode" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                    </div>
                                </div>


                                <h3 style="font-size: 18px; font-weight: 700; margin-bottom: 10px; color: #1665b5; padding-bottom: 5px; border-bottom: 1px dashed #1665b5;">अन्य जानकारी</h3>
                                <div class="srow clrleft typer_details" style="font-size: 14px;">
                                    <div class="row">
                                        <dfn class="col-5" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">क्या शासकीय सेवा में है? :</label>
                                            <asp:Label ID="lblisGovtservice" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-7" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">क्या आप भूतपूर्व सैनिक है? :</label>
                                            <asp:Label ID="lblexserviceman" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-6" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">क्या आप शारीरिक रूप से असक्षम (पी एच / दिव्यांग) हैं? (हाँ / नहीं)</label>
                                            <asp:Label ID="lblIsDisabled" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn id="dfndisabletype" runat="server" visible="false" class="col-6" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">हाँ तो विकलांगता का प्रकार -</label>
                                            <asp:Label ID="lbldisablestype" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>


                                        <div class="col-12" style="font-style: normal; margin-bottom: 10px;">
                                            <div class="row">
                                                <div class="col-2">
                                                    <label style="float: left; font-weight: 700;">पद हेतु आवेदन -</label>
                                                </div>
                                                <div class="col-10">
                                                    <asp:Label ID="lblAppliedAttendent" runat="server" Text="" Style="float: left; margin-bottom: 10px; width: 100%;"></asp:Label>
                                                    <asp:Label ID="lblotherpost" runat="server" Text="" Style="float: left; margin-bottom: 10px;"></asp:Label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row" id="divprathmikta" runat="server" visible="false">
                                        <dfn class="col-3" style="font-style: normal;"><span style="font-weight: 700;">भृत्य/चौकीदार/स्वीपर पद हेतु प्राथमिकता -</span></dfn>
                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">भृत्य:</label>
                                            <asp:Label ID="lblbhrity" runat="server" Text="" Style="width: 75%; float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">चौकीदार:</label>
                                            <asp:Label ID="lblchaukidar" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-3" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">स्वीपर :</label>
                                            <asp:Label ID="lblsweepar" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                    </div>
                                    <div class="row">

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 10px;">
                                            <label style="float: left; font-weight: 700;">परीक्षा केंद्र प्राथमिकता जिला -</label>
                                            <asp:Label ID="lblexamcenterprefrence" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px; margin-top: 20px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आवेदक को कभी छत्तीसगढ़ शासन द्वारा अंतर-जातीय विवाह प्रोत्साहन के लिए सम्मानित किया गया है? :</label>
                                            <asp:Label ID="lblinertercast" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आवेदक को छत्तीसगढ़ शासन द्वारा निम्नलिखित में से किसी पुरस्कार से सम्मानित किया गया है- (राजीव पांडे पुरस्कार/गुन्डाधुर पुरस्कार/ महाराजा प्रवीर चंद्र भंजदेव पुरस्कार या राष्ट्रीय युवा पुरस्कार) ? :</label>
                                            <asp:Label ID="lblpuraskar" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आप एक रक्षा सेवा कर्मी हैं, जो किसी विदेशी देश के साथ शत्रुता के दौरान या अशांत क्षेत्र में संचालन में अक्षम हैं और जिसके परिणामस्वरूप उन्हें रिहा किया गया है ? :</label>
                                            <asp:Label ID="lblrakshakarmi" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आप एक ऐसे अभ्यर्थी हैं जो वियतनाम से भारतीय मूल (भारतीय पासपोर्ट धारक) का एक वास्तविक प्रत्यावर्तन है और साथ ही वियतनाम में भारतीय दूतावास द्वारा उसे जारी किया गया आपातकालीन प्रमाण पत्र रखने वाले अभ्यर्थी हैं और जो जुलाई, 1975 से पहले वियतनाम से भारत पहुंचे हैं? :</label>
                                            <asp:Label ID="lbl1975viyatname" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आप भारतीय हैं?:</label>
                                            <asp:Label ID="lblis_certifiedindian" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आप अलग या विधवा हैं?:</label>
                                            <asp:Label ID="lblis_sepereated" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आपको कभी किसी प्राधिकारी/संस्था द्वारा परीक्षा/चयन से बाहर रखा गया है?:</label>
                                            <asp:Label ID="lblis_excluded" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या अभ्यर्थी को सेवा अवधि के दौरान कभी दंडित किया गया या सेवा समाप्त की गई है?:</label>
                                            <asp:Label ID="lblis_terminated" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>

                                        <dfn class="col-12" style="font-style: normal; margin-bottom: 5px;">
                                            <label style="width: 90%; float: left; font-weight: 700;">क्या आपको कभी किसी आपराधिक मामले में दोषी घोषित किया गया है?:</label>
                                            <asp:Label ID="lblisaccused" runat="server" Text="" Style="float: left; margin-left: 20px;"></asp:Label>
                                        </dfn>
                                        <dfn class="col-12" style="font-style: normal;">
                                            <b>घोषणा :-</b>
                                            <p style="text-align: justify;">मैं एतद् द्वारा घोषणा करता/करती हूं कि, मैंने विज्ञापन और उसमें दर्शाए गए सभी नियमों और शर्तों को ध्यान से पढ़ा है एवं समझा है। मैं चयन प्रक्रिया के किसी भी चरण में भाग लेने के लिए विज्ञापन के नियमों और शर्तों का पालन करने का वचन देता/देती हूं। आवेदन पत्र में मेरे द्वारा प्रदान की गई जानकारी मेरी सर्वोत्तम जानकारी के अनुसार सत्य और सही है। मैं यह भी वचन देता/देती हूं कि मैं किसी भी श्रेणी/उपश्रेणी/छूट/आरक्षण के किसी भी लाभ का दावा नहीं करूंगा/करूंगी, जिसे मैंने इस ऑनलाइन आवेदन पत्र में नहीं भरा है। यदि मेरे द्वारा प्रदान की गई जानकारी किसी भी स्तर पर गलत या असत्य पायी जाती है तो चयन प्रक्रिया के किसी भी स्तर पर मुझे पूर्व सूचना दिये बिना मेरी अभ्यर्थिता समाप्त की जा सकती है।</p>
                                        </dfn>
                                    </div>
                                </div>
                            </div>
                            <div class="srow clrleft" style="width: 100%; text-align: right; margin-top: 20px;">
                                <div class="row">
                                    <div class="col-3 offset-9">
                                        <dfn style="font-style: normal; width: 100%; display: inline-block; text-align: center;">
                                            <asp:Image ID="imgsign" runat="server" Style="width: 150px; height: 60px;" />
                                            <span style="width: 100%; display: block; font-weight: 700; margin-top: 5px; color: #1665b5; font-size: 14px;">हस्ताक्षर</span>
                                        </dfn>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <%--<script type="text/javascript" src="https://code.jquery.com/jquery-3.3.1.slim.min.js"></script>--%>
        <script type="text/javascript" src="<%=ResolveClientUrl("~/js/Slim.min.js") %>"></script>
        <script type="text/javascript" src="<%=ResolveClientUrl("~/js/bootstarp4.min.js") %>"></script>
        <%--<script type="text/javascript" src="https://getbootstrap.com/docs/4.1/dist/js/bootstrap.min.js"></script>--%>
        <script type="text/javascript" src="js/glightbox.min.js"></script>
        <script type="text/javascript" src="js/isotope.pkgd.min.js"></script>
        <script type="text/javascript" src="js/swiper-bundle.min.js"></script>
        <script type="text/javascript" src="js/main.js"></script>
        <script type="text/javascript" src="js/zebra_datepicker.min.js"></script>
        <script src="js/Myscript.js"></script>
        <script src="js/CustomScript.js"></script>


        <script type="text/javascript">           
            var RegistrationNo = document.getElementById("<%=lblRegistrationNo.ClientID %>").innerText;

            function PrintPanel() {
                var panel = document.getElementById("<%=PrintArea.ClientID %>");
                var printWindow = window.open('', '', 'height=600,width=800');
                printWindow.document.write('<html><head><link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Montserrat:300,400,500,700" rel="stylesheet" /> <link rel="stylesheet" type="text/css" href="css/bootstrap4.min.css" /><link href="css/bootstrap-icons.css" rel="stylesheet" />    <link href="css/glightbox.min.css" rel="stylesheet" /><link href="css/swiper-bundle.min.css" rel="stylesheet" />  <link href="css/stylesheet.css" rel="stylesheet" /></style> <title>Application_' + RegistrationNo + '</title>');
                printWindow.document.write('</head><body>');
                printWindow.document.write(panel.innerHTML);
                printWindow.document.write('</body></html>');
                printWindow.document.close();

                // Show the print dialog immediately after loading the content
                printWindow.onload = function () {
                    setTimeout(function () {
                        printWindow.print();
                        printWindow.close();
                    }, 500);
                };

                return false;
            }
        </script>




        <%--<script type="text/javascript">
            var ApplicationNo = document.getElementById("<%=LblApplicationNo.ClientID %>").innerText;
            function PrintPanel() {
                var panel = document.getElementById("<%=PrintArea.ClientID %>");
                var printWindow = window.open('', '', 'height=600,width=800');
                printWindow.document.write('<html><head><link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Montserrat:300,400,500,700" rel="stylesheet" /> <link rel="stylesheet" type="text/css" href="assets/css/bootstrap4.min.css"/><link href="assets/css/bootstrap-icons.css" rel="stylesheet" />    <link href="assets/css/glightbox.min.css" rel="stylesheet" /><link href="assets/css/swiper-bundle.min.css" rel="stylesheet" />  <link href="assets/css/stylesheet.css" rel="stylesheet" /></style> <title>Application_'+ApplicationNo+'</title>');
                printWindow.document.write('</head><body>');
                printWindow.document.write(panel.innerHTML);
                printWindow.document.write('</body></html>');
                printWindow.document.close();
                setTimeout(function () {
                    printWindow.print();
                }, 500);
                return false;
            }

        </script>--%>
    </form>
</body>
</html>

