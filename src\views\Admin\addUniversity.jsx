import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  FormFeedback,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";

const University = () => {

  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem('authToken')

  const [formData, setFormData] = useState({
    name: '',
    universityEmail: '',
    contactPerson: '',
    contactNumber: '',
    divison: '',
    establishYear: '',
    district: '',
    vidhansabha: '',
    registerationNumber: '',
    universityType: '',
    uniRegDate: '',
    address: '',
    universityUrl: '',
  });
  const [errors, setErrors] = useState({
    contactNumber: "",
    universityEmail: "",
  });

  const validateContactNumber = (number) => {
    const regex = /^[0-9]{10}$/; // Change as per your requirements (e.g., length, format)
    return regex.test(number);
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email format validation
    return regex.test(email);
  };
  const handleKeyPress = (e) => {
    const key = e.key;
    const isNumber = /^[0-9]$/.test(key);
    if (!isNumber && key !== "Backspace" && key !== "Tab") {
      e.preventDefault(); // Prevents the input of non-numeric characters
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));

    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));

    if (name === "contactNumber") {
      if (!validateContactNumber(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          contactNumber: "Please enter a valid 10-digit contact number.",
        }));
      }
    }

    if (name === "universityEmail") {
      if (!validateEmail(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          universityEmail: "Please enter a valid email address.",
        }));
      }
    }
  };
  const validateFields = () => {
    const newErrors = {};

    Object.keys(formData).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if there are no errors
  };
  function togglePreviewModal() {
    // Check if form is valid
    if (validateFields()) {
      // Open modal if form is valid
      setPreviewModal(!previewModal);
    } else {
      // Form is invalid, don't show modal
      SwalMessageAlert(
        "Please fill out all fields before submitting.",
        "warning"
      );
    }
  }

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);
  const [previewModal, setPreviewModal] = useState(false);

  // const division = [
  //   { value: 1, name: "Bastar" },
  //   { value: 2, name: "Bilaspur" },
  //   { value: 3, name: "Durg" },
  //   { value: 4, name: "Raipur" },
  //   { value: 5, name: "Sarguja" },
  // ]

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/division/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  // const getDivisionName = (value) => {
  //   const divisionObj = division.find((div) => div.value === value);
  //   return divisionObj ? divisionObj.name : "Unknown Division";
  // };



  const handleDivisionChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(`${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
      if (response.status === 200) {
        setDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      divison: value,
    });
  }


  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({
      ...formData,
      district: value,
    });
    try {
      const response = await axios.get(`${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
      if (response.status === 200) {
        setVidhansabha(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
  }


  const formatFieldValue = (key, value) => {
    if (key === 'universityType') {
      // Ensure that both values are treated as strings
      return String(value) === '1' ? 'Government' : 'Private';
    }
    if (key === 'uniRegDate') {
      return new Date(value).toLocaleDateString();
    }
    return value || "Not provided";
  };


  const handleSubmit = async (e) => {

    e.preventDefault();
    if (formData.contactNumber.length !== 10) {
      SwalMessageAlert('Please enter a valid 10-digit phone number!', "error");
    } else {
      SwalMessageAlert('Contact number is valid!', "success");
      // Handle form submission (e.g., API call)
    }
    // Create an array of the field values to validate    
    const valuesToValidate = [
      formData.name,
      formData.universityEmail,
      formData.contactPerson,
      formData.contactNumber,
      formData.divison,
      formData.establishYear,
      formData.district,
      formData.vidhansabha,
      formData.registerationNumber,
      formData.universityType,
      formData.uniRegDate,
      formData.address,
      formData.universityUrl,
    ];

    const hasEmptyFields = valuesToValidate.some(value => value === null || value === '' || value === undefined);
    const allFieldsFilled = valuesToValidate.every(value => value !== null && value !== '' && value !== undefined);

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      SwalMessageAlert("All fields are filled. You can proceed with submission.", "success");

    }

    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    


    try {
      const body = {
        name: formData.name,
        universityEmail: formData.universityEmail,
        contactPerson: formData.contactPerson,
        contactNumber: formData.contactNumber,
        divison: String(formData.divison),
        establishYear: String(formData.establishYear),
        district: String(formData.district),
        vidhansabha: String(formData.vidhansabha),
        registerationNumber: formData.registerationNumber,
        universityType: String(formData.universityType),
        uniRegDate: formData.uniRegDate,
        address: formData.address,
        universityUrl: formData.universityUrl,
      }
      const response = await axios.post(`${endPoint}/api/university/add`, { ...body }, {
        headers: {
          'Content-Type': 'application/json',
          'web-url': window.location.href,
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        setFormData({
          name: "",
          universityEmail: "",
          contactPerson: "",
          contactNumber: "",
          division: "",
          establishYear: "",
          district: "",
          vidhansabha: "",
          registrationNumber: "",
          uniRegDate: "",
          universityType: "",
          universityUrl: "",
          address: "",
        });
        setTimeout(() => {
          window.location.replace("admin/university");
        }, 5000);
      } else {
        SwalMessageAlert(
          "Adding for University failed. Please try again.",
          "error"
        );

      }

    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      // alert("An error occurred. Please try again later.");
      SwalMessageAlert(
        "An error occurred. Please try again.",
        "error"
      );
      // navigate("admin/university");
    }

  };
  return (
    <>
      <Header />


      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col >
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">ADD UNIVERSITY</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                   
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-name">
                            University Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="University Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required

                          />

                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-universityEmail">
                            University Email
                          </label>
                          <Input
                            name="universityEmail"
                            id="input-universityEmail"
                            placeholder="University Email"
                            type="email"
                            autoComplete="pope"
                            value={formData.universityEmail}
                            onChange={handleInputChange}
                            invalid={!!errors.universityEmail}
                          />
                          {errors.universityEmail && <FormFeedback>{errors.universityEmail}</FormFeedback>}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-contactPerson">
                            Contact Person
                          </label>
                          <Input
                            name="contactPerson"
                            id="input-contactPerson"
                            placeholder="Contact Person"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactPerson}
                            onChange={handleInputChange}
                            required
                          />

                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-contactNumber">
                            Contact Number
                          </label>
                          <Input
                            name="contactNumber"
                            id="input-contactNumber"
                            placeholder="Contact Number"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactNumber}
                            maxLength={10}
                            onChange={(e) => {
                              const value = e.target.value;

                              // Check if the input is empty
                              if (value === "") {
                                handleInputChange(e);
                                return;
                              }

                              // Regex to match numbers starting with 6, 7, 8, or 9
                              const validStartRegex = /^[6-9]/;

                              if (validStartRegex.test(value)) {
                                // If valid, pass 'true' as the second argument to handleInputChange
                                handleInputChange(e);
                              } else {
                                // Show alert if the input starts with invalid numbers
                                // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                SwalMessageAlert(
                                  " Mobile number must start with digits 6, 7, 8, or 9.",
                                  "warning"
                                );

                              }
                            }}
                            required
                            invalid={!!errors.contactNumber}
                            onKeyPress={handleKeyPress} // Attach key press event
                          />
                          {errors.contactNumber && <FormFeedback>{errors.contactNumber}</FormFeedback>}

                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-division">
                            Division
                          </label>
                          <Input
                            name="divison"
                            id="input-division"
                            type="select"
                            value={formData.divison}
                            onChange={handleDivisionChange}
                            required
                          ><option value="" disabled>Select Division</option>
                            {division && division.length > 0 && division.map((type, index) => (
                              <option key={index} value={type.divisionCode}>
                                {type.name}
                              </option>
                            ))}
                          </Input>

                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-district">
                            District
                          </label>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            onChange={handleDistrictChange}
                            required
                          ><option value="" disabled>Select Division</option>
                            {district && district.length > 0 && district.map((type, index) => (
                              <option key={index} value={type.LGDCode}>
                                {type.districtNameEng}
                              </option>
                            ))}
                            {/* Add more districts as needed */}
                          </Input>

                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-vidhansabha">
                            Vidhan Sabha
                          </label>
                          <Input
                            name="vidhansabha"
                            id="input-vidhansabha"
                            type="select"
                            value={formData.vidhansabha}
                            onChange={handleInputChange}
                            required
                          ><option value="" disabled>Select Vidhansabha</option>
                            {vidhansabha && vidhansabha.length > 0 && vidhansabha.map((type, index) => (
                              <option key={index} value={type.ConstituencyNumber}>
                                {type.ConstituencyName}
                              </option>
                            ))}
                          </Input>

                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-establishYear">
                            Establishment Year
                          </label>
                          <Input
                            name="establishYear"
                            id="input-establishYear"
                            placeholder="Establishment Year"
                            type="number"
                            autoComplete="pope"
                            value={formData.establishYear}
                            required
                            onChange={handleInputChange}
                          />

                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-registrationNumber">
                            Registration Number
                          </label>
                          <Input
                            name="registerationNumber"
                            id="input-registrationNumber"
                            autoComplete="pope"
                            placeholder="Registration Number"
                            type="text"
                            value={formData.registerationNumber}
                            onChange={handleInputChange}
                            required
                          />

                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-uniRegDate">
                            Registration Date
                          </label>
                          <Input
                            name="uniRegDate"
                            id="input-uniRegDate"
                            placeholder="Registration Date"
                            type="date"
                            autoComplete="pope"
                            value={formData.uniRegDate}
                            onChange={handleInputChange}
                            required
                          />

                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <Label className='Label'>University Type</Label>
                          <div className="row" style={{ justifyContent: "space-evenly" }}>
                            <FormGroup check>
                              <Input type="radio" name="universityType" id="input-universityType" value="1" checked={formData.universityType === '1'} onChange={handleInputChange} />
                              <Label check>Govt</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input type="radio" name="universityType" id="input-universityType" value="0" checked={formData.universityType === '0'} onChange={handleInputChange} />
                              <Label check>Private</Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>
                      {/* <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-universityType">
                            University Type
                          </label>
                          <Input
                            name="universityType"
                            id="input-universityType"
                            type="select"
                            value={formData.universityType}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select University Type</option>
                            <option value="1">Govt.</option>
                            <option value="2">Private</option>
                            {/* Add more university types if needed */}
                      {/* </Input> */}

                      {/* </FormGroup>
                      // </Col> */}
                      <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-universityUrl">
                            University URL
                          </label>
                          <Input
                            name="universityUrl"
                            id="input-universityUrl"
                            autoComplete="pope"
                            placeholder="University URL"
                            type="url"
                            value={formData.universityUrl}
                            required
                            onChange={handleInputChange}
                          />

                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>

                      <Col lg="6">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-address">
                            Address
                          </label>
                          <Input
                            name="address"
                            id="input-address"
                            placeholder="Address"
                            // type="text"
                            as="textarea"
                            value={formData.address}
                            onChange={handleInputChange}
                            required
                          />

                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={togglePreviewModal}>
                      Preview & Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Preview of Update University</h2>
            <h5 className="text-danger mt-2">
              Note: Please check all fields carefully before submitting
            </h5>
          </ModalHeader>
          <ModalBody style={{ backgroundColor: "#f8f9fa", padding: "20px" }}>
            <div>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>University Name:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>University Email:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.universityEmail}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Contact Person:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.contactPerson}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Contact Number:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.contactNumber}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Establishment Year:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.establishYear}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Registration Number:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.registerationNumber}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Registration Date:</strong>
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue('uniRegDate', formData.uniRegDate)}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>University Type:</strong>
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue('universityType', formData.universityType)}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Division:</strong>
                    </label>
                    <input
                      type="text"
                      value={division.find(
                        (type) => String(type.divisionCode) === String(formData.divison)
                      )?.name || "N/A"} disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>District:</strong>
                    </label>
                    <input
                      type="text"
                      value={
                        district.find(
                          (type) => String(type.LGDCode) === String(formData.district)
                        )?.districtNameEng || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Vidhan Sabha:</strong>
                    </label>
                    <input
                      type="text"
                      value={
                        vidhansabha.find(
                          (type) =>
                            String(type.ConstituencyNumber) === String(formData.vidhansabha)
                        )?.ConstituencyName || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>University URL:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.universityUrl}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <div className="preview-item mb-3">
                    <label className="form-label">
                      <strong>Address:</strong>
                    </label>
                    <input
                      type="text"
                      value={formData.address}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button color="secondary" onClick={togglePreviewModal} style={{ borderRadius: "5px" }}>
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{ borderRadius: "5px", backgroundColor: "#007bff", border: "none" }}
            >
              Submit
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default University;
