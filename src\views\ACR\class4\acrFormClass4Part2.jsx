import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Label,
  Button,
  Input,
  Table,
} from "reactstrap";
import Swal from "sweetalert2";
import axios from "axios";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";

const ACRFormClass4Part2 = ({ employee }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);
  const [formData, setFormData] = useState({
    applicationId: employee[0].applicationId,
    employeeId: employee[0].employeeId,
    employeeName: employee[0].employeeName,
    collegeName: employee[0].collegeName,
    collegeId: employee[0].collegeId,
    acharan<PERSON><PERSON><PERSON>: "",
    sama<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>: "",
    shari<PERSON><PERSON><PERSON><PERSON>: "",
    karya<PERSON>i<PERSON>ogyata: "",
    stanantaran: "",
    shreni<PERSON><PERSON>: "",
    remark: "",
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.acharanVevhar.trim())
      newErrors.acharanVevhar = "This field is required.";
    if (!formData.samayKiPabandi.trim())
      newErrors.samayKiPabandi = "This field is required.";
    if (!formData.sharirikShamta.trim())
      newErrors.sharirikShamta = "This field is required.";
    if (!formData.karyaKiYogyata.trim())
      newErrors.karyaKiYogyata = "This field is required.";
    if (!formData.stanantaran.trim())
      newErrors.stanantaran = "This field is required.";
    if (!formData.shreniKaran.trim())
      newErrors.shreniKaran = "This field is required.";
    if (!formData.remark.trim())
      newErrors.remark = "This field is required.";
    return newErrors;
  };

  const handleFinalSubmit = async (e) => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });
      if (result.isConfirmed) {
        e.target.disabled = true;

        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("प्रथम मतांकन सफल हुआ", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  return (
    <>
      <Row>
        <Col>
          <Card className="shadow pb-4">
            <CardHeader
              className="bg-white border-0"
              style={{ textAlign: "center" }}
            >
              <h2 className="mb-0">प्रपत्र --- दो</h2>
              <h3 className="mb-0">
                चतुर्थ श्रेणी के शासकीय कर्मचारियों के संबंध में प्रतिवर्ष माह
                अप्रैल के प्रथम सप्ताह में लिखी जाने वाली चरित्र पंजी का फार्म
                31 मार्च {lastYearDate}
                को समाप्त होने वाले वर्ष के लिये
              </h3>
            </CardHeader>
            <CardBody>
              <div className="mb-4">
                <Row className="mb-3">
                  <Table>
                    <tbody>
                      <tr>
                        <h2 style={{ textAlign: "center" }}>प्रतिवेदक</h2>
                      </tr>
                      <tr>
                        <td>
                          <Row className="mb-3">
                            <Col md="3">
                              <Label>कर्मचारी का नाम</Label>
                              <Input
                                type="text"
                                name="employeeName"
                                value={employee[0].basicDetails[0].employeeName}
                                readOnly
                                className="form-control"
                              />
                            </Col>
                            <Col md="3">
                              <Label>पिता अथवा पति का नाम </Label>
                              <Input
                                type="text"
                                name="fatherName"
                                value={employee[0].basicDetails[0].fatherName}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.fatherName && (
                                <small className="text-danger">
                                  {errors.fatherName}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>जन्म तिथि</Label>
                              <Input
                                type="date"
                                name="dateofBirth"
                                value={employee[0].basicDetails[0].dateofBirth}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.dateofBirth && (
                                <small className="text-danger">
                                  {errors.dateofBirth}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>शैक्षणिक योग्यता, यदि कोई हो</Label>
                              <Input
                                type="text"
                                name="shenikYogyata"
                                value={employee[0].basicDetails[0].shenikYogyata}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.shenikYogyata && (
                                <small className="text-danger">
                                  {errors.shenikYogyata}
                                </small>
                              )}
                            </Col>
                          </Row>
                          <Row className="mb-3">
                            <Col md="3">
                              <Label>वरिष्ठता सूची क्रमांक </Label>
                              <Input
                                type="number"
                                name="varishtSuchiNumber"
                                value={employee[0].basicDetails[0].varishtSuchiNumber}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.varishtSuchiNumber && (
                                <small className="text-danger">
                                  {errors.varishtSuchiNumber}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>वेतन</Label>
                              <Input
                                type="number"
                                name="salary"
                                value={employee[0].basicDetails[0].currentSalary} // Use employee data here
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                            </Col>
                            <Col md="3">
                              <Label>पदनाम / स्थायी / अस्थायी </Label>
                              <Input
                                type="text"
                                name="designation"
                                value={employee[0].basicDetails[0].designation}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.kartavyaKaVivran && (
                                <small className="text-danger">
                                  {errors.kartavyaKaVivran}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>नियमित नियुक्ति की तिथि </Label>
                              <Input
                                type="date"
                                name="niyamitNiyukti"
                                value={employee[0].basicDetails[0].niyamitNiyukti}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.niyamitNiyukti && (
                                <small className="text-danger">
                                  {errors.niyamitNiyukti}
                                </small>
                              )}
                            </Col>
                          </Row>
                          <Row className="mb-4">
                            <Col md="6">
                              <Label>महाविद्यालय का नाम</Label>
                              <Input
                                type="text"
                                name="collegeName"
                                value={employee[0].basicDetails[0].collegeName} // Use employee data here
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                            </Col>
                            <Col md="6">
                              <Label>
                                अवधि जिसके लिए मत अंकित किया जा रहा है{" "}
                              </Label>
                              <Input
                                type="textarea"
                                name="avadhiAnkit"
                                value={employee[0].basicDetails[0].avadhiAnkit}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.avadhiAnkit && (
                                <small className="text-danger">
                                  {errors.avadhiAnkit}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <h2 style={{ textAlign: "center" }}>प्रथम मतांकन</h2>
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="6">
                    <Label>आचरण / व्यवहार तथा आज्ञाकारिता</Label>
                    <Input
                      type="textarea"
                      name="acharanVevhar"
                      maxLength={150}
                      value={formData.acharanVevhar}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.acharanVevhar && (
                      <small className="text-danger">
                        {errors.acharanVevhar}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>समय की पाबंदी </Label>
                    <Input
                      type="textarea"
                      name="samayKiPabandi"
                      maxLength={150}
                      value={formData.samayKiPabandi}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.samayKiPabandi && (
                      <small className="text-danger">
                        {errors.samayKiPabandi}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="6">
                    <Label>शारीरिक क्षमता</Label>
                    <Input
                      type="textarea"
                      name="sharirikShamta"
                      maxLength={150}

                      value={formData.sharirikShamta}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.sharirikShamta && (
                      <small className="text-danger">
                        {errors.sharirikShamta}
                      </small>
                    )}
                  </Col>

                  <Col md="6">
                    <Label>सौपे गये कार्य को करने की समझ और योग्यता</Label>
                    <Input
                      type="textarea"
                      maxLength={150}
                      name="karyaKiYogyata"
                      value={formData.karyaKiYogyata}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.karyaKiYogyata && (
                      <small className="text-danger">
                        {errors.karyaKiYogyata}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="6">
                    <Label>स्थानांतरण, दण्ड आदि के संबंध में सामान्य मत</Label>
                    <Input
                      type="textarea"
                      maxLength={150}
                      name="stanantaran"
                      value={formData.stanantaran}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.stanantaran && (
                      <small className="text-danger">
                        {errors.stanantaran}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>श्रेणीकरण</Label>
                    <Input
                      name="shreniKaran"
                      type="select"
                      id="recordsPerPage"
                      value={formData.shreniKaran}
                      onChange={handleInputChange}
                    >
                      <option value="">चुने</option>
                      <option value="उत्कृष्ट">उत्कृष्ट</option>
                      <option value="बहुत अच्छा">बहुत अच्छा</option>
                      <option value="अच्छा">अच्छा</option>
                      <option value="साधारण">साधारण</option>
                      <option value="घटिया">घटिया</option>
                    </Input>
                    {errors.shreniKaran && (
                      <small className="text-danger">
                        {errors.shreniKaran}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="12">
                    <Label>रिमार्क</Label>
                    <Input
                      maxLength={500}
                      name="remark"
                      type="textarea"
                      style={{ height: "100px" }}
                      value={formData.remark}
                      onChange={handleInputChange}
                    />
                    {errors.remark && (
                      <small className="text-danger">{errors.remark}</small>
                    )}
                  </Col>
                </Row>
                <Button color="success" onClick={handleFinalSubmit}>
                  Submit
                </Button>
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row>
    </>
  );
};

ACRFormClass4Part2.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormClass4Part2;