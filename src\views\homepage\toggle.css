/* Navbar Styling */
.navbar {
  background-color: #0056b3 !important; /* Set background */
}

/* Navbar links */
.navbar-nav .nav-link {
  color: rgb(203, 200, 209) !important; /* Set default color */
  font-weight: 600;
}

.navbar-nav .nav-link:hover {
  color: #46dbcf !important; /* Change color on hover */
}

/* Navbar Toggle Button */
.navbar-toggler {
  background-color: maroon !important; /* Background color */
  border: none !important; /* Remove border */
  padding: 2px 8px !important; /* Reduce padding */
  width: 36px; /* Reduce width */
  height: 36px; /* Reduce height */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Custom Toggle Icon */
.navbar-toggler-icon {
  width: 20px !important; /* Reduce width */
  height: 20px !important; /* Reduce height */
  background-image: none !important; /* Remove default Bootstrap icon */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: aliceblue;

}

/* Custom Bars */
.navbar-toggler:before,
.navbar-toggler:after {
  content: '';
  display: block;
  width: 22px; /* Reduce bar width */
  height: 2px; /* Reduce bar height */
  background-color: white;
  margin: 4px auto; /* Reduce spacing */

}

/* Middle Bar */
.navbar-toggler span {
  display: block;
  width: 22px; /* Reduce bar width */
  height: 2px; /* Reduce bar height */
  background-color: white;
  margin-top: 42px auto;
}

/* Adjust for Mobile View */
@media (max-width: 768px) {
  .navbar-toggler {
    width: 32px; /* Smaller width */
    height: 32px; /* Smaller height */
  }

  .navbar-toggler:before,
  .navbar-toggler:after,
  .navbar-toggler span {
    width: 50px; /* Smaller bars */
    height: 2px; /* Thinner bars */
  }
}
/* Navbar Toggler Fix */
.navbar-toggler {
  /* background-color: #f5e9e9; Fixes navbar toggle button */
  border: none;
}

.navbar-toggler-icon {
  background-image: none; /* Remove default icon */
  width: 30px;
  height: 30px;
  background-color: white; /* Custom white icon */
  border-radius: 3px;
}



