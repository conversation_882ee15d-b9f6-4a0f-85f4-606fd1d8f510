import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Header,
  CardFooter,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Table,
  Pagination,
  PaginationItem,
  PaginationLink,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
const AddVidhansabha = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredVidhansabha, setFilterdVidhansabha] = useState([]);

  const [formData, setFormData] = useState({
    DistCode: "",
    ConstituencyNumber: "",
    ConstituencyName: "",
    ConstituencyNameHindi: "",
  });
  const [district, setDistrict] = useState([]);
  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          //   // console.log(response.data);

          setDistrict(response.data);
        } else {
          alert("Failed to District  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDistrict();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change




  const [vidhansabha, setVidhansabha] = useState([]);
  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-vidhansabha`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          //   // console.log(response.data);
          setFilterdVidhansabha(response.data);
          setVidhansabha(response.data);
        } else {
          alert("Failed to District  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchVidhansabha();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  useEffect(() => {
    const filteredItems = vidhansabha.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilterdVidhansabha(filteredItems);
    setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, vidhansabha]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.DistCode,
      formData.ConstituencyNumber,
      formData.ConstituencyName,
      formData.ConstituencyNameHindi,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    try {
      const body = {
        DistCode: String(formData.DistCode),
        ConstituencyNumber: String(formData.ConstituencyNumber),
        ConstituencyName: String(formData.ConstituencyName),
        ConstituencyNameHindi: String(formData.ConstituencyNameHindi),
      };

      e.target.disabled = true;

      setTimeout(() => {
        e.target.disabled = false;
      }, 5000);
      

      const response = await axios.post(
        `${endPoint}/api/vidhansabha/add`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
          DistCode: "",
          ConstituencyNumber: "",
          ConstituencyName: "",
          ConstituencyNameHindi: "",
        });
        window.location.reload();
      } else {
        alert("Vidhansabha Already Exists.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred while submitting the form:");
    }
  };

  // Handle Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredVidhansabha.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(vidhansabha.length / itemsPerPage);
  const handleDistrictPageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Add Vidhansabha</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-DistCode"
                          >
                            Select District
                          </label>
                          <Input
                            name="DistCode"
                            id="input-DistCode"
                            type="select"
                            value={formData.DistCode}
                            onChange={handleInputChange}
                          >
                            <option value="">Select District</option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option key={index} value={type.LGDCode}>
                                  {type.districtNameEng}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyNumber"
                          >
                            Constituency Number
                          </label>
                          <Input
                            name="ConstituencyNumber"
                            id="input-ConstituencyNumber"
                            placeholder="Constituency Number"
                            type="number"
                            value={formData.ConstituencyNumber}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyName"
                          >
                            Constituency Name
                          </label>
                          <Input
                            name="ConstituencyName"
                            id="input-ConstituencyName"
                            placeholder="Constituency Name"
                            type="text"
                            value={formData.ConstituencyName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyNameHindi"
                          >
                            Constituency Name in Hindi
                          </label>
                          <Input
                            name="ConstituencyNameHindi"
                            id="input-ConstituencyNameHindi"
                            placeholder="Constituency Name in Hindi"
                            type="text"
                            value={formData.ConstituencyNameHindi}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex">
                <Col md={9}><h3 className="mb-0">Vidhansabha List</h3></Col>
                <Col md={3}>
                  <Input
                    type="text"
                    placeholder="Search Vidhansabha.."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ marginBottom: "10px" }}
                  />
                </Col>
              </CardHeader>
              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                    <th scope="col">Action</th>
                    <th scope="col">District</th>
                    <th scope="col">Constituency Number</th>
                    <th scope="col">Vidhansabha Name</th>
                    <th scope="col">Vidhansabha Name in Hindi</th>
                  </tr>
                </thead>
                <tbody>
                  {vidhansabha &&
                    vidhansabha.length > 0 &&
                    currentItems.map((vidhansabha, index) => (
                      <tr key={index}>
                        <td>
                          <Link to={`/admin/update-vidhansabha/${vidhansabha._id}`}>
                            <button className="btn btn-warning btn-sm">
                              Edit
                            </button>
                          </Link>
                        </td>
                        <td>
                          {district &&
                            district.length > 0 &&
                            district.find(
                              (a) =>
                                String(a.LGDCode) ===
                                String(vidhansabha.DistCode)
                            )?.districtName}
                        </td>
                        <td>{vidhansabha.ConstituencyNumber}</td>
                        <td>{vidhansabha.ConstituencyName}</td>
                        <td>{vidhansabha.ConstituencyNameHindi}</td>

                      </tr>
                    ))}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <nav aria-label="...">
                  {/* Pagination Component */}
                  <Pagination className="pagination justify-content-end">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i} active={i + 1 === currentPage}>
                        <PaginationLink
                          onClick={() => handleDistrictPageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddVidhansabha;
