﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using System.Drawing;
using System.IO;
using System.Runtime.InteropServices.WindowsRuntime;
using AjaxControlToolkit.HTMLEditor.ToolbarButton;
using System.Web.UI.DataVisualization.Charting;

public partial class candreg_registration : System.Web.UI.Page
{
    MyDbOps dbops = new MyDbOps();
    public bool isFinalsubmit = false;
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["userID"] == null)
        {
            Response.Redirect("~//candreg_login.aspx");
        }
        else
        {
            if (!IsPostBack)
            {
                Txtmobileno.Text = Session["userID"].ToString();
                IsFinalCompleted(Txtmobileno.Text.Trim());
                IsReadNirdesh(Txtmobileno.Text.Trim());
                bindgender();
                bindcaste();
                bindstate();
                FillPermanentDistrict();
                dvhandicapetype.Visible = false;
                dvcategory.Visible = false;
                dvagevalid.Visible = false;
                GetData();
            }
        }
    }
    #region Methods
    public void GetData()
    {
        int id = 0;
        if (Request.QueryString["Id"] != null)
        {
            id = int.Parse(Request.QueryString["Id"]);
        }
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select RegistrationNo,First_nameH,Middle_nameH,Last_NameH,First_nameE,Middle_nameE,Last_NameE,
GenderID,DOB,age,Relative_name,Relative_nameE,Mother_name,Mother_nameE,CasteID,Email,StateCode,DistrictCCode,Address,PinCode,
Per_StateCode,Per_DistrictCCode,Per_Address,Per_Pincode,Photopath,Signpath,Marksheet10Path,Marksheet5Path,CastCertificatePath,
IsGovEmp,IsServiceEmp,maritalstatus,IsDisabled,DisabledType,attendantpost,otherpost,servantpriority,watchmanpriority,
sweeperpriority,is_intercastemarriagepromotion,is_govawards,is_defenceservices,is_bonafidecand,is_certifiedindian,
is_sepereated,is_excluded,is_terminated,is_guilty,dist_preferencecode,castesrno,castedist,castename,castedate
  FROM tbl_CandidateMaster where Id=@id";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@id", SqlDbType.Int).Value = id;
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                txtfnameh.Text = dtShops.Rows[0]["First_nameH"].ToString();
                txtmnameh.Text = dtShops.Rows[0]["Middle_nameH"].ToString();
                txtlnameh.Text = dtShops.Rows[0]["Last_NameH"].ToString();
                txtfnameE.Text = dtShops.Rows[0]["First_nameE"].ToString();
                txtMnameE.Text = dtShops.Rows[0]["Middle_nameE"].ToString();
                txtlnameE.Text = dtShops.Rows[0]["Last_NameE"].ToString();
                Txtrelativename.Text = dtShops.Rows[0]["Relative_name"].ToString();
                TxtrelativenameE.Text = dtShops.Rows[0]["Relative_nameE"].ToString();
                Txtmothername.Text = dtShops.Rows[0]["Mother_name"].ToString();
                TxtmothernameE.Text = dtShops.Rows[0]["Mother_nameE"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Photopath"].ToString()))
                {
                    a_photoUplodad.Visible = true;
                    string str = dtShops.Rows[0]["Photopath"].ToString().Replace("candreg/", "");
                    a_photoUplodad.NavigateUrl = "../" + str;
                    Hiddenphoto.Value = dtShops.Rows[0]["Photopath"].ToString();
                }
                else
                {
                    a_photoUplodad.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Signpath"].ToString()))
                {
                    a_SignUpload.Visible = true;
                    string str = dtShops.Rows[0]["Signpath"].ToString().Replace("candreg/", "");
                    a_SignUpload.NavigateUrl = "../" + str;
                    Hiddensign.Value = dtShops.Rows[0]["Signpath"].ToString();
                }
                else
                {
                    a_SignUpload.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["StateCode"].ToString()))
                {
                    ddlstate.SelectedValue = dtShops.Rows[0]["StateCode"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DistrictCCode"].ToString()))
                {
                    ddldistrict.SelectedValue = dtShops.Rows[0]["DistrictCCode"].ToString();
                }
                txtaddress.Text = dtShops.Rows[0]["Address"].ToString();
                Txtpincode.Text = dtShops.Rows[0]["PinCode"].ToString();
                //Chkboxaddress.Text = dtShops.Rows[0]["First_nameH"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Per_StateCode"].ToString()))
                {
                    perddlstate.SelectedValue = dtShops.Rows[0]["Per_StateCode"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Per_DistrictCCode"].ToString()))
                {
                    perddldistrict.SelectedValue = dtShops.Rows[0]["Per_DistrictCCode"].ToString();
                }
                pertxtadd.Text = dtShops.Rows[0]["Per_Address"].ToString();
                pertxtpincode.Text = dtShops.Rows[0]["Per_Pincode"].ToString();
                if (dtShops.Rows[0]["IsGovEmp"].ToString() == "Y")
                {
                    chkgov.Checked = true;
                }
                if (dtShops.Rows[0]["IsServiceEmp"].ToString() == "Y")
                {
                    chkservice.Checked = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["CasteID"].ToString()))
                {
                    ddlcategory.SelectedValue = dtShops.Rows[0]["CasteID"].ToString();
                    if (dtShops.Rows[0]["CasteID"].ToString() != "1")
                    {
                        dvcategory.Visible = true;
                    }
                    else
                    {
                        dvcategory.Visible = false;
                    }
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["maritalstatus"].ToString()))
                {
                    ddlmarriedstatus.SelectedValue = dtShops.Rows[0]["maritalstatus"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["GenderID"].ToString()))
                {
                    ddlgender.SelectedValue = dtShops.Rows[0]["GenderID"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DOB"].ToString()))
                {
                    txtdob.Text = Convert.ToDateTime(dtShops.Rows[0]["DOB"]).ToString("yyyy-MM-dd");
                }
                txtage.Text = dtShops.Rows[0]["age"].ToString();
                if (!string.IsNullOrEmpty(txtage.Text.Trim()))
                {
                    validateage(int.Parse(txtage.Text.Trim()));
                }
                txtcastesrno.Text = dtShops.Rows[0]["castesrno"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["castedist"].ToString()))
                {
                    ddlcastedist.SelectedValue = dtShops.Rows[0]["castedist"].ToString();
                }
                txtcastename.Text = dtShops.Rows[0]["castename"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["castedate"].ToString()))
                {
                    txtcastedate.Text = Convert.ToDateTime(dtShops.Rows[0]["castedate"]).ToString("yyyy-MM-dd");
                }
                Txtmobileno.Text = dtShops.Rows[0]["RegistrationNo"].ToString();
                Textemail.Text = dtShops.Rows[0]["Email"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["IsDisabled"].ToString()))
                {
                    ddlhandicape.SelectedValue = dtShops.Rows[0]["IsDisabled"].ToString();
                    if (dtShops.Rows[0]["IsDisabled"].ToString() == "Y")
                    {
                        dvhandicapetype.Visible = true;
                    }
                    else
                    {
                        dvhandicapetype.Visible = false;
                    }
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DisabledType"].ToString()))
                {
                    ddlhandicapetype.SelectedValue = dtShops.Rows[0]["DisabledType"].ToString();
                }
                if (dtShops.Rows[0]["attendantpost"].ToString() == "Y")
                {
                    chklabattendant.Checked = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Marksheet10Path"].ToString()))
                {
                    a_file10thmarksheet.Visible = true;
                    string str = dtShops.Rows[0]["Marksheet10Path"].ToString().Replace("candreg/", "");
                    a_file10thmarksheet.NavigateUrl = "../" + str;
                    HiddenMarksheet.Value = dtShops.Rows[0]["Marksheet10Path"].ToString();
                }
                else
                {
                    a_file10thmarksheet.Visible = false;
                }
                if (dtShops.Rows[0]["otherpost"].ToString() == "Y")
                {
                    chkpost.Checked = true;
                    dvpriority.Visible = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Marksheet5Path"].ToString()))
                {
                    a_file5thmarksheet.Visible = true;
                    string str = dtShops.Rows[0]["Marksheet5Path"].ToString().Replace("candreg/", "");
                    a_file5thmarksheet.NavigateUrl = "../" + str;
                    HiddenMarksheet5th.Value = dtShops.Rows[0]["Marksheet5Path"].ToString();
                }
                else
                {
                    a_file5thmarksheet.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["servantpriority"].ToString()))
                {
                    ddlservantpriority.SelectedValue = dtShops.Rows[0]["servantpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["watchmanpriority"].ToString()))
                {
                    ddlwatchmanpriority.SelectedValue = dtShops.Rows[0]["watchmanpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["sweeperpriority"].ToString()))
                {
                    ddlsweeperpriority.SelectedValue = dtShops.Rows[0]["sweeperpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_intercastemarriagepromotion"].ToString()))
                {
                    rdnis_intercastemarriagepromotion.SelectedValue = dtShops.Rows[0]["is_intercastemarriagepromotion"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_govawards"].ToString()))
                {
                    rdnis_govawards.SelectedValue = dtShops.Rows[0]["is_govawards"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_defenceservices"].ToString()))
                {
                    rdnis_defenceservices.SelectedValue = dtShops.Rows[0]["is_defenceservices"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_bonafidecand"].ToString()))
                {
                    rdnis_bonafidecand.SelectedValue = dtShops.Rows[0]["is_bonafidecand"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_certifiedindian"].ToString()))
                {
                    rdnis_certifiedindian.SelectedValue = dtShops.Rows[0]["is_certifiedindian"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_sepereated"].ToString()))
                {
                    rdnis_sepereated.SelectedValue = dtShops.Rows[0]["is_sepereated"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_excluded"].ToString()))
                {
                    rdnis_excluded.SelectedValue = dtShops.Rows[0]["is_excluded"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_terminated"].ToString()))
                {
                    rdnis_terminated.SelectedValue = dtShops.Rows[0]["is_terminated"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_guilty"].ToString()))
                {
                    rdnis_guilty.SelectedValue = dtShops.Rows[0]["is_guilty"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["dist_preferencecode"].ToString()))
                {
                    ddlpreference.SelectedValue = dtShops.Rows[0]["dist_preferencecode"].ToString();
                }
            }
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindgender()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select GenderID,GenderNameHin from GenderMaster";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlgender.DataSource = dtShops;
                ddlgender.DataTextField = "GenderNameHin";
                ddlgender.DataValueField = "GenderID";
                ddlgender.DataBind();
            }
            ddlgender.Items.Insert(0, new ListItem("--चुने--", "0"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindcaste()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select CasteID,CasteNameHin from casteMaster";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlcategory.DataSource = dtShops;
                ddlcategory.DataTextField = "CasteNameHin";
                ddlcategory.DataValueField = "CasteID";
                ddlcategory.DataBind();
            }
            ddlcategory.Items.Insert(0, new ListItem("--चुने--", "0"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindstate()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select StateCode,NameHin from tStateView  order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlstate.DataSource = dtShops;
                ddlstate.DataTextField = "NameHin";
                ddlstate.DataValueField = "StateCode";
                ddlstate.DataBind();
                perddlstate.DataSource = dtShops;
                perddlstate.DataTextField = "NameHin";
                perddlstate.DataValueField = "StateCode";
                perddlstate.DataBind();
            }
            ddlstate.SelectedValue = "22";
            perddlstate.SelectedValue = "22";
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void binddistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", ddlstate.SelectedValue);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddldistrict.DataSource = dtShops;
                ddldistrict.DataTextField = "NameHin";
                ddldistrict.DataValueField = "LGDDistCode";
                ddldistrict.DataBind();
            }
            ddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddldistrict.Items.Remove(ddldistrict.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindperdistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", ddlstate.SelectedValue);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                perddldistrict.DataSource = dtShops;
                perddldistrict.DataTextField = "NameHin";
                perddldistrict.DataValueField = "LGDDistCode";
                perddldistrict.DataBind();
            }
            perddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            perddldistrict.Items.Remove(perddldistrict.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void FillPermanentDistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", "22");
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddldistrict.DataSource = dtShops;
                ddldistrict.DataTextField = "NameHin";
                ddldistrict.DataValueField = "LGDDistCode";
                ddldistrict.DataBind();
                perddldistrict.DataSource = dtShops;
                perddldistrict.DataTextField = "NameHin";
                perddldistrict.DataValueField = "LGDDistCode";
                perddldistrict.DataBind();
                ddlcastedist.DataSource = dtShops;
                ddlcastedist.DataTextField = "NameHin";
                ddlcastedist.DataValueField = "LGDDistCode";
                ddlcastedist.DataBind();
                ddlpreference.DataSource = dtShops;
                ddlpreference.DataTextField = "NameHin";
                ddlpreference.DataValueField = "LGDDistCode";
                ddlpreference.DataBind();
            }
            ddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            perddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddlcastedist.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddlpreference.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddldistrict.Items.Remove(ddldistrict.Items.FindByValue("-1"));
            perddldistrict.Items.Remove(perddldistrict.Items.FindByValue("-1"));
            ddlcastedist.Items.Remove(ddlcastedist.Items.FindByValue("-1"));
            ddlpreference.Items.Remove(ddlpreference.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }

    #endregion
    public void IsFinalCompleted(string rmn)
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select IsCompleted from tbl_CandidateMaster where RegistrationNo=@rmn";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@rmn", rmn);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                if (dtShops.Rows[0][0].ToString() == "Y")
                {
                    dvregform.Visible = false;
                    dvfinal.Visible = true;
                }
                else
                {
                    dvregform.Visible = true;
                    dvfinal.Visible = false;
                }
            }
            else
            {
                dvregform.Visible = true;
                dvfinal.Visible = false;
            }
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void IsReadNirdesh(string rmn)
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select is_readnirdesh from tbl_nirdesh where Registration_No = @regno";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@regno", rmn);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                if (dtShops.Rows[0][0].ToString() != "Y")
                {
                    Response.Redirect("candidate.aspx");
                }
            }
            else
            {
                Response.Redirect("candidate.aspx");
            }
            conn.Close();
            dtShops.Dispose();
        }
    }
    protected void ddlstate_SelectedIndexChanged(object sender, EventArgs e)
    {
        binddistrict();
    }

    protected void perddlstate_SelectedIndexChanged(object sender, EventArgs e)
    {
        bindperdistrict();
    }

    protected void ddlhandicape_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlhandicape.SelectedValue == "Y")
        {
            dvhandicapetype.Visible = true;
        }
        else
        {
            dvhandicapetype.Visible = false;
        }
    }
    protected void Chkboxaddress_CheckedChanged(object sender, EventArgs e)
    {
        if (Chkboxaddress.Checked)
        {
            perddlstate.SelectedIndex = ddlstate.SelectedIndex;
            perddlstate.SelectedValue = ddlstate.SelectedValue;
            if (ddlstate.SelectedValue == "22")
            {
                perddldistrict.SelectedIndex = ddldistrict.SelectedIndex;
                perddldistrict.SelectedValue = ddldistrict.SelectedValue;
            }
            else
            {
                perddldistrict.SelectedIndex = 0;
            }
            pertxtadd.Text = txtaddress.Text;
            pertxtpincode.Text = Txtpincode.Text;
        }
        else
        {
            perddlstate.SelectedIndex = 0;
            perddldistrict.SelectedIndex = 0;
            pertxtadd.Text = "";
            pertxtpincode.Text = "";
        }
    }
    public int CalculateAgeCorrect(DateTime birthDate, DateTime now)
    {
        int age = now.Year - birthDate.Year;

        if (now.Month < birthDate.Month || (now.Month == birthDate.Month && now.Day < birthDate.Day))
            age--;

        return age;
    }
    protected void txtdob_TextChanged(object sender, EventArgs e)
    {
        if (ddlcategory.SelectedValue == "0")
        {
            string jv = "<script>alert('Select Category!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlcategory.Focus();
            ddlcategory.BackColor = Color.Pink;
            return;
        }
        if (ddlgender.SelectedValue == "0")
        {
            string jv = "<script>alert('Select Gender!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            return;
        }
        if (txtdob.Text.Trim() == "")
        {
            string jv = "<script>alert('Enter DOB!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            return;
        }
        DateTime now = new DateTime(2023, 1, 1);
        int age = CalculateAgeCorrect(DateTime.Parse(txtdob.Text), now);
        txtage.Text = age.ToString();
        validateage(age);
    }
    protected void ddlcategory_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlcategory.SelectedValue != "1" && ddlcategory.SelectedValue != "0")
        {
            dvcategory.Visible = true;
        }
        else
        {
            dvcategory.Visible = false;
        }
    }

    protected void ddlhandicapetype_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlhandicapetype.SelectedValue != "OL" && ddlhandicapetype.SelectedValue != "HH")
        {
            dv10thmarksheet.Visible = false;
        }
        else
        {
            dv10thmarksheet.Visible = true;
        }
    }

    protected void ddlservantpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlservantpriority.SelectedValue != "" && ddlwatchmanpriority.SelectedValue != "" && ddlsweeperpriority.SelectedValue != "")
        {
            int s = int.Parse(ddlservantpriority.SelectedValue);
            int w = int.Parse(ddlwatchmanpriority.SelectedValue);
            int p = int.Parse(ddlsweeperpriority.SelectedValue);
            if (s != 0 && p != 0 && w != 0)
            {
                if (s == w || s == p)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (w == p || w == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (p == w || p == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
            }
        }
    }

    protected void ddlwatchmanpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlservantpriority.SelectedValue != "" && ddlwatchmanpriority.SelectedValue != "" && ddlsweeperpriority.SelectedValue != "")
        {
            int s = int.Parse(ddlservantpriority.SelectedValue);
            int w = int.Parse(ddlwatchmanpriority.SelectedValue);
            int p = int.Parse(ddlsweeperpriority.SelectedValue);
            if (s != 0 && p != 0 && w != 0)
            {
                if (s == w || s == p)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (w == p || w == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (p == w || p == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
            }
        }
    }

    protected void ddlsweeperpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlservantpriority.SelectedValue != "" && ddlwatchmanpriority.SelectedValue != "" && ddlsweeperpriority.SelectedValue != "")
        {
            int s = int.Parse(ddlservantpriority.SelectedValue);
            int w = int.Parse(ddlwatchmanpriority.SelectedValue);
            int p = int.Parse(ddlsweeperpriority.SelectedValue);
            if (s != 0 && p != 0 && w != 0)
            {
                if (s == w || s == p)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (w == p || w == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
                if (p == w || p == s)
                {
                    string jv = "<script>alert('Priority should be different!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    ddlservantpriority.SelectedValue = "0";
                    ddlwatchmanpriority.SelectedValue = "0";
                    ddlsweeperpriority.SelectedValue = "0";
                }
            }
        }
    }

    protected void Btnuplodphoto_Click(object sender, EventArgs e)
    {
        try
        {
            if (Session["userID"] != null)
            {
                if (photoUplodad.HasFile)
                {
                    if (CheckDoubleExtension(photoUplodad.FileName, photoUplodad.FileBytes))
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                        return;
                    }
                    string newname = Session["userID"].ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss").ToString();
                    string extension = Path.GetExtension(photoUplodad.PostedFile.FileName);
                    if (extension.ToLower() == ".jpg" || extension.ToLower() == ".png" || extension.ToLower() == ".jpeg")
                    {
                        string filename = "PhotoOf" + newname + extension;
                        Hiddenphoto.Value = "candregfile/candphoto/" + filename;
                        //string rootFolder = Server.MapPath("~/candreg/candphoto/");
                        string rootFolder = @"E:\\candregfile\\candphoto\\";
                        //string rootFolder = @"D:\\candregfile\\candphoto\\";
                        if (File.Exists(Path.Combine(rootFolder, filename)))
                        {
                            File.Delete(Path.Combine(rootFolder, filename));
                        }
                        //photoUplodad.SaveAs(Server.MapPath("~/candreg/candphoto/" + filename));
                        photoUplodad.SaveAs(@"E:\\candregfile\\candphoto\\" + filename);
                        //photoUplodad.SaveAs(@"D:\\candregfile\\candphoto\\" + filename);
                        a_photoUplodad.Visible = true;
                        a_photoUplodad.NavigateUrl = "../candregfile/candphoto/" + filename;
                        string jv = "<script>alert('फोटो अपलोडेड।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                    else
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                }
                else
                {
                    string jv = "<script>alert('कृपया फोटो अपलोड करे ।');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    photoUplodad.Focus();
                }
            }
            else
            {
                Response.Redirect("candreg_login.aspx");
            }
        }
        catch (Exception ex)
        {
            string jv = "<script>alert('Error has been Occur!!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
        }
    }
    protected void BtnUploadSign_Click(object sender, EventArgs e)
    {
        try
        {
            if (Session["userID"] != null)
            {
                if (SignUpload.HasFile)
                {
                    if (CheckDoubleExtension(SignUpload.FileName, SignUpload.FileBytes))
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                        return;
                    }

                    string newname = Session["userID"].ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss").ToString();
                    string extension = Path.GetExtension(SignUpload.PostedFile.FileName);
                    if (extension.ToLower() == ".jpg" || extension.ToLower() == ".png" || extension.ToLower() == ".jpeg")
                    {
                        string filename = "SignOf" + newname + extension;
                        //string rootFolder = Server.MapPath("~/candreg/candsign/");
                        string rootFolder = @"E:\\candregfile\\candsign\\";
                        //string rootFolder = @"D:\\candregfile\\candsign\\";
                        if (File.Exists(Path.Combine(rootFolder, filename)))
                            File.Delete(Path.Combine(rootFolder, filename));

                        Hiddensign.Value = "candregfile/candsign/" + filename;
                        //SignUpload.SaveAs(Server.MapPath("~/candreg/candsign/" + filename));
                        SignUpload.SaveAs(@"E:\\candregfile\\candsign\\" + filename);
                        //SignUpload.SaveAs(@"D:\\candregfile\\candsign\\" + filename);
                        a_SignUpload.Visible = true;
                        a_SignUpload.NavigateUrl = "../candregfile/candsign/" + filename;
                        string jv = "<script>alert('हस्ताक्षर अपलोडेड।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                    else
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे ।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                }
                else
                {
                    string jv = "<script>alert('कृपया साइन अपलोड करे ।');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    SignUpload.Focus();
                }
            }
            else
            {
                Response.Redirect("candreg_login.aspx");
            }
        }
        catch (Exception ex)
        {
            string jv = "<script>alert('Error has been Occur!!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
        }
    }
    public bool CheckDoubleExtension(string fileName, byte[] fileBytes)
    {
        string ext = Path.GetExtension(
                        Path.GetFileNameWithoutExtension(
                            fileName));

        bool hasExecutableExtension = ext.Equals(".exe", StringComparison.InvariantCultureIgnoreCase);
        bool hasbatExtension = ext.Equals(".bat", StringComparison.InvariantCultureIgnoreCase);

        bool isExecutableFile = fileBytes.Length > 1 &&
                        fileBytes[0] == 0x4D &&
                        fileBytes[1] == 0x5A;

        //int count = photoUplodad.PostedFile.FileName.Split('.').Length - 1;
        int count = fileName.Split('.').Length - 1;

        if (hasExecutableExtension || isExecutableFile || hasbatExtension || count > 1)
            return true;
        else
            return false;
    }

    protected void btupload10th_Click(object sender, EventArgs e)
    {
        if (Session["userID"] != null)
        {
            try
            {
                string newName = Session["userID"].ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss").ToString();
                if (file10thmarksheet.HasFile)
                {
                    if (CheckDoubleExtension(file10thmarksheet.FileName, file10thmarksheet.FileBytes))
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                        return;
                    }
                    string extension = Path.GetExtension(file10thmarksheet.PostedFile.FileName);
                    if (extension.ToLower() == ".pdf")
                    {
                        string filename = "MS_10thOf" + newName + extension;
                        //string rootFolder = Server.MapPath("~/candreg/MS_10th/");
                        string rootFolder = @"E:\\candregfile\\MS_10th\\";
                        //string rootFolder = @"D:\\candregfile\\MS_10th\\";
                        if (File.Exists(Path.Combine(rootFolder, filename)))
                            File.Delete(Path.Combine(rootFolder, filename));
                        HiddenMarksheet.Value = "candregfile/MS_10th/" + filename;
                        //file10thmarksheet.SaveAs(Server.MapPath("~/candreg/MS_10th/" + filename));
                        file10thmarksheet.SaveAs(@"E:\\candregfile\\MS_10th\\" + filename);
                        //file10thmarksheet.SaveAs(@"D:\\candregfile\\MS_10th\\" + filename);
                        a_file10thmarksheet.Visible = true;
                        a_file10thmarksheet.NavigateUrl = "../candregfile/MS_10th/" + filename;
                        string jv = "<script>alert('मार्कशीट अपलोडेड।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                    else
                    {
                        string jv = "<script>alert('केवल PDF फाइल का चयन करे ।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                }
                else
                {
                    string jv = "<script>alert('कृपया मार्कशीट अपलोड करे ।');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            catch (Exception ex)
            {
                string jv = "<script>alert('Error has been Occur!!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        else
        {
            Response.Redirect("candreg_login.aspx");
        }
    }

    protected void btnupload5th_Click(object sender, EventArgs e)
    {
        if (Session["userID"] != null)
        {
            try
            {
                string newName = Session["userID"].ToString() + "_" + DateTime.Now.ToString("yyyyMMddHHmmss").ToString();
                if (file5thmarksheet.HasFile)
                {
                    if (CheckDoubleExtension(file5thmarksheet.FileName, file5thmarksheet.FileBytes))
                    {
                        string jv = "<script>alert('कृपया सही फाइल का चयन करे।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                        return;
                    }
                    string extension = Path.GetExtension(file5thmarksheet.PostedFile.FileName);
                    if (extension.ToLower() == ".pdf")
                    {
                        string filename = "MS_5thOf" + newName + extension;
                        //string rootFolder = Server.MapPath("~/candreg/MS_5th/");
                        string rootFolder = @"E:\\candregfile\\MS_5th\\";
                        //string rootFolder = @"D:\\candregfile\\MS_5th\\";
                        if (File.Exists(Path.Combine(rootFolder, filename)))
                            File.Delete(Path.Combine(rootFolder, filename));
                        HiddenMarksheet5th.Value = "candregfile/MS_5th/" + filename;
                        //file5thmarksheet.SaveAs(Server.MapPath("~/candreg/MS_5th/" + filename));
                        file5thmarksheet.SaveAs(@"E:\\candregfile\\MS_5th\\" + filename);
                        //file5thmarksheet.SaveAs(@"D:\\candregfile\\MS_5th\\" + filename);
                        a_file5thmarksheet.Visible = true;
                        a_file5thmarksheet.NavigateUrl = "../candregfile/MS_5th/" + filename;
                        string jv = "<script>alert('मार्कशीट अपलोडेड।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                    else
                    {
                        string jv = "<script>alert('केवल PDF फाइल का चयन करे ।');</script>";
                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    }
                }
                else
                {
                    string jv = "<script>alert('कृपया मार्कशीट अपलोड करे ।');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            catch (Exception ex)
            {
                string jv = "<script>alert('Error has been Occur!!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        else
        {
            Response.Redirect("candreg_login.aspx");
        }
    }

    protected void chktrue_CheckedChanged(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(txtfnameh.Text.Trim()) || txtfnameh.Text.Trim() == "")
        {
            string jv = "<script>alert('प्रथम नाम हिंदी में भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtfnameh.Focus();
            txtfnameh.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(txtfnameE.Text.Trim()) || txtfnameE.Text.Trim() == "")
        {
            string jv = "<script>alert('प्रथम नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtfnameE.Focus();
            txtfnameE.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(Txtrelativename.Text.Trim()) || Txtrelativename.Text.Trim() == "")
        {
            string jv = "<script>alert('पिता /पति का नाम (हिंदी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtrelativename.Focus();
            Txtrelativename.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(TxtrelativenameE.Text.Trim()) || TxtrelativenameE.Text.Trim() == "")
        {
            string jv = "<script>alert('पिता /पति का नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            TxtrelativenameE.Focus();
            TxtrelativenameE.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(TxtmothernameE.Text.Trim()) || TxtmothernameE.Text.Trim() == "")
        {
            string jv = "<script>alert('माता का नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            TxtmothernameE.Focus();
            TxtmothernameE.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(Txtmothername.Text.Trim()) || Txtmothername.Text.Trim() == "")
        {
            string jv = "<script>alert('माता का नाम (हिंदी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtmothername.Focus();
            Txtmothername.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(Hiddenphoto.Value.Trim()) || Hiddenphoto.Value == "")
        {
            string jv = "<script>alert('फोटो अपलोड करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            photoUplodad.Focus();
            photoUplodad.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(Hiddensign.Value.Trim()) || Hiddensign.Value == "")
        {
            string jv = "<script>alert('हस्ताक्षर अपलोड करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            SignUpload.Focus();
            SignUpload.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(ddldistrict.SelectedValue.Trim()) || ddldistrict.SelectedValue == "0")
        {
            string jv = "<script>alert('जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddldistrict.Focus();
            ddldistrict.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(txtaddress.Text.Trim()) || txtaddress.Text.Trim() == "")
        {
            string jv = "<script>alert('पता भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtaddress.Focus();
            txtaddress.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(Txtpincode.Text.Trim()) || Txtpincode.Text.Trim() == "")
        {
            string jv = "<script>alert('पिनकोड भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtpincode.Focus();
            Txtpincode.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(perddldistrict.SelectedValue.Trim()) || perddldistrict.SelectedValue == "0")
        {
            string jv = "<script>alert('जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            perddldistrict.Focus();
            perddldistrict.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(pertxtadd.Text.Trim()) || pertxtadd.Text.Trim() == "")
        {
            string jv = "<script>alert('पता भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            pertxtadd.Focus();
            pertxtadd.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(pertxtpincode.Text.Trim()) || pertxtpincode.Text.Trim() == "")
        {
            string jv = "<script>alert('पिनकोड भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            pertxtpincode.Focus();
            pertxtpincode.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(ddlcategory.SelectedValue.Trim()) || ddlcategory.SelectedValue == "0")
        {
            string jv = "<script>alert('जाति संवर्ग का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlcategory.Focus();
            ddlcategory.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(ddlmarriedstatus.SelectedValue.Trim()) || ddlmarriedstatus.SelectedValue == "0")
        {
            string jv = "<script>alert('वैवाहिक स्थिति का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlmarriedstatus.Focus();
            ddlmarriedstatus.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(ddlgender.SelectedValue.Trim()) || ddlgender.SelectedValue == "0")
        {
            string jv = "<script>alert('लिंग का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(txtdob.Text.Trim()) || txtdob.Text.Trim() == "")
        {
            string jv = "<script>alert('जन्म दिनांक भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtdob.Focus();
            txtdob.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (!string.IsNullOrEmpty(ddlcategory.SelectedValue.Trim()) && ddlcategory.SelectedValue != "1")
        {
            if (string.IsNullOrEmpty(txtcastesrno.Text.Trim()) || txtcastesrno.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र क्रमांक भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastesrno.Focus();
                txtcastesrno.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(ddlcastedist.SelectedValue.Trim()) || ddlcastedist.SelectedValue == "0")
            {
                string jv = "<script>alert('प्रमाण पत्र जारी करने वाले जिला का नाम चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlcastedist.Focus();
                ddlcastedist.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(txtcastename.Text.Trim()) || txtcastename.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र किसके द्वारा जारी किया गया भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastename.Focus();
                txtcastename.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(txtcastedate.Text.Trim()) || txtcastedate.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र जारी करने की तिथि भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastedate.Focus();
                txtcastedate.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
        }
        if (string.IsNullOrEmpty(ddlhandicape.SelectedValue.Trim()) || ddlhandicape.SelectedValue == "0")
        {
            string jv = "<script>alert('क्या आप शारीरिक रूप से असक्षम (पी एच / दिव्यांग) हैं? हाँ / नहीं का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlhandicape.Focus();
            ddlhandicape.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (ddlhandicape.SelectedValue == "Y")
        {
            if (string.IsNullOrEmpty(ddlhandicapetype.SelectedValue.Trim()) || ddlhandicapetype.SelectedValue == "0")
            {
                string jv = "<script>alert('विकलांगता का प्रकार चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlhandicapetype.Focus();
                ddlhandicapetype.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
        }
        if (chklabattendant.Checked)
        {
            if (string.IsNullOrEmpty(HiddenMarksheet.Value.Trim()) || HiddenMarksheet.Value == "")
            {
                string jv = "<script>alert('10th मार्कशीट अपलोड करें!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                file10thmarksheet.Focus();
                file10thmarksheet.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
        }
        if (chkpost.Checked)
        {
            if (string.IsNullOrEmpty(HiddenMarksheet5th.Value.Trim()) || HiddenMarksheet5th.Value == "")
            {
                string jv = "<script>alert('5th मार्कशीट अपलोड करें!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                file5thmarksheet.Focus();
                file5thmarksheet.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(ddlservantpriority.SelectedValue.Trim()) || ddlservantpriority.SelectedValue == "")
            {
                string jv = "<script>alert('भृत्य की प्राथमिकता का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.Focus();
                ddlservantpriority.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(ddlwatchmanpriority.SelectedValue.Trim()) || ddlwatchmanpriority.SelectedValue == "")
            {
                string jv = "<script>alert('चौकीदार की प्राथमिकता का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlwatchmanpriority.Focus();
                ddlwatchmanpriority.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
            if (string.IsNullOrEmpty(ddlsweeperpriority.SelectedValue.Trim()) || ddlsweeperpriority.SelectedValue == "")
            {
                string jv = "<script>alert('स्वीपर का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlsweeperpriority.Focus();
                ddlsweeperpriority.BackColor = Color.Pink;
                chktrue.Checked = false;
                return;
            }
        }
        if (!chklabattendant.Checked && !chkpost.Checked)
        {
            string jv = "<script>alert('किसी एक पद का चयन करें!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            chklabattendant.Focus();
            chklabattendant.BackColor = Color.Pink;
            chkpost.Focus();
            chkpost.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (string.IsNullOrEmpty(ddlpreference.SelectedValue.Trim()) || ddlpreference.SelectedValue == "0")
        {
            string jv = "<script>alert('परीक्षा केंद्र प्राथमिकता जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlpreference.Focus();
            ddlpreference.BackColor = Color.Pink;
            chktrue.Checked = false;
            return;
        }
        if (chktrue.Checked)
        {
            btnRegister.Enabled = true;
        }
        else
        {
            btnRegister.Enabled = false;
        }
    }

    protected void btndraft_Click(object sender, EventArgs e)
    {
        InsertRecord();
    }

    protected void btnpreview_Click(object sender, EventArgs e)
    {
        int candId = 0;
        if (Request.QueryString["Id"] != null)
        {
            candId = int.Parse(Request.QueryString["Id"].ToString());
        }
        Response.Redirect("PreviewForm.aspx?Id=" + candId);
    }

    protected void btnRegister_Click(object sender, EventArgs e)
    {
        if (string.IsNullOrEmpty(txtfnameh.Text.Trim()) || txtfnameh.Text.Trim() == "")
        {
            string jv = "<script>alert('प्रथम नाम हिंदी में भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtfnameh.Focus();
            txtfnameh.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(txtfnameE.Text.Trim()) || txtfnameE.Text.Trim() == "")
        {
            string jv = "<script>alert('प्रथम नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtfnameE.Focus();
            txtfnameE.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(Txtrelativename.Text.Trim()) || Txtrelativename.Text.Trim() == "")
        {
            string jv = "<script>alert('पिता /पति का नाम (हिंदी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtrelativename.Focus();
            Txtrelativename.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(TxtrelativenameE.Text.Trim()) || TxtrelativenameE.Text.Trim() == "")
        {
            string jv = "<script>alert('पिता /पति का नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            TxtrelativenameE.Focus();
            TxtrelativenameE.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(TxtmothernameE.Text.Trim()) || TxtmothernameE.Text.Trim() == "")
        {
            string jv = "<script>alert('माता का नाम (अंग्रेजी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            TxtmothernameE.Focus();
            TxtmothernameE.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(Txtmothername.Text.Trim()) || Txtmothername.Text.Trim() == "")
        {
            string jv = "<script>alert('माता का नाम (हिंदी में) भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtmothername.Focus();
            Txtmothername.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(Hiddenphoto.Value.Trim()) || Hiddenphoto.Value == "")
        {
            string jv = "<script>alert('फोटो अपलोड करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            photoUplodad.Focus();
            photoUplodad.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(Hiddensign.Value.Trim()) || Hiddensign.Value == "")
        {
            string jv = "<script>alert('हस्ताक्षर अपलोड करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            SignUpload.Focus();
            SignUpload.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(ddldistrict.SelectedValue.Trim()) || ddldistrict.SelectedValue == "0")
        {
            string jv = "<script>alert('जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddldistrict.Focus();
            ddldistrict.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(txtaddress.Text.Trim()) || txtaddress.Text.Trim() == "")
        {
            string jv = "<script>alert('पता भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtaddress.Focus();
            txtaddress.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(Txtpincode.Text.Trim()) || Txtpincode.Text.Trim() == "")
        {
            string jv = "<script>alert('पिनकोड भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            Txtpincode.Focus();
            Txtpincode.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(perddldistrict.SelectedValue.Trim()) || perddldistrict.SelectedValue == "0")
        {
            string jv = "<script>alert('जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            perddldistrict.Focus();
            perddldistrict.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(pertxtadd.Text.Trim()) || pertxtadd.Text.Trim() == "")
        {
            string jv = "<script>alert('पता भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            pertxtadd.Focus();
            pertxtadd.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(pertxtpincode.Text.Trim()) || pertxtpincode.Text.Trim() == "")
        {
            string jv = "<script>alert('पिनकोड भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            pertxtpincode.Focus();
            pertxtpincode.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(ddlcategory.SelectedValue.Trim()) || ddlcategory.SelectedValue == "0")
        {
            string jv = "<script>alert('जाति संवर्ग का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlcategory.Focus();
            ddlcategory.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(ddlmarriedstatus.SelectedValue.Trim()) || ddlmarriedstatus.SelectedValue == "0")
        {
            string jv = "<script>alert('वैवाहिक स्थिति का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlmarriedstatus.Focus();
            ddlmarriedstatus.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(ddlgender.SelectedValue.Trim()) || ddlgender.SelectedValue == "0")
        {
            string jv = "<script>alert('लिंग का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(txtdob.Text.Trim()) || txtdob.Text.Trim() == "")
        {
            string jv = "<script>alert('जन्म दिनांक भरे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            txtdob.Focus();
            txtdob.BackColor = Color.Pink;
            return;
        }
        if (!string.IsNullOrEmpty(ddlcategory.SelectedValue.Trim()) && ddlcategory.SelectedValue != "1")
        {
            if (string.IsNullOrEmpty(txtcastesrno.Text.Trim()) || txtcastesrno.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र क्रमांक भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastesrno.Focus();
                txtcastesrno.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(ddlcastedist.SelectedValue.Trim()) || ddlcastedist.SelectedValue == "0")
            {
                string jv = "<script>alert('प्रमाण पत्र जारी करने वाले जिला का नाम चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlcastedist.Focus();
                ddlcastedist.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(txtcastename.Text.Trim()) || txtcastename.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र किसके द्वारा जारी किया गया भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastename.Focus();
                txtcastename.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(txtcastedate.Text.Trim()) || txtcastedate.Text.Trim() == "")
            {
                string jv = "<script>alert('प्रमाण पत्र जारी करने की तिथि भरे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                txtcastedate.Focus();
                txtcastedate.BackColor = Color.Pink;
                return;
            }
        }
        if (string.IsNullOrEmpty(ddlhandicape.SelectedValue.Trim()) || ddlhandicape.SelectedValue == "0")
        {
            string jv = "<script>alert('क्या आप शारीरिक रूप से असक्षम (पी एच / दिव्यांग) हैं? हाँ / नहीं का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlhandicape.Focus();
            ddlhandicape.BackColor = Color.Pink;
            return;
        }
        if (ddlhandicape.SelectedValue == "Y")
        {
            if (string.IsNullOrEmpty(ddlhandicapetype.SelectedValue.Trim()) || ddlhandicapetype.SelectedValue == "0")
            {
                string jv = "<script>alert('विकलांगता का प्रकार चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlhandicapetype.Focus();
                ddlhandicapetype.BackColor = Color.Pink;
                return;
            }
        }
        if (chklabattendant.Checked)
        {
            if (string.IsNullOrEmpty(HiddenMarksheet.Value.Trim()) || HiddenMarksheet.Value == "")
            {
                string jv = "<script>alert('10th मार्कशीट अपलोड करें!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                file10thmarksheet.Focus();
                file10thmarksheet.BackColor = Color.Pink;
                return;
            }
        }
        if (chkpost.Checked)
        {
            if (string.IsNullOrEmpty(HiddenMarksheet5th.Value.Trim()) || HiddenMarksheet5th.Value == "")
            {
                string jv = "<script>alert('5th मार्कशीट अपलोड करें!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                file5thmarksheet.Focus();
                file5thmarksheet.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(ddlservantpriority.SelectedValue.Trim()) || ddlservantpriority.SelectedValue == "")
            {
                string jv = "<script>alert('भृत्य की प्राथमिकता का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.Focus();
                ddlservantpriority.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(ddlwatchmanpriority.SelectedValue.Trim()) || ddlwatchmanpriority.SelectedValue == "")
            {
                string jv = "<script>alert('चौकीदार की प्राथमिकता का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlwatchmanpriority.Focus();
                ddlwatchmanpriority.BackColor = Color.Pink;
                return;
            }
            if (string.IsNullOrEmpty(ddlsweeperpriority.SelectedValue.Trim()) || ddlsweeperpriority.SelectedValue == "")
            {
                string jv = "<script>alert('स्वीपर का चयन करे!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlsweeperpriority.Focus();
                ddlsweeperpriority.BackColor = Color.Pink;
                return;
            }
        }
        if (!chklabattendant.Checked && !chkpost.Checked)
        {
            string jv = "<script>alert('किसी एक पद का चयन करें!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            chklabattendant.Focus();
            chklabattendant.BackColor = Color.Pink;
            chkpost.Focus();
            chkpost.BackColor = Color.Pink;
            return;
        }
        if (string.IsNullOrEmpty(ddlpreference.SelectedValue.Trim()) || ddlpreference.SelectedValue == "0")
        {
            string jv = "<script>alert('परीक्षा केंद्र प्राथमिकता जिला का चयन करे!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlpreference.Focus();
            ddlpreference.BackColor = Color.Pink;
            return;
        }

        int candId = 0;
        int insertId = 0;
        string userID = Session["userID"].ToString();
        if (Request.QueryString["Id"] != null)
        {
            candId = int.Parse(Request.QueryString["Id"].ToString());
        }
        string ipaddress = "";
        ipaddress = Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
        if (ipaddress == "" || ipaddress == null)
        {
            ipaddress = Request.ServerVariables["REMOTE_ADDR"];
        }
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conNew = new SqlConnection(builder.ConnectionString))
        {
            conNew.Open();
            SqlCommand command = conNew.CreateCommand();
            SqlTransaction transaction;
            transaction = conNew.BeginTransaction("Higher EducationTransaction");
            command.Connection = conNew;
            command.Transaction = transaction;
            bool check = false;
            try
            {
                if (candId == 0)
                {
                    command.CommandText = @"IF NOT EXISTS(select RegistrationNo from tbl_CandidateMaster WHERE RegistrationNo = @Txtmobileno) BEGIN INSERT INTO tbl_CandidateMaster 
(Candidate_loginID,RegistrationNo,First_nameH,Middle_nameH,Last_NameH,First_nameE,Middle_nameE,Last_NameE,GenderID,DOB,age,Relative_name,Relative_nameE,Mother_name,Mother_nameE,CasteID,
Email,StateCode,DistrictCCode,Address,PinCode,Per_StateCode,Per_DistrictCCode,Per_Address,Per_Pincode,Photopath,Signpath,Marksheet10Path,Marksheet5Path,Createddate,
Createdby,IP_Address,IsGovEmp,IsServiceEmp,maritalstatus,IsDisabled,DisabledType,attendantpost,otherpost,
servantpriority,watchmanpriority,sweeperpriority,is_intercastemarriagepromotion,is_govawards,is_defenceservices,is_bonafidecand,is_certifiedindian,is_sepereated,is_excluded,is_terminated,
is_guilty,dist_preferencecode,IsCompleted,castesrno,castedist,castename,castedate) VALUES (@loginId,@Txtmobileno,@txtfnameh,
@txtmnameh,@txtlnameh,@txtfnameE,@txtMnameE,@txtlnameE,@ddlgender,@txtdob,@txtage,@Txtrelativename,
@TxtrelativenameE,@Txtmothername,@TxtmothernameE,@ddlcategory,@Textemail,@ddlstate,@ddldistrict,@txtaddress,@Txtpincode,
@perddlstate,@perddldistrict,@pertxtadd,@pertxtpincode,@Hiddenphoto,
@Hiddensign,@HiddenMarksheet,@HiddenMarksheet5th,@datenow,@createdby,@ipaddress,@chkgov,@chkservice,@ddlmarriedstatus,
@ddlhandicape,@ddlhandicapetype,@chklabattendant,@chkpost,
@ddlservantpriority,@ddlwatchmanpriority,@ddlsweeperpriority,@rdnis_intercastemarriagepromotion,@rdnis_govawards,
@rdnis_defenceservices,@rdnis_bonafidecand,
@rdnis_certifiedindian,@rdnis_sepereated,@rdnis_excluded,@rdnis_terminated,@rdnis_guilty,@ddlpreference,@IsCompleted,
@txtcastesrno,@ddlcastedist,@txtcastename,@txtcastedate) END";
                }
                else
                {
                    command.CommandText = @"UPDATE tbl_CandidateMaster SET 
		Candidate_loginID = @loginId
      ,RegistrationNo = @Txtmobileno
      ,First_nameH = @txtfnameh
      ,Middle_nameH = @txtmnameh
      ,Last_NameH = @txtlnameh
      ,First_nameE = @txtfnameE
      ,Middle_nameE = @txtMnameE
      ,Last_NameE = @txtlnameE
      ,GenderID = @ddlgender
      ,DOB = @txtdob
      ,age = @txtage
      ,Relative_name = @Txtrelativename
      ,Relative_nameE = @TxtrelativenameE
      ,Mother_name = @Txtmothername
      ,Mother_nameE = @TxtmothernameE
      ,CasteID = @ddlcategory
      ,Email = @Textemail
      ,StateCode = @ddlstate
      ,DistrictCCode = @ddldistrict
      ,Address = @txtaddress
      ,PinCode = @Txtpincode
      ,Per_StateCode = @perddlstate
      ,Per_DistrictCCode = @perddldistrict
      ,Per_Address = @pertxtadd
      ,Per_Pincode = @pertxtpincode
      ,Photopath = @Hiddenphoto
      ,Signpath = @Hiddensign
      ,Marksheet10Path = @HiddenMarksheet
      ,Marksheet5Path = @HiddenMarksheet5th
      ,Updated_Date = @datenow
      ,Updatedby = @createdby
      ,IP_Address = @ipaddress
      ,IsGovEmp = @chkgov
      ,IsServiceEmp = @chkservice
      ,maritalstatus = @ddlmarriedstatus
      ,IsDisabled = @ddlhandicape
      ,DisabledType = @ddlhandicapetype
      ,attendantpost = @chklabattendant
      ,otherpost = @chkpost
      ,servantpriority = @ddlservantpriority
      ,watchmanpriority = @ddlwatchmanpriority
      ,sweeperpriority = @ddlsweeperpriority
      ,is_intercastemarriagepromotion = @rdnis_intercastemarriagepromotion
      ,is_govawards = @rdnis_govawards
      ,is_defenceservices = @rdnis_defenceservices
      ,is_bonafidecand = @rdnis_bonafidecand
      ,is_certifiedindian = @rdnis_certifiedindian
      ,is_sepereated = @rdnis_sepereated
      ,is_excluded = @rdnis_excluded
      ,is_terminated = @rdnis_terminated
      ,is_guilty = @rdnis_guilty
      ,dist_preferencecode = @ddlpreference
      ,IsCompleted = @IsCompleted
      ,castesrno = @txtcastesrno
      ,castedist = @ddlcastedist
      ,castename = @txtcastename
      ,castedate = @txtcastedate
 WHERE Id=@candId";
                }
                try
                {
                    command.Parameters.Add("@loginId", SqlDbType.BigInt).Value = int.Parse(GetCandidateLoginId(userID));
                    command.Parameters.Add("@Txtmobileno", SqlDbType.VarChar).Value = Txtmobileno.Text.Trim();
                    command.Parameters.Add("@txtfnameh", SqlDbType.NVarChar).Value = txtfnameh.Text.Trim();
                    command.Parameters.Add("@txtmnameh", SqlDbType.NVarChar).Value = txtmnameh.Text.Trim();
                    command.Parameters.Add("@txtlnameh", SqlDbType.NVarChar).Value = txtlnameh.Text.Trim();
                    command.Parameters.Add("@txtfnameE", SqlDbType.NVarChar).Value = txtfnameE.Text.ToUpper().Trim();
                    command.Parameters.Add("@txtMnameE", SqlDbType.NVarChar).Value = txtMnameE.Text.ToUpper().Trim();
                    command.Parameters.Add("@txtlnameE", SqlDbType.NVarChar).Value = txtlnameE.Text.ToUpper().Trim();
                    command.Parameters.Add("@ddlgender", SqlDbType.TinyInt).Value = int.Parse(ddlgender.SelectedValue.Trim());
                    command.Parameters.Add("@txtdob", SqlDbType.Date).Value = Convert.ToDateTime(txtdob.Text);
                    command.Parameters.Add("@txtage", SqlDbType.Int).Value = int.Parse(txtage.Text.Trim());
                    command.Parameters.Add("@Txtrelativename", SqlDbType.NVarChar).Value = Txtrelativename.Text.Trim();
                    command.Parameters.Add("@TxtrelativenameE", SqlDbType.NVarChar).Value = TxtrelativenameE.Text.ToUpper().Trim();
                    command.Parameters.Add("@Txtmothername", SqlDbType.NVarChar).Value = Txtmothername.Text.Trim();
                    command.Parameters.Add("@TxtmothernameE", SqlDbType.NVarChar).Value = TxtmothernameE.Text.ToUpper().Trim();
                    command.Parameters.Add("@ddlcategory", SqlDbType.TinyInt).Value = int.Parse(ddlcategory.SelectedValue.Trim());
                    command.Parameters.Add("@Textemail", SqlDbType.VarChar).Value = Textemail.Text.Trim();
                    command.Parameters.Add("@ddlstate", SqlDbType.Int).Value = int.Parse(ddlstate.SelectedValue.Trim());
                    command.Parameters.Add("@ddldistrict", SqlDbType.Int).Value = int.Parse(ddldistrict.SelectedValue.Trim());
                    command.Parameters.Add("@txtaddress", SqlDbType.NVarChar).Value = txtaddress.Text.Trim();
                    command.Parameters.Add("@Txtpincode", SqlDbType.Int).Value = int.Parse(Txtpincode.Text.Trim());
                    command.Parameters.Add("@perddlstate", SqlDbType.Int).Value = int.Parse(perddlstate.SelectedValue.Trim());
                    command.Parameters.Add("@perddldistrict", SqlDbType.Int).Value = int.Parse(perddldistrict.SelectedValue.Trim());
                    command.Parameters.Add("@pertxtadd", SqlDbType.NVarChar).Value = pertxtadd.Text.Trim();
                    command.Parameters.Add("@pertxtpincode", SqlDbType.Int).Value = int.Parse(pertxtpincode.Text.Trim());
                    command.Parameters.Add("@Hiddenphoto", SqlDbType.NVarChar).Value = Hiddenphoto.Value.Trim();
                    command.Parameters.Add("@Hiddensign", SqlDbType.NVarChar).Value = Hiddensign.Value.Trim();
                    command.Parameters.Add("@HiddenMarksheet", SqlDbType.NVarChar).Value = HiddenMarksheet.Value.Trim();
                    command.Parameters.Add("@HiddenMarksheet5th", SqlDbType.NVarChar).Value = HiddenMarksheet5th.Value.Trim();
                    command.Parameters.Add("@datenow", SqlDbType.DateTime).Value = DateTime.Now;
                    command.Parameters.Add("@createdby", SqlDbType.NVarChar).Value = Txtmobileno.Text.Trim();
                    command.Parameters.Add("@ipaddress", SqlDbType.NVarChar).Value = ipaddress;
                    if (chkgov.Checked)
                    {
                        command.Parameters.Add("@chkgov", SqlDbType.Char).Value = "Y";
                    }
                    else
                    {
                        command.Parameters.Add("@chkgov", SqlDbType.Char).Value = "N";
                    }
                    if (chkservice.Checked)
                    {
                        command.Parameters.Add("@chkservice", SqlDbType.Char).Value = "Y";
                    }
                    else
                    {
                        command.Parameters.Add("@chkservice", SqlDbType.Char).Value = "N";
                    }
                    command.Parameters.Add("@ddlmarriedstatus", SqlDbType.TinyInt).Value = int.Parse(ddlmarriedstatus.SelectedValue.Trim());
                    command.Parameters.Add("@ddlhandicape", SqlDbType.Char).Value = ddlhandicape.SelectedValue.Trim();
                    command.Parameters.Add("@ddlhandicapetype", SqlDbType.Char).Value = ddlhandicapetype.SelectedValue.Trim();
                    if (chklabattendant.Checked)
                    {
                        command.Parameters.Add("@chklabattendant", SqlDbType.Char).Value = "Y";
                    }
                    else
                    {
                        command.Parameters.Add("@chklabattendant", SqlDbType.Char).Value = "N";
                    }
                    if (chkpost.Checked)
                    {
                        command.Parameters.Add("@chkpost", SqlDbType.Char).Value = "Y";
                    }
                    else
                    {
                        command.Parameters.Add("@chkpost", SqlDbType.Char).Value = "N";
                    }
                    command.Parameters.Add("@ddlservantpriority", SqlDbType.Char).Value = ddlservantpriority.SelectedValue.Trim();
                    command.Parameters.Add("@ddlwatchmanpriority", SqlDbType.Char).Value = ddlwatchmanpriority.SelectedValue.Trim();
                    command.Parameters.Add("@ddlsweeperpriority", SqlDbType.Char).Value = ddlsweeperpriority.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_intercastemarriagepromotion", SqlDbType.Char).Value = rdnis_intercastemarriagepromotion.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_govawards", SqlDbType.Char).Value = rdnis_govawards.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_defenceservices", SqlDbType.Char).Value = rdnis_defenceservices.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_bonafidecand", SqlDbType.Char).Value = rdnis_bonafidecand.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_certifiedindian", SqlDbType.Char).Value = rdnis_certifiedindian.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_sepereated", SqlDbType.Char).Value = rdnis_sepereated.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_excluded", SqlDbType.Char).Value = rdnis_excluded.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_terminated", SqlDbType.Char).Value = rdnis_terminated.SelectedValue.Trim();
                    command.Parameters.Add("@rdnis_guilty", SqlDbType.Char).Value = rdnis_guilty.SelectedValue.Trim();
                    command.Parameters.Add("@ddlpreference", SqlDbType.Int).Value = int.Parse(ddlpreference.SelectedValue.Trim());
                    command.Parameters.Add("@IsCompleted", SqlDbType.Char).Value = "N";
                    command.Parameters.Add("@txtcastesrno", SqlDbType.VarChar).Value = txtcastesrno.Text.Trim();
                    command.Parameters.Add("@txtcastename", SqlDbType.NVarChar).Value = txtcastename.Text.Trim();
                    if (ddlcastedist.SelectedValue != "0")
                    {
                        command.Parameters.Add("@ddlcastedist", SqlDbType.Char).Value = int.Parse(ddlcastedist.SelectedValue);
                    }
                    else
                    {
                        command.Parameters.Add("@ddlcastedist", SqlDbType.Int).Value = DBNull.Value;
                    }
                    if (txtcastedate.Text != "")
                    {
                        command.Parameters.Add("@txtcastedate", SqlDbType.Date).Value = Convert.ToDateTime(txtcastedate.Text);
                    }
                    else
                    {
                        command.Parameters.Add("@txtcastedate", SqlDbType.Date).Value = DBNull.Value;
                    }
                    command.Parameters.Add("@candId", SqlDbType.Int).Value = candId;
                }
                catch (Exception ex)
                {
                    string jv = "<script>alert('कृपया अपने द्वारा भरे गए डेटा को ध्यानपूर्वक जांच लें!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    return;
                }
                command.ExecuteNonQuery();
                command.Parameters.Clear();

                command.CommandText = "select Id from tbl_CandidateMaster where RegistrationNo = @txt_mob";
                command.Parameters.Add("@txt_mob", SqlDbType.NVarChar).Value = Txtmobileno.Text.Trim();
                DataTable dtcount = new DataTable();
                using (SqlDataAdapter da = new SqlDataAdapter(command))
                {
                    da.Fill(dtcount);
                }
                if (dtcount.Rows.Count > 0)
                {
                    insertId = int.Parse(dtcount.Rows[0][0].ToString());
                }
                command.Parameters.Clear();


                string app_no = string.Empty;
                string s = (insertId).ToString().PadLeft(6, '0');
                app_no = "A" + s;
                command.CommandText = @"update tbl_CandidateMaster set application_no=@applicationno,IsCompleted=@IsCompleted1 
where Id=@insertid";
                command.Parameters.Add("@applicationno", SqlDbType.NVarChar).Value = app_no;
                command.Parameters.Add("@IsCompleted1", SqlDbType.Char).Value = "Y";
                command.Parameters.Add("@insertid", SqlDbType.Int).Value = insertId;
                command.ExecuteNonQuery();
                command.Parameters.Clear();

                transaction.Commit();
                check = true;
            }
            catch (Exception ex)
            {
                LogClass.InsertDetails(this.Page.ToString(), ex.ToString());
                if (transaction != null)
                {
                    transaction.Rollback();
                    string jv = "<script>alert('Please Try Again');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    return;
                }
            }
            finally
            {
                if (check == true)
                {
                    string jv = "<script>alert('Saved Successfully');window.location.href = 'ApplicationPrintOfCandidate.aspx';</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
        }

    }
    public string GetCandidateLoginId(string userID)
    {
        string id = string.Empty;
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select Id from tbl_Candidatelogin where MobileNo=@userid";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@userid", SqlDbType.NVarChar).Value = userID.Trim();
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                id = dtShops.Rows[0][0].ToString();
            }
            conn.Close();
            dtShops.Dispose();
        }
        return id;
    }
    public void validateage(int age)
    {
        bool is_ok = true;
        if (age > 17)
        {
            if (chkgov.Checked && ddlgender.SelectedValue == "1" && ddlcategory.SelectedValue == "1")
            {
                if (age > 39)
                {
                    is_ok = false;
                    string jv = "<script>alert('Age is not valid, Age limit is 40 !');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            else if (chkgov.Checked && ddlcategory.SelectedValue != "1")
            {
                if (age > 44)
                {
                    is_ok = false;
                    string jv = "<script>alert('Age is not valid, Age limit is 45 !');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            else if (!chkgov.Checked && !chkservice.Checked && ddlgender.SelectedValue == "1" && ddlcategory.SelectedValue == "1")
            {
                if (age > 39)
                {
                    is_ok = false;
                    string jv = "<script>alert('Age is not valid, Age limit is 40 !');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            else if (chkservice.Checked && !chkgov.Checked && ddlgender.SelectedValue == "1" && ddlcategory.SelectedValue == "1")
            {
                if (age > 44)
                {
                    is_ok = false;
                    string jv = "<script>alert('Age is not valid, Age limit is 45 !');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
            else if (!chkgov.Checked)
            {
                if (age > 44)
                {
                    is_ok = false;
                    string jv = "<script>alert('Age is not valid, Age limit is 45 !');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                }
            }
        }
        else
        {
            is_ok = false;
            string jv = "<script>alert('Age is not valid, Age should be minimum 18 !');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
        }
        if (is_ok)
        {
            dvagevalid.Visible = true;
        }
        else
        {
            txtage.Text = string.Empty;
            txtdob.Text = string.Empty;
            dvagevalid.Visible = false;
        }
    }

    protected void chkpost_CheckedChanged(object sender, EventArgs e)
    {
        if (chkpost.Checked)
        {
            dvpriority.Visible = true;
        }
        else
        {
            dvpriority.Visible = false;
        }
    }
    protected void InsertRecord()
    {
        int candId = 0;
        int insertId = 0;
        string userID = Session["userID"].ToString();
        if (Request.QueryString["Id"] != null)
        {
            candId = int.Parse(Request.QueryString["Id"].ToString());
        }
        string ipaddress = "";
        ipaddress = Request.ServerVariables["HTTP_X_FORWARDED_FOR"];
        if (ipaddress == "" || ipaddress == null)
        {
            ipaddress = Request.ServerVariables["REMOTE_ADDR"];
        }
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conNew = new SqlConnection(builder.ConnectionString))
        {
            conNew.Open();
            SqlCommand command = conNew.CreateCommand();
            SqlTransaction transaction;
            transaction = conNew.BeginTransaction("Higher EducationTransaction");
            command.Connection = conNew;
            command.Transaction = transaction;
            bool check = false;
            try
            {
                if (candId == 0)
                {
                    command.CommandText = @"IF NOT EXISTS(select RegistrationNo from tbl_CandidateMaster WHERE RegistrationNo = @Txtmobileno) BEGIN INSERT INTO tbl_CandidateMaster 
(Candidate_loginID,RegistrationNo,First_nameH,Middle_nameH,Last_NameH,First_nameE,Middle_nameE,Last_NameE,GenderID,DOB,age,Relative_name,Relative_nameE,Mother_name,Mother_nameE,CasteID,
Email,StateCode,DistrictCCode,Address,PinCode,Per_StateCode,Per_DistrictCCode,Per_Address,Per_Pincode,Photopath,Signpath,Marksheet10Path,Marksheet5Path,Createddate,
Createdby,IP_Address,IsGovEmp,IsServiceEmp,maritalstatus,IsDisabled,DisabledType,attendantpost,otherpost,
servantpriority,watchmanpriority,sweeperpriority,is_intercastemarriagepromotion,is_govawards,is_defenceservices,is_bonafidecand,is_certifiedindian,is_sepereated,is_excluded,is_terminated,
is_guilty,dist_preferencecode,IsCompleted,castesrno,castedist,castename,castedate) VALUES (@loginId,@Txtmobileno,@txtfnameh,
@txtmnameh,@txtlnameh,@txtfnameE,@txtMnameE,@txtlnameE,@ddlgender,@txtdob,@txtage,@Txtrelativename,
@TxtrelativenameE,@Txtmothername,@TxtmothernameE,@ddlcategory,@Textemail,@ddlstate,@ddldistrict,@txtaddress,@Txtpincode,
@perddlstate,@perddldistrict,@pertxtadd,@pertxtpincode,@Hiddenphoto,
@Hiddensign,@HiddenMarksheet,@HiddenMarksheet5th,@datenow,@createdby,@ipaddress,@chkgov,@chkservice,@ddlmarriedstatus,
@ddlhandicape,@ddlhandicapetype,@chklabattendant,@chkpost,
@ddlservantpriority,@ddlwatchmanpriority,@ddlsweeperpriority,@rdnis_intercastemarriagepromotion,@rdnis_govawards,
@rdnis_defenceservices,@rdnis_bonafidecand,
@rdnis_certifiedindian,@rdnis_sepereated,@rdnis_excluded,@rdnis_terminated,@rdnis_guilty,@ddlpreference,@IsCompleted,
@txtcastesrno,@ddlcastedist,@txtcastename,@txtcastedate) END";
                }
                else
                {
                    command.CommandText = @"UPDATE tbl_CandidateMaster SET 
		Candidate_loginID = @loginId
      ,RegistrationNo = @Txtmobileno
      ,First_nameH = @txtfnameh
      ,Middle_nameH = @txtmnameh
      ,Last_NameH = @txtlnameh
      ,First_nameE = @txtfnameE
      ,Middle_nameE = @txtMnameE
      ,Last_NameE = @txtlnameE
      ,GenderID = @ddlgender
      ,DOB = @txtdob
      ,age = @txtage
      ,Relative_name = @Txtrelativename
      ,Relative_nameE = @TxtrelativenameE
      ,Mother_name = @Txtmothername
      ,Mother_nameE = @TxtmothernameE
      ,CasteID = @ddlcategory
      ,Email = @Textemail
      ,StateCode = @ddlstate
      ,DistrictCCode = @ddldistrict
      ,Address = @txtaddress
      ,PinCode = @Txtpincode
      ,Per_StateCode = @perddlstate
      ,Per_DistrictCCode = @perddldistrict
      ,Per_Address = @pertxtadd
      ,Per_Pincode = @pertxtpincode
      ,Photopath = @Hiddenphoto
      ,Signpath = @Hiddensign
      ,Marksheet10Path = @HiddenMarksheet
      ,Marksheet5Path = @HiddenMarksheet5th
      ,Updated_Date = @datenow
      ,Updatedby = @createdby
      ,IP_Address = @ipaddress
      ,IsGovEmp = @chkgov
      ,IsServiceEmp = @chkservice
      ,maritalstatus = @ddlmarriedstatus
      ,IsDisabled = @ddlhandicape
      ,DisabledType = @ddlhandicapetype
      ,attendantpost = @chklabattendant
      ,otherpost = @chkpost
      ,servantpriority = @ddlservantpriority
      ,watchmanpriority = @ddlwatchmanpriority
      ,sweeperpriority = @ddlsweeperpriority
      ,is_intercastemarriagepromotion = @rdnis_intercastemarriagepromotion
      ,is_govawards = @rdnis_govawards
      ,is_defenceservices = @rdnis_defenceservices
      ,is_bonafidecand = @rdnis_bonafidecand
      ,is_certifiedindian = @rdnis_certifiedindian
      ,is_sepereated = @rdnis_sepereated
      ,is_excluded = @rdnis_excluded
      ,is_terminated = @rdnis_terminated
      ,is_guilty = @rdnis_guilty
      ,dist_preferencecode = @ddlpreference
      ,IsCompleted = @IsCompleted
      ,castesrno = @txtcastesrno
      ,castedist = @ddlcastedist
      ,castename = @txtcastename
      ,castedate = @txtcastedate
 WHERE Id=@candId";
                }
                command.Parameters.Add("@loginId", SqlDbType.BigInt).Value = int.Parse(GetCandidateLoginId(userID));
                command.Parameters.Add("@Txtmobileno", SqlDbType.VarChar).Value = Txtmobileno.Text.Trim();
                command.Parameters.Add("@txtfnameh", SqlDbType.NVarChar).Value = txtfnameh.Text.Trim();
                command.Parameters.Add("@txtmnameh", SqlDbType.NVarChar).Value = txtmnameh.Text.Trim();
                command.Parameters.Add("@txtlnameh", SqlDbType.NVarChar).Value = txtlnameh.Text.Trim();
                command.Parameters.Add("@txtfnameE", SqlDbType.NVarChar).Value = txtfnameE.Text.ToUpper().Trim();
                command.Parameters.Add("@txtMnameE", SqlDbType.NVarChar).Value = txtMnameE.Text.ToUpper().Trim();
                command.Parameters.Add("@txtlnameE", SqlDbType.NVarChar).Value = txtlnameE.Text.ToUpper().Trim();
                if (ddlgender.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlgender", SqlDbType.TinyInt).Value = int.Parse(ddlgender.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddlgender", SqlDbType.TinyInt).Value = DBNull.Value;
                }
                if (txtdob.Text == "")
                {
                    command.Parameters.Add("@txtdob", SqlDbType.Date).Value = DBNull.Value;
                }
                else
                {
                    command.Parameters.Add("@txtdob", SqlDbType.Date).Value = Convert.ToDateTime(txtdob.Text);
                }
                if (txtdob.Text == "")
                {
                    command.Parameters.Add("@txtage", SqlDbType.Int).Value = DBNull.Value;
                }
                else
                {
                    command.Parameters.Add("@txtage", SqlDbType.Int).Value = int.Parse(txtage.Text.Trim());
                }
                command.Parameters.Add("@Txtrelativename", SqlDbType.NVarChar).Value = Txtrelativename.Text.Trim();
                command.Parameters.Add("@TxtrelativenameE", SqlDbType.NVarChar).Value = TxtrelativenameE.Text.ToUpper().Trim();
                command.Parameters.Add("@Txtmothername", SqlDbType.NVarChar).Value = Txtmothername.Text.Trim();
                command.Parameters.Add("@TxtmothernameE", SqlDbType.NVarChar).Value = TxtmothernameE.Text.ToUpper().Trim();
                if (ddlcategory.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlcategory", SqlDbType.TinyInt).Value = int.Parse(ddlcategory.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddlcategory", SqlDbType.TinyInt).Value = DBNull.Value;
                }
                command.Parameters.Add("@Textemail", SqlDbType.VarChar).Value = Textemail.Text.Trim();
                if (ddlstate.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlstate", SqlDbType.Int).Value = int.Parse(ddlstate.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddlstate", SqlDbType.Int).Value = DBNull.Value;
                }
                if (ddldistrict.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddldistrict", SqlDbType.Int).Value = int.Parse(ddldistrict.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddldistrict", SqlDbType.Int).Value = DBNull.Value;
                }
                command.Parameters.Add("@txtaddress", SqlDbType.NVarChar).Value = txtaddress.Text.Trim();
                if (Txtpincode.Text == "")
                {
                    command.Parameters.Add("@Txtpincode", SqlDbType.Int).Value = DBNull.Value;
                }
                else
                {
                    command.Parameters.Add("@Txtpincode", SqlDbType.Int).Value = int.Parse(Txtpincode.Text.Trim());
                }
                if (perddlstate.SelectedValue != "0")
                {
                    command.Parameters.Add("@perddlstate", SqlDbType.Int).Value = int.Parse(perddlstate.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@perddlstate", SqlDbType.Int).Value = DBNull.Value;
                }
                if (perddldistrict.SelectedValue != "0")
                {
                    command.Parameters.Add("@perddldistrict", SqlDbType.Int).Value = int.Parse(perddldistrict.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@perddldistrict", SqlDbType.Int).Value = DBNull.Value;
                }
                command.Parameters.Add("@pertxtadd", SqlDbType.NVarChar).Value = pertxtadd.Text.Trim();
                if (pertxtpincode.Text == "")
                {
                    command.Parameters.Add("@pertxtpincode", SqlDbType.Int).Value = DBNull.Value;
                }
                else
                {
                    command.Parameters.Add("@pertxtpincode", SqlDbType.Int).Value = int.Parse(pertxtpincode.Text.Trim());
                }
                command.Parameters.Add("@Hiddenphoto", SqlDbType.NVarChar).Value = Hiddenphoto.Value.Trim();
                command.Parameters.Add("@Hiddensign", SqlDbType.NVarChar).Value = Hiddensign.Value.Trim();
                command.Parameters.Add("@HiddenMarksheet", SqlDbType.NVarChar).Value = HiddenMarksheet.Value.Trim();
                command.Parameters.Add("@HiddenMarksheet5th", SqlDbType.NVarChar).Value = HiddenMarksheet5th.Value.Trim();
                command.Parameters.Add("@datenow", SqlDbType.DateTime).Value = DateTime.Now;
                command.Parameters.Add("@createdby", SqlDbType.NVarChar).Value = Txtmobileno.Text.Trim();
                command.Parameters.Add("@ipaddress", SqlDbType.NVarChar).Value = ipaddress;
                if (chkgov.Checked)
                {
                    command.Parameters.Add("@chkgov", SqlDbType.Char).Value = "Y";
                }
                else
                {
                    command.Parameters.Add("@chkgov", SqlDbType.Char).Value = "N";
                }
                if (chkservice.Checked)
                {
                    command.Parameters.Add("@chkservice", SqlDbType.Char).Value = "Y";
                }
                else
                {
                    command.Parameters.Add("@chkservice", SqlDbType.Char).Value = "N";
                }
                if (ddlmarriedstatus.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlmarriedstatus", SqlDbType.TinyInt).Value = int.Parse(ddlmarriedstatus.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddlmarriedstatus", SqlDbType.TinyInt).Value = DBNull.Value;
                }
                if (ddlhandicape.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlhandicape", SqlDbType.Char).Value = ddlhandicape.SelectedValue.Trim();
                }
                else
                {
                    command.Parameters.Add("@ddlhandicape", SqlDbType.Char).Value = DBNull.Value;
                }
                if (ddlhandicapetype.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlhandicapetype", SqlDbType.Char).Value = ddlhandicapetype.SelectedValue.Trim();
                }
                else
                {
                    command.Parameters.Add("@ddlhandicapetype", SqlDbType.Char).Value = DBNull.Value;
                }
                if (chklabattendant.Checked)
                {
                    command.Parameters.Add("@chklabattendant", SqlDbType.Char).Value = "Y";
                }
                else
                {
                    command.Parameters.Add("@chklabattendant", SqlDbType.Char).Value = "N";
                }
                if (chkpost.Checked)
                {
                    command.Parameters.Add("@chkpost", SqlDbType.Char).Value = "Y";
                }
                else
                {
                    command.Parameters.Add("@chkpost", SqlDbType.Char).Value = "N";
                }
                if (ddlservantpriority.SelectedValue != "")
                {
                    command.Parameters.Add("@ddlservantpriority", SqlDbType.Char).Value = ddlservantpriority.SelectedValue.Trim();
                }
                else
                {
                    command.Parameters.Add("@ddlservantpriority", SqlDbType.Char).Value = DBNull.Value;
                }
                if (ddlwatchmanpriority.SelectedValue != "")
                {
                    command.Parameters.Add("@ddlwatchmanpriority", SqlDbType.Char).Value = ddlwatchmanpriority.SelectedValue.Trim();
                }
                else
                {
                    command.Parameters.Add("@ddlwatchmanpriority", SqlDbType.Char).Value = DBNull.Value;
                }
                if (ddlsweeperpriority.SelectedValue != "")
                {
                    command.Parameters.Add("@ddlsweeperpriority", SqlDbType.Char).Value = ddlsweeperpriority.SelectedValue.Trim();
                }
                else
                {
                    command.Parameters.Add("@ddlsweeperpriority", SqlDbType.Char).Value = DBNull.Value;
                }
                command.Parameters.Add("@rdnis_intercastemarriagepromotion", SqlDbType.Char).Value = rdnis_intercastemarriagepromotion.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_govawards", SqlDbType.Char).Value = rdnis_govawards.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_defenceservices", SqlDbType.Char).Value = rdnis_defenceservices.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_bonafidecand", SqlDbType.Char).Value = rdnis_bonafidecand.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_certifiedindian", SqlDbType.Char).Value = rdnis_certifiedindian.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_sepereated", SqlDbType.Char).Value = rdnis_sepereated.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_excluded", SqlDbType.Char).Value = rdnis_excluded.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_terminated", SqlDbType.Char).Value = rdnis_terminated.SelectedValue.Trim();
                command.Parameters.Add("@rdnis_guilty", SqlDbType.Char).Value = rdnis_guilty.SelectedValue.Trim();
                if (ddlpreference.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlpreference", SqlDbType.Int).Value = int.Parse(ddlpreference.SelectedValue.Trim());
                }
                else
                {
                    command.Parameters.Add("@ddlpreference", SqlDbType.Int).Value = DBNull.Value;
                }
                command.Parameters.Add("@IsCompleted", SqlDbType.Char).Value = "N";
                command.Parameters.Add("@txtcastesrno", SqlDbType.VarChar).Value = txtcastesrno.Text.Trim();
                command.Parameters.Add("@txtcastename", SqlDbType.NVarChar).Value = txtcastename.Text.Trim();
                if (ddlcastedist.SelectedValue != "0")
                {
                    command.Parameters.Add("@ddlcastedist", SqlDbType.Char).Value = int.Parse(ddlcastedist.SelectedValue);
                }
                else
                {
                    command.Parameters.Add("@ddlcastedist", SqlDbType.Int).Value = DBNull.Value;
                }
                if (txtcastedate.Text != "")
                {
                    command.Parameters.Add("@txtcastedate", SqlDbType.Date).Value = Convert.ToDateTime(txtcastedate.Text);
                }
                else
                {
                    command.Parameters.Add("@txtcastedate", SqlDbType.Date).Value = DBNull.Value;
                }
                command.Parameters.Add("@candId", SqlDbType.Int).Value = candId;
                command.ExecuteNonQuery();
                command.Parameters.Clear();

                command.CommandText = "select Id from tbl_CandidateMaster where RegistrationNo = @txt_mob";
                command.Parameters.Add("@txt_mob", SqlDbType.NVarChar).Value = Txtmobileno.Text.Trim();
                DataTable dtcount = new DataTable();
                using (SqlDataAdapter da = new SqlDataAdapter(command))
                {
                    da.Fill(dtcount);
                }
                if (dtcount.Rows.Count > 0)
                {
                    insertId = int.Parse(dtcount.Rows[0][0].ToString());
                }
                command.Parameters.Clear();

                transaction.Commit();
                check = true;
            }
            catch (Exception ex)
            {
                LogClass.InsertDetails(this.Page.ToString(), ex.ToString());
                if (transaction != null)
                {
                    transaction.Rollback();
                    string jv = "<script>alert('Please Try Again');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    return;
                }
            }
            finally
            {
                if (check == true)
                {
                    string jv = "<script>alert('Saved Successfully');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    Response.Redirect("registration.aspx?Id=" + insertId);
                }
            }
        }
    }
}