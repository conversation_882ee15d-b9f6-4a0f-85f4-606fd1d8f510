import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  Container,
  Col,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card,
  Card<PERSON>eader,
  Card<PERSON>ody,
  Modal,
  Modal<PERSON>eader,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Uncontrolled<PERSON><PERSON>down,
  DropdownToggle,
  DropdownMenu,
  Dropdown<PERSON><PERSON>,
  <PERSON>ge,
  Spinner,
  Input,
} from "reactstrap";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import DataTable from "react-data-table-component";
import formatDate from "../../utils/formateDate.jsx";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const ErrorLogUploader = () => {
  const [totalRows, setTotalRows] = useState(0);
  const type = sessionStorage.getItem("userType");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const id = sessionStorage.getItem("id");
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);
  const [selectedTicket, setSelectedTickets] = useState("");
  const [selectedTicketNo, setSelectedTicketsNo] = useState("");
  const [modal, setModal] = useState(false);
  const [errorLogs, setErrorLogs] = useState([]);
  const [notifications, setNotifications] = useState({});
  const [unreadCounts, setUnreadCounts] = useState({});
  const [lastNotifiedMessage, setLastNotifiedMessage] = useState({});
  const [processingNotifications, setProcessingNotifications] = useState(false);
  const toggleChatBox = () => {
    if (modal && selectedTicketNo) {
      removeNotification(selectedTicketNo);
    }
    setModal(!modal);
  };
  const hasFetched = useRef(false);
  const pollingRef = useRef(null);
  const [transferOptions, setTransferOptions] = useState([
    { label: "NIC", value: "NIC" },
    { label: "Department", value: "Department" },
  ]);
  const [currentStatus, setCurrentStatus] = useState(
    errorLogs.status || (errorLogs.isTransfer ? "NIC" : "Department")
  );
 
    const fetchErrorList = async () => {
      try {
        setLoading(true); // Add loading state
        const response = await axios.get(
          `${endPoint}/api/error-logs-list-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const ticketsArray = response.data;
          if (ticketsArray.length > 0) {
            setErrorLogs(ticketsArray.reverse());
            setTotalRows(ticketsArray.length);
            const statusMap = {};
            ticketsArray.forEach(ticket => {
              statusMap[ticket.ticketNo] = ticket.TransferStatus || "NIC";
            });
            setCurrentStatus(statusMap);
            
            if (!processingNotifications) {
              setProcessingNotifications(true);
              checkForNewMessages(ticketsArray);
              setProcessingNotifications(false);
            }
          } else {
            setErrorLogs([]);
            setTotalRows(0);
          }
        } else {
          alert("Failed to fetch Errorlist. Please try again.");
        }
      } catch (error) {
        console.error("Failed to Load Data:", error);
        alert("Failed to Load Data.");
      } finally {
        setLoading(false);
      }
    };


    useEffect(() => {
      if (!hasFetched.current && id && token) {
        hasFetched.current = true;
        fetchErrorList();
      }
    
      if (!pollingRef.current) {
        pollingRef.current = setInterval(() => {
          if (id && token) {
            fetchErrorList(true);
          }
        }, 5000);
      }
    
      return () => {
        if (pollingRef.current) {
          clearInterval(pollingRef.current);
          pollingRef.current = null;
        }
        Object.values(notificationTimersRef.current).forEach((timer) =>
          clearTimeout(timer)
        );
      };
    }, [id, token]);


  const checkForNewMessages = (ticketsArray) => {
    ticketsArray.forEach((ticket) => {
      const latestMessage = ticket.chat?.slice(-1)[0];
  
      if (
        latestMessage &&
        !latestMessage.status.read &&
        lastNotifiedMessage[ticket.ticketNo] !== latestMessage._id &&
        selectedTicketNo !== ticket.ticketNo
      ) {
        showNotification(ticket.ticketNo, latestMessage.message);
        updateUnreadCount(ticket.ticketNo, ticket.chat);
        showNotificationWhenReceived(latestMessage._id, ticket.ticketNo, latestMessage.message);
  
        setLastNotifiedMessage((prev) => ({
          ...prev,
          [ticket.ticketNo]: latestMessage._id,
        }));
      }
    });
  };
  
  
  const [sentByUserMessage, setSentByUserMessage] = useState(null);
  const handleChatBox = async   (ticketId, ticketNo) => {
    
    setModal(true);
    setSelectedTickets(ticketId);
    const message = errorLogs.find((a) => String(a._id) === ticketId).message;
    setSentByUserMessage(message);
    setSelectedTicketsNo(ticketNo);
   
    if (message) {
      setSentByUserMessage(message.message);
      setModal(true);
      await markMessagesAsRead(ticketNo);
      removeNotification(ticketNo);
      fetchChatMessages();
    }
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  const fetchChatMessages = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/error-logs-chat/${selectedTicket}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const chatData = response.data.chat || [];
        setMessages(chatData);
        updateUnreadCount(ticketNo, chatData)
      } else {
        console.log("Error:", response.data.message);
      }
    } catch (error) {
      console.error("API Error:", error);
    }
  };
  useEffect(() => {
    if (modal && selectedTicket) {
     
      fetchChatMessages();
    }
  }, [modal, selectedTicket]);

 

  const openChatWindow = async (ticketNo) => {
    const ticket = errorLogs.find((a) => String(a.ticketNo) === String(ticketNo));
    if (ticket) {
      handleChatBox(ticket._id, ticketNo);
      await fetchChatMessages(ticket._id);
    }
  };
  
  const handleNotificationClick = async (ticketNo) => {
    const ticket = errorLogs.find((a) => String(a.ticketNo) === String(ticketNo));
    if (ticket) {
      const latestMessage = ticket.chat?.slice(-1)[0];
  
      if (latestMessage) {
        await markMessagesAsRead(latestMessage._id, ticketNo);
      }
  
      openChatWindow(ticketNo);
    }
  };
  
  
  useEffect(() => {
    let isFetching = false;
    const interval = setInterval(async () => {
      if (!isFetching && id && token) {
        isFetching = true;
        await fetchErrorList();
        isFetching = false;
      }
    }, 5e3);
  
    return () => clearInterval(interval);
  }, [id, token]);
  
  
 
  const showNotification = (ticketNo, message) => {
    if (!notifications[ticketNo]) {
      const toastId = toast.info(`New message: ${message} (Ticket: ${ticketNo})`, {
        position: "top-right",
        autoClose: false,
        onClick: () => handleNotificationClick(ticketNo),
      });
  
      setNotifications((prev) => ({
        ...prev,
        [ticketNo]: toastId,
      }));
    }
  };
  
  

  const removeNotification = (ticketNo) => {
    setNotifications((prev) => {
      const updated = { ...prev };
      delete updated[ticketNo];
      return updated;
    });
  };
  

  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem(
        `chatMessages_${selectedTicket}`,
        JSON.stringify(messages)
      );
    }
  }, [messages, selectedTicket]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);
  const handleMessageSent = async () => {
    if (!newMessage.trim()) return;

    try {
      const body = {
        message: newMessage,
        submittedById: "7987342537",
        submittedBy: "Tech Team",
        receiverId: selectedTicket,
      };
      const response = await axios.post(
        `${endPoint}/api/error-logs/sendMessage/${selectedTicket}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            message: newMessage,
            submittedById: "7987342537",
            submittedBy: "Tech Team",
            receiverId: "",
            createdAt: new Date().toISOString(),
          },
        ]);
        setNewMessage("");
      } else {
        SwalMessageAlert("Message Sent Failed", "error");
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };
  const updateUnreadCount = (ticketNo, chatData) => {
    const unreadCount = chatData.filter(
      (chat) =>
        (chat.status.send || chat.status.received) && !chat.status.read
    ).length;
  
    setUnreadCounts((prev) => ({
      ...prev,
      [ticketNo]: unreadCount,
    }));
  };
  
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  const handleStatusChange = async (ticketNo, newStatus) => {
    try {
     
      let correctedStatus =
        newStatus === "inprogress"
          ? "In Progress"
          : newStatus === "pending"
          ? "Pending"
          : newStatus === "resolved"
          ? "Resolved"
          : newStatus;
      const response = await axios.post(
        `${endPoint}/api/error-update-status`,
        {
          ticketNo,
          status: correctedStatus,
        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        SwalMessageAlert(response.data.msg, "success");
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (err) {
      console.error(
        "Status update error:",
        err.response ? err.response.data : err
      );
      SwalMessageAlert("Failed to update status", "error");
    }
  };

  const handleTransfer = async (ticketNo, newTransferStatus) => {
    try {
      let TransferStatus =
        newTransferStatus === "Department" ? "Department" : "NIC";
  
      if (newTransferStatus === "NIC" && currentStatus === "Department") {
        SwalMessageAlert("Transfer to NIC is not allowed after Department!", "error");
        return;
      }
  
      const response = await axios.post(
        `${endPoint}/api/transfer-status`,
        {
          ticketNo,
          TransferStatus: TransferStatus,
        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
  
      if (response.status === 200) {
        const data = response.data;
        setCurrentStatus(data.TransferStatus); 
        SwalMessageAlert(response.data.msg, "success");
  
      
        if (newTransferStatus === "Department") {
          setTransferOptions([{ label: "Department", value: "Department" }]);
        }
  
      
        setTimeout(() => {
          window.location.reload(); 
        }, 1000);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (err) {
      console.error(
        "Status update error:",
        err.response ? err.response.data : err
      );
      SwalMessageAlert("Failed to Transfer status", "error");
    }
  };
  const markMessagesAsRead = async (chatId, ticketNo) => {
    try {
      const response = await axios.post(
        `${endPoint}/api/mark-read`,
        { chatId },
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'web-url': window.location.href,
          },
        }
      );
  
      if (response.status === 200 && response.data.success) {
        console.log("Messages marked as read successfully!");
  
        
        removeNotification(ticketNo);
        updateUnreadCount(ticketNo, []);
      }
    } catch (error) {
      console.error("Error marking messages as read:", error);
    }
  };
  
  const showNotificationWhenReceived = async (chatId, ticketNo, message) => {
    try {
      const response = await axios.post(
        `${endPoint}/api/mark-as-received`, 
        { chatId }, 
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'web-url': window.location.href,
          },
        }
      );
  
      if (response.status === 200 && response.data.success) {
        const chatData = response.data.data;
        const latestMessage = chatData.find((chat) => chat._id === chatId);
  
        if (latestMessage && !latestMessage.status.read) {
          showNotification(ticketNo, message);
        }
      }
    } catch (error) {
      console.error("Error marking message as received:", error);
    }
  };
  
  
  const columns = [
    {
      name: "S. No",
      selector: (errorLogs, index) => index + 1,
      sortable: true,
      width: "70px",
    },
    {
      name: "Ticket No",
      selector: (errorLogs) => errorLogs.ticketNo,
      sortable: true,
    },
    {
      name: "Date of Raise Ticket",
      selector: (errorLogs) =>
        `${formatDate(errorLogs.createdAt)} (${new Date(
          errorLogs.createdAt
        ).toLocaleTimeString()})`,
      sortable: true,
    },
    {
      name: "Submitted By",
      selector: (errorLogs) => errorLogs.submittedBy,
      sortable: true,
    },
    {
      name: "Title",
      selector: (errorLogs) => errorLogs.title,
      sortable: true,
    },
    // {
    //   name: "Message",
    //   selector: (errorLogs) => errorLogs.message,
    //   style: { maxWidth: "200px", whiteSpace: "normal" },
    // },
    {
      name: "Attachments",
      cell: (errorLogs) => (
        <>
          <span>
            Download Images <br />
          </span>
          {errorLogs.image && errorLogs.image.length > 0 ? (
            errorLogs.image.map((image, index) => (
              <a
                key={index}
                target="_blank"
                href={`https://heonline.cg.nic.in/${image}`}
                download
              >
                <Button className="btn btn-sm m-2 btn-primary">
                  {index + 1}
                </Button>
              </a>
            ))
          ) : (
            <Button className="btn btn-sm m-2 btn-secondary" disabled>
              No Files
            </Button>
          )}
        </>
      ),
    },
    {
      name: "Remarks",
      cell: (errorLogs) => (
        <Button
          onClick={() => handleChatBox(errorLogs._id, errorLogs.ticketNo)}
          className="btn-sm bg-success text-white"
        >
          Chat
          {unreadCounts[errorLogs.ticketNo] > 0 && (
              <span style={{ color: "red", fontWeight: "bold" }}>
                ({unreadCounts[errorLogs.ticketNo]} unread)
              </span>
            )}
        </Button>
      ),
    },
    {
      name: "Transfer",
      cell: (errorLogs) => {
        const displayStatus =
          currentStatus[errorLogs.ticketNo] || 
          errorLogs.TransferStatus || 
           "NIC" ;
    
        return (
          <UncontrolledDropdown>
          <DropdownToggle caret size="sm" className="text-dark">
            {displayStatus}
          </DropdownToggle>
          <DropdownMenu>
            {transferOptions
              .filter((item) => item.label !== displayStatus) 
              .map((item) => (
                <DropdownItem
                  key={item.value}
                  onClick={() => handleTransfer(errorLogs.ticketNo, item.value)}
                >
                  {item.label}
                </DropdownItem>
              ))}
          </DropdownMenu>
        </UncontrolledDropdown>
        
        );
      },
    },
    {
      name: "Status",
      cell: (errorLogs) => {
        const isStateAdmin = errorLogs.submittedBy === "StateAdmin";
        const getButtonColor = () => {
          const normalizedStatus = (errorLogs.status || "").toLowerCase();

          switch (normalizedStatus) {
            case "resolved":
              return "success";
            case "in progress":
            case "inprogress":
              return "info";
            case "pending":
            default:
              return "warning";
          }
        };
        const displayStatus =
          errorLogs.status || (errorLogs.isResolved ? "Resolved" : "Pending");
        return (
          <UncontrolledDropdown>
            <DropdownToggle
              caret
              color={getButtonColor()}
              size="sm"
              className="text-white"
            >
              {displayStatus}
            </DropdownToggle>
            <DropdownMenu>
              {/* Ensure these are the EXACT strings you want stored in the database */}
              {[
                { label: "In Progress", value: "In Progress" },
                { label: "Resolved", value: "Resolved" },
              ]
                .filter((item) => item.label !== displayStatus)
                .map((item) => (
                  <DropdownItem
                    key={item.value}
                    onClick={() =>
                      handleStatusChange(errorLogs.ticketNo, item.value)
                    }
                  >
                    {item.label}
                  </DropdownItem>
                ))}
            </DropdownMenu>
          </UncontrolledDropdown>
        );
      },
    },
  ];
  useEffect(() => {
    if (modal) {
      const interval = setInterval(() => {
        handleReloadChat();
      }, 5000);
      return () => clearInterval(interval);
    }
  });
  const handleReloadChat = async () => {
    try {
      const receiverId = "7987342537";
      const response = await axios.get(
        `${endPoint}/api/error-logs-chat/${selectedTicket}?receiverId=${receiverId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const chatData = response.data.chat || [];
        setMessages(chatData);
      } else {
        console.log("Error:", response.data.message);
      }
    } catch (error) {
      console.error("API Error:", error);
    }
  };
  return (
    <>
      <Header />
      <ToastContainer />
    
      <Container className="mt--7" fluid>
        <Card>
          <CardHeader>
            <h2>Error List</h2>
          </CardHeader>
          <div className="error-logs-table">
            <DataTable
              columns={columns}
              data={errorLogs}
              progressPending={loading}
              progressComponent={
                <div className="p-4">
                  <Spinner animation="border" />
                </div>
              }
              pagination
              paginationTotalRows={totalRows}
              persistTableHead
              responsive
              highlightOnHover
              noDataComponent={<div className="p-4">No error logs found</div>}
            />
          </div>
        </Card>
        <Modal isOpen={modal} toggle={toggleChatBox} size="lg">
          <ModalHeader toggle={toggleChatBox}>
            Chat for Ticket No.
            <b>( {selectedTicketNo} )</b>
          </ModalHeader>

          <ModalBody
            style={{
              height: "480px",

              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#f0f0f0",
            }}
          >
            <div style={{ flexGrow: 1, overflowY: "auto", padding: "10px" }}>
              <div className="text-center text-muted mt-4">
                {sentByUserMessage !== "" || sentByUserMessage !== null ? (
                  <div
                    style={{
                      textAlign: "left",
                      marginBottom: "10px",
                    }}
                  >
                    <div
                      style={{
                        display: "inline-block",
                        backgroundColor: "#fff",
                        borderRadius: "8px",
                        padding: "5px 10px",
                        boxShadow: "0 0 5px rgba(0, 0, 0, 0.1)",
                        maxWidth: "70%",
                      }}
                    >
                      <strong style={{ color: "#555", fontSize: "12px" }}>
                        {selectedTicketNo}
                      </strong>
                      <p style={{ margin: "2px 0", wordBreak: "break-word" }}>
                        {sentByUserMessage}
                      </p>
                      {/* <small style={{ fontSize: "10px", color: "#888" }}>
                          {new Date(msg.createdAt).toLocaleTimeString()}
                        </small> */}
                    </div>
                  </div>
                ) : (
                  "No messages yet. Start a conversation!"
                )}
              </div>
              {messages.length > 0
                ? messages.map((msg, index) => (
                    <div
                      key={index}
                      style={{
                        textAlign:
                          msg.submittedBy === "Tech Team" ? "right" : "left",
                        marginBottom: "10px",
                      }}
                    >
                      <div
                        style={{
                          display: "inline-block",
                          backgroundColor:
                            msg.submittedBy === "Tech Team"
                              ? "#DCF8C6"
                              : "#fff",
                          borderRadius: "8px",
                          padding: "5px 10px",
                          boxShadow: "0 0 5px rgba(0, 0, 0, 0.1)",
                          maxWidth: "70%",
                        }}
                      >
                        <strong style={{ color: "#555", fontSize: "12px" }}>
                          {msg.submittedBy}
                        </strong>
                        <p style={{ margin: "2px 0", wordBreak: "break-word" }}>
                          {msg.message}
                        </p>
                        <small style={{ fontSize: "10px", color: "#888" }}>
                          {new Date(msg.createdAt).toLocaleTimeString()}
                        </small>
                      </div>
                    </div>
                  ))
                : ""}
              <div ref={messagesEndRef} />
            </div>
          </ModalBody>

          <ModalFooter className="p-2">
            <div className="d-flex w-100">
              <Input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                className="mr-2"
                style={{
                  borderRadius: "20px",
                  flexGrow: 1,
                }}
              />
              <Button
                color="primary"
                onClick={handleMessageSent}
                disabled={!newMessage.trim()}
                style={{
                  borderRadius: "50%",
                  width: "40px",
                  height: "40px",
                  padding: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <i className="fas fa-paper-plane"></i>
              </Button>
            </div>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default ErrorLogUploader;
