import { useEffect, useState } from "react";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    CardHeader,
    CardBody,
    FormControl,
    Row,
    Col,
    Button,
} from "react-bootstrap";
import { <PERSON><PERSON>, ModalHeader, ModalBody, Badge, ModalFooter, Input, ListGroup, ListGroupItem } from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import { MultiSelect } from "react-multi-select-component";

import axios from "axios";
import { Link } from "react-router-dom";
import NavImage from "../../assets/img/theme/user-icon.png";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
const PrincipalChargeList = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const collegeId = sessionStorage.getItem("id");
    const [employee, setEmployee] = useState([]);
    const [filterText, setFilterText] = useState("");

    const [modalOpen, setModalOpen] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState([]);

    const toggleModal = () => setModalOpen(!modalOpen);

    useEffect(() => {
        const fetchCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/get-principal-list`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    // console.log(response.data, "response.data");
                    // setEmployee(response.data.getAllEmployeeCollegeWise,"response.data.,getAllEmployeeCollegeWise");


                    setEmployee(response.data);
                } else {
                    alert("Failed to fetch Employee data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        // Call the function
        fetchCollege();

    }, [endPoint, token]);

    const [division, setDivision] = useState([]);
    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    const data = response.data;
                    setDivision(data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };

        getDivision(); // Call the function inside useEffect
    }, [endPoint, token]); // Dependencies

    const getDivisionName = (value) => {
        const divisionObj = division.find((div) => div.divisionCode === value);
        return divisionObj ? divisionObj.name : "Unknown Division";
    };

    // Handle Pagination
    const [college, setCollege] = useState([]);

    const options = college.map((type) => ({
        value: type._id,
        label: type.name,
    }));

    useEffect(() => {
        const fetchCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-all-college`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {

                    const data = response.data;
                    const NoPrincipal = data.filter((item) => item.isPrincipalPosted === false || item.principalType === "Additional Charge")
                    setCollege(NoPrincipal);


                } else {
                    alert("Failed to fetch College data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchCollege();
    }, [endPoint, token]);



    const [base64Image, setBase64Image] = useState("");
    const [isImageModalOpen, setImageIsModalOpen] = useState(false);
    function handleConvertAndDisplay(base64) {
        const sampleBase64Image = `data:image/png;base64,${base64}`;
        setBase64Image(sampleBase64Image);
        setImageIsModalOpen(true);
    }
    const toggleImageModal = () => {
        setImageIsModalOpen(!isImageModalOpen);
    };

    function copyTextFallback(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand("copy");
            // console.log("Text copied using fallback!");
        } catch (err) {
            console.error("Fallback copy failed:", err);
        }
        document.body.removeChild(textArea);
    }


    const [selectedPrincipal, setSelectedPrincipal] = useState({})

    const removeCharge = async (value, Id) => {
        console.log();
        try {
            const response = await axios.put(`${endPoint}/api/additional-principal-remove/${Id}`, {
                additionalCharge: value
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'web-url': window.location.href,
                    "Authorization": `Bearer ${token}`
                }
            });
            if (response.status === 200) {
                SwalMessageAlert("Additional Charge Updated.", "success");
                setTimeout(function () {
                    window.location.reload();
                }, 1000);
            }
            else {
                SwalMessageAlert(response.data.msg, "error");

            }
        } catch (error) {
            console.error('Error updating application:', error);
            alert('Failed to update the application');
        }
    };

    const handleSave = async () => {
        const empId = selectedPrincipal._id;
        console.log(selectedOptions, "Getting Selected Options");
        
        try {
            const response = await axios.put(`${endPoint}/api/additional-principal/${empId}`, {
                additionalCharge: selectedOptions
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'web-url': window.location.href,
                    "Authorization": `Bearer ${token}`
                }
            });
            if (response.status === 200) {
                SwalMessageAlert("Additional Charge Updated.", "success");
                toggleModal();
                setTimeout(function () {
                    window.location.reload();
                }, 1000);
            }
            else {
                SwalMessageAlert(response.data.msg, "error");

            }
        } catch (error) {
            console.error('Error updating application:', error);
            alert('Failed to update the application');
        }
    };

    const columns = [
        {
            name: "Basic Details",
            width: "300px",

            cell: (employee) => (
                <div>
                    <div className="mb-2">
                        <strong>Name: </strong>
                        {employee.title} {employee.name}
                    </div>
                    <div className="mb-2">
                        <strong>Gender: </strong>
                        {employee.gender}
                    </div>
                    <div className="mb-2">
                        <strong>Email: </strong>
                        {employee.email}
                    </div>
                    <div className="mb-2">
                        <strong>Contact: </strong>
                        {employee.contact}
                    </div>
                    <div className="mb-2">
                        <strong>Emp Code: </strong>
                        <strong className="badge-primary">{employee.empCode}</strong>
                    </div>
                    <div className="mb-2">
                        <strong>College: </strong>
                        {employee.collegeDetails.name}
                    </div>
                    <div className="mb-2">
                        <strong>Class: </strong>
                        {employee.class_details["className"]}
                    </div>
                    <div className="mb-2">
                        <strong>Designation: </strong>
                        {employee.designationDetails["designation"]}
                    </div>
                </div>
            ),
            sortable: true,
            sortFunction: (rowA, rowB) =>
                parseInt(rowA.empCode) - parseInt(rowB.empCode),
            wrap: true,
        },

        {
            name: "Photo",
            width: "100px",
            cell: (employee) =>
                employee.encodedImage &&
                    employee.faceVerified !== false &&
                    employee.encodedImage !== "" ? (
                    <img
                        src={`data:image/png;base64,${employee.encodedImage}`}
                        onClick={() => handleConvertAndDisplay(employee.encodedImage)}
                        style={{ width: "50px" }}
                    />
                ) : (
                    <img src={NavImage} style={{ width: "50px" }} />
                ),
        },
        {
            name: "Principal At ",
            width: "200px",
            cell: (employee) => (
                <div>
                    {(employee.designationDetails["designation"] === "UG Principal" ||
                        employee.designationDetails["designation"] === "PG Principal") && <> <div className="mb-2">
                            <strong>Principal At  <span style={{ fontWeight: "bold", color: "blue" }}> {employee.collegeDetails.name} </span></strong>
                            <h4 className="text-red">As Regular Prinicpal</h4>
                        </div> </>}
                    {(employee.isInchargePrincipal === true) && <> <div className="mb-2">
                        <strong>Principal At  <span style={{ fontWeight: "bold", color: "blue" }}> {employee.collegeDetails.name} </span></strong>
                        <h4 style={{ color: "red" }}>As In-Charge Prinicpal</h4>
                    </div> </>}
                </div >
            ),
            sortable: true,
            sortFunction: (rowA, rowB) =>
                parseInt(rowA.empCode) - parseInt(rowB.empCode),
            wrap: true,
        },
        {
            name: "Additional Charge of",
            cell: (employee) => (
                <div>
                    <Button color="success" size="sm" onClick={() => {
                        toggleModal();
                        setSelectedPrincipal(employee);
                        // setSelectedOptions(employee.additionalCharge.length > 0 && employee.additionalCharge)
                    }} title="Edit Profile Details">
                        Add
                    </Button>
                    <table className="table table-hover">
                        <thead>
                            <tr>
                                <th width="200px">College Name</th>
                                <th >Charge Date</th>

                                <th>Action</th> {/* New column for actions */}
                            </tr>
                        </thead>
                        <tbody className="table-striped">
                            {employee.additionalCharge && employee.additionalCharge.length > 0 ? (
                                employee.additionalCharge.map((option) => (
                                    <tr key={option.value}>
                                        <td >{option.label}</td> {/* Display the label */}
                                        <td>{formatDate(option.date)}</td>
                                        <td>
                                            <button
                                                className="btn btn-sm btn-danger" // Bootstrap button for styling
                                                onClick={() => removeCharge(option.value, employee._id)} // Call the remove function
                                            >
                                                Remove
                                            </button>
                                        </td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan="2">No options saved</td> {/* Span across two columns */}
                                </tr>
                            )}
                        </tbody>
                    </table>
                </div >
            ),
            sortable: true,
            sortFunction: (rowA, rowB) =>
                parseInt(rowA.empCode) - parseInt(rowB.empCode),
            wrap: true,
        },

    ];
    const sortedData = employee.sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();
        if (nameA < nameB) return -1;
        if (nameA > nameB) return 1;
        return 0;
    });

    const filteredData = employee.filter((item) => {
        if (item?.activeStatus === true) {
            const filterTextLower = filterText.toLowerCase();

            // Check for "Verified" and "Not Verified"
            if (filterTextLower === "verified") {
                return item.verified === true; // Only include verified employees
            } else if (filterTextLower === "not verified") {
                return item.verified === false; // Only include verified employees
            } else if (filterTextLower === "not") {
                return item.verified === false; // Only include verified employees
            }

            // Default filtering for name, empCode, and contact
            return (
                (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
                (item.empCode &&
                    item.empCode.toLowerCase().includes(filterTextLower)) ||
                (item.contact &&
                    item.contact.toString().toLowerCase().includes(filterTextLower))
            );
        }
    });
    const exportToExcel = async (e) => {
        e.preventDefault();
        if (employee.length > 0) {
            let tableHTML = `
      <table>
        <thead>
          <tr>
            <th>Sno</th>
            <th>Employee Name</th>
            <th>Employee Code</th>
            <th>Email</th>
            <th>Contact</th>
            <th>Division</th>
            <th>District</th>
            <th>Vidhansabha</th>
            <th>Work Type</th>
            <th>Class</th>
            <th>Designation</th>
            <th>Address</th>
          </tr>
        </thead>
        <tbody>
    `;
            employee
                .sort((a, b) => {
                    // Ensure case-insensitive comparison for Division
                    const nameA = a.name.toLowerCase();
                    const nameB = b.name.toLowerCase();
                    if (nameA < nameB) return -1; // a comes before b
                    if (nameA > nameB) return 1; // a comes after b
                    return 0; // a and b are equal
                })
                .forEach((details, subIndex) => {
                    tableHTML += "<tr>";
                    tableHTML += `<td>${subIndex + 1}</td>`;
                    tableHTML += `<td>${details.name}</td>`;
                    tableHTML += `<td>${details.empCode}</td>`;
                    tableHTML += `<td>${details.email}</td>`;
                    tableHTML += `<td>${details.contact}</td>`;
                    tableHTML += `<td>${getDivisionName(details.divison)}</td>`;
                    tableHTML += `<td>${details.districtName}</td>`;
                    tableHTML += `<td>${details.vidhansabhaName}</td>`;
                    tableHTML += `<td>${details.workType}</td>`;
                    tableHTML += `<td>${details.class_details["className"] || "N/A"
                        }</td>`;
                    tableHTML += `<td>${details.designation_details["designation"] || "N/A"
                        }</td>`;
                    tableHTML += `<td>${details.address}</td>`;
                    tableHTML += "</tr>";
                });
            tableHTML += "</tbody></table>";

            const excelFileContent = `
    <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
      <head><!--[if gte mso 9]><xml>
        <x:ExcelWorkbook>
          <x:ExcelWorksheets>
            <x:ExcelWorksheet>
              <x:Name>Sheet1</x:Name>
              <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
            </x:ExcelWorksheet>
          </x:ExcelWorksheets>
        </x:ExcelWorkbook>
      </xml><![endif]-->
      </head>
      <body>${tableHTML}</body>
    </html>
  `;
            const blob = new Blob([excelFileContent], {
                type: "application/vnd.ms-excel;charset=utf-8;",
            });
            const date = new Date().toLocaleDateString();
            const downloadLink = document.createElement("a");
            downloadLink.href = URL.createObjectURL(blob);
            downloadLink.download = `Employee_Report_${date}.xls`;
            downloadLink.click();
        }
    };
    return (
        <>
            <Header />

            {/* Page content */}
            <Container className="mt--7" fluid>
                {/* Table to display institutes with pagination */}
                <Row className="mt-5">
                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                                <Col xs="6">
                                    <h3 className="mb-0">Employee List</h3>
                                </Col>
                                <Col className="text-right" xs="4">
                                    <Button
                                        color="primary"
                                        href="#"
                                        size="sm"
                                        onClick={exportToExcel}
                                    >
                                        Generate Report
                                    </Button>
                                </Col>
                                <FormControl
                                    type="text"
                                    placeholder="Search Employee..."
                                    className="ml-auto"
                                    value={filterText}
                                    onChange={(e) => setFilterText(e.target.value)}
                                    style={{ width: "250px", borderRadius: "30px" }}
                                />
                            </CardHeader>
                            <CardBody>
                                <DataTable
                                    columns={columns}
                                    data={filteredData}
                                    pagination
                                    paginationPerPage={10}
                                    highlightOnHover
                                    striped
                                    sortable // Enable sorting
                                    defaultSortField="name" // Sort by the 'name' column initially
                                    defaultSortAsc={true} // Ascending order
                                    customStyles={{
                                        header: {
                                            style: {
                                                backgroundColor: "#f8f9fa", // Light background color for header
                                                fontWeight: "bold",
                                            },
                                        },
                                        rows: {
                                            style: {
                                                backgroundColor: "#fff", // Row color
                                                borderBottom: "1px solid #ddd",
                                            },
                                            // Apply hover effect through the :hover pseudo-class directly in custom CSS
                                            onHoverStyle: {
                                                backgroundColor: "#ffff99", // Hover color
                                            },
                                        },
                                    }}
                                />
                            </CardBody>
                        </Card>
                    </Col>

                    <Modal
                        isOpen={isImageModalOpen}
                        toggle={toggleImageModal}
                        style={{ textAlign: "center" }}
                    >
                        <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
                        <ModalBody>
                            <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
                        </ModalBody>
                    </Modal>

                    <Modal isOpen={modalOpen} toggle={toggleModal}>
                        <ModalHeader toggle={toggleModal}>Select Institute
                            <br /> <h3>
                                Additional Charge for <span style={{ color: "blue" }}>{selectedPrincipal.name}</span>
                            </h3>
                        </ModalHeader>
                        <ModalBody>
                            <Input
                                type="select"
                                value={selectedOptions ? selectedOptions.value : ""}
                                onChange={(e) => {
                                    const selected = options.find(option => option.value === e.target.value);
                                    setSelectedOptions(selected || null);
                                }}
                            >
                                <option value="">Select an option</option>
                                {options.map(opt => (
                                    <option key={opt.value} value={opt.value}>
                                        {opt.label}
                                    </option>
                                ))}
                            </Input>
                        </ModalBody>
                        <ModalFooter>
                            <Button color="secondary" onClick={toggleModal}>
                                Close
                            </Button>
                            <Button color="primary" onClick={handleSave}>
                                Add
                            </Button>
                        </ModalFooter>
                    </Modal>

                </Row>
            </Container>
        </>
    );
};

export default PrincipalChargeList;
