import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Table,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import axios from "axios";
import Select from "react-select";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import DataTable from "react-data-table-component";
import formatDate from "../../utils/formateDate.jsx";
import Swal from "sweetalert2";

const EmployeeTransfer = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [formData, setFormData] = useState({
    name: "",
    empId: "",
    empCode: "",
    designation: "",
    fatherName: "",
    mobileNo: "",
    transferToCollege: "",
    transferFromCollege: "",
  });

  const [transferData, setTransferData] = useState({
    transferOrderNo: "",
    transferOrderDate: "",
    transferId: [],
  });

  const [errors, setErrors] = useState({});
  const [allColleges, setAllColleges] = useState([]);
  const [previewModal, setPreviewModal] = useState(false);
  const [employees, setEmployees] = useState([]);

  const collegeOptions =
    allColleges.map((college) => ({
      value: college._id,
      label: `${college.name} (${college.aisheCode})`,
    })) || [];

  useEffect(() => {
    const fetchAllColleges = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setAllColleges(response.data);
        } else {
          alert("Failed to fetch college data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchAllColleges();
  }, [endPoint, token]);

  const [transferEmpId, setTransferEmpId] = useState([]);

  const handleInputChange2 = (event) => {
    const { name, value } = event.target;
    setTransferData((prevData) => ({
      ...prevData,
      [name]: value,
      transferId: transferEmpId,
    }));
  };

  const [transferOrderLetterFile, setTransferOrderLetterFile] = useState(null);

  const [pdfFile, setPdfFile] = useState(null);
  const handleFileChange = (event) => {
    const file = event.target.files[0];

    if (file.type !== "application/pdf") {
      setErrors((prevErrors) => ({
        ...prevErrors,
        transferOrderLetter: "Please upload a PDF file.",
      }));
      setTransferOrderLetterFile(null);
      event.target.value = "";
      return;
    }

    if (file.size > 200 * 1024) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        transferOrderLetter: "File size must be less than 200 KB.",
      }));
      setTransferOrderLetterFile(null);
      event.target.value = "";

      return;
    }
    setErrors((prevErrors) => ({
      ...prevErrors,
      transferOrderLetter: null,
    }));
    setTransferOrderLetterFile(file);
    setPdfFile(URL.createObjectURL(file));
  };

  const handleInputChange = async (e) => {
    const { name, value } = e.target;

    if (name === "name") {
      // console.log(value, "Got Selected Employee");

      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee-byId/${value}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const selectedEmployee = response.data[0];

          if (selectedEmployee) {
            setFormData((prev) => ({
              ...prev,
              empId: value,
              empCode: selectedEmployee.empCode,
              designation: selectedEmployee.designationDetails.designation,
              mobileNo: selectedEmployee.contact,
              fatherName: selectedEmployee.fatherName || "",
              name: selectedEmployee.name,
            }));
          }
        }
      } catch (error) {
        console.error("Error fetching Employee data:", error);
        alert("Failed to load Employee data.");
      }
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    setErrors((prev) => ({
      ...prev,
      [name]: "",
    }));
  };

  const handleRemoveEmp = async (emp, event) => {
    event.preventDefault(); // Prevent the default form submission behavior
    console.log("Getting Employee", emp);

    // SweetAlert confirmation dialog
    const result = await Swal.fire({
      title: "क्या आप सुनिश्चित हैं?",
      text: "आप इस कर्मचारी को हटाने जा रहे हैं!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "हाँ, हटाएँ!",
      cancelButtonText: "नहीं, रद्द करें!",
      reverseButtons: true,
    });

    if (result.isConfirmed) {
      try {
        const response = await axios.delete(
          `${endPoint}/api/remove-emp-transfer-list/${emp}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setEmployees(response.data.getAllEmployeeCollegeWise);
          SwalMessageAlert("कर्मचारी सफलतापूर्वक हटा दिया गया!", "success");
          setTimeout(() => window.location.reload(), 2000);
        } else {
          SwalMessageAlert(
            "कर्मचारी को हटाने में विफल। कृपया पुनः प्रयास करें।",
            "error"
          );
        }
      } catch (error) {
        console.error("An error occurred while fetching employees:", error);
        alert("An error occurred. Please try again later.");
      }
    }
  };

  // const [selectedClg, setSelectedClg] = useState("");
  const handleCollegeChange = async (selectedOption) => {
    const collegeId = selectedOption?.value || "";

    setFormData((prev) => ({
      ...prev,
      transferFromCollege: collegeId,
    }));

    try {
      const response = await axios.get(
        `${endPoint}/api/employee/college-wise/${collegeId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setEmployees(response.data.getAllEmployeeCollegeWise);
      } else {
        alert(
          "Failed to fetch employees for the selected college. Please try again."
        );
      }
    } catch (error) {
      console.error("An error occurred while fetching employees:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const togglePreviewModal = () => {
    setPreviewModal(!previewModal);
  };

  const validate = () => {
    const newErrors = {};
    if (!formData.transferToCollege) {
      newErrors.transferToCollege = "Transfer To College is required.";
    }
    if (!formData.transferFromCollege) {
      newErrors.transferFromCollege = "Transfer From College Name is required.";
    }
    if (!formData.fatherName?.trim()) {
      newErrors.fatherName = "Father name is required.";
    }
    if (!formData.name) {
      newErrors.EmpName = "Employee name is required.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const columns = [
    {
      name: "S.no",
      selector: (row, index) => index + 1,
      width: "100px",
      sortable: true,
    },
    {
      name: "Transfer ID / Date",
      selector: (row) => (
        <>
          <div className="font-weight-700">{row.transferID}</div>
          <div className="font-weight-700">({formatDate(row.createdAt)})</div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Employee Details",
      selector: (row) => (
        <div>
          <strong className="text-primary">{row.name || "N/A"}</strong>
          <br />
          <strong>Emp Code: </strong>
          {row.empCode || "N/A"}
          <br />
          <strong>S/o: </strong>
          {row.fatherName || "N/A"}
          <br />
          <strong>Designation: </strong>
          {row.designation || "N/A"}
          <br />
          <strong>Contact No: </strong>
          {row.mobileNo || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Institute Name",
      selector: (row) => (
        <div>
          <strong>From College: </strong>
          {row.transferFromCollege?.name || "N/A"}
          <br />
          <strong>Target College: </strong>
          {row.transferToCollege?.name || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Remove",
      cell: (row) => (
        <button
          className="btn btn-sm btn-danger"
          onClick={(event) => handleRemoveEmp(row._id, event)}
          style={{ cursor: "pointer" }}
        >
          Remove
        </button>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
      width: "100px",
    },
  ];

  const handleTransfer = async (e) => {
    e.preventDefault();

    if (
      transferData.transferOrderDate === "" ||
      transferData.transferOrderDate === null ||
      transferData.transferOrderNo === "" ||
      transferData.transferOrderNo === null
    ) {
      SwalMessageAlert("Please Fill All Details", "error");
      return;
    }

    if (transferOrderLetterFile === "" || transferOrderLetterFile === null) {

      SwalMessageAlert("Please Upload File", "error");
      return;
    }

    if (transferData.transferId.length < 1) {
      SwalMessageAlert("No Employee Selected For Transfer", "error");
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      text: "क्या आप सुनिश्चित करते है की आप कर्मचारियों का स्थानांतरण करना चाहते है ",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "हाँ , स्थानांतरण करें!",
      cancelButtonText: "नहीं , रद्द करें !",
      reverseButtons: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        e.target.disabled = true;
        setTimeout(() => {
          e.target.disabled = false;
        }, 5000);


        // Create a FormData object
        const newformData = new FormData();


        newformData.append("transferData", JSON.stringify(transferData));
        newformData.append("transferOrderLetter", transferOrderLetterFile);


        try {
          const response = await axios.post(
            `${endPoint}/api/final-transferred`,
            newformData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            SwalMessageAlert("Employee Transferred successfully!", "success");
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            SwalMessageAlert(response.data.msg, "error");
          }
        } catch (error) {
          console.error("An error occurred while submitting the form:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    Swal.fire({

      title: "क्या आप सुनिश्चित करते हैं?",
      text: "क्या आप कर्मचारियों का स्थानांतरण के लिए जोड़ना चाहते हैं?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "हाँ, स्थानांतरण करें!",
      cancelButtonText: "नहीं, रद्द करें!",
      reverseButtons: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        e.target.disabled = true; // Disable the form
        setTimeout(() => {
          e.target.disabled = false; // Re-enable after 5 seconds

        }, 5000);

        // Validate the form data
        if (validate()) {
          try {
            const response = await axios.post(
              `${endPoint}/api/transfer-employee/add`,
              formData,
              {
                headers: {
                  "Content-Type": "application/json",
                  'web-url': window.location.href,
                  Authorization: `Bearer ${token}`,
                },
              }
            );

            if (response.status === 200) {
              SwalMessageAlert(

                "कर्मचारी सफलतापूर्वक स्थानांतरण के लिए जोड़ा गया!",
                "success"
              );
              setTimeout(() => {
                window.location.reload(); // Reload the page after 1 second
              }, 1000);

            } else {
              SwalMessageAlert(response.data.msg, "error");
            }
          } catch (error) {
            console.error(
              "An error occurred while submitting the form:",
              error
            );
            SwalMessageAlert(
              "An error occurred. Please try again later.",
              "error"
            );
          }
        } else {
          // Handle validation failure
          SwalMessageAlert("Please correct the errors in the form.", "error");

        }
      }
    });
  };

  const [data, setData] = useState([]);
  const [list, setList] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-all/selected-transferred`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setData(response.data);
          const transferEmpId = response.data.map((emp) => emp._id);
          setTransferEmpId(transferEmpId);
        } else {
          SwalMessageAlert("Data Not Found", "error");
        }
      } catch (error) {
        console.error("An error occurred while fetching employees:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchData();
  }, [endPoint, token]);

  const viewListForTransfer = () => setList(!list);

  const filteredCollegeOptions = collegeOptions.filter(
    (option) => option.value !== formData.transferFromCollege
  );

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Select Employee For Transfer</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-class">College</Label>
                          <Select
                            options={collegeOptions}
                            onChange={handleCollegeChange}
                            placeholder="Select College"
                          />
                          {errors.transferToCollege && (
                            <span className="text-danger">
                              {errors.transferToCollege}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-designation">Employee</Label>
                          <Input
                            type="select"
                            name="name"
                            value={formData.empId}
                            onChange={handleInputChange}
                          >
                            <option value="">Filter by Employee</option>
                            {employees.map((employee) => (
                              <option key={employee._id} value={employee._id}>
                                {employee.name} ({employee.empCode})
                              </option>
                            ))}
                          </Input>
                          {errors.EmpName && (
                            <span className="text-danger">
                              {errors.EmpName}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button
                      disabled={formData.name === ""}
                      color="primary"
                      onClick={togglePreviewModal}
                    >
                      Select
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
        <br />
        <br />
        <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Transfer Employees</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-transfer-order-no">
                            Transfer Order No.
                          </Label>
                          <Input
                            type="text"
                            name="transferOrderNo"
                            value={formData.transferOrderNo}
                            onChange={handleInputChange2}
                            autocomplete="off"
                            placeholder="Enter Transfer Order No."
                          />
                          {errors.transferOrderNo && (
                            <span className="text-danger">
                              {errors.transferOrderNo}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-transfer-order-date">
                            Transfer Order Date
                          </Label>
                          <Input
                            type="date"
                            name="transferOrderDate"
                            max={new Date().toISOString().split("T")[0]}
                            value={formData.transferOrderDate}
                            onChange={handleInputChange2}
                          />
                          {errors.transferOrderDate && (
                            <span className="text-danger">
                              {errors.transferOrderDate}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                       <Col>
                        {pdfFile && (
                          <iframe
                            src={pdfFile}
                            title="PDF Preview"
                            width="100%"
                            height="500px"
                            style={{
                              marginTop: "20px",
                              border: "1px solid #ccc",
                            }}
                          />
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-copy-of-transfer">
                            Copy of Transfer Order
                          </Label>

                          <input
                            type="file"
                            style={{ fontWeight: "bolder" }}
                            onChange={handleFileChange}
                            name="transferOrderLetter"
                            className="form-control"
                            id="inputGroupFile01"
                            accept=".pdf"
                          ></input>
                          {errors.transferOrderLetter && (
                            <span className="text-danger">
                              {errors.transferOrderLetter}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                     
                      <Col>
                        <Button
                          className="bg-primary mt-4 text-white"
                          onClick={handleTransfer}
                        >
                          Submit
                        </Button>
                      </Col>
                    </Row>
                    <Button color="primary" onClick={viewListForTransfer}>
                      View List of Selected Employee
                    </Button>

                    <br />
                    <br />

                    {list && list === true && (
                      <DataTable
                        columns={columns}
                        data={data}
                        noHeader
                        pagination
                        highlightOnHover
                        pointerOnHover
                        dense
                        defaultSortField="transferID"
                      />
                    )}
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
        <br />
        <br />

        {/* <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Employee Transfer Selection List</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Table bordered hover responsive>
                  <thead>
                    <tr >
                      <th style={{ fontWeight: "bold" }}>S.no</th>
                      <th style={{ fontWeight: "bold" }}>Transfer ID / Date</th>
                      <th style={{ fontWeight: "bold" }}>Employee Details</th>
                      <th style={{ fontWeight: "bold" }}>Contact No</th>
                      <th style={{ fontWeight: "bold" }}>Institute Name</th>
                      <th style={{ fontWeight: "bold" }}>Remove</th>
                    </tr>
                  </thead>

                  <tbody>
                    {data.map((record, index) => (
                      <tr key={index}>
                        <td>{index + 1}</td>
                        <td>{record.transferID} <br />{formatDate(record.createdAt)}</td>
                        <td>{record.name || "N/A"} <br />
                          <strong>Emp Code :</strong> {record.empCode || "N/A"} <br />
                          <strong>S/o :</strong> {record.fatherName || "N/A"} <br />
                          <strong>Designation :</strong> {record.designation || "N/A"}</td>
                        <td>{record.mobileNo}</td>
                        <td><strong>From College : </strong>{record.transferFromCollege?.name || "N/A"} <br /> <strong>Target College :</strong> {record.transferToCollege?.name || "N/A"}</td>
                        <td><Button className="btn-sm btn-danger">Remove</Button> </td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row> */}
        <Row></Row>

        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Select For Transfer</h2>
            <h5 className="text-danger mt-2">
              Note: 1. Please check all fields carefully before submitting
            </h5>
            <h5 className="text-danger mt-2">
              2. The selected college should not be available for selection.
            </h5>
          </ModalHeader>
          <ModalBody style={{ backgroundColor: "#f8f9fa", padding: "20px" }}>
            <Row>
              <Col>
                <h4>
                  Transfer From :{" "}
                  {allColleges.find(
                    (college) => college._id === formData.transferFromCollege
                  )?.name || "College not found"}
                </h4>
              </Col>
            </Row>
            <Row>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-emp Code">Employee Name</Label>
                  <Input disabled name="name" value={formData.name} />
                </FormGroup>
              </Col>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-fatherName">Father`s Name</Label>
                  <Input
                    placeholder="Enter Father Name"
                    name="fatherName"
                    value={formData.fatherName}
                    onChange={handleInputChange}
                  />
                  {errors.fatherName && (
                    <span className="text-danger">{errors.fatherName}</span>
                  )}
                </FormGroup>
              </Col>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-empCode">Employee Code</Label>
                  <Input
                    type="text"
                    name="empCode"
                    disabled
                    value={formData.empCode}
                  />
                </FormGroup>
              </Col>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-mobileNo">Mobile No.</Label>
                  <Input
                    type="text"
                    name="mobileNo"
                    disabled
                    value={formData.mobileNo}
                  />
                </FormGroup>
              </Col>
            </Row>
            <Row>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-designation">Designation</Label>
                  <Input
                    name="designation"
                    type="text"
                    value={formData.designation}
                    disabled
                  />
                </FormGroup>
              </Col>
              <Col lg="6">
                <FormGroup>
                  <Label htmlFor="input-transferToCollege">Transfer To</Label>
                  <Select
                    name="transferToCollege"
                    options={filteredCollegeOptions}
                    onChange={(selectedOption) =>
                      setFormData((prev) => ({
                        ...prev,
                        transferToCollege: selectedOption.value,
                      }))
                    }
                    placeholder="Select College"
                  />
                  {errors.transferToCollege && (
                    <span className="text-danger">
                      {errors.transferToCollege}
                    </span>
                  )}
                </FormGroup>
              </Col>
            </Row>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button
              color="secondary"
              onClick={togglePreviewModal}
              style={{ borderRadius: "5px" }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{
                borderRadius: "5px",
                backgroundColor: "#007bff",
                border: "none",
              }}
            >
              Add
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default EmployeeTransfer;
