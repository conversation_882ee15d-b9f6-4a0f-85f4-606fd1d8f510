import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    Label,
    Button,
    Input,
} from "reactstrap";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";
import axios from "axios";
import Swal from "sweetalert2";


const ACRformForRegistrar = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);


    const [formData, setFormData] = useState({
        employeeId: employee._id,
        employeeName: employee.name,
        collegeName: employee.collegeDetails.name,
        collegeId: employee.collegeDetails._id,
        designation: employee.designationDetails.designation,
        niyojanKaPrakar: "",
        padSthapnaKaJ<PERSON>: "",
        karyaKaVivran: "",
        lakshya: "",
        uplabdhi: "",
        purtiKeKamiKaVivran: "",
        atiMahatvaPurnUplabdhi: "",
    })


    const [isTask1Checked, setIsTask1Checked] = useState(false);
    const [isTask2Checked, setIsTask2Checked] = useState(false);
    const [isTask3Checked, setIsTask3Checked] = useState(false);
    const [isTask4Checked, setIsTask4Checked] = useState(false);

    const [appointment, setAppointment] = useState([]);
    useEffect(() => {
        const getAppointment = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/get-appointment-type`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data;
                    setAppointment(data);
                }
            } catch (error) {
                console.error("Error fetching appointment data:", error);
                alert("Failed to load appointment data.");
            }
        };

        getAppointment(); // Call the function inside useEffect
    }, [endPoint, token]); // Dependencies

    const [district, setDistrict] = useState([]);
    useEffect(() => {
        const fetchDistrict = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/district/get-all-district`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    //   // console.log(response.data);

                    setDistrict(response.data);
                } else {
                    alert("Failed to District  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDistrict();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token]);


    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" });
    };

    const handleFinalSubmit = async (e) => {

        // Validation function to check for required fields
        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {
                niyojanKaPrakar: "",
                padSthapnaKaJila: "",
                karyaKaVivran: "",
                lakshya: "",
                uplabdhi: "",
                purtiKeKamiKaVivran: "",
                atiMahatvaPurnUplabdhi: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";
                    }
                }
            }

            return newErrors;
        };



        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert("स्व-प्रतिवेदन सफल हुआ", "success");
                    setTimeout(() => window.location.reload(), 5000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };


    return (
        <>
            <Container className="mt--7" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2"> <u>प्रपत्र - तीन</u></h2>
                                <h2>प्रथम श्रेणी, द्वितीय श्रेणी, कार्यपालिक श्रेणी के अधिकारी का</h2>
                                <br /><h2> <u>गोपनीय प्रतिवेदन</u></h2>
                                <h3>|| 31 मार्च {year} को समाप्त होने होने वाली अवधि ||</h3>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - एक</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong>अधिकारी का नाम

                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={formData.employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पदनाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="designation"
                                                        value={formData.designation} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.designation && (
                                                        <small className="text-danger">
                                                            {errors.designation}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong>नियोजन का प्रकार</Label>
                                                    <Input
                                                        type="select"
                                                        name="niyojanKaPrakar"
                                                        value={formData.niyojanKaPrakar} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    ><option value="">
                                                            No options available
                                                        </option>
                                                        {appointment &&
                                                            appointment.length > 0 &&
                                                            appointment.map((type, index) => (
                                                                <option
                                                                    key={index}
                                                                    value={type.appointment}
                                                                >
                                                                    {type.appointment}
                                                                </option>
                                                            ))}
                                                    </Input>
                                                    {errors.niyojanKaPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyojanKaPrakar}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>4.</strong>पदस्थापना का जिला</Label>
                                                    <Input
                                                        type="select"
                                                        name="padSthapnaKaJila"
                                                        value={formData.padSthapnaKaJila} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    ><option value="">
                                                            No options available
                                                        </option>
                                                        {district &&
                                                            district.length > 0 &&
                                                            district.map((type, index) => (
                                                                <option
                                                                    key={index}
                                                                    value={type.districtNameEng}
                                                                >
                                                                    {type.districtNameEng}({type.districtName})
                                                                </option>
                                                            ))}
                                                    </Input>

                                                    {errors.padSthapnaKaJila && (
                                                        <small className="text-danger">
                                                            {errors.padSthapnaKaJila}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - दो ( प्रतिवेदित अधिकारी द्वारा जाये ).</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="12">
                                                    <Label> <strong>1.</strong>कार्य का संक्षिप्त विवरण</Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={200}
                                                        name="karyaKaVivran"
                                                        value={formData.karyaKaVivran} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.karyaKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <Label> <strong>2.</strong>कृपया आपके लिये निर्धारित गुणात्मक / भौतिक/वित्तीय लक्ष्यों को प्राथमिकता क्रम में और प्रत्येक लक्ष्य के विरूद्ध उपलब्धि का उल्लेख करें ।
                                                    </Label>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>लक्ष्य : </strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        name="lakshya"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={formData.lakshya} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.lakshya && (
                                                        <small className="text-danger">
                                                            {errors.lakshya}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label><strong>उपलब्धियाँ :</strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        name="uplabdhi"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={formData.uplabdhi} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.uplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>3. </strong>
                                                        कृपया कालम 2 के संदर्भ में लक्ष्यों /
                                                        उद्देश्यों की पूर्ति में कमी का संक्षिप्त विवरण दें ।
                                                        यदि लक्ष्यों की पूर्ति में कोई कठिनाई
                                                        (बाधा) आई हो तो उसको भी बतायें ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="purtiKeKamiKaVivran" style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={formData.purtiKeKamiKaVivran} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.purtiKeKamiKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.purtiKeKamiKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label><strong>4. </strong>
                                                        कृपया उन मदों को भी दर्शाये
                                                        जिनमें अति महत्वपूर्ण उपलब्धियां
                                                        और आपका सहयोग रहा हो ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="atiMahatvaPurnUplabdhi"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={formData.atiMahatvaPurnUplabdhi} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.atiMahatvaPurnUplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.atiMahatvaPurnUplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>

                                    <Button color="success" onClick={handleFinalSubmit} >
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row >
            </Container >
        </>
    );
};


ACRformForRegistrar.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRformForRegistrar;
