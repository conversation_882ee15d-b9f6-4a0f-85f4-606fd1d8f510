import React, { useState, useEffect } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  Card,
  CardHeader,
  FormGroup,
  CardBody,
  Input,
  Button,
  Container,
  Row,
  Label,
  Col,
} from "reactstrap";
import { FormControl } from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";

const PHD_Form_set = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [formConfig, setFormConfig] = useState({
    formName: "PhD NOC Form",
    sessionYear: getSessionYear(),
    openDate: "",
    closeDate: "",
  });

  function getSessionYear() {
    const today = new Date();
    const year = today.getFullYear();
    const month = today.getMonth() + 1; // 0 = Jan, so add 1

    if (month >= 7) {
      return `${year}-${(year + 1).toString().slice(-2)}`;
    } else {
      return `${year - 1}-${year.toString().slice(-2)}`;
    }
  }

  const handleChange = (e) => {
    let { name, value } = e.target;

    setFormConfig((prev) => ({ ...prev, [name]: value }));
  };

  const [phdList, setPhdList] = useState({});

  useEffect(() => {
    const fetchPhdChangeLog = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/phd-logs`, {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setPhdList(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchPhdChangeLog();
  }, [endPoint, token]);

  const handleSubmit = async () => {
    try {
      const response = await axios.post(
        `${endPoint}/api/phd-control-form`,
        formConfig,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("Form Control Updated Successfully", "success");
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      } else if( response.status === 400) {
        SwalMessageAlert(`${response.data.msg}`, "error");
      }
    } catch (error) {
      SwalMessageAlert(`Form Control Failed! ${error.response.data.msg}`, "error");
    }
  };

  function getSessionOptions(yearsBack = 5) {
    const currentYear = new Date().getFullYear();
    const options = [];

    for (let i = yearsBack; i >= 0; i--) {
      const startYear = currentYear - i;
      const endYear = (startYear + 1).toString().slice(-2);
      options.push(`${startYear}-${endYear}`);
    }

    return options;
  }

  const [session, setSession] = useState();

  useEffect(() => {
    const SessionYearDropdown = () => {
      const sessionOptions = getSessionOptions(5);
      setSession(sessionOptions);
    };
    SessionYearDropdown();
  }, []);

  const columns = [
    {
      name: "Form Name",
      selector: (row) => row.body?.formName || "-",
      sortable: true,
    },
    {
      name: "Session Year",
      selector: (row) => row.body?.sessionYear || "-",
      sortable: true,
    },
    {
      name: "Open Date",
      selector: (row) => formatDate(row.body?.openDate),
    },
    {
      name: "Close Date",
      selector: (row) => formatDate(row.body?.closeDate),
    },
    {
      name: "Created At",
      selector: (row) => formatDate(row.createdAt),
    },
  ];

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col className="order-xl-1" xl="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <h3 className="mb-0">Ph.D Exam Control</h3>
              </CardHeader>
              <CardBody>
                <Row className="d-flex">
                  <Col md={6}>
                    <FormGroup>
                      <Label for="formName">Form Name</Label>
                      <Input
                        type="text"
                        name="formName"
                        id="formName"
                        readOnly
                        value={formConfig.formName}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>{" "}
                  <Col>
                    <FormGroup>
                      <Label for="sessionYear">Session Year</Label>
                      <Input
                        type="select"
                        name="sessionYear"
                        id="sessionYear"
                        value={formConfig.sessionYear}
                        onChange={handleChange}
                        required
                      >
                        <option value="">-- Select Session Year --</option>
                        {session &&
                          session.map((session) => (
                            <option key={session} value={session}>
                              {session}
                            </option>
                          ))}
                      </Input>
                    </FormGroup>
                  </Col>{" "}
                </Row>

                <Row form>
                  <Col md={6}>
                    <FormGroup>
                      <Label for="openDate">Open Date</Label>
                      <Input
                        type="datetime-local"
                        name="openDate"
                        id="openDate"
                        max={formConfig.closeDate || undefined}
                        value={formConfig.openDate}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>

                  <Col md={6}>
                    <FormGroup>
                      <Label for="closeDate">Close Date</Label>
                      <Input
                        type="datetime-local"
                        name="closeDate"
                        id="closeDate"
                        min={formConfig.openDate || undefined}
                        max={formConfig.correctionOpenDate || undefined}
                        value={formConfig.closeDate}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                <Button color="primary" onClick={handleSubmit} className="mt-3">
                  Submit
                </Button>
              </CardBody>
            </Card>
            <Card className="mt-5">
              <CardHeader style={{backgroundColor:"#5294ad"}}>
               <h2><i className="text-white">Previous Dates</i></h2> 
              </CardHeader>
              <CardBody style={{backgroundColor:"#cbdddd"}}>
                <DataTable
                  columns={columns}
                  data={Array.isArray(phdList) ? phdList : []}
                  pagination
                  highlightOnHover
                  defaultSortField="createdAt"
                  noDataComponent="No records found"
                  customStyles={{
                  
                    header: {
                      style: {
                        backgroundColor: "black",
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        // backgroundColor:"beige",
                        // border:"2px solid black",
                
                        borderBottom: "1px solid #eee",
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default PHD_Form_set;
