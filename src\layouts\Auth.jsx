import React from "react";
import { useLocation, Route, Routes, Navigate } from "react-router-dom";
import { Container, Row } from "reactstrap";
import '../assets/css/authLoginRegister.css';

import Register from "../views/authorization/Register.jsx";
import Login from "../views/authorization/Login.jsx";

// core components
import AuthNavbar from "../components/Navbars/AuthNavbar.jsx";
import AuthFooter from "../components/Footers/AuthFooter.jsx";
import OpenMarkAttendance from "../views/Attendance/open-mark-attendance.jsx";
import OpenMarkNodeAttendance from "../views/Attendance/open-mark-node-attendance.jsx";

const routes = [
  {
    path: "/login",
    name: "Login",
    icon: "ni ni-key-25 text-info",
    component: <Login />,
    layout: "/auth",
  },
  {
    path: "/register",
    name: "Register",
    icon: "ni ni-circle-08 text-pink",
    component: <Register />,
    layout: "/auth",
  },
  {
    path: "/attendance",
    name: "Register",
    icon: "ni ni-circle-08 text-pink",
    component: <OpenMarkAttendance />,
    layout: "/auth",
  },
  {
    path: "/attendance-node",
    name: "Register",
    icon: "ni ni-circle-08 text-pink",
    component: <OpenMarkNodeAttendance />,
    layout: "/auth",
  },
];

const Auth = () => {
  const mainContent = React.useRef(null);
  const location = useLocation();

  React.useEffect(() => {
    document.body.classList.add("bg-default");
    return () => {
      document.body.classList.remove("bg-default");
    };
  }, []);

  React.useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
    mainContent.current.scrollTop = 0;
  }, [location]);

  const getRoutes = () => {
    return routes.map((route, key) => {
      if (route.layout === "/auth") {
        return (
          <Route path={route.path} element={route.component} key={key} />
        );
      }
      return null;
    });
  };

  return (
    <>
      <div className="main-content" ref={mainContent}>
        <AuthNavbar />
        <div className="header bg-gradient-info py-7 py-lg-8">
          
          <div className="separator separator-bottom separator-skew zindex-100">
            <svg
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="fill-default"
                points="2560 0 2560 100 0 100"
              />
            </svg>
          </div>
        </div>
        {/* Page content */}
        <Container className="mt--8 pb-5">
          <Row className="justify-content-center">
            <Routes>
              {getRoutes()}
              <Route path="*" element={<Navigate to="/auth/login" replace />} />
            </Routes>
          </Row>
        </Container>
      </div>
      <AuthFooter />
    </>
  );
};

export default Auth;
