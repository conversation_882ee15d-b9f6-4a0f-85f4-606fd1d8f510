import { useEffect, useState, useMemo } from "react";
// import Chart from "chart.js";
import DataTable from "react-data-table-component";
import {
  Table,
  Row,
  Col,
  CardTitle,
  CardFooter,
  Pagination,
  PaginationItem,
  PaginationLink,
  Spinner,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Input,
  Modal,
  ModalHeader,
  ModalBody,
} from "reactstrap";
import {
  Container,
  Card,
  CardHeader,
  CardBody,
  FormControl,
  Button,
  Form,
  FormGroup,
} from "react-bootstrap";
import "bootstrap/dist/css/bootstrap.min.css";
import axios from "axios";
import { Link } from "react-router-dom";
import "../../assets/plugins/nucleo/css/nucleo.css";
import "../../assets/scss/argon-dashboard-react.scss";
import "@fortawesome/fontawesome-free/css/all.min.css";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
import NavImage from "../../assets/img/theme/user-icon.png";
const Index = (props) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const userType = sessionStorage.getItem("type");
  const collegeId = sessionStorage.getItem("id");
  const type = sessionStorage.getItem("userType");
  const [isTribal, setIsTribal] = useState("false");
  const [degreeTypes, setDegreeTypes] = useState("");
  const [searchTerm, setSearchTerm] = useState('');


  const [clgData, setClgData] = useState();
  const [countedData, setcountedData] = useState();
  // const [updateField, setUpdateField] = useState(false);
  // const toggleUpdateFieldModal = () => setUpdateField(!updateField);

  const [previewPasswordModal, setPreviewPasswordModal] = useState(false);
  const togglePreviewPasswordModal = () =>
    setPreviewPasswordModal(!previewPasswordModal);

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();
    try {
      const body = {
        isTribal: isTribal,
        degreeTypes: degreeTypes,
      };
      e.target.disabled = true;
      setTimeout(() => {
        e.target.disabled = false;
      }, 5000);

      const response = await axios.put(
        `${endPoint}/api/college/update-field/${collegeId}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("College Updated Successfully.", "success");
        // toggleUpdateFieldModal();
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        SwalMessageAlert("Adding for College failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      // alert("An error occurred. Please try again later.");
    }
  };
  const [showUpdateProfile, setShowUpdateProfile] = useState(false);
  useEffect(() => {
    if (type === "Institute" && userType === "3") {
      const getCollegeData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-single-college/${collegeId}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data;
            setClgData(data);
            // if (data.degreeTypes === undefined) {
            //   toggleUpdateFieldModal(true);
            // }

            if (data.isPassChangeFirst === false) {
              setPreviewPasswordModal(true);
            }

            // console.log(
            //   response.data.isProfileUpdated,
            //   "response.data.isProfileUpdated"
            // );
            setShowUpdateProfile(response.data.isProfileUpdated);
          } else {
            SwalMessageAlert("Failed to load college data", "error");
          }
        } catch (error) {
          console.error("Error fetching college data:", error);
          SwalMessageAlert("Failed to load college data.", "error");
        }
      };
      getCollegeData();

      // RX
      const getCollegeCountData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/college-count-all/${collegeId}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response?.status === 200 && response?.data) {
            setcountedData(response.data);
          } else {
            setcountedData(null);
          }
        } catch (error) {
          console.error("Error fetching college count data:", error);
          SwalMessageAlert("Failed to load college count data.", "error");
        }
      };

      getCollegeCountData();
    }
  }, [collegeId, endPoint, token]);

  // if (clgData && clgData.length > 0 && clgData.aisheCode === "C-21813") {
  //   // console.log("Entering Here 2");
  // }

  const [PasswordFormData, setPasswordFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });

  const handleFieldInputChange = (e) => {
    const { name, value } = e.target; // Destructure name and value from e.target

    if (name === "isTribal") {
      setIsTribal(value); // Convert string to boolean
    } else if (name === "degreeTypes") {
      setDegreeTypes(value); // Update degreeTypes state
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setPasswordFormData({ ...PasswordFormData, [name]: value });
    if (name === "newPassword") {
      validatePassword(value);
    }
  };

  const [showPassword, setShowPassword] = useState({
    password: false,
    newPassword: false,
    confirmPassword: false,
  });
  const toggleShowPassword = (field) => {
    setShowPassword((prevState) => ({
      ...prevState,
      [field]: !prevState[field],
    }));
  };
  const [validationRules, setValidationRules] = useState({
    minLength: false,
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  const validatePassword = (password) => {
    const rules = {
      minLength: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[@$!%*?&]/.test(password),
    };

    setValidationRules(rules);
  };
  const handleSubmitPassword = async () => {
    const allValid = Object.values(validationRules).every((rule) => rule);

    if (!allValid) {
      SwalMessageAlert("Password does not meet the requirements.", "error");
      return;
    }

    if (PasswordFormData.newPassword !== PasswordFormData.confirmPassword) {
      SwalMessageAlert("Passwords do not match.", "error");
      return;
    }
    const body = {
      password: PasswordFormData.newPassword,
      confirmPassword: PasswordFormData.confirmPassword,
    };
    try {
      const response = await axios.put(
        `${endPoint}/api/update-password?id=${collegeId}&key=College`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        SwalMessageAlert(data.msg, "success");
        setPreviewPasswordModal(false);
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      } else {
        SwalMessageAlert(`An error occurred. ${response.data.msg}`, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      // setTimeout(() => {
      //   window.location.reload();
      // }, 5000);
    }
  };

  // const [activeNav, setActiveNav] = useState(1);
  // const [chartExample1Data, setChartExample1Data] = useState("data1");

  const [universityCount, setUniversityCount] = useState([]);
  useEffect(() => {
    if (userType === "1") {
      const fetchUniversityTotal = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/count/get-all-count`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            const data = response.data;
            setUniversityCount({
              ...data,
              totalVerifiedUniversity: data.getAllVerifiedUniversity || 0,
              totalUniversity: data.getAllUniversityCount || 0,
              totalVerifiedCollege: data.getAllVerifiedCollege || 0,
              totalCollege: data.getAllCollegeCount || 0,
              totalVerifiedEmployee: data.getAllVerifiedEmployee || 0,
              totalEmployee: data.getAllEmployeeCount || 0,
              totalGuestFacultyCount: data.fetchAllGuestFacultyCount || 0,
              notVerifiedAllGuestFacultyCount:
                data.notVerifiedAllGuestFacultyCount || 0,
              verifiedAllGuestFacultyCount:
                data.verifiedAllGuestFacultyCount || 0,
              totalVerifiedEmployeeCount: data.getAllVerifiedEmployeeCount || 0,
              commissionerEmpCount: data.commissionerEmpCount || 0, //RX
              directorateEmpCount: data.directorateEmpCount || 0, //RX
              instituteEmpCount: data.instituteEmpCount || 0, //RX
              notVerifiedUniversityCount: data.notVerifiedUniversity || 0, //RX
              notVerifiedCollege: data.notVerifiedCollege || 0, //RX
              notVerifiedAllEmployeeCount:
                data.notVerifiedAllEmployeeCount || 0, //RX
              verifiedAllEmployeeCount: data.verifiedAllEmployeeCount || 0, //RX
              notVerifiedAllUniversityCount:
                data.notVerifiedAllUniversityCount || 0, //RX
              verifiedCollegeProfile: data.verifiedCollegeProfile || 0,
              notVerifiedClgProfile: data.notVerifiedClgProfile || 0,
              finalSubmittedClgProfile: data.finalSubmittedClgProfile || 0,
              EmployeeProfileVerifiedCount: data.EmployeeProfileVerifiedCount || 0,
              notVerifiedEmployeeProfile: data.notVerifiedEmployeeProfile || 0,
              finalSubmiteedCountEmployeeProfile: data.finalSubmiteedCountEmployeeProfile || 0,
              NotfinalSubmiteedCountEmployeeProfile: data.NotfinalSubmiteedCountEmployeeProfile || 0,
            });
          } else {
            alert("Failed to Total University. Please try again.");
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          // alert("An error occurred. Please try again later.");
        }
      };

      // Call the function
      fetchUniversityTotal();
    }

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };

  //College Table
  const [college, setCollege] = useState([]);

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchCollege();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]);

  const [university, setUniversity] = useState([]);

  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch University data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const updateStatus = async (id, type) => {
    try {
      if (type === "University") {
        const response = await axios.put(
          `${endPoint}/api/university/update-status/${id}`,
          {}, // Pass empty object if no data needs to be sent in the body
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          // Check the status code to ensure success
          window.location.replace("admin/dashboard");
        } else {
          alert("Failed to verify university. Please try again.");
        }
      } else if (type === "College") {
        const swalWithBootstrapButtons = Swal.mixin({
          customClass: {
            confirmButton: "btn btn-primary",
            cancelButton: "btn btn-warning",
          },
          buttonsStyling: false,
        });
        swalWithBootstrapButtons
          .fire({
            title: "Are you sure?",
            text: "Note: Verify and Reset Password will generate new password for the institute",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Verify and Reset Password",
            cancelButtonText: "Verify Only",
            reverseButtons: true,
          })
          .then(async (result) => {
            if (result.isConfirmed) {
              const response = await axios.put(
                `${endPoint}/api/college/update-status-reset-password/${id}`,
                {}, // Pass empty object if no data needs to be sent in the body
                {
                  headers: {
                    "Content-Type": "application/json",
                    "web-url": window.location.href,
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              if (response.status === 200) {
                const data = response.data;
                SwalMessageAlert(data.msg, "success");
                setTimeout(() => {
                  window.location.reload();
                }, 5000);
              } else {
                SwalMessageAlert(response.data.msg, "error");
              }
            } else if (
              /* Read more about handling dismissals below */
              result.dismiss === Swal.DismissReason.cancel
            ) {
              const response = await axios.put(
                `${endPoint}/api/college/update-status/${id}`,
                {}, // Pass empty object if no data needs to be sent in the body
                {
                  headers: {
                    "Content-Type": "application/json",
                    "web-url": window.location.href,
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              if (response.status === 200) {
                SwalMessageAlert("Verified Successfully", "success");
                setTimeout(() => {
                  window.location.reload();
                }, 5000);
              } else {
                SwalMessageAlert(
                  "Failed to verify Institute. Please try again!",
                  "error"
                );
              }
            }
          });
      } else if (type === "Employee") {
        const response = await axios.put(
          `${endPoint}/api/employee/update-status/${id}`,
          {}, // Pass empty object if no data needs to be sent in the body
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          // Check the status code to ensure success
          window.location.replace("admin/dashboard");
        } else {
          alert("Failed to verify employee. Please try again.");
        }
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      // alert("An error occurred. Please try again later.");
    }
  };

  //Employee Dashboard Data
  const [employee, setEmployee] = useState([]);
  const [showEmployeeList, setShowEmployeeList] = useState(false);
  useEffect(() => {
    if (userType === "3" || userType === "2") {
      const fetchEmployee = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/employee/college-wise/${collegeId}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setEmployee(response.data.getAllEmployeeCollegeWise);
            setUniversityCount({
              totalVerifiedEmployee:
                response.data.getAllEmployeeCollegeWiseVerifiedCount || 0,
              totalEmployee: response.data.getAllEmployeeCollegeWiseCount || 0,
            });
            const employeeCountNotVerified =
              response.data.getAllEmployeeCollegeWise &&
              response.data.getAllEmployeeCollegeWise.length > 0 &&
              response.data.getAllEmployeeCollegeWise.filter(
                (item) =>
                  item?.verified === false && item?.activeStatus === true
              ).length;
            if (employeeCountNotVerified === 0) {
              setShowEmployeeList(true);
            }
          } else {
            alert("Failed to fetch College data. Please try again.");
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          // alert("An error occurred. Please try again later.");
        }
      };
      fetchEmployee();
    }
  }, [endPoint, token]);

  const [filterTextEmployee, setFilterTextEmployee] = useState("");
  const columns = [
    {
      name: "Action",
      width: "300px",
      cell: (employee) => (
        <>
          <button
            className="btn btn-warning btn-sm"
            onClick={() => updateStatus(employee._id, "Employee")}
          >
            <Spinner size="sm" color="white" style={{ marginRight: "8px" }} />
            Verify
          </button>
        </>
      ),
    },
    {
      name: "Basic Details",
      width: "300px",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {employee.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {employee.email}
          </div>
          <div className="mb-2">
            <strong>Contact: </strong>
            {employee.contact}
          </div>
          <div className="mb-2">
            <strong>Emp Code: </strong>
            <strong className="badge-primary">{employee.empCode}</strong>
          </div>
        </div>
      ),
      sortable: true,
      sortFunction: (rowA, rowB) =>
        parseInt(rowA.empCode) - parseInt(rowB.empCode),
      wrap: true,
    },
    {
      name: "Class Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>College: </strong>
            {employee.collegeDetails["name"]}
          </div>
          <div className="mb-2">
            <strong>Class: </strong>
            {employee.class_details["className"]}
          </div>
          <div className="mb-2">
            <strong>Designation: </strong>
            {employee.designation_details["designation"]}
          </div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Division/District/Vidhansabha",
      selector: (row) =>
        `${getDivisionName(row.divison)} / ${row.districtName} / ${row.vidhansabhaName
        }`,
    },
  ];
  const sortedDataEmployee =
    employee &&
    employee.length > 0 &&
    employee.sort((a, b) => {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();
      if (nameA < nameB) return -1; // a comes before b
      if (nameA > nameB) return 1; // a comes after b
      return 0; // a and b are equal
    });
  const filteredDataEmployee = employee.filter((item) => {
    if (item?.verified === false && item?.activeStatus === true) {
      const filterTextLowerEmployee = filterTextEmployee.toLowerCase();

      // Check for "Verified" and "Not Verified"
      if (filterTextLowerEmployee === "verified") {
        return item.verified === true; // Only include verified employees
      } else if (filterTextLowerEmployee === "not verified") {
        return item.verified === false; // Only include not verified employees
      } else if (filterTextLowerEmployee === "not") {
        return item.verified === false; // Only include not verified employees
      }

      // Default filtering for name, empCode, and contact
      return (
        (item.name &&
          item.name.toLowerCase().includes(filterTextLowerEmployee)) ||
        (item.empCode &&
          item.empCode.toLowerCase().includes(filterTextLowerEmployee)) ||
        (item.contact &&
          item.contact
            .toString()
            .toLowerCase()
            .includes(filterTextLowerEmployee))
      );
    }
  });

  const univerityPercent =
    universityCount.totalVerifiedUniversity === 0
      ? 0
      : (universityCount.totalVerifiedUniversity /
        universityCount.totalUniversity) *
      100;
  const collegePercent =
    universityCount.totalVerifiedCollege === 0
      ? 0
      : (universityCount.totalVerifiedCollege / universityCount.totalCollege) *
      100;
  const employeePercent =
    universityCount.totalVerifiedEmployee === 0
      ? 0
      : (universityCount.totalVerifiedEmployee /
        universityCount.totalEmployee) *
      100;

  // Handle Pagination College

  // Handle Pagination University
  const itemsPerPageUniversity = 10; // Items per page
  const [currentPageUniversity, setCurrentPageUniversity] = useState(1);
  const filteredUniversity = useMemo(() => {
    return university.filter((item) => item.status !== true);
  }, [university]);

  useEffect(() => {
    setCurrentPageUniversity(1);
  }, [filteredUniversity]);
  const totalPagesUniversity = Math.ceil(
    filteredUniversity.length / itemsPerPageUniversity
  );
  const indexOfLastItemUniversity =
    currentPageUniversity * itemsPerPageUniversity;
  const indexOfFirstItemUniversity =
    indexOfLastItemUniversity - itemsPerPageUniversity;
  const currentItemsUniversity = filteredUniversity.slice(
    indexOfFirstItemUniversity,
    indexOfLastItemUniversity
  );
  const handleUniversityPageChange = (pageNumber) => {
    if (
      pageNumber !== currentPageUniversity &&
      pageNumber >= 1 &&
      pageNumber <= totalPagesUniversity
    ) {
      setCurrentPageUniversity(pageNumber);
    }
  };

  const handleInputChangeUniversity = (e) => {
    const { value } = e.target;
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university?search=${value}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch University data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };
    fetchUniversity();
  };

  const updateFaceStatus = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/employee/verify-face/${id}`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.replace("admin/dashboard");
      } else {
        SwalMessageAlert("Failed to Face. Please try again.", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      // alert("An error occurred. Please try again later.");
    }
  };
  const uploadAgain = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/employee/reenter-face/${id}`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.replace("admin/dashboard");
      } else {
        SwalMessageAlert("Failed to Face. Please try again.", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      // alert("An error occurred. Please try again later.");
    }
  };
  const updateFaceStatusNode = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/employee/verify-face-node/${id}`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.replace("admin/dashboard");
      } else {
        SwalMessageAlert("Failed to Face. Please try again.", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      // alert("An error occurred. Please try again later.");
    }
  };
  const uploadAgainNode = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/employee/reenter-face-node/${id}`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.replace("admin/dashboard");
      } else {
        SwalMessageAlert("Failed to Face. Please try again.", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      // alert("An error occurred. Please try again later.");
    }
  };
  const [unverifiedFace, setUnVerified] = useState([]);
  const [unverifiedFaceNode, setUnVerifiedNode] = useState([]);
  const [showunverifiedFaceNode, setShowUnVerifiedNode] = useState(false);
  const [filterText, setFilterText] = useState("");
  const [unverifiedFaceList, setShowUnverifiedFaceList] = useState(false);
  useEffect(() => {
    const fetchUniVerifiedFace = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/last-updated-face/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setUnVerified(response.data);
          if (response.data.msg === "No Record Found") {
            setShowUnverifiedFaceList(true);
          }
        } else {
          console.log("No Record Found", response.data.msg);
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };
    const fetchUniVerifiedFaceNode = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get/unverified-faces-node/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setUnVerifiedNode(response.data);
          setUnverifiedFaceFilteredDataNode(response.data);
          if (response.data.msg === "No Record Found") {
            setShowUnVerifiedNode(true);
          }
        } else {
          if (response.data.msg === "No Record Found") {
            setShowUnVerifiedNode(true);
          }
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };
    fetchUniVerifiedFace();
    fetchUniVerifiedFaceNode();
  }, [endPoint, token]);

  const [verifiedFace, setVerified] = useState([]);
  useEffect(() => {
    const fetchVerifiedFace = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/verified-faces/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          console.log("Data=>", response.data);
          setVerified(response.data);
        } else {
          SwalMessageAlert("Failed to fetch data. Please try again.", "error");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        // alert("An error occurred. Please try again later.");
      }
    };

    fetchVerifiedFace();
  }, [endPoint, token]);

  const [base64Image, setBase64Image] = useState("");
  const [isImageModalOpen, setImageIsModalOpen] = useState(false);
  function handleConvertAndDisplay(base64) {
    const sampleBase64Image = `data:image/png;base64,${base64}`;
    setBase64Image(sampleBase64Image);
    setImageIsModalOpen(true);
  }

  const toggleImageModal = () => {
    setImageIsModalOpen(!isImageModalOpen);
  };
  const unverifiedFaceColumns = [
    {
      name: "Action",
      cell: (unverifiedFace) => (
        <div className="d-flex gap-2">
          <button
            className="btn btn-primary btn-sm"
            onClick={() => uploadAgain(unverifiedFace._id)}
          >
            Upload Again
          </button>
          {unverifiedFace.reRegisteredFace !== true && (
            <button
              className="btn btn-warning btn-sm d-flex align-items-center"
              onClick={() => updateFaceStatus(unverifiedFace._id)}
            >
              <Spinner size="sm" color="white" style={{ marginRight: "8px" }} />
              Verify
            </button>
          )}
        </div>
      ),
    },

    {
      name: "Photo",
      cell: (unverifiedFace) =>
        unverifiedFace.encodedImage ? (
          <img
            src={`data:image/png;base64,${unverifiedFace.encodedImage}`}
            onClick={() => handleConvertAndDisplay(unverifiedFace.encodedImage)}
            style={{ width: "50px" }}
          />
        ) : (
          <>
            <img src={NavImage} style={{ width: "30px" }} />
          </>
        ),

      wrap: true,
    },

    {
      name: "Pending From",
      cell: (unverifiedFace) => (
        <div>
          <div className="mb-2 d-flex align-items-center">
            <strong className="me-3">Days</strong>
            <span className="mx-1">|</span>
            <strong className="me-3">Hr</strong>
            <span className="mx-1">|</span>
            <strong>Min</strong>
          </div>
          <div className="mb-2 d-flex align-items-center">
            <span className="badge bg-primary fw-bold text-white me-3">
              {unverifiedFace?.timeSinceUpdate?.days ?? 0}
            </span>
            <span className="mx-1">|</span>
            <span className="badge bg-primary fw-bold text-white me-3">
              {unverifiedFace?.timeSinceUpdate?.hours ?? 0}
            </span>
            <span className="mx-1">|</span>
            <span className="badge bg-primary fw-bold text-white">
              {unverifiedFace?.timeSinceUpdate?.minutes ?? 0}
            </span>
          </div>
        </div>
      ),
      wrap: true,
    },

    {
      name: "Basic Details",
      cell: (unverifiedFace) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {unverifiedFace.name}
          </div>
          <div className="mb-2">
            <strong>Employee Code: </strong>
            <strong className="badge-primary">{unverifiedFace.empCode}</strong>
          </div>
        </div>
      ),
      wrap: true,
    },
  ];

  const unverifiedFaceColumnsNode = [
    {
      name: "Action",
      width: "300px",
      cell: (unverifiedFaceNode) => (
        <div>
          <>
            <div>
              {unverifiedFaceNode.reRegister !== true ? (
                <button
                  className="btn btn-primary btn-sm"
                  onClick={() => uploadAgainNode(unverifiedFaceNode._id)}
                >
                  Upload Again
                </button>
              ) : (
                <span className="btn btn-warning btn-sm mt-2">
                  Sent for Re-Register
                </span>

              )}
            </div>
            {unverifiedFaceNode.reRegister !== true && (
              <>
                <button
                  className="btn btn-warning btn-sm mt-2"
                  onClick={() => updateFaceStatusNode(unverifiedFaceNode._id)}
                >
                  <Spinner
                    size="sm"
                    color="white"
                    style={{ marginRight: "8px" }}
                  />
                  Verify
                </button>
              </>
            )}
          </>{" "}
        </div>
      ),
      wrap: true,
    },
    {
      name: "Photo",
      cell: (unverifiedFaceNode) =>
        unverifiedFaceNode.imageBase64 ? (
          <img
            src={`data:image/png;base64,${unverifiedFaceNode.imageBase64}`}
            onClick={() =>
              handleConvertAndDisplay(unverifiedFaceNode.imageBase64)
            }
            style={{ width: "50px" }}
          />
        ) : (
          <>
            <img src={NavImage} style={{ width: "30px" }} />
          </>
        ),

      wrap: true,
    },

    {
      name: "Pending From",
      width: "200px",
      cell: (unverifiedFaceNode) => (
        <>
          {unverifiedFaceNode.reRegister !== true ? (
            <>
              {(() => {
                const updatedAt = new Date(unverifiedFaceNode.updatedAt);
                const now = new Date();
                const diffInMs = now - updatedAt;
                const days = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
                return (
                  <div>
                    <div className="mb-2">
                      <strong className="me-2">Pending Since  </strong>
                      <span className=" fw-bold text-danger">
                        <strong>{days} {days === 1 ? "Day" : "Days"}</strong>
                      </span>
                    </div>
                  </div>
                );
              })()}
            </>
          ) : (
            <>
              {(() => {
                const updatedAt = new Date(unverifiedFaceNode.updatedAt);
                const day = String(updatedAt.getDate()).padStart(2, "0");
                const month = String(updatedAt.getMonth() + 1).padStart(2, "0");
                const year = updatedAt.getFullYear();
                const h = updatedAt.getHours() % 12 || 12;
                const m = String(updatedAt.getMinutes()).padStart(2, "0");
                const s = String(updatedAt.getSeconds()).padStart(2, "0");
                const ampm = updatedAt.getHours() >= 12 ? "PM" : "AM";
                const formattedDate = `${day}/${month}/${year}, ${h}:${m}:${s} ${ampm}`;
                return (
                  <div>
                    <div className="mb-2">
                      <strong className="me-2">Send for Re-Register On </strong>
                      <span className="fw-bold text-warning">
                        <strong>{(formattedDate)}</strong>
                      </span>
                    </div>
                  </div>
                );
              })()}
            </>
          )}
        </>
      ),
      wrap: true,
    },



    {
      name: "Basic Details",
      width: "300px",
      cell: (unverifiedFace) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {unverifiedFace.name}
          </div>
          <div className="mb-2">
            <strong>Employee Code: </strong>
            <strong className="badge-primary">{unverifiedFace.empCode}</strong>
          </div>
        </div>
      ),
      wrap: true,
    },
  ];

  // const unverifiedFaceFilteredDataNode = Array.isArray(unverifiedFaceNode)
  //   ? unverifiedFaceNode.filter(
  //       (item) =>
  //         //item.name.toLowerCase().includes(filterText.toLowerCase()) ||
  //         item.empCode.toLowerCase().includes(filterText.toLowerCase())
  //       //item.contact.toLowerCase().includes(filterText.toLowerCase())
  //     )
  //   : [];

  const [unverifiedFaceFilteredDataNode, setUnverifiedFaceFilteredDataNode] =
    useState([]);

  useEffect(() => {
    const filteredData = Array.isArray(unverifiedFaceNode)
      ? unverifiedFaceNode.filter((item) =>
        Object.keys(item).some((key) =>
          String(item[key] || "")
            .toLowerCase()
            .includes(searchTerm.toLowerCase())
        )
      )
      : [];

    setUnverifiedFaceFilteredDataNode(filteredData);
    // setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, unverifiedFaceNode]);

  const columnsInstitute = [
    {
      name: "Action",
      cell: (college) => (
        <>
          <button
            className="btn btn-warning btn-sm"
            onClick={() => updateStatus(college._id, "College")}
          >
            <Spinner size="sm" color="white" style={{ marginRight: "8px" }} />
            Verify
          </button>
        </>
      ),
    },
    {
      name: "Basic Details",
      cell: (college) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {college.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {college.collegeEmail}
          </div>
          <div className="mb-2">
            <strong>AISHE Code: </strong>
            <strong className="badge-primary">{college.aisheCode}</strong>
          </div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Contact Details",
      cell: (college) => (
        <div>
          <div className="mb-2">
            <strong>Contact Person: </strong>
            {college.contactPerson}
          </div>
          <div className="mb-2">
            <strong>Mobile: </strong>
            {college.contactNumber}
          </div>
          <div className="mb-2">
            <strong>Institute Type: </strong>
            {college.collegeType === "1" ? "GOVERNMENT" : "PRIVATE"}
          </div>
        </div>
      ),
      wrap: true,
    },
    // { name: "University", selector: (college) => college.universityName },
    {
      name: "Division/District",
      selector: (college) =>
        `${getDivisionName(college.divison)} / ${college.districtName}`,
    },
    // {
    //   name: "Institute Type",
    //   selector: (college) => (college.collegeType === "1" ? "GOVERNMENT" : "PRIVATE"),
    // },
    // { name: "Address", selector: (college) => college.address },
    // {
    //   name: "Institute URL",
    //   cell: (college) => (
    //     <a
    //       href={college.collegeUrl}
    //       target="_blank"
    //       className="btn btn-default btn-sm"
    //     >
    //       Website Link
    //     </a>
    //   ),
    // },
  ];
  const filteredDataCollege = college.filter(
    (item) =>
      item.status === false
        ? (item.name &&
          item.name.toLowerCase().includes(filterText.toLowerCase())) ||
        (item.universityName &&
          item.universityName
            .toLowerCase()
            .includes(filterText.toLowerCase())) ||
        (item.aisheCode &&
          item.aisheCode
            .toString()
            .toLowerCase()
            .includes(filterText.toLowerCase()))
        : "" // Ensures contact is treated as a string
  );
  const [iprFormCount, setIprFormCount] = useState([]);
  useEffect(() => {
    if (userType === "1") {
      const fetchForStateAdmin = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/ipr/count?userType=StateAdmin`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setIprFormCount(response.data);
          } else {
            SwalMessageAlert(
              "Failed to fetch data. Please try again.",
              "error"
            );
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          // alert("An error occurred. Please try again later.");
        }
      };
      fetchForStateAdmin();
    } else if (type === "Institute" || type === "Lead College") {
      const fetchForInstitute = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/ipr/count?id=${collegeId}&userType=Institute`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setIprFormCount(response.data);
          } else {
            SwalMessageAlert(
              "Failed to fetch data. Please try again.",
              "error"
            );
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          // alert("An error occurred. Please try again later.");
        }
      };
      fetchForInstitute();
    }
  }, []);

  const [totalFaceVerifiedCount, setTotalFaceVerifiedCount] = useState(null);
  const [attendanceCount, setAttendanceCount] = useState([]);
  useEffect(() => {
    if (Number(userType) === 3 || Number(userType) === 2) {
      const countAttendanceCollegeWise = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/attendance/count-college-wise/${collegeId}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data;
            setAttendanceCount(data.count);
          } else {
            SwalMessageAlert("Employee Data Not Found", "error");
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          alert("An error occurred. Please try again later.");
        }
      };
      countAttendanceCollegeWise();

      const getVerifiedFaceCountCollegeWise = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/get/face-verified-count/${collegeId}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data;
            setTotalFaceVerifiedCount(data);
          }
        } catch (error) {
          handleApiError(error);
        }
      };
      getVerifiedFaceCountCollegeWise();
    }
  }, []);
  const employeeFaceVerifiedPercent =
    totalFaceVerifiedCount === 0
      ? 0
      : (totalFaceVerifiedCount / universityCount.totalEmployee) * 100;
  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };

  return (
    <>
      <br />
      <br />

      <br />
      {userType === "1" ? (
        <Container className="mt--1" fluid>
          <Row>
            <Col lg="6" xl="3">
              <Link
                to={`/admin/university-list`}
                style={{ textDecoration: "none" }}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    height: "100%",
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          University
                        </CardTitle>
                        <span className="h2 font-weight-bold mb-0">
                          {universityCount.totalUniversity}
                        </span>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                          <i className="fas fa-chart-bar" />
                        </div>
                      </Col>
                    </Row>
                    <p className="mt-3 mb-0 text-muted text-sm">
                      <span
                        style={{ fontWeight: "bold" }}
                        className={
                          univerityPercent < 80
                            ? "text-dark mr-2"
                            : "text-success mr-2"
                        }
                      >
                        Verified :{" "}
                        <strong className="" style={{ color: "green" }}>
                          {universityCount.totalVerifiedUniversity}
                        </strong>
                        <br></br> NotVerified :{" "}
                        <strong className="text-primary">
                          {universityCount.notVerifiedUniversityCount}
                        </strong>
                        <br></br> Total :{" "}
                        <strong className="text-primary">
                          {universityCount.totalUniversity}
                        </strong>
                      </span>
                    </p>
                  </CardBody>
                </Card>
              </Link>
            </Col>
            <Col lg="6" xl="3">
              <Link
                to={`/admin/institute-list`}
                style={{ textDecoration: "none" }}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    height: "100%",
                    border: "1px solid #d9dcdc",
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Institute
                        </CardTitle>
                        <span className="h2 font-weight-bold mb-0">
                          {universityCount.totalCollege}
                        </span>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-warning text-white rounded-circle shadow">
                          <i className="fas fa-chart-pie" />
                        </div>
                      </Col>
                    </Row>
                    <p className="mt-3 mb-0 text-muted text-sm">
                      <span
                        style={{ fontWeight: "bold" }}
                        className={
                          collegePercent < 80
                            ? "text-dark mr-2"
                            : "text-success mr-2"
                        }
                      >
                        Verified:{" "}
                        <strong className="" style={{ color: "green" }}>
                          {universityCount.totalVerifiedCollege}
                        </strong>
                        <br></br>
                        Not Verified:{" "}
                        <strong className="text-primary">
                          {universityCount.notVerifiedCollege}
                        </strong>
                        <br></br>
                        Total :{" "}
                        <strong className="text-primary">
                          {universityCount.totalCollege}
                        </strong>
                      </span>
                    </p>
                  </CardBody>
                </Card>
              </Link>
            </Col>

            {/*   All Employee Count (INSTITUTE + DIRECTORATE + COMMISSIONER)  */}
            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Employees Count`s
                      </CardTitle>
                      <span className="h2 font-weight-bold mb-0">
                        {universityCount.totalEmployee}
                      </span>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-yellow text-white rounded-circle shadow">
                        <i className="fas fa-users" />
                      </div>
                    </Col>
                  </Row>
                  <br></br>
                  <span className=" font-weight-bold mb-0 text-dark me-5">
                    INSTITUTE:{" "}
                    <strong className="text-primary">
                      {universityCount.instituteEmpCount}
                    </strong>
                    <br></br>
                    <h4>
                      Verified:{" "}
                      <strong className="" style={{ color: "green" }}>
                        {universityCount?.verifiedAllEmployeeCount?.INSTITUTE}
                      </strong>{" "}
                      | Not Verified:{" "}
                      <strong className="text-danger">
                        {
                          universityCount?.notVerifiedAllEmployeeCount
                            ?.INSTITUTE
                        }
                      </strong>
                    </h4>
                  </span>
                  {/* <hr className="m-0 p-0"></hr> */}
                  <span className=" font-weight-bold mb-0 text-dark ">
                    DIRECTORATE :{" "}
                    <strong className="text-primary ">
                      {universityCount.directorateEmpCount}
                    </strong>
                    <br></br>
                    <h4>
                      Verified:{" "}
                      <strong className=" " style={{ color: "green" }}>
                        {universityCount?.verifiedAllEmployeeCount?.DIRECTORATE}
                      </strong>{" "}
                      | Not Verified:{" "}
                      <strong className="text-danger ">
                        {
                          universityCount?.notVerifiedAllEmployeeCount
                            ?.DIRECTORATE
                        }
                      </strong>
                    </h4>
                    {/* <hr className="m-0 p-0"></hr> */}
                  </span>
                  <span className=" font-weight-bold mb-0 text-dark">
                    COMMISSIONER :{" "}
                    <strong className="text-primary">
                      {universityCount.commissionerEmpCount}
                    </strong>
                    <br></br>
                    <h4>
                      {" "}
                      Verified:{" "}
                      <strong className="" style={{ color: "green" }}>
                        {
                          universityCount?.verifiedAllEmployeeCount
                            ?.COMMISSIONER
                        }
                      </strong>{" "}
                      | Not Verified:{" "}
                      <strong className="text-danger">
                        {
                          universityCount?.notVerifiedAllEmployeeCount
                            ?.COMMISSIONER
                        }
                      </strong>
                    </h4>
                  </span>
                </CardBody>
              </Card>
            </Col>

            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Employee IPR Form Current Year
                      </CardTitle>
                      <span className="h2  font-weight-bold mb-0">
                        {iprFormCount.iprCount} Out Of <br />
                        {iprFormCount.employeeCount} Employees
                      </span>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                        <i className="fas fa-chart-bar" />
                      </div>
                    </Col>
                  </Row>
                  {/* <p className="mt-3 mb-0 text-muted text-sm">
                    <span
                      style={{ fontWeight: "bold" }}
                      className={
                        employeePercent < 80
                          ? "text-danger mr-2"
                          : "text-success mr-2"
                      }
                    >
                      {universityCount.totalVerifiedEmployee} /{" "}
                      {universityCount.totalEmployee} Verified
                    </span>
                  </p> */}
                </CardBody>
              </Card>
            </Col>
          </Row>
          <br />
          <Row>
            {/* Guest Faculty Count  */}
            <Col lg="6" xl="3">
              <Link
                to={`/admin/guest-faculty-list`}
                style={{ textDecoration: "none" }}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    height: "100%",
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Guest Faculty Counts
                        </CardTitle>
                        <span className=" font-weight-bold mb-0 text-dark me-5">
                          Total:{" "}
                          <strong className="text-primary">
                            {universityCount.totalGuestFacultyCount}
                          </strong>
                        </span>{" "}
                        <br></br>
                        <Link
                          to="/admin/guest-faculty-list?isVerified=true"
                          className="text-decoration-none"
                        >
                          <span className=" font-weight-bold mb-0 text-dark ">
                            Verified :{" "}
                            <strong className="text-primary ">
                              {universityCount.verifiedAllGuestFacultyCount}
                            </strong>
                          </span>
                        </Link>
                        <br></br>
                        <Link
                          to="/admin/guest-faculty-list?isVerified=false"
                          className="text-decoration-none"
                        >
                          <span className=" font-weight-bold mb-0 text-dark">
                            NotVerified :{" "}
                            <strong className="text-primary">
                              {universityCount.notVerifiedAllGuestFacultyCount}
                            </strong>
                          </span>
                        </Link>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-yellow text-white rounded-circle shadow">
                          <i className="fas fa-users" />
                        </div>
                      </Col>
                    </Row>
                    {/* <p className="mt-3 mb-0 text-muted text-sm">
                    <span
                      style={{ fontWeight: "bold" }}
                      className={
                        employeePercent < 80
                          ? "text-danger mr-2"
                          : "text-success mr-2"
                      }
                    >
                      {universityCount.totalVerifiedEmployee} /{" "}
                      {universityCount.totalEmployee} Verified
                    </span>
                  </p> */}
                  </CardBody>
                </Card>
              </Link>
            </Col>

            {/* Transfered Employee Counts */}

            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Transferred Employees
                      </CardTitle>

                      <span className=" font-weight-bold mb-0 text-dark ">
                        Joining Pending :{" "}
                        <strong className="text-primary ">
                          {universityCount.isJoiningPending}
                        </strong>
                      </span>
                      <br></br>
                      <span className=" font-weight-bold mb-0 text-dark ">
                        Releasing Pending :{" "}
                        <strong className="text-primary">
                          {universityCount.isChargeReleasingPending}
                        </strong>
                      </span>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-indigo text-white rounded-full shadow p-4">
                        <i className="fas fa-exchange-alt" />
                      </div>

                    </Col>
                  </Row>
                  {/* <p className="mt-3 mb-0 text-muted text-sm">
                    <span
                      style={{ fontWeight: "bold" }}
                      className={
                        employeePercent < 80
                          ? "text-danger mr-2"
                          : "text-success mr-2"
                      }
                    >
                      {universityCount.totalVerifiedEmployee} /{" "}
                      {universityCount.totalEmployee} Verified
                    </span>
                  </p> */}
                </CardBody>
              </Card>
            </Col>

            {/*  clg */}
            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Institute Profile Status
                      </CardTitle>

                      <span className="h2 font-weight-bold mb-0">
                        {universityCount.totalCollege}
                      </span>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-green text-white rounded-full shadow p-4">
                        <i className="fas fa-check-circle" />
                      </div>

                    </Col>
                  </Row>
                  <p className="text-muted text-sm">
                    <span style={{ fontWeight: "bold", color: "black" }}>
                      {/* Total Institute :{" "}
                         <strong className="text-primary">
                          {universityCount.totalCollege}
                        </strong> <br></br> */}
                      Verified :{" "}
                      <strong className="text-success">
                        {universityCount.verifiedCollegeProfile}
                      </strong>
                      <br></br> Not Verified :{" "}
                      <strong className="text-danger">
                        {universityCount.notVerifiedClgProfile}
                      </strong>
                      <br></br> Final Submitted :{" "}
                      <strong className="text-primary">
                        {universityCount.finalSubmittedClgProfile}
                      </strong>
                    </span>
                  </p>
                </CardBody>
              </Card>
            </Col>
            {/* --- state Admin employee profile count */}
            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Employee Profile Status
                      </CardTitle>
                      <br></br>
                      <span className="font-weight-bold mb-0 text-dark">
                        Total Employee:{" "}
                        <strong className="text-primary">
                          {universityCount.totalEmployee || 0}
                        </strong>
                      </span>
                      <Link
                        to="/admin/employee-list?verified=true"
                        style={{ textDecoration: "none" }}
                      >
                        <br />

                        <h5 className="text-sm">
                          Verified Profile:{" "}
                          <strong className="" style={{ color: "green" }}>
                            {universityCount?.EmployeeProfileVerifiedCount}
                          </strong>{" "}
                          | Not Verified Profile:{" "}
                          <strong className="text-danger">
                            {
                              universityCount?.notVerifiedEmployeeProfile
                            }
                          </strong>
                        </h5>
                        <h5 className="text-sm">
                          Final Submitted:{" "}
                          <strong className="" style={{ color: "green" }}>
                            {universityCount?.finalSubmiteedCountEmployeeProfile}
                          </strong>{" "}
                          | Not Submitted:{" "}
                          <strong className="text-danger">
                            {
                              universityCount?.NotfinalSubmiteedCountEmployeeProfile
                            }
                          </strong>
                        </h5>

                      </Link>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-purple text-white rounded-full shadow p-4">
                        <i className="fas fa-chart-line" />
                      </div>

                    </Col>
                  </Row>
                  <p className="mt-3 mb-0 text-muted text-sm"></p>
                </CardBody>
              </Card>
            </Col>
          </Row>
          <br />
          <Row className="mt-5">
            <Col>
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                  <h3 className="mb-0">Institute List</h3>
                  <FormControl
                    type="text"
                    placeholder="Search Institute..."
                    className="ml-auto"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columnsInstitute}
                    data={filteredDataCollege}
                    pagination
                    paginationPerPage={10}
                    highlightOnHover
                    striped
                    customStyles={{
                      header: {
                        style: {
                          backgroundColor: "#f8f9fa", // Light background color for header
                          fontWeight: "bold",
                        },
                      },
                      rows: {
                        style: {
                          backgroundColor: "#fff", // Row color
                          borderBottom: "1px solid #ddd",
                        },
                        // Apply hover effect through the :hover pseudo-class directly in custom CSS
                        onHoverStyle: {
                          backgroundColor: "#ffff99", // Hover color
                        },
                      },
                    }}
                  />
                </CardBody>
              </Card>
              {/* <Card className="shadow">
                <CardHeader className="border-0">
                  <Row>
                    <Col lg="3">
                      <h3 className="mb-0">Institute List</h3>
                    </Col>
                    <Col lg="3">
                      <input
                        type="text"
                        placeholder="Search Name OR AISHE"
                        onChange={handleInputChangeCollege}
                      />
                    </Col>
                  </Row>
                </CardHeader>
                <Table
                  className="align-items-center table-flush  table-hover"
                  style={{ fontWeight: "normal" }}
                  responsive
                >
                  <thead className="thead-light">
                    <tr>
                      <th scope="col">Name / Addres</th>
                      <th scope="col">Institute Email</th>
                      <th scope="col">University</th>
                      <th scope="col">Contact Person / Contact No</th>
                      <th scope="col">Division/District/Vidhansabha</th>
                      <th scope="col">College Type</th>
                      <th scope="col">Verify Status</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currentItemsCollege.length > 0 ? (
                      currentItemsCollege.map((college, index) =>
                        !college.status ? (
                          <tr key={index}>
                            <td>
                              Name : {college.name} <br /> Address:{" "}
                              {college.address} <br />
                              <span style={{ color: "green" }}>
                                {" "}
                                Code: {college.aisheCode}
                              </span>
                            </td>
                            <td>{college.collegeEmail}</td>
                            <td>{college.universityName}</td>
                            <td>
                              Name: {college.contactPerson} <br /> Mobile :{" "}
                              {college.contactNumber}
                            </td>
                            <td>
                              {getDivisionName(college.divison)} /{" "}
                              {college.districtName} / {college.vidhansabhaName}
                            </td>
                            <td>
                              {college.collegeType === "1"
                                ? "GOVERNMENT"
                                : "PRIVATE"}
                            </td>
                            <td>
                              <button
                                className="btn btn-warning btn-sm"
                                onClick={() =>
                                  updateStatus(college._id, "College")
                                }
                              >
                                <Spinner
                                  size="sm"
                                  color="white"
                                  style={{ marginRight: "8px" }}
                                />
                                Verify
                              </button>
                            </td>
                          </tr>
                        ) : null
                      )
                    ) : (
                      <tr>
                        <td colSpan="8" className="text-center">
                          No colleges to display.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </Table>
                <CardFooter className="py-4">
                  <nav aria-label="...">
                    {/* Pagination Component */}
              {/* <Pagination className="pagination justify-content-end">
                      <PaginationItem disabled={currentPageCollege === 1}>
                        <PaginationLink
                          previous
                          onClick={() =>
                            handleCollegePageChange(currentPageCollege - 1)
                          }
                        />
                      </PaginationItem>
                      {Array.from({ length: totalPagesCollege }, (_, i) => (
                        <PaginationItem
                          key={i}
                          active={i + 1 === currentPageCollege}
                        >
                          <PaginationLink
                            onClick={() => handleCollegePageChange(i + 1)}
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem
                        disabled={currentPageCollege === totalPagesCollege}
                      >
                        <PaginationLink
                          next
                          onClick={() =>
                            handleCollegePageChange(currentPageCollege + 1)
                          }
                        />
                      </PaginationItem>
                    </Pagination>
                  </nav>
                </CardFooter>
              </Card> */}
            </Col>
          </Row>

          <Row className="mt-5">
            <Col>
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader className="border-0">
                  <Row>
                    <Col lg="3">
                      <h3 className="mb-0">University List</h3>
                    </Col>
                    <Col lg="3">
                      <input
                        type="text"
                        placeholder="Search By Name"
                        onChange={handleInputChangeUniversity}
                      />
                    </Col>
                  </Row>
                </CardHeader>
                <Table className="align-items-center table-flush" responsive>
                  <thead className="thead-light">
                    <tr>
                      <th scope="col">Action</th>
                      <th scope="col">University Name</th>
                      <th scope="col">University Email</th>
                      <th scope="col">Contact Person / Contact No</th>
                      <th scope="col">Division / District / Vidhansabha</th>
                      <th scope="col">Registration Date</th>
                      <th scope="col">University Type</th>
                    </tr>
                  </thead>
                  <tbody>
                    {university.length > 0 ? (
                      currentItemsUniversity.map((university, index) =>
                        university.status !== true ? (
                          <tr key={index}>
                            <td>
                              <button
                                className="btn btn-warning btn-sm"
                                onClick={() =>
                                  updateStatus(university._id, "University")
                                }
                              >
                                <Spinner
                                  size="sm"
                                  color="white"
                                  style={{ marginRight: "8px" }}
                                />
                                Verify
                              </button>
                              <Link
                                to={`/admin/update-university/${university._id}`}
                              >
                                <button className="btn btn-primary btn-sm">
                                  <span className="fa fa-eye"></span>
                                </button>
                              </Link>
                            </td>
                            <td>
                              Name : {university.name} <br /> Address :{" "}
                              {university.address} <br /> Reg.No. :{" "}
                              {university.registerationNumber}
                            </td>
                            <td>{university.universityEmail}</td>
                            <td>
                              Name : {university.contactPerson} <br /> Mobile :{" "}
                              {university.contactNumber}
                            </td>
                            <td>
                              {getDivisionName(university.divison)} /{" "}
                              {university.districtName} /{" "}
                              {university.vidhansabhaName}
                            </td>
                            <td>
                              {
                                new Date(university.uniRegDate)
                                  .toISOString()
                                  .split("T")[0]
                              }
                            </td>
                            <td>
                              {university.universityType === 0
                                ? "PRIVATE"
                                : "GOVT."}
                            </td>
                          </tr>
                        ) : null
                      )
                    ) : (
                      <tr>
                        <td colSpan="8" className="text-center">
                          No University to display.
                        </td>
                      </tr>
                    )}
                  </tbody>
                </Table>

                <CardFooter className="py-4">
                  <nav aria-label="...">
                    {/* Pagination Component */}
                    <Pagination className="pagination justify-content-end">
                      <PaginationItem disabled={currentPageUniversity === 1}>
                        <PaginationLink
                          previous
                          onClick={() =>
                            handleUniversityPageChange(
                              currentPageUniversity - 1
                            )
                          }
                        />
                      </PaginationItem>
                      {Array.from({ length: totalPagesUniversity }, (_, i) => (
                        <PaginationItem
                          key={i}
                          active={i + 1 === currentPageUniversity}
                        >
                          <PaginationLink
                            onClick={() => handleUniversityPageChange(i + 1)}
                          >
                            {i + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem
                        disabled={
                          currentPageUniversity === totalPagesUniversity
                        }
                      >
                        <PaginationLink
                          next
                          onClick={() =>
                            handleUniversityPageChange(
                              currentPageUniversity + 1
                            )
                          }
                        />
                      </PaginationItem>
                    </Pagination>
                  </nav>
                </CardFooter>
              </Card>
            </Col>
          </Row>
        </Container>
      ) : userType === "3" || userType === "2" ? (
        <Container className="mt--1" fluid>
          <Modal
            isOpen={previewPasswordModal}
            toggle={togglePreviewPasswordModal}
          >
            <ModalHeader toggle={togglePreviewPasswordModal}>
              <h2>Change Password</h2>
            </ModalHeader>
            <ModalBody>
              <Form role="form">
                <FormGroup className="mb-3">
                  <InputGroup className="input-group-alternative">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i className="ni ni-lock-circle-open" />
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      name="newPassword"
                      placeholder="New Password"
                      type={showPassword.newPassword ? "text" : "password"}
                      value={PasswordFormData.newPassword}
                      style={{ color: "#404446" }}
                      autoComplete="off"
                      onChange={handleInputChange}
                    />
                    <InputGroupAddon addonType="append">
                      <InputGroupText
                        style={{ cursor: "pointer" }}
                        onClick={() => toggleShowPassword("newPassword")}
                      >
                        <i
                          className={
                            showPassword.newPassword
                              ? "fas fa-eye-slash"
                              : "fas fa-eye"
                          }
                        />
                      </InputGroupText>
                    </InputGroupAddon>
                  </InputGroup>
                  {/* Password Validation Rules */}
                  <ul style={{ listStyle: "none", paddingLeft: 0 }}>
                    <li
                      style={{
                        color: validationRules.minLength ? "green" : "red",
                      }}
                    >
                      {validationRules.minLength ? "✔" : "✖"} Minimum 8
                      characters
                    </li>
                    <li
                      style={{
                        color: validationRules.uppercase ? "green" : "red",
                      }}
                    >
                      {validationRules.uppercase ? "✔" : "✖"} At least 1
                      uppercase letter
                    </li>
                    <li
                      style={{
                        color: validationRules.lowercase ? "green" : "red",
                      }}
                    >
                      {validationRules.lowercase ? "✔" : "✖"} At least 1
                      lowercase letter
                    </li>
                    <li
                      style={{
                        color: validationRules.number ? "green" : "red",
                      }}
                    >
                      {validationRules.number ? "✔" : "✖"} At least 1 number
                    </li>
                    <li
                      style={{
                        color: validationRules.specialChar ? "green" : "red",
                      }}
                    >
                      {validationRules.specialChar ? "✔" : "✖"} At least 1
                      special character
                    </li>
                  </ul>
                </FormGroup>
                <FormGroup className="mb-3">
                  <InputGroup className="input-group-alternative">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i className="ni ni-lock-circle-open" />
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      name="confirmPassword"
                      placeholder="Confirm New Password"
                      type={showPassword.confirmPassword ? "text" : "password"}
                      value={PasswordFormData.confirmPassword}
                      style={{ color: "#404446" }}
                      autoComplete="off"
                      onChange={handleInputChange}
                    />
                    <InputGroupAddon addonType="append">
                      <InputGroupText
                        style={{ cursor: "pointer" }}
                        onClick={() => toggleShowPassword("confirmPassword")}
                      >
                        <i
                          className={
                            showPassword.confirmPassword
                              ? "fas fa-eye-slash"
                              : "fas fa-eye"
                          }
                        />
                      </InputGroupText>
                    </InputGroupAddon>
                  </InputGroup>
                </FormGroup>
                <Row>
                  <div className="text-center">
                    <Button
                      className="btn btn-sm btn-primary"
                      color="primary"
                      type="button"
                      onClick={handleSubmitPassword}
                    >
                      Submit
                    </Button>
                  </div>
                </Row>
              </Form>
            </ModalBody>
          </Modal>
          <Modal
            isOpen={isImageModalOpen}
            toggle={toggleImageModal}
            style={{ textAlign: "center" }}
          >
            <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
            <ModalBody>
              <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
            </ModalBody>
          </Modal>

          {/* <Modal isOpen={updateField} toggle={toggleUpdateFieldModal}>
            <ModalHeader toggle={toggleUpdateFieldModal}>
              Update Information
            </ModalHeader>
            <ModalBody>
              <Form>
                <Row>
                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-isTribal"
                      >
                        Is Tribal Institute
                      </label>
                      <div
                        className="row"
                        style={{ justifyContent: "space-evenly" }}
                      >
                        <FormGroup check>
                          <Input
                            type="radio"
                            name="isTribal"
                            id="input-tribal-yes"
                            value="true" // Set value to "true"
                            checked={isTribal === "true"} // Check if isTribal is true
                            onChange={handleFieldInputChange}
                          />
                          <Label check>Yes</Label>
                        </FormGroup>
                        <FormGroup check>
                          <Input
                            type="radio"
                            name="isTribal"
                            id="input-tribal-no"
                            value="false" // Set value to "false"
                            checked={isTribal === "false"} // Check if isTribal is false
                            onChange={handleFieldInputChange}
                          />
                          <Label check>No</Label>
                        </FormGroup>
                      </div>
                    </FormGroup>
                  </Col>

                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-degreeTypes"
                      >
                        Degree Types
                      </label>
                      <Input
                        type="select"
                        name="degreeTypes"
                        id="input-degreeTypes"
                        value={degreeTypes}
                        onChange={handleFieldInputChange}
                      >
                        <option value="">Select College Type</option>
                        <option value="UG">Under Graduate</option>
                        <option value="PG">Post Graduate</option>
                      </Input>
                    </FormGroup>
                  </Col>
                </Row>
                <ModalFooter>
                  <Button color="primary" onClick={handleUpdateSubmit}>
                    Submit
                  </Button>
                  <Button color="secondary" onClick={toggleUpdateFieldModal}>
                    Cancel
                  </Button>
                </ModalFooter>
              </Form>
            </ModalBody>
          </Modal> */}
          <Row>
            <Col lg="6" xl="3">
              <Link
                style={{ textDecoration: "none" }}
                to={`/admin/employee-list`}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                    height: "100%",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Employee Basic Details
                        </CardTitle>
                        <span className="h2 font-weight-bold mb-0">
                          <h4>
                            Total Employee {universityCount.totalEmployee}
                          </h4>
                        </span>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                          <i className="fas fa-chart-bar" />
                        </div>
                      </Col>
                    </Row>
                    <p className="mt-3 mb-0 text-dark text-sm">
                      <span style={{ fontWeight: "bold" }}>
                        Verified :
                        <span className="text-success">
                          {universityCount.totalVerifiedEmployee}
                        </span>{" "}
                        <br /> Not Verified :{" "}
                        <span className="text-danger">
                          {universityCount.totalEmployee -
                            universityCount.totalVerifiedEmployee}
                        </span>
                      </span>
                    </p>
                  </CardBody>
                </Card>
              </Link>
            </Col>
            <Col lg="6" xl="3">
              <Link style={{ textDecoration: "none" }} to={`/admin/iprlist`}>
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    height: "100%",
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Employee IPR Form Current Year
                        </CardTitle>
                        <span className="h2 font-weight-bold mb-0">
                          {iprFormCount.iprCount} Out Of{" "}
                          {iprFormCount.employeeCount} Employees
                        </span>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                          <i className="fas fa-chart-bar" />
                        </div>
                      </Col>
                    </Row>
                  </CardBody>
                </Card>
              </Link>
            </Col>
            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Employee Face Verified
                      </CardTitle>
                      <span className="h4 font-weight-bold mb-0">
                        Total Employee {universityCount.totalEmployee}
                      </span>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                        <i className="fas fa-chart-bar" />
                      </div>
                    </Col>
                  </Row>
                  <p className="mt-3 mb-0 text-black text-sm">
                    <span style={{ fontWeight: "bold" }}>
                      Verified :{" "}
                      <span className="text-success">
                        {totalFaceVerifiedCount > 0
                          ? totalFaceVerifiedCount
                          : 0}
                      </span>
                      <br />
                      Not Verified : <span className="text-danger"></span>{" "}
                      {universityCount.totalEmployee - totalFaceVerifiedCount}
                    </span>
                  </p>
                </CardBody>
              </Card>
            </Col>
            <Col lg="6" xl="3">
              <Link
                style={{ textDecoration: "none" }}
                to={`/admin/today-attendance-list/`}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                    height: "100%",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Today's Attendance
                        </CardTitle>
                        <span className="h3 font-weight-bold mb-0">
                          {attendanceCount} out of{" "}
                          {universityCount.totalEmployee} Employees
                        </span>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-danger text-white rounded-circle shadow">
                          <i className="fas fa-chart-bar" />
                        </div>
                      </Col>
                    </Row>
                  </CardBody>
                </Card>
              </Link>
            </Col>
          </Row>

          <Row className="mt-5">
            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Transferred Employees
                      </CardTitle>
                      <div>
                        <Link
                          to="/admin/incoming-transfer"
                          style={{ textDecoration: "none" }}
                        >
                          <h5 className="font-weight-bold text-success fw-bold">
                            Incoming
                          </h5>
                          <span className="font-weight-bold mb-0 text-dark">
                            Total:{" "}
                            <strong className="text-primary">
                              {countedData?.listIncomingTotal || 0}
                            </strong>
                          </span>
                          <br />
                          <span className="font-weight-bold mb-0 text-dark">
                            Relieving Pending:{" "}
                            <strong className="text-primary">
                              {countedData?.listIncomingRelivingPending || 0}
                            </strong>
                          </span>
                          <br />
                          <span className="font-weight-bold mb-0 text-dark">
                            Joining Pending:{" "}
                            <strong className="text-primary">
                              {countedData?.listIncomingJoinnigPending || 0}
                            </strong>
                          </span>
                        </Link>
                        <Link
                          to="/admin/outgoing-transfer"
                          style={{ textDecoration: "none" }}
                        >
                          <h5 className="font-weight-bold text-danger fw-bold">
                            Outgoing
                          </h5>
                          <span className="font-weight-bold mb-0 text-dark">
                            Total:{" "}
                            <strong className="text-primary">
                              {countedData?.listOutgoingTotal || 0}
                            </strong>
                          </span>
                          <br />
                          <span className="font-weight-bold mb-0 text-dark">
                            Relieving Pending:{" "}
                            <strong className="text-primary">
                              {countedData?.listOutgoingRelivingPending || 0}
                            </strong>
                          </span>
                          <br />
                          <span className="font-weight-bold mb-0 text-dark">
                            Joining Pending:{" "}
                            <strong className="text-primary">
                              {countedData?.listOutgoingJoinnigPending || 0}
                            </strong>
                          </span>
                        </Link>
                      </div>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-indigo text-white rounded-full shadow p-4">
                        <i className="fas fa-exchange-alt" />
                      </div>
                    </Col>
                  </Row>
                </CardBody>
              </Card>
            </Col>

            <Col lg="6" xl="3">
              <Card
                className="card-stats mb-4 mb-xl-0"
                style={{
                  height: "100%",
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardBody>
                  <Row>
                    <div className="col">
                      <CardTitle
                        tag="h5"
                        className="text-uppercase text-muted mb-0"
                      >
                        Employee Profile Status
                      </CardTitle>
                      <span className="font-weight-bold mb-0 text-dark">
                        Total Employee:{" "}
                        <strong className="text-primary">
                          {countedData?.totalEmployeeData || 0}
                        </strong>
                      </span>
                      <Link
                        to="/admin/employee-list?verified=true"
                        style={{ textDecoration: "none" }}
                      >
                        <br />
                        <span className="font-weight-bold mb-0 text-dark">
                          Verified:{" "}
                          <strong className="text-success">
                            {countedData?.empProfileVerifiedCount || 0}
                          </strong>
                        </span>
                        <br />
                        <span className="font-weight-bold mb-0 text-dark">
                          Not Verified:{" "}
                          <strong className="text-danger">
                            {countedData?.notVerifiedEmpProfile || 0}
                          </strong>
                        </span>
                        <br />
                        <span className="font-weight-bold mb-0 text-dark">
                          Final Submitted:{" "}
                          <strong className="text-primary">
                            {countedData?.finalSubmiteedCountEmpProfile || 0}
                          </strong>
                        </span>
                        <span className="font-weight-bold mb-0 text-dark">
                          {" "}
                          <br />
                          Not Submitted:{" "}
                          <strong className="text-primary">
                            {countedData?.NotfinalSubmiteedCountEmpProfile || 0}
                          </strong>
                        </span>
                      </Link>
                    </div>
                    <Col className="col-auto">
                      <div className="icon icon-shape bg-purple text-white rounded-full shadow p-4">
                        <i className="fas fa-chart-line" />
                      </div>
                    </Col>
                  </Row>
                  <p className="mt-3 mb-0 text-muted text-sm"></p>
                </CardBody>
              </Card>
            </Col>

            {/* Guest Faculty Count  */}
            <Col lg="6" xl="3">
              <Link
                to={`/admin/guest-faculty-list`}
                style={{ textDecoration: "none" }}
              >
                <Card
                  className="card-stats mb-4 mb-xl-0"
                  style={{
                    height: "100%",
                    boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                  }}
                >
                  <CardBody>
                    <Row>
                      <div className="col">
                        <CardTitle
                          tag="h5"
                          className="text-uppercase text-muted mb-0"
                        >
                          Guest Faculty Counts
                        </CardTitle>
                        <span className=" font-weight-bold mb-0 text-dark me-5">
                          Total:{" "}
                          <strong className="text-primary">
                            {countedData?.ClgWiseGuestFacultyCount}
                          </strong>
                        </span>{" "}
                        <br></br>
                        <Link
                          to="/admin/guest-faculty-list?isVerified=true"
                          className="text-decoration-none"
                        >
                          <span className=" font-weight-bold mb-0 text-dark ">
                            Verified :{" "}
                            <strong className="text-success ">
                              {countedData?.ClgWiseverifiedAllGuestFacultyCount}
                            </strong>
                          </span>
                        </Link>
                        <br></br>
                        <Link
                          to="/admin/guest-faculty-list?isVerified=false"
                          className="text-decoration-none"
                        >
                          <span className=" font-weight-bold mb-0 text-dark">
                            NotVerified :{" "}
                            <strong className="text-danger">
                              {
                                countedData?.ClgWisenotVerifiedAllGuestFacultyCount
                              }
                            </strong>
                          </span>
                        </Link>
                      </div>
                      <Col className="col-auto">
                        <div className="icon icon-shape bg-yellow text-white rounded-circle shadow">
                          <i className="fas fa-users" />
                        </div>
                      </Col>
                    </Row>
                    {/* <p className="mt-3 mb-0 text-muted text-sm">
                    <span
                      style={{ fontWeight: "bold" }}
                      className={
                        employeePercent < 80
                          ? "text-danger mr-2"
                          : "text-success mr-2"
                      }
                    >
                      {universityCount.totalVerifiedEmployee} /{" "}
                      {universityCount.totalEmployee} Verified
                    </span>
                  </p> */}
                  </CardBody>
                </Card>
              </Link>
            </Col>
          </Row>

          <br />
          <Row className="mt-5">
            <Col style={{ display: showUpdateProfile !== false ? "none" : "" }}>
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader
                  className="border-0 d-flex justify-content-between align-items-center"
                  style={{
                    borderRadius: "5px",
                    background:
                      "linear-gradient(to right, white,rgb(109, 209, 255))",
                  }}
                >
                  <h3 className="mb-0">Update Institute Profile</h3>
                  <Link to={`/admin/update/institute-profile`}>
                    <Button
                      className="btn btn-primary btn-sm"
                      style={{
                        background:
                          "linear-gradient(to right, #84d9d2, #07cdae)",
                        color: "black",
                      }}
                    >
                      Update Profile
                    </Button>
                  </Link>
                </CardHeader>
              </Card>
            </Col>
          </Row>
          <Row className="mt-5">
            <Col style={{ display: showEmployeeList !== false ? "none" : "" }}>
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                  <h3 className="mb-0">
                    Employee List For Basic Details Verification
                  </h3>
                  <FormControl
                    type="text"
                    placeholder="Search Employee..."
                    className="ml-auto"
                    value={filterTextEmployee}
                    onChange={(e) => setFilterTextEmployee(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={columns}
                    data={filteredDataEmployee}
                    pagination
                    paginationPerPage={10}
                    highlightOnHover
                    striped
                    sortable // Enable sorting
                    defaultSortField="name" // Sort by the 'name' column initially
                    defaultSortAsc={true} // Ascending order
                    customStyles={{
                      header: {
                        style: {
                          backgroundColor: "#f8f9fa", // Light background color for header
                          fontWeight: "bold",
                        },
                      },
                      rows: {
                        style: {
                          backgroundColor: "#fff", // Row color
                          borderBottom: "1px solid #ddd",
                        },
                        // Apply hover effect through the :hover pseudo-class directly in custom CSS
                        onHoverStyle: {
                          backgroundColor: "#ffff99", // Hover color
                        },
                      },
                    }}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
          <Row className="mt-5">
            {/* <Col
              sm="6"
              style={{ display: unverifiedFaceList !== false ? "none" : "" }}
            >
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                  <h3 className="mb-0">
                    Employee Registered Face Verification
                  </h3>
                  <FormControl
                    type="text"
                    placeholder="Search..."
                    className="ml-auto"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={unverifiedFaceColumns}
                    data={unverifiedFaceFilteredData}
                    pagination
                    paginationPerPage={10}
                    highlightOnHover
                    striped
                    sortable // Enable sorting
                    defaultSortField="name" // Sort by the 'name' column initially
                    defaultSortAsc={true} // Ascending order
                    customStyles={{
                      header: {
                        style: {
                          backgroundColor: "#f8f9fa", // Light background color for header
                          fontWeight: "bold",
                        },
                      },
                      rows: {
                        style: {
                          backgroundColor: "#fff", // Row color
                          borderBottom: "1px solid #ddd",
                        },
                        // Apply hover effect through the :hover pseudo-class directly in custom CSS
                        onHoverStyle: {
                          backgroundColor: "#ffff99", // Hover color
                        },
                      },
                    }}
                  />
                </CardBody>
              </Card>
            </Col> */}
            <Col

              style={{
                display: showunverifiedFaceNode !== false ? "none" : "",
              }}
            >
              <Card
                style={{
                  boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
                }}
              >
                <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                  <h3 className="mb-0">
                    Employee Registered Face Verification
                    <br />
                  </h3>
                  <FormControl
                    type="text"
                    placeholder="Search..."
                    className="ml-auto"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </CardHeader>
                <CardBody>
                  <DataTable
                    columns={unverifiedFaceColumnsNode}
                    data={unverifiedFaceFilteredDataNode}
                    pagination
                    paginationPerPage={10}
                    highlightOnHover
                    striped
                    sortable // Enable sorting
                    defaultSortField="name" // Sort by the 'name' column initially
                    defaultSortAsc={true} // Ascending order
                    customStyles={{
                      header: {
                        style: {
                          backgroundColor: "#f8f9fa", // Light background color for header
                          fontWeight: "bold",
                        },
                      },
                      rows: {
                        style: {
                          backgroundColor: "#fff", // Row color
                          borderBottom: "1px solid #ddd",
                        },
                        // Apply hover effect through the :hover pseudo-class directly in custom CSS
                        onHoverStyle: {
                          backgroundColor: "#ffff99", // Hover color
                        },
                      },
                    }}
                  />
                </CardBody>
              </Card>
            </Col>
          </Row>
        </Container>
      ) : (
        ""
      )}
    </>
  );
};

export default Index;
