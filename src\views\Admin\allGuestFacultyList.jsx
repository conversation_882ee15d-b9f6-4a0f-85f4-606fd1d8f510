
// BY RX200
import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  Container,
  Card,
  CardHeader,
  CardBody,
  FormControl,
  Col,
  Row,
  FormCheck,
} from "react-bootstrap";
import {  Input, FormGroup} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate";

const GuestFacultyList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");

  const [guestFaculty, setGuestFaculty] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [subjectData, setSubjectData] = useState([]);
  const todaydate = new Date().toISOString().split("T")[0]; // "YYYY-MM-DD"
  const [otrFilter, setOtrFilter] = useState("");
  const [nameFilter, setNameFilter] = useState("");
  const [emailFilter, setEmailFilter] = useState("");
  const [mobileFilter, setMobileFilter] = useState("");
  const [fromJoiningDateFilter, setFromJoiningDateFilter] = useState("");
  const [tojoiningDateFilter, setToJoiningDateFilter] = useState(""); // Default to today
  const [phdFilter, setPhdFilter] = useState(false);
  const [netFilter, setNetFilter] = useState(false);
  const [setFilter, setSetFilter] = useState(false);
  const [mphilFilter, setMphilFilter] = useState(false);
  const [experienceYearsFilter, setExperienceYearsFilter] = useState("");
  const [researchPapersFilter, setResearchPapersFilter] = useState("");
  const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
 const [totalCountData, setTotalCountData] = useState(0);

  const resetFilters = () => {
  setOtrFilter("");
  setNameFilter("");
  setEmailFilter("");
  setMobileFilter("");
  setFromJoiningDateFilter("");
  setToJoiningDateFilter("");
  setPhdFilter(false);
  setNetFilter(false);
  setSetFilter(false);
  setMphilFilter(false);
  setExperienceYearsFilter("");
  setResearchPapersFilter("");
  setIsVerifiedFilter("");
  
  // Optional: trigger fetch manually (if you're not relying on useEffect)
  // fetchGuestFaculty(); // uncomment if needed
};


  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };

  useEffect(() => {
  const queryParams = new URLSearchParams(location.search);
  const value = queryParams.get("isVerified");

  // Set initial filter from URL
  if (value === "true" || value === "false") {
    setIsVerifiedFilter(value);
  } else {
    setIsVerifiedFilter(""); // default for no param
  }
}, []); // runs only once when component mounts

const handleVerificationChange = (e) => {
  const value = e.target.value; // "true", "false", or ""
  setIsVerifiedFilter(value);
};

  useEffect(() => {

    const fetchGuestFaculty = async () => {
      try {
        const params = {};

        if (otrFilter.trim() !== "") params.OTRCode = otrFilter.trim();
        if (nameFilter.trim() !== "") params.name = nameFilter.trim();
        if (emailFilter.trim() !== "") params.email = emailFilter.trim();
        if (mobileFilter.trim() !== "") params.mobile = mobileFilter.trim();

          if (isVerifiedFilter === "true" || isVerifiedFilter === "false" || isVerifiedFilter !== "") {
    params.isVerified = isVerifiedFilter === "true" ? true : isVerifiedFilter === "false" ? false : undefined;
}
        

        if (fromJoiningDateFilter) {
          params.fromJoiningDate = fromJoiningDateFilter;
        }
        if (tojoiningDateFilter) {
          params.toJoiningDate = tojoiningDateFilter;
        }

        if (netFilter) params.net = "yes";
        if (setFilter) params.set = "yes";
        if (mphilFilter) params.mphil = "yes";
        if (phdFilter) params.phd = "yes";
        if (experienceYearsFilter)
          params.experienceYears = experienceYearsFilter;
        if (researchPapersFilter.trim() !== "")
          params.researchPapers = researchPapersFilter.trim();

        const response = await axios.get(
          `${endPoint}/api/fetch/guest-faculty/list-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
            params,
          }
        );

       if (response.status === 200) {
  const data = response.data || {};
  setGuestFaculty(data.results || []);
  setTotalCountData(data.totalCountData || 0);
  setTotalCount((data.results || []).length);
}
 else {
          setGuestFaculty([]);
          setTotalCount(0);
        }
      } catch (error) {
        handleApiError(error);
      }
    };

    fetchGuestFaculty();
  }, [
    experienceYearsFilter,
    endPoint,
    token,
    id,
    otrFilter,
    nameFilter,
    emailFilter,
    mobileFilter,
    fromJoiningDateFilter,
    tojoiningDateFilter,
    phdFilter,
    netFilter,
    setFilter,
    mphilFilter,
    researchPapersFilter,
    isVerifiedFilter,
    
  ]);

// For filter counted with PHD/NET/SET/MPhil
  const selectedQualifications = [
  phdFilter ? "PhD+" : "",
  netFilter ? "NET+" : "",
  setFilter ? "SET+" : "",
  mphilFilter ? "MPhil+" : "",
  experienceYearsFilter ? `Experience: ${experienceYearsFilter} Years+` : "",
  researchPapersFilter ? `Research Papers: ${researchPapersFilter} +` : "",
  isVerifiedFilter === "true" ? "Verified" : isVerifiedFilter === "false" ? "Not Verified" : "",

]
  .filter(Boolean) // removes empty strings
  .join(" "); // joins with space

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: {
             "Content-Type": "application/json",
             'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token]);

  // const finalSubmit = async (id) => {
  //     try {
  //         const response = await axios.get(
  //             `${endPoint}/api/final-submit/guest-faculty/${id}`,
  //             {
  //                 headers: {
  //                     "Content-Type": "application/json",
  //                     'web-url': window.location.href,
  //                     Authorization: `Bearer ${token}`,
  //                 },
  //             }
  //         );
  //         if (response.status === 200) {
  //             setTimeout(() => window.location.reload(), 1000);
  //             SwalMessageAlert("Final Submitted Successfully", "success");
  //         } else {
  //             setTimeout(() => window.location.reload(), 2000);
  //             SwalMessageAlert("Final Submission Failed", "error");
  //         }
  //     } catch (error) {
  //         handleApiError(error);
  //     }
  // };

  const columns = [
    // {
    //     name: "Action",
    //     cell: (row) => (
    //         <>
    //             <a href={`/admin/update/guest-lecture?guestId=${row._id}`}>
    //                 <button className="btn btn-warning btn-sm" title="Edit Details">
    //                     Edit
    //                 </button>
    //             </a>
    //             <button
    //                 style={{ display: row.isFinalSubmit ? "none" : "" }}
    //                 onClick={() => finalSubmit(row._id)}
    //                 className="btn btn-primary mt-2 btn-sm"
    //                 title="Final Submit"
    //             >
    //                 Final Submit
    //             </button>
    //         </>
    //     ),
    // },
    {
      name: "OTR Number",
      cell: (row) => <div>{row.OTRCode}</div>,
    },
    {
      name: "Basic Details",
      cell: (row) => (
        <div>
          <div><strong>Name:</strong> {row.name}</div>
          <div><strong>Email:</strong> {row.email}</div>
          <div><strong>Contact:</strong> {row.mobile}</div>
          <div><strong>DOB:</strong> {formatDate(row.dob)}</div>
        </div>
      ),
    },
    {
      name: "Joining Date",
      cell: (row) => <div>{formatDate(row.joiningDate)}</div>,
    },
    {
      name: "Subject",
      selector: (row) =>
        subjectData.find((a) => String(a._id) === String(row.subject))?.subjectName || "",
    },
    {
      name: "PHD/NET/SET/Mphil",
      selector: (row) => (
        <div>
          <div>{row.phd === "yes" ? `PHD Date: ${formatDate(row.phdNotificationDate)}` : null}</div>
          <div>{row.net === "yes" ? `NET Date: ${formatDate(row.netNotificationDate)}` : null}</div>
          <div>{row.set === "yes" ? `SET Date: ${formatDate(row.setNotificationDate)}` : null}</div>
          <div>{row.mphil === "yes" ? `MPhil Date: ${formatDate(row.mphilNotificationDate)}` : null}</div>
        </div>

      ),
    },
    {
      name: "Experience",
      selector: (row) => (
        <div>
          <div><strong>Years:</strong> {row.experienceYears}</div>
          <div><strong>Months:</strong> {row.experienceMonths}</div>
          <div><strong>Days:</strong> {row.experienceDays}</div>
        </div>
      ),
    },
    {
      name: "Research Papers",
      selector: (row) => <b>{row.researchPapers}</b>,
    },
  ];


  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="border-0 d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">Guest Faculty List</h3>
            </Col>

          </CardHeader>
          <CardBody>
            <Row className="mb-3">
              <Col md={3}>
                <label>Filter by OTR</label>
                <FormControl placeholder="Filter by OTR" value={otrFilter} onChange={(e) => setOtrFilter(e.target.value)} />
              </Col>
              <Col md={3}>
                <label>Filter by Name</label>
                <FormControl placeholder="Filter by Name" value={nameFilter} onChange={(e) => setNameFilter(e.target.value)} />
              </Col>
              <Col md={3}>
                <label>Filter by Email</label>
                <FormControl placeholder="Filter by Email" value={emailFilter} onChange={(e) => setEmailFilter(e.target.value)} />
              </Col>
              <Col md={3}>
                <label>Filter by Mobile</label>
                <FormControl placeholder="Filter by Mobile" value={mobileFilter} onChange={(e) => setMobileFilter(e.target.value)} />
              </Col>
            </Row>
            <Row className="mb-3">
              <Col md={3}>
                <label>Filter by Experience Years</label>
                <FormControl
                  type="number"
                  placeholder="Experience Years"
                  min={0}
                  value={experienceYearsFilter}
                  onChange={(e) => setExperienceYearsFilter(e.target.value)}
                />
              </Col>
              <Col md={3}>
                <label>Filter by Research Papers</label>
                <FormControl
                  type="number"
                  min={0}
                  placeholder="Research Papers"
                  value={researchPapersFilter}
                  onChange={(e) => setResearchPapersFilter(e.target.value)}
                />
              </Col>
              <Col md={3}>
                <label>From Joining Date</label>
                <FormControl
                  type="date"
                  value={fromJoiningDateFilter}
                  max={todaydate}
                  onChange={(e) => setFromJoiningDateFilter(e.target.value)}
                />
              </Col>
              <Col md={3}>
                <label>To Joining Date</label>
                <FormControl
                  type="date"
                  max={todaydate}
                  value={tojoiningDateFilter}
                  onChange={(e) => setToJoiningDateFilter(e.target.value)}
                />
              </Col>
            </Row>
            <Row className="mb-3 text-center">
              <Col md={1}>
                <FormCheck label="PHD" checked={phdFilter} onChange={(e) => setPhdFilter(e.target.checked)} />
              </Col>
              <Col md={1}>
                <FormCheck label="NET" checked={netFilter} onChange={(e) => setNetFilter(e.target.checked)} />
              </Col>
              <Col md={1}>
                <FormCheck label="SET" checked={setFilter} onChange={(e) => setSetFilter(e.target.checked)} />
              </Col>
              <Col md={1}>
                <FormCheck label="MPhil" checked={mphilFilter} onChange={(e) => setMphilFilter(e.target.checked)} />
              </Col>
              
              <Col md={2}>
                            <FormGroup>
                                <Input
                                id="isVerifiedSelect"
                                type="select"
                                value={isVerifiedFilter}
                                onChange={handleVerificationChange}
                                >
                                <option value=""><strong>All</strong></option>
                                <option value="true"><strong>Verified</strong></option>
                                <option value="false"><strong>Not Verified</strong></option>
                                </Input>
                            </FormGroup>
              </Col>
              <Col md={2}>
                    <button onClick={resetFilters} className="btn btn-danger">
                      Reset Filters
                    </button>
              </Col>

              <Col md={4}>
                <strong className="mb-0">
                  Total Guest Faculty : <span className="text-danger">{totalCountData}</span>
                </strong>
                <br />
                {selectedQualifications && selectedQualifications !== "" && (
                  <strong className="mb-0">
                    {selectedQualifications} :={" "}
                    <span className="text-primary">{totalCount}</span>
                  </strong>
                )}
              </Col>

            </Row>
            <DataTable
              columns={columns}
              data={guestFaculty.length > 0 ? guestFaculty : []}
              pagination
              highlightOnHover
              striped
              responsive
              persistTableHead
            />
          </CardBody>
        </Card>
      </Container>
    </>
  );    
};

export default GuestFacultyList;
// This code is a React component that displays a list of guest faculty members with various filters and actions.