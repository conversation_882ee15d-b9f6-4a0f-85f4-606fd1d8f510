import { useState, useEffect } from "react";
import {
  Form,
  Row,
  Col,
  FormGroup,
  Label,
  Input,
  Button,
  Container,
  Card,
  CardHeader,
  CardBody,
  Modal,
  ModalHeader,
  ModalBody,
} from "reactstrap";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Swal from "sweetalert2";
import { useLocation, useNavigate } from "react-router-dom";

import axios from "axios";
import formatDate from "../../utils/formateDate";
const UpdateGuestFacultyForm = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const guestId = searchParams.get("guestId");
  const [subjectData, setSubjectData] = useState([]);
  const [isDraftSaved, setIsDraftSaved] = useState(false);
  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
          console.log("");
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token]);
  const [formData, setFormData] = useState({
    collegeId: id,
    name: "",
    dob: "",
    mobile: "",
    email: "",
    address: "",
    joiningDate: "",
    subject: "",
    OTRCode: "",
    phd: "",
    net: "",
    set: "",
    mphil: "",
    phdNotificationDate: "",
    netNotificationDate: "",
    setNotificationDate: "",
    mphilNotificationDate: "",
    experienceYears: "",
    experienceMonths: "",
    experienceDays: "",
    researchPapers: "",
  });

  const [alreadyotrCode, setalreadyotrCode] = useState(null);
  useEffect(() => {
    if (guestId !== null) {
      const getGuestFacultyData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/fetch/guest-faculty/${guestId}`,
            {
              headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
            }
          );
          if (response.status === 200) {
            console.log();
            setalreadyotrCode(response.data.OTRCode);
            setFormData({
              collegeId: response.data.collegeId,
              name: response.data.name,
              dob: response.data.dob,
              mobile: response.data.mobile,
              email: response.data.email,
              address: response.data.address,
              joiningDate: response.data.joiningDate,
              subject: response.data.subject,
              OTRCode: response.data.OTRCode,
              phd: response.data.phd,
              net: response.data.net,
              set: response.data.set,
              mphil: response.data.mphil,
              phdNotificationDate: response.data.phdNotificationDate,
              netNotificationDate: response.data.netNotificationDate,
              setNotificationDate: response.data.setNotificationDate,
              mphilNotificationDate: response.data.mphilNotificationDate,
              experienceYears: response.data.experienceYears,
              experienceMonths: response.data.experienceMonths,
              experienceDays: response.data.experienceDays,
              researchPapers: response.data.researchPapers,
            });
          }
        } catch (error) {
          handleApiError(error);
        }
      };
      getGuestFacultyData();
    }
  }, [guestId, endPoint, token]);

  const [errors, setErrors] = useState({});
  const validate = () => {
    const newErrors = {};

    if (!formData.name) newErrors.name = "Name is Required";
    if (!formData.dob) newErrors.dob = "Date of Birth is Required";
    if (!/^\d{10}$/.test(formData.mobile))
      newErrors.mobile = "Enter a valid 10-digit mobile number.";
    if (!/^[\w-.]+@[\w-]+\.[a-z]{2,4}$/.test(formData.email))
      newErrors.email = "Enter a valid email address.";
    if (!formData.address) newErrors.address = "Address is Required";
    if (!formData.joiningDate)
      newErrors.joiningDate = "Joining date is Required";
    if (!formData.subject) newErrors.subject = "Subject is Required";
    if (formData.researchPapers && isNaN(Number(formData.researchPapers)))
      newErrors.researchPapers = "Enter a valid number for research papers.";
    if (formData.experienceYears && isNaN(Number(formData.experienceYears)))
      newErrors.experienceYears =
        "Enter a valid number for years of experience.";
    if (formData.experienceMonths && isNaN(Number(formData.experienceMonths)))
      newErrors.experienceMonths =
        "Enter a valid number for months of experience.";
    if (formData.experienceDays && isNaN(Number(formData.experienceDays)))
      newErrors.experienceDays = "Enter a valid number for days of experience.";

    if (formData.phd === "yes" && !formData.phdNotificationDate) {
      newErrors.phdNotificationDate = "PHD Notification Date is Required.";
    }
    if (formData.net === "yes" && !formData.netNotificationDate) {
      newErrors.netNotificationDate = "NET Notification Date is Required.";
    }
    if (formData.set === "yes" && !formData.setNotificationDate) {
      newErrors.setNotificationDate = "SET Notification Date is Required.";
    }
    if (formData.mphil === "yes" && !formData.mphilNotificationDate) {
      newErrors.mphilNotificationDate = "Mphil Notification Date is Required.";
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const calculateAge = (dob) => {
    const birthDate = new Date(dob);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Max selectable date = today - 18 years
  const getMaxDOB = () => {
    const today = new Date();
    today.setFullYear(today.getFullYear() - 18);
    return today.toISOString().split("T")[0];
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const generateOTRCode = () => {
    const randomSixDigit = Math.floor(100000 + Math.random() * 900000);
    return `GF${randomSixDigit}`;
  };

  useEffect(() => {
    const fetchOTRCode = async () => {
      if (!formData.OTRCode) {
        try {
          const response = await axios.get(
            `${endPoint}/api/generate-otr-code`,
            {
              headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
            }
          );
          if (response.status === 200) {
            console.log();
            setFormData((prev) => ({
              ...prev,
              OTRCode:
                guestId !== null || guestId !== "null"
                  ? alreadyotrCode
                  : response.data.OTRCode,
            }));
          }
        } catch (err) {
          console.error("Error fetching OTR code", err);
        }
      }
    };

    fetchOTRCode();
  }, [alreadyotrCode, endPoint, formData.OTRCode, guestId, token]);

  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);
  // const handleSubmit = async (e) => {
  //   e.preventDefault();
  //   if (validate()) {
  //     const response = await axios.post(
  //       `${endPoint}/api/add/guest-faculty`,
  //       formData,
  //       {
  //         headers: {
  //           "Content-Type": "application/json",
  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );
  //     if (response.status === 200) {
  //       SwalMessageAlert("Guest Faculty Added SuccessFully", "success");
  //       setFormData({
  //         name: "",
  //         dob: "",
  //         mobile: "",
  //         email: "",
  //         address: "",
  //         joiningDate: "",
  //         subject: "",
  //         phd: "",
  //         net: "",
  //         set: "",
  //         mphil: "",
  //         phdNotificationDate: "",
  //         netNotificationDate: "",
  //         setNotificationDate: "",
  //         mphilNotificationDate: "",
  //         experienceYears: "",
  //         experienceMonths: "",
  //         experienceDays: "",
  //         researchPapers: "",
  //       });
  //     } else {
  //       SwalMessageAlert("Guest Faculty Add Failed", "error");
  //     }
  //   }
  // };

  const [guestFacultyId, setGuestFacultyId] = useState(null);
  const handleFinalSubmit = async (e, isFinal = false) => {
    e.preventDefault();

    if (validate()) {
      let dataToSubmit = {
        ...formData,
        isFinalSubmit: isFinal,
        isDrafted: true,
      };
      if (isFinal && !formData.OTRCode) {
        dataToSubmit.OTRCode = generateOTRCode();
      }

      try {
        const response = await axios.post(
          `${endPoint}/api/add/guest-faculty`,
          dataToSubmit,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          Swal.fire({
            title: isFinal ? "Form Submitted!" : "Draft Saved!",
            text: isFinal
              ? "Your form has been successfully submitted."
              : "Your draft has been saved successfully.",
            icon: "success",
          });
          setGuestFacultyId(response.data.data._id);
          setFormData(response.data.data);
          setPreviewModal(true);
          setIsDraftSaved(true);
        }
      } catch (err) {
        console.error(err);
        SwalMessageAlert("Something went wrong", "error");
      }
    }
  };

  const finalSubmit = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/final-submit/guest-faculty/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setTimeout(() => {
          window.location.reload();
        }, 3000);
        SwalMessageAlert("Final Submitted Successfully", "success");
      } else {
        setTimeout(() => {
          window.location.reload();
        }, 3000);
        SwalMessageAlert("Final Submission Failed", "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };

  function formatDateForInput(isoDate) {
    if (!isoDate) return "";
    const date = new Date(isoDate);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are 0-indexed
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  }

  const handleUpdateFinalSubmit = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/faculty/update/${id}`,
        formData,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Details Updated Successfully", "success");
        setTimeout(() => {
          window.location.replace("/admin/guest-faculty-list");
        }, 1000);
      } else {
        SwalMessageAlert("Updation Failed", "error");
        setTimeout(() => {
          window.location.replace("/admin/guest-faculty-list");
        }, 1000);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        {/* Form Section */}
        <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Update Guest Faculty</h3>
                    <h4 style={{ color: "blue", fontWeight: "bold" }}> (OTR Number : {formData.OTRCode})</h4>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form className="pl-lg-4">
                  <Row>
                    {/* <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-subject">OTR Code</Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-hat-3" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            type="OTRCode"
                            id="input-OTRCode"
                            name="OTRCode"
                            value={formData.OTRCode}
                            maxLength={8}
                            onChange={handleChange}
                            disabled
                          ></Input>
                          {errors.OTRCode && (
                            <span className="text-danger">
                              {errors.OTRCode}
                            </span>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col> */}
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-name">Name</Label>
                        <Input
                          type="text"
                          id="input-name"
                          name="name"
                          value={formData.name}
                          onChange={handleChange}
                        />
                        {errors.name && (
                          <span className="text-danger">{errors.name}</span>
                        )}
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-dob">Date of Birth</Label>
                        <Input
                          type="date"
                          id="input-dob"
                          name="dob"
                          value={formatDateForInput(formData.dob)}
                          max={getMaxDOB()}
                          onChange={(e) => {
                            const dob = e.target.value;
                            const age = calculateAge(dob);

                            if (age < 18) {
                              SwalMessageAlert(
                                "Age must be at least 18 years.",
                                "warning"
                              );
                              return;
                            }

                            handleChange(e);
                          }}
                        />
                        {errors.dob && (
                          <span className="text-danger">{errors.dob}</span>
                        )}
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-mobile">Mobile No</Label>
                        <Input
                          type="text"
                          id="input-mobile"
                          name="mobile"
                          maxLength={10}
                          value={formData.mobile}
                          onChange={(e) => {
                            const value = e.target.value;

                            // Check if the input is empty
                            if (value === "") {
                              handleChange(e);
                              return;
                            }

                            // Regex to match numbers starting with 6, 7, 8, or 9
                            const validStartRegex = /^[6-9]/;

                            if (validStartRegex.test(value)) {
                              // If valid, pass 'true' as the second argument to handleInputChange
                              handleChange(e);
                            } else {
                              // Show alert if the input starts with invalid numbers
                              // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                              SwalMessageAlert(
                                " Mobile number must start with digits 6, 7, 8, or 9.",
                                "warning"
                              );
                            }
                          }}
                        />
                        {errors.mobile && (
                          <span className="text-danger">{errors.mobile}</span>
                        )}
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-email">Email ID</Label>
                        <Input
                          type="email"
                          id="input-email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                        />
                        {errors.email && (
                          <span className="text-danger">{errors.email}</span>
                        )}
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-address">Address</Label>
                        <Input
                          type="textarea"
                          id="input-address"
                          name="address"
                          value={formData.address}
                          onChange={handleChange}
                        />
                        {errors.address && (
                          <span className="text-danger">{errors.address}</span>
                        )}
                      </FormGroup>
                    </Col>

                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-joining-date">Joining Date</Label>
                        <Input
                          type="date"
                          id="input-joining-date"
                          name="joiningDate"
                          value={formatDateForInput(formData.joiningDate)}
                          onChange={handleChange}
                        />
                        {errors.joiningDate && (
                          <span className="text-danger">
                            {errors.joiningDate}
                          </span>
                        )}
                      </FormGroup>
                    </Col>
                  </Row>

                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label htmlFor="input-subject">Subject</Label>
                        <Input
                          type="select"
                          id="input-subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleChange}
                        >
                          <option value="">Select Subject</option>
                          {subjectData.length > 0 ? (
                            subjectData.map((type, index) => (
                              <option key={index} value={type._id}>
                                {type.subjectName}
                              </option>
                            ))
                          ) : (
                            <option disabled>No options available</option>
                          )}
                        </Input>
                        {errors.subject && (
                          <span className="text-danger">{errors.subject}</span>
                        )}
                      </FormGroup>
                    </Col>
                    <Col lg="6">
                      <FormGroup>
                        <Label>
                          Experience (as on 30th June of previous year):
                        </Label>
                        <Row>
                          <Col lg="4">
                            <Input
                              type="text"
                              name="experienceYears"
                              placeholder="Years"
                              value={formData.experienceYears}
                              onChange={handleChange}
                            />
                          </Col>
                          <Col lg="4">
                            <Input
                              type="text"
                              name="experienceMonths"
                              placeholder="Months"
                              value={formData.experienceMonths}
                              onChange={handleChange}
                            />
                          </Col>
                          <Col lg="4">
                            <Input
                              type="text"
                              name="experienceDays"
                              placeholder="Days"
                              value={formData.experienceDays}
                              onChange={handleChange}
                            />
                          </Col>
                        </Row>
                        {(errors.experienceYears ||
                          errors.experienceMonths ||
                          errors.experienceDays) && (
                            <span className="text-danger">
                              Invalid experience details.
                            </span>
                          )}
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label htmlFor="input-research-papers">
                          No. of Research Papers Published
                        </Label>
                        <Input
                          type="text"
                          id="input-research-papers"
                          name="researchPapers"
                          value={formData.researchPapers}
                          onChange={handleChange}
                        />
                        {errors.researchPapers && (
                          <span className="text-danger">
                            {errors.researchPapers}
                          </span>
                        )}
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label>PHD</Label>
                        <Input
                          type="select"
                          name="phd"
                          value={formData.phd}
                          onChange={handleChange}
                        >
                          <option value="">Select</option>
                          <option value="yes">Yes</option>
                          <option value="no">No</option>
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>NET</Label>
                        <Input
                          type="select"
                          name="net"
                          value={formData.net}
                          onChange={handleChange}
                        >
                          <option value="">Select</option>
                          <option value="yes">Yes</option>
                          <option value="no">No</option>
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>SET</Label>
                        <Input
                          type="select"
                          name="set"
                          value={formData.set}
                          onChange={handleChange}
                        >
                          <option value="">Select</option>
                          <option value="yes">Yes</option>
                          <option value="no">No</option>
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>Mphil</Label>
                        <Input
                          type="select"
                          name="mphil"
                          value={formData.mphil}
                          onChange={handleChange}
                        >
                          <option value="">Select</option>
                          <option value="yes">Yes</option>
                          <option value="no">No</option>
                        </Input>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    {formData.phd === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-phd">
                            PHD Notification Date
                          </Label>
                          <Input
                            type="date"
                            id="input-phd"
                            name="phdNotificationDate"
                            value={formatDateForInput(
                              formData.phdNotificationDate
                            )}
                            onChange={handleChange}
                          />
                          {errors.phdNotificationDate && (
                            <span className="text-danger">
                              {errors.phdNotificationDate}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    )}
                    {formData.net === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-net">
                            NET Notification Date
                          </Label>
                          <Input
                            type="date"
                            id="input-net"
                            name="netNotificationDate"
                            value={formatDateForInput(
                              formData.netNotificationDate
                            )}
                            onChange={handleChange}
                          />
                          {errors.netNotificationDate && (
                            <span className="text-danger">
                              {errors.netNotificationDate}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    )}
                    {formData.set === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-set">
                            SET Notification Date
                          </Label>
                          <Input
                            type="date"
                            id="input-set"
                            name="setNotificationDate"
                            value={formatDateForInput(
                              formData.setNotificationDate
                            )}
                            onChange={handleChange}
                          />
                          {errors.setNotificationDate && (
                            <span className="text-danger">
                              {errors.setNotificationDate}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    )}
                    {formData.mphil === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-mphil">
                            Mphil Notification Date
                          </Label>
                          <Input
                            type="date"
                            id="input-mphil"
                            name="mphilNotificationDate"
                            value={formatDateForInput(
                              formData.mphilNotificationDate
                            )}
                            onChange={handleChange}
                          />
                          {errors.mphilNotificationDate && (
                            <span className="text-danger">
                              {errors.mphilNotificationDate}
                            </span>
                          )}
                        </FormGroup>
                      </Col>
                    )}
                  </Row>
                  <Row>
                  </Row>
                  <Row>
                  </Row>
                  <Button
                    color="primary"
                    type="button"
                    onClick={() => handleUpdateFinalSubmit(guestId)}
                  >
                    Update
                  </Button>
                </Form>
              </CardBody>
            </Card>

            <Modal
              isOpen={previewModal}
              toggle={togglePreviewModal}
              style={{
                maxWidth: "90%",
                width: "40%",
              }}
            >
              <ModalHeader toggle={togglePreviewModal}>
                <h2>Guest Faculty Preview</h2>
              </ModalHeader>
              <ModalBody>
                <Form className="pl-lg-4">
                  <Row>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-subject">
                          OTR Code : {formData.OTRCode}
                        </Label>
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-name">
                          Name : {formData.name}
                        </Label>
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-dob">
                          Date of Birth : {formatDate(formData.dob)}
                        </Label>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-mobile">
                          Mobile No : {formData.mobile}
                        </Label>
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-email">
                          Email ID : {formData.email}
                        </Label>
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-address">
                          Address : {formData.address}
                        </Label>
                      </FormGroup>
                    </Col>
                  </Row>

                  <Row>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-joining-date">
                          Joining Date : {formatDate(formData.joiningDate)}
                        </Label>
                      </FormGroup>
                    </Col>
                    <Col lg="4">
                      <FormGroup>
                        <Label htmlFor="input-subject">
                          Subject :{" "}
                          {subjectData.length > 0 && formData.subject !== ""
                            ? subjectData.find(
                              (a) =>
                                String(a._id) === String(formData.subject)
                            )?.subjectName
                            : ""}
                        </Label>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label>PHD : {formData.phd.toLocaleUpperCase()}</Label>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>NET : {formData.net.toLocaleUpperCase()}</Label>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>SET : {formData.set.toLocaleUpperCase()}</Label>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>
                          Mphil : {formData.mphil.toLocaleUpperCase()}
                        </Label>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    {formData.phd === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-phd">
                            PHD Notification Date :{" "}
                            {formatDate(formData.phdNotificationDate)}
                          </Label>
                        </FormGroup>
                      </Col>
                    )}
                    {formData.net === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-net">
                            NET Notification Date :{" "}
                            {formatDate(formData.netNotificationDate)}
                          </Label>
                        </FormGroup>
                      </Col>
                    )}
                    {formData.set === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-set">
                            SET Notification Date :{" "}
                            {formatDate(formData.setNotificationDate)}
                          </Label>
                        </FormGroup>
                      </Col>
                    )}
                    {formData.mphil === "yes" && (
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-mphil">
                            Mphil Notification Date :{" "}
                            {formatDate(formData.setNotificationDate)}
                          </Label>
                        </FormGroup>
                      </Col>
                    )}
                  </Row>
                  <Row>
                    <Col lg="12">
                      <FormGroup>
                        <Label>
                          Experience (as on 30th June of previous year):{" "}
                          {formData.experienceYears}
                        </Label>
                        <Row>
                          <Col lg="4">
                            <Label>Year : {formData.experienceYears}</Label>
                          </Col>
                          <Col lg="4">
                            <Label>Months : {formData.experienceMonths}</Label>
                          </Col>
                          <Col lg="4">
                            <Label>Days : {formData.experienceDays}</Label>
                          </Col>
                        </Row>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="12">
                      <FormGroup>
                        <Label htmlFor="input-research-papers">
                          No. of Research Papers Published :{" "}
                          {formData.researchPapers}
                        </Label>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Button
                    color="success"
                    className="ml-2"
                    type="button"
                    disabled={!isDraftSaved}
                    onClick={() => finalSubmit(guestFacultyId)} // Final Submit
                  >
                    Final Submit
                  </Button>
                </Form>
              </ModalBody>
            </Modal>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateGuestFacultyForm;
