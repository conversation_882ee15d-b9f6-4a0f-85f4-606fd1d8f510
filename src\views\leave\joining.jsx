import { useState, useEffect, useMemo } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ooter,
    Col,
    Container,
    Row,
    Table,
    Badge,
    Input,
    Pagination,
    PaginationItem,
    PaginationLink,
    Modal, ModalHeader, ModalBody, ModalFooter,

} from "reactstrap";
import { BsDownload, BsEye, } from 'react-icons/bs';
import Header from "../../components/Headers/Header.jsx";
import { Spinner } from "reactstrap";
import axios from "axios";
import { Link } from 'react-router-dom';
import Swal from "sweetalert2";
import formatDate  from "../../utils/formateDate.jsx";

const JoiningList = () => {



    const empId = sessionStorage.getItem('id')
    const token = sessionStorage.getItem('authToken');
    const endPoint = import.meta.env.VITE_API_URL;



    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 4;
    const [applications, setApplications] = useState([]);
    const [applicationsJoined, setApplicationsJoined] = useState([]);
    const [searchTerm, setSearchTerm] = useState('');

    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = applications.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(applications.length / itemsPerPage);

    useEffect(() => {
        const fetchApplications = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/leave/applied_Leaves/${empId}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
                if (response.status === 200) {
                    const DataForFilter = response.data.reverse();
                    const PendingForJoining = DataForFilter.filter(leaves => (
                        (leaves.leaveStatus === 3 && leaves.isJoined === 0) &&
                        (leaves.leaveType === 'Earned Leave' || leaves.leaveType === 'Medical Leave' || leaves.leaveType === 'Half Pay Leave')
                    ));
                    const joined = DataForFilter.filter(leaves => (
                        (leaves.leaveStatus === 3 && leaves.isJoined === 1) &&
                        (leaves.leaveType === 'Earned Leave' || leaves.leaveType === 'Medical Leave' || leaves.leaveType === 'Half Pay Leave')
                    ));
                    setApplications(PendingForJoining);
                    setApplicationsJoined(joined);
                } else {
                    alert("Data Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchApplications();
    }, [endPoint, empId, token]);

    // Filter applications based on search term
    const filteredApplications = useMemo(() => {
        return applicationsJoined.filter(application => {
            return (
                application.applicantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                application.applicationId.toString().includes(searchTerm) // Assuming applicationId is a number
            );
        });
    }, [applications, searchTerm]);



    const handlePageChange = (pageNumber) => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber);
        }
    };

    const [selectedStatus, setSelectedStatus] = useState(null);
    const [modal, setModal] = useState(false);
    const [currentStep, setCurrentStep] = useState(0); // Track the current step


    const toggle = (status) => {
        setModal(!modal);
        setSelectedStatus(status);
    };

    const statusCode = selectedStatus;
    useEffect(() => {
        if (statusCode >= 1 && statusCode <= 3) {
            setCurrentStep(statusCode - 1); // Convert status code to zero-based index
        }
    }, [statusCode]);

    const steps = [
        { label: "Employee", icon: "fa-user" },
        { label: "Office", icon: "fa-solid fa-briefcase" },
        { label: "Directorate", icon: "fa-solid fa-user-shield" }
    ];

    const handleShowDetails = (remark1, remark2, remark3) => {
        // console.log("Getting Users Remark,", remark1);
        // console.log("Getting Principle Remark,", remark2);
        // console.log("Getting Director Remark,", remark3);

        Swal.fire({
            html: `<div style="text-align: left; max-height: 300px; overflow-y: auto;"><h3><b>Remark By User</h3><p> ${remark1}<p>
           </br> <h3><b>Remark By Principal</b></h3><p> ${remark2}<p>
           </br> <h3><b>Remark By ByDirectorate</b></h3><p> ${remark3}<p></div>`,
            showCloseButton: true,
            // showCancelButton: false,
            confirmButtonText: 'Close',
        });
    };

   const handleJoin = async (id) => {
    
    
    Swal.fire({
        title: 'Are you sure?',
        text: "You won't be able to revert this!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'Yes, Join',
        cancelButtonText: 'Not Now!',
        
    }).then(async (result) => {
        if (result.isConfirmed) {
            
            try {
                const response = await axios.put(`${endPoint}/api/leave/apply_joining/${id}`, { isJoined: "1" }, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });

                if (response.status === 200) {
                    Swal.fire('Applied for Join!', 'Your request has been sent with your reason.', 'success');
                    setTimeout(function() {
                        window.location.reload();
                    }, 3000);
                }
            } catch (error) {
                Swal.fire('Error!', 'There was an error updating your status.', 'error');
            }
        }
    });
};

    // #endregion

    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Row>
                    <div className="col">
                        <Card className="shadow mb-5" >
                            <CardHeader className="border-0">
                                <h3 className="mb-0">Pending For Joining / ज्वाइनिंग हेतु लंबित</h3>
                            </CardHeader>

                            <Table className="align-items-center table-flush" responsive>
                                <thead className="thead-light">
                                    <tr>
                                        <th>Sno./ क्र.</th>
                                        <th>Applicant Name.<br /> (आवेदक का नाम )</th>
                                        <th>Application ID <br /> (आवेदन क्रमांक)</th>
                                        <th>Applied Date <br /> (आवेदन दिनांक)</th>
                                        <th>Leave Type <br /> (अवकाश का प्रकार)</th>
                                        <th>Reason <br /> (कारण)</th>
                                        <th>From Date <br /> (दिनांक से)</th>
                                        <th>Till Date <br /> (दिनांक तक)</th>
                                        <th>Total Days <br /> (अवकाश (कुल दिन))</th>
                                        <th>Status <br /> (अवकाश की स्थिति)</th>
                                        <th>Join <br /> (ज्वाईन)</th>
                                        <th>Remark <br /> (टिप्पणी)</th>
                                        <th>Application Copy <br /> (आवेदन की प्रति)</th>
                                        <th>Uploaded Document <br /> (अपलोडेड दस्तावेज)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {applications.map((application, index) => (
                                        <tr key={application.id}>
                                            <td>{index + 1}</td>
                                            <td>{application.applicantName}</td>
                                            <td>{application.applicationId}</td>
                                            <td>{formatDate(application.appliedDate)}</td>
                                            <td>{application.leaveType === 'Restricted Holiday'? 'Optional Holiday' : application.leaveType}</td>
                                            <td>{application.reason}</td>
                                            <td>{formatDate(application.fromDate)}</td>
                                            <td>{formatDate(application.tillDate)}</td>
                                            <td>{application.dayCount}</td>
                                            <td>

                                                <h2>
                                                    <Badge

                                                        className="badge-sm"
                                                        onClick={application.leaveStatus === 1 || application.leaveStatus === 2 ? () => toggle(application.leaveStatus) : null}
                                                        style={{
                                                            fontWeight: 'bolder',
                                                            color: 'white',
                                                            backgroundColor:
                                                                application.leaveStatus === 3
                                                                    ? 'green'  // Approved background color
                                                                    : application.leaveStatus === 4
                                                                        ? 'red'   // Rejected background color
                                                                        : (application.leaveStatus === 1)
                                                                            ? '#f3c70c'  // Pending background color

                                                                            : application.leaveStatus === 5
                                                                                ? 'blue'
                                                                                : (application.leaveStatus === 2)
                                                                                    ? '#ff8a00'
                                                                                    : 'lavender'
                                                        }}
                                                    >
                                                        {application.leaveStatus === 3
                                                            ? 'Approved'
                                                            : application.leaveStatus === 4
                                                                ? 'Rejected'
                                                                : (application.leaveStatus === 1)
                                                                    ? (
                                                                        <>
                                                                            <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                            Pending
                                                                        </>
                                                                    )
                                                                    : (application.leaveStatus === 2)
                                                                        ? (
                                                                            <>
                                                                                {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                Forwarded
                                                                            </>
                                                                        )
                                                                        : application.leaveStatus === 5
                                                                            ? 'Cancelled'
                                                                            : 'Error'}
                                                    </Badge>

                                                </h2>

                                            </td>
                                            <td>
                                                <Button
                                                    style={{ width: "100%", backgroundColor: "#ff8a00", color: "white" }}
                                                  onClick={() => handleJoin(application._id)}
                                                >Join
                                                </Button>
                                            </td>
                                            <td>
                                                <button
                                                    className="btn btn-primary"
                                                    onClick={() => handleShowDetails(application.remarkByUser, application.remarkByPrincipal, application.remarkByDirector)}>
                                                    Remark
                                                </button>
                                            </td>

                                            <td>
                                                <Link to={`/admin/Preview/${application._id}`}>
                                                    <Button
                                                        style={{ backgroundColor: "yellow" }}
                                                    ><BsDownload size={18} /><BsEye size={18} /></Button>
                                                </Link>
                                            </td>
                                            <td>
                                            <a href={`https://heonline.cg.nic.in/${application.uploadFile}`} download>
                                                    <Button className="btn btn-primary">

                                                        <BsDownload size={18} />
                                                    </Button>
                                                </a>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <Modal isOpen={modal} toggle={toggle}>
                                <ModalHeader toggle={toggle}>Status</ModalHeader>
                                <ModalBody>
                                    <div className="main">
                                        <ul style={{ display: 'flex', justifyContent: 'space-around' }}>
                                            {steps.map((step, index) => (
                                                <li key={index} style={{ listStyle: 'none', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                    <i className={`icons awesome fa-solid ${step.icon}`} style={{ fontSize: '25px', color: '#1b761b' }}></i>
                                                    <div
                                                        className={`step ${index === currentStep ? 'active' : ''}`}
                                                        style={{
                                                            height: '30px',
                                                            width: '30px',
                                                            borderRadius: '50%',
                                                            backgroundColor: index <= currentStep ? '#1b761b' : '#d7d7c3',
                                                            margin: '16px 0 10px',
                                                            display: 'grid',
                                                            placeItems: 'center',
                                                            color: 'ghostwhite',
                                                            position: 'relative',
                                                            cursor: 'default' // Change cursor to default since clicking is disabled
                                                        }}
                                                    >
                                                        <p style={{ fontSize: '18px', display: index <= currentStep ? 'none' : 'block' }}>{index + 1}</p>
                                                        <i className="awesome fa-solid fa-check" style={{ display: index <= currentStep ? 'flex' : 'none' }}></i>
                                                    </div>
                                                    <p className="label" style={{ fontFamily: 'sans-serif', letterSpacing: '1px', fontSize: '14px', fontWeight: 'bold', color: '#1b761b' }}>{step.label}</p>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggle}>Close</Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <CardFooter className="py-4">

                            </CardFooter>
                        </Card>
                    </div>
                </Row>
                <Row>
                    <div className="col">
                        <Card className="shadow">
                            <CardHeader className="border-0">
                                <h3 className="mb-0">Previous Joined / ज्वाइनिंग किया गया </h3>
                                <Row className="mt-3">
                                    <Col md={3}>
                                        <Input
                                            type="text"
                                            placeholder="Search by Applicant Name or Application ID..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            style={{ marginBottom: '10px' }}
                                        />
                                    </Col>
                                </Row>
                            </CardHeader>

                            <Table className="align-items-center table-flush" responsive>
                                <thead className="thead-light">
                                    <tr>
                                        <th>Sno./ क्र.</th>
                                        <th>Applicant Name.<br /> (आवेदक का नाम )</th>
                                        <th>Application ID <br /> (आवेदन क्रमांक)</th>
                                        <th>Applied Date <br /> (आवेदन दिनांक)</th>
                                        <th>Leave Type <br /> (अवकाश का प्रकार)</th>
                                        <th>Reason <br /> (कारण)</th>
                                        <th>From Date <br /> (दिनांक से)</th>
                                        <th>Till Date <br /> (दिनांक तक)</th>
                                        <th>Total Days <br /> (अवकाश (कुल दिन))</th>
                                        <th>Status <br /> (अवकाश की स्थिति)</th>
                                        <th>Join Status<br /> (ज्वाईन स्थिति)</th>
                                        <th>Joining Date<br /> (ज्वाईन तिथि)</th>
                                        <th>Remark <br /> (टिप्पणी)</th>
                                        <th>Application Copy <br /> (आवेदन की प्रति)</th>
                                        <th>Uploaded Document <br /> (अपलोडेड दस्तावेज)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {filteredApplications.map((application, index) => (
                                        <tr key={application.id}>
                                            <td>{index + 1}</td>
                                            
                                            <td>{application.applicantName}</td>
                                            <td>{application.applicationId}</td>
                                            <td>{formatDate(application.appliedDate)}</td>
                                            <td>{application.leaveType === 'Restricted Holiday'? 'Optional Holiday' : application.leaveType}</td>
                                            <td>{application.reason}</td>
                                            <td>{formatDate(application.fromDate)}</td>
                                            <td>{formatDate(application.tillDate)}</td>
                                            <td>{application.dayCount}</td>
                                            <td>

                                                <h2>
                                                    <Badge

                                                        className="badge-sm"
                                                        onClick={application.leaveStatus === 1 || application.leaveStatus === 2 ? () => toggle(application.leaveStatus) : null}
                                                        style={{
                                                            fontWeight: 'bolder',
                                                            color: 'white',
                                                            backgroundColor:
                                                                application.leaveStatus === 3
                                                                    ? 'green'  // Approved background color
                                                                    : application.leaveStatus === 4
                                                                        ? 'red'   // Rejected background color
                                                                        : (application.leaveStatus === 1)
                                                                            ? '#f3c70c'  // Pending background color

                                                                            : application.leaveStatus === 5
                                                                                ? 'blue'
                                                                                : (application.leaveStatus === 2)
                                                                                    ? '#ff8a00'
                                                                                    : 'lavender'
                                                        }}
                                                    >
                                                        {application.leaveStatus === 3
                                                            ? 'Approved'
                                                            : application.leaveStatus === 4
                                                                ? 'Rejected'
                                                                : (application.leaveStatus === 1)
                                                                    ? (
                                                                        <>
                                                                            <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                            Pending
                                                                        </>
                                                                    )
                                                                    : (application.leaveStatus === 2)
                                                                        ? (
                                                                            <>
                                                                                {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                Forwarded
                                                                            </>
                                                                        )
                                                                        : application.leaveStatus === 5
                                                                            ? 'Cancelled'
                                                                            : 'Error'}
                                                    </Badge>

                                                </h2>

                                            </td>
                                            <td>
                                            <Badge style={{ fontWeight: 'bolder',
                                                            color: 'white',
                                                            backgroundColor:'blue'}}><span style={{fontSize:"14px",fontWeight:"bolder"}}>Joined</span></Badge>
                                            </td>
                                            <td>{formatDate(application.joiningDate)}</td>
                                            <td>
                                                <button
                                                    className="btn btn-primary"
                                                    onClick={() => handleShowDetails(application.remarkByUser, application.remarkByPrincipal, application.remarkByDirector)}>
                                                    Remark
                                                </button>
                                            </td>

                                            <td>
                                                <Link to={`/admin/Preview/${application._id}`}>
                                                    <Button
                                                        style={{ backgroundColor: "yellow" }}
                                                    ><BsDownload size={18} /><BsEye size={18} /></Button>
                                                </Link>
                                            </td>
                                            <td>
                                            <a href={`https://heonline.cg.nic.in/${application.uploadFile}`} download>
                                                    <Button className="btn btn-primary">

                                                        {application.uploadFile? <BsDownload size={18} /> :"No File"}
                                                    </Button>
                                                </a>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <Modal isOpen={modal} toggle={toggle}>
                                <ModalHeader toggle={toggle}>Status</ModalHeader>
                                <ModalBody>
                                    <div className="main">
                                        <ul style={{ display: 'flex', justifyContent: 'space-around' }}>
                                            {steps.map((step, index) => (
                                                <li key={index} style={{ listStyle: 'none', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                    <i className={`icons awesome fa-solid ${step.icon}`} style={{ fontSize: '25px', color: '#1b761b' }}></i>
                                                    <div
                                                        className={`step ${index === currentStep ? 'active' : ''}`}
                                                        style={{
                                                            height: '30px',
                                                            width: '30px',
                                                            borderRadius: '50%',
                                                            backgroundColor: index <= currentStep ? '#1b761b' : '#d7d7c3',
                                                            margin: '16px 0 10px',
                                                            display: 'grid',
                                                            placeItems: 'center',
                                                            color: 'ghostwhite',
                                                            position: 'relative',
                                                            cursor: 'default' // Change cursor to default since clicking is disabled
                                                        }}
                                                    >
                                                        <p style={{ fontSize: '18px', display: index <= currentStep ? 'none' : 'block' }}>{index + 1}</p>
                                                        <i className="awesome fa-solid fa-check" style={{ display: index <= currentStep ? 'flex' : 'none' }}></i>
                                                    </div>
                                                    <p className="label" style={{ fontFamily: 'sans-serif', letterSpacing: '1px', fontSize: '14px', fontWeight: 'bold', color: '#1b761b' }}>{step.label}</p>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggle}>Close</Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <CardFooter className="py-4">
                                <nav aria-label="...">
                                    <Pagination className="pagination justify-content-end mb-0">
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem key={i} className={currentPage === i + 1 ? 'active' : ''}>
                                                <PaginationLink
                                                    href="#"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(i + 1);
                                                    }}
                                                >
                                                    {i + 1}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                    </Pagination>
                                </nav>
                            </CardFooter>
                        </Card>
                    </div>
                </Row>
            </Container>
        </>
    );
};

export default JoiningList;