import { useNavigate } from "react-router-dom";
import { useState } from "react";
import '../../assets/css/Login.css';
import { FaUser } from 'react-icons/fa';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Toast,
  Row,
  ToastBody, ToastHeader,
  Col,
  Container
} from "reactstrap";
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
const ChangePassword = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const userType = sessionStorage.getItem("type");
  const users = Number(userType) === 1 ? "StateAdmin" : Number(userType) === 3 ? "College" : Number(userType) === 4 ? "Employee" : Number(userType) === 5 ? "Director" : "";
  // console.log(users, "USERTYPE");

  // console.log(userType, "USERTYPE");



  // // console.log(token);
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    newPassword: '',
    confirmPassword: '',
  });
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState(''); // 'success' or 'error'
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    const { newPassword, confirmPassword } = formData;
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    if (!passwordRegex.test(newPassword)) {
      alert(
        "Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character."
      );
      return;
    }

    if (newPassword !== confirmPassword) {
      SwalMessageAlert("New Password and Confirm Password Not Matched.", "error");
      return;
    }
    SwalMessageAlert("Password is valid!", "success");
    try {
      const body = {
        password: formData.newPassword,
      };
      const response = await axios.put(
        `${endPoint}/api/update-password?id=${id}&key=${users}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.reload();
      } else {
        SwalMessageAlert("Password Update Failed", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert("Password Update Failed.", "error");
    }
  };
  return (
    <>
       <Header />
      {/* Page content */}
      <Container className="mt--7" fluid>
      <Row>
      <Col md="4">
        <Card className="bg-secondary shadow border-0">
          <CardHeader className="bg-transparent pb-5">
            <Container>
              <div className="header-body text-center">
                <Row className="justify-content-center">
                  <Col lg="12" md="0">
                    <p className="text-primary font-weight-bold">Change Password</p>
                  </Col>
                </Row>
              </div>
            </Container>
          </CardHeader>
          <CardBody className="px-lg-5 py-lg-5">
            <Form role="form" >
              <FormGroup className="mb-3">
                <InputGroup className="input-group-alternative">
                  <InputGroupAddon addonType="prepend">
                    <InputGroupText>
                      <i className="ni ni-lock-circle-open" />
                    </InputGroupText>
                  </InputGroupAddon>
                  <Input
                    name="newPassword"
                    placeholder="New Password"
                    type="password"
                    value={formData.newPassword}
                    style={{ color: '#404446' }}
                     autoComplete="off"
                    onChange={handleInputChange}
                  />
                </InputGroup>
              </FormGroup>
              <FormGroup className="mb-3">
                <InputGroup className="input-group-alternative">
                  <InputGroupAddon addonType="prepend">
                    <InputGroupText>
                      <i className="ni ni-lock-circle-open" />
                    </InputGroupText>
                  </InputGroupAddon>
                  <Input
                    name="confirmPassword"
                    placeholder="Confirm New Password"
                    type="password"
                    value={formData.confirmPassword}
                    style={{ color: '#404446' }}
                     autoComplete="off"
                    onChange={handleInputChange}
                  />
                </InputGroup>
              </FormGroup>
              <Row>
                <Col>
                  {toastVisible && (
                    <Toast>
                      <ToastHeader toggle={() => setToastVisible(false)}>
                      </ToastHeader>
                      <ToastBody style={{ color: toastType === 'error' ? 'red' : 'green', fontSize: "12px" }}>
                        {toastMessage}
                      </ToastBody>
                    </Toast>
                  )}
                </Col>
              </Row>
              <div className="text-center">
                <Button className="my-4" color="primary" onClick={handleSubmit}>
                  Change Password
                </Button>
              </div>
            </Form>
          </CardBody>
        </Card>
      </Col>
      </Row>
      </Container>
    </>
  );
};
export default ChangePassword;