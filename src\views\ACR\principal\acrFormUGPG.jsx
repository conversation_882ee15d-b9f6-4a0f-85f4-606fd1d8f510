import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Container,
  Row,
  FormGroup,
  Col,
  Label,
  Button,
  Input,
} from "reactstrap";
import Swal from "sweetalert2";
import axios from "axios";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";

const ACRFormUGPG = ({ employee }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);



  const [books, setBooks] = useState([
    { name: "", writer: "", publisher: "" },
    { name: "", writer: "", publisher: "" },
    { name: "", writer: "", publisher: "" },
  ]);


  const [formData, setFormData] = useState({
    samaptAvadhi: "",
    employeeId: employee._id,
    employeeName: employee.name,
    dateofBirth: "",
    dharitPad: employee.designationDetails.designation,
    collegeName: employee.collegeDetails.name,
    collegeId: employee.collegeDetails._id,
    currentSalary: employee.currentSalary,
    wartmanSansthaWorkingDate: "",
    varshikSampattiSubmitDate: "",
    rajpatritStaff: "",
    arajpatritStaff: "",
    sekshnikStaffKeLiye: "",
    sansthaAdhyapanKaryaDiwas: "",
    gairsekshnikArajpatrit: "",
    gairsekshnikStaffKeLiye: "",
    varshikGopniyaMulyankanYears: "",
    pradhyapakSahayakAttendance: "",
    viseshKamjorSamanyaShreni: "",
    viseshKamjorAnusuchitJati: "",
    viseshKamjorAnusuchitJanJati: "",
    viseshKamjorPichadiJati: "",
    viseshKamjorAlpShankhyak: "",
    AnudanRashiUpyogKiGai: "",
    upYogitRashiPratishat: "",
    janBhagidariBaithakoKiSankhya: "",
    janBhagidariekatritRashi: "",
    janBhagiDariUpyogKiGaiRashi: "",
    auditApptiyonKiSankhya: "",
    kitnePensionPrakarBhejeGai: "",
    anirnitPensionPrakaran: "",
    purvPrakaran: "",
    sansthitPrakran: "",
    karyaWahiPrakaran: "",
    sheshPrakaran: "",
    ugperiods: "",
    pgperiods: "",
    studentNougStarPar: "",
    pgperistudentNoPgStarParods: "",
    ugFirstExamResult: "",
    ugSecondExamResult: "",
    ugThirdExamResult: "",
    pgFirstHalfExamResult: "",
    pgSecondHalfExamResult: "",
    visisthUplabdhi: "",
    mahatvapurnaSaskiyaSevaChayan: "",
    books: books,
  });




  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
  };


  const handleChange = (index, field, value) => {
    const newBooks = [...books];
    newBooks[index][field] = value;
    setBooks(newBooks);
  };





  const handleFinalSubmit = async (e) => {
    // Validation function to check for required fields

    const validateForm = () => {
      const newErrors = {};
      const requiredFields = {
        samaptAvadhi: "",
        employeeId: employee._id,
        employeeName: employee.name,
        dateofBirth: "",
        dharitPad: employee.designationDetails.designation,
        collegeName: employee.collegeDetails.name,
        collegeId: employee.collegeDetails._id,
        currentSalary: employee.currentSalary,
        wartmanSansthaWorkingDate: "",
        varshikSampattiSubmitDate: "",
        rajpatritStaff: "",
        arajpatritStaff: "",
        sekshnikStaffKeLiye: "",
        sansthaAdhyapanKaryaDiwas: "",
        gairsekshnikArajpatrit: "",
        gairsekshnikStaffKeLiye: "",
        varshikGopniyaMulyankanYears: "",
        pradhyapakSahayakAttendance: "",
        viseshKamjorSamanyaShreni: "",
        viseshKamjorAnusuchitJati: "",
        viseshKamjorAnusuchitJanJati: "",
        viseshKamjorPichadiJati: "",
        viseshKamjorAlpShankhyak: "",
        AnudanRashiUpyogKiGai: "",
        upYogitRashiPratishat: "",
        janBhagidariBaithakoKiSankhya: "",
        janBhagidariekatritRashi: "",
        janBhagiDariUpyogKiGaiRashi: "",
        auditApptiyonKiSankhya: "",
        kitnePensionPrakarBhejeGai: "",
        anirnitPensionPrakaran: "",
        purvPrakaran: "",
        sansthitPrakran: "",
        karyaWahiPrakaran: "",
        sheshPrakaran: "",
        ugperiods: "",
        pgperiods: "",
        studentNougStarPar: "",
        pgperistudentNoPgStarParods: "",
        ugFirstExamResult: "",
        ugSecondExamResult: "",
        ugThirdExamResult: "",
        pgFirstHalfExamResult: "",
        pgSecondHalfExamResult: "",
        visisthUplabdhi: "",
        mahatvapurnaSaskiyaSevaChayan: ""
      };

      // Iterate over each field in the requiredFields object
      for (const field in requiredFields) {
        if (requiredFields.hasOwnProperty(field)) {
          // Check if the field is empty or not
          if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
            newErrors[field] = "This Field is Required.";
          }
        }
      }
      return newErrors;
    };



    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("स्व-मतांकन सफल हुआ", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const currentDate = new Date();
  const currentToday = currentDate.toISOString().split('T')[0];

  const minDate = new Date();
  minDate.setFullYear(currentDate.getFullYear() - 18);

  // Calculate the maximum date (65 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(currentDate.getFullYear() - 65);

  // Format dates to YYYY-MM-DD for the input
  const formattedMinDate = minDate.toISOString().split('T')[0];
  const formattedMaxDate = maxDate.toISOString().split('T')[0];


  return (
    <>
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="shadow pb-4">
              <CardHeader
                className="bg-white border-0"
                style={{ textAlign: "center" }}
              >
                <h2 className="mb-0">
                  छत्तीसगढ़ शासन, उच्च शिक्षा विभाग, मंत्रालय, रायपुर <br />{" "}
                  वार्षिक गोपनीय मूल्यांकन पत्रक (महाविद्यालयीन प्राचार्य के
                  लिए)
                </h2>
              </CardHeader>
              <CardBody>
                <div className="mb-4">
                  <Row>
                    <Col>
                      <h3>
                        <u>क्रमांक 1 से 5 तक कार्यालय द्वारा भरा जावेगा</u>
                      </h3>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Row className="mb-3 mt-4">
                        <Col md="3" className="mt--4">
                          <Label>
                            {" "}
                            <strong>1.</strong> वर्ष {year} के 31 मार्च <br /> को
                            समाप्त अवधि के लिए
                          </Label>
                          <Input
                            type="text"
                            className="form-control"
                            name="samaptAvadhi"
                            value={formData.samaptAvadhi}
                            onChange={handleInputChange}
                          />
                          {errors.samaptAvadhi && (
                            <small className="text-danger">
                              {errors.samaptAvadhi}
                            </small>
                          )}
                        </Col>
                        <Col md="3">
                          <Label>
                            <strong>2.</strong> पूरा नाम
                          </Label>
                          <Input
                            type="text"
                            name="employeeName"
                            value={formData.employeeName}
                            readOnly
                            className="form-control"
                          />
                          {errors.employeeName && (
                            <small className="text-danger">
                              {errors.employeeName}
                            </small>
                          )}
                        </Col>
                        <Col md="3">
                          <Label>
                            <strong>3.</strong> जन्म तिथि
                          </Label>
                          <Input
                            type="date"
                            name="dateofBirth"
                            value={formData.dateofBirth}
                            className="form-control"
                            min={formattedMaxDate} // Set the minimum date to 65 years ago
                            max={formattedMinDate}
                            onChange={handleInputChange}

                          />
                          {errors.dateofBirth && (
                            <small className="text-danger">
                              {errors.dateofBirth}
                            </small>
                          )}
                        </Col>
                        <Col md="3">
                          <Label>
                            <strong>4.</strong> प्रतिवेदित अवधि में धारित पद :
                          </Label>
                          <Input
                            type="text"
                            name="designation"
                            readOnly
                            value={formData.dharitPad}
                            className="form-control"
                          />
                        </Col>
                      </Row>
                      <Row className="mb-3 mt-5">
                        <Col md="3">
                          <Label>
                            <strong>5.(a)</strong> संस्था का नाम :
                          </Label>
                          <Input
                            type="text"
                            name="collegeName"
                            value={formData.collegeName} // Use employee data here
                            readOnly
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.collegeName && (
                            <small className="text-danger">
                              {errors.collegeName}
                            </small>
                          )}
                        </Col>
                        <Col md="3" className="mt--4">
                          <Label>
                            <strong>5.(b)</strong> वर्तमान संस्था / महाविद्यालय
                            में कब से कार्यरत है
                          </Label>
                          <Input
                            type="date"
                            className="form-control"
                            name='wartmanSansthaWorkingDate'
                            max={currentToday}
                            value={formData.wartmanSansthaWorkingDate}
                            onChange={handleInputChange}

                          />
                          {errors.wartmanSansthaWorkingDate && (
                            <small className="text-danger">
                              {errors.wartmanSansthaWorkingDate}
                            </small>
                          )}
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <hr />
                  <Row className="mt-3 mb-3">
                    <Col>
                      <h3>
                        <u>
                          {" "}
                          क्रमांक 6 से 22 तक प्रतिवेदित प्राधिकारी / प्राचार्य
                          द्वारा भरा जायेगा
                        </u>
                      </h3>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Row className="mb-3">
                        <Col md="3">
                          <Label>
                            <strong> 6.</strong> वार्षिक संपत्ति प्रतिवेदन
                            प्रस्तुत करने का दिनांक :
                          </Label>
                          <Input
                            type="date"
                            className="form-control"
                            name="varshikSampattiSubmitDate"
                            max={currentToday}
                            value={formData.varshikSampattiSubmitDate}
                            onChange={handleInputChange}
                          // readOnly
                          />
                          {errors.varshikSampattiSubmitDate && (
                            <small className="text-danger">
                              {errors.varshikSampattiSubmitDate}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>7.</strong> प्राचार्य के अधीन कार्यरत
                                शैक्षणिक एवं अशैक्षणिक स्टॉप की संख्या
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                {" "}
                                (एक). राजपत्रित :
                              </Label>
                              <Input
                                type="number"
                                className="form-control"
                                name="rajpatritStaff"
                                value={formData.rajpatritStaff}
                                onChange={handleInputChange}
                              />
                              {errors.rajpatritStaff && (
                                <small className="text-danger">
                                  {errors.rajpatritStaff}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो ). अराजपत्रित :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="arajpatritStaff"
                                value={formData.arajpatritStaff}
                                onChange={handleInputChange} />
                              {errors.arajpatritStaff && (
                                <small className="text-danger">
                                  {errors.arajpatritStaff}
                                </small>
                              )}

                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>8.</strong> प्राचार्य द्वारा प्रतिवेदक
                                अधिकारी के रूप में लिखे जाने वाले वार्षिक गोपनीय
                                मूल्यांकन पत्रक कितने शैक्षणिक अधिकारियों एवं
                                गैर शैक्षणिक कर्मचारियों के संबंध में लिखे जाकर
                                समीक्षक प्राधिकारी को भेजे जा चुके हैं।
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) शैक्षणिक स्टॉफ के लिए :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="sekshnikStaffKeLiye"
                                value={formData.sekshnikStaffKeLiye}
                                onChange={handleInputChange} />
                              {errors.sekshnikStaffKeLiye && (
                                <small className="text-danger">
                                  {errors.sekshnikStaffKeLiye}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) गैर शैक्षणिक राजपत्रित अधिकारियों के लिए :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="gairsekshnikStaffKeLiye"
                                value={formData.gairsekshnikStaffKeLiye}
                                onChange={handleInputChange} />
                              {errors.gairsekshnikStaffKeLiye && (
                                <small className="text-danger">
                                  {errors.gairsekshnikStaffKeLiye}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (तीन) गैर शैक्षणिक अराजपत्रित स्टॉफ के लिए :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="gairsekshnikArajpatrit"
                                value={formData.gairsekshnikArajpatrit}
                                onChange={handleInputChange} />
                              {errors.gairsekshnikArajpatrit && (
                                <small className="text-danger">
                                  {errors.gairsekshnikArajpatrit}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col md="4">
                              <Label>
                                {" "}
                                <strong>9. </strong> वार्षिक गोपनीय मूल्यांकन
                                पत्रक के मास्टर रजिस्टर में किस-किस वर्ष की
                                प्रविष्टियों की जा चुकी हैं :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="varshikGopniyaMulyankanYears"
                                value={formData.varshikGopniyaMulyankanYears}
                                onChange={handleInputChange} />
                              {errors.varshikGopniyaMulyankanYears && (
                                <small className="text-danger">
                                  {errors.varshikGopniyaMulyankanYears}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label>
                                {" "}
                                <strong>10. </strong> क्या प्राध्यापकों व सहायक
                                प्राध्यापकों से उपस्थिति रजिस्टर पूर्ण करवाकर
                                जमा कर लिये गये हैं:
                              </Label>
                              <Input
                                type="select" // Change the type to "select"
                                name="pradhyapakSahayakAttendance"
                                value={formData.pradhyapakSahayakAttendance}
                                className="form-control"
                                onChange={handleInputChange}
                              >
                                <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                                <option value="हाँ">हाँ</option>
                                <option value="नहीं">नहीं</option>
                              </Input>

                              {errors.pradhyapakSahayakAttendance && (
                                <small className="text-danger">
                                  {errors.pradhyapakSahayakAttendance}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label>
                                {" "}
                                <strong>11. </strong>संस्था में अध्यापन कार्य
                                दिवस कितनी रही :
                              </Label>
                              <Input
                                type="number"
                                className="form-control mt-4"
                                name="sansthaAdhyapanKaryaDiwas"
                                max={9999}
                                value={formData.sansthaAdhyapanKaryaDiwas}
                                onChange={handleInputChange}
                              />
                              {errors.sansthaAdhyapanKaryaDiwas && (
                                <small className="text-danger">
                                  {errors.sansthaAdhyapanKaryaDiwas}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>12.</strong> वर्ष में कितने छात्रों को
                                विशेष कमजोर चिन्हित कर उनको विशेष कोचिंग दी गई :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) सामान्य श्रेणी :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="viseshKamjorSamanyaShreni"
                                value={formData.viseshKamjorSamanyaShreni}
                                onChange={handleInputChange} />
                              {errors.viseshKamjorSamanyaShreni && (
                                <small className="text-danger">
                                  {errors.viseshKamjorSamanyaShreni}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) अनुसूचित जाति श्रेणी :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="viseshKamjorAnusuchitJati"
                                value={formData.viseshKamjorAnusuchitJati}
                                onChange={handleInputChange} />
                              {errors.viseshKamjorAnusuchitJati && (
                                <small className="text-danger">
                                  {errors.viseshKamjorAnusuchitJati}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (तीन) अनुसूचित जनजाति श्रेणी :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="viseshKamjorAnusuchitJanJati"
                                value={formData.viseshKamjorAnusuchitJanJati}
                                onChange={handleInputChange} />
                              {errors.viseshKamjorAnusuchitJanJati && (
                                <small className="text-danger">
                                  {errors.viseshKamjorAnusuchitJanJati}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (चार) पिछडी जाति श्रेणी :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="viseshKamjorPichadiJati"
                                value={formData.viseshKamjorPichadiJati}
                                onChange={handleInputChange} />
                            </Col>
                            {errors.viseshKamjorPichadiJati && (
                              <small className="text-danger">
                                {errors.viseshKamjorPichadiJati}
                              </small>
                            )}
                          </Row>
                          <Row className="mt-2">
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (पांच) अल्प संख्यक श्रेणी :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="viseshKamjorAlpShankhyak"
                                value={formData.viseshKamjorAlpShankhyak}
                                onChange={handleInputChange} />
                              {errors.viseshKamjorAlpShankhyak && (
                                <small className="text-danger">
                                  {errors.viseshKamjorAlpShankhyak}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>13.</strong> संस्था को वर्ष के दौरान
                                विश्वविद्यालय अनुदान आयोग से प्राप्त सहायता में
                                कितनी राशि उपयोग की गई :{" "}
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label>(एक) राशि उपयोग की गई :</Label>
                              <Input type="text"
                                className="form-control"
                                name="AnudanRashiUpyogKiGai"
                                value={formData.AnudanRashiUpyogKiGai}
                                onChange={handleInputChange} />
                              {errors.AnudanRashiUpyogKiGai && (
                                <small className="text-danger">
                                  {errors.AnudanRashiUpyogKiGai}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>
                                (दो) कुल उपयोग की गई राशि का प्रतिशत :
                              </Label>
                              <Input type="text"
                                name="upYogitRashiPratishat"
                                className="form-control"
                                value={formData.upYogitRashiPratishat}
                                onChange={handleInputChange} />
                              {errors.upYogitRashiPratishat && (
                                <small className="text-danger">
                                  {errors.upYogitRashiPratishat}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>14.</strong> संस्था का जनभागीदारी समिति
                                की कितनी बैठक आयोजित की गई एवं कितनी-कितनी
                                अतिरिक्त राशि जनभागीदारी समिति द्वारा संस्था को
                                प्राप्त हुई :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) बैठकों की संख्या :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="janBhagidariBaithakoKiSankhya"
                                value={formData.janBhagidariBaithakoKiSankhya}
                                onChange={handleInputChange} />
                              {errors.janBhagidariBaithakoKiSankhya && (
                                <small className="text-danger">
                                  {errors.janBhagidariBaithakoKiSankhya}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) एकत्रित राशि :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="janBhagidariekatritRashi"
                                value={formData.janBhagidariekatritRashi}
                                onChange={handleInputChange} />
                              {errors.janBhagidariekatritRashi && (
                                <small className="text-danger">
                                  {errors.janBhagidariekatritRashi}
                                </small>
                              )}
                            </Col>
                            <Col md="4">
                              <Label style={{ fontSize: "15px" }}>
                                (तीन) उपयोग की गई राशि :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="janBhagiDariUpyogKiGaiRashi"
                                value={formData.janBhagiDariUpyogKiGaiRashi}
                                onChange={handleInputChange} />
                              {errors.janBhagiDariUpyogKiGaiRashi && (
                                <small className="text-danger">
                                  {errors.janBhagiDariUpyogKiGaiRashi}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col md="4">
                              <Label>
                                {" "}
                                <strong>15. </strong> संबंधित आडिट आपत्तियों की
                                संख्या :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="auditApptiyonKiSankhya"
                                value={formData.auditApptiyonKiSankhya}
                                onChange={handleInputChange} />
                              {errors.auditApptiyonKiSankhya && (
                                <small className="text-danger">
                                  {errors.auditApptiyonKiSankhya}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>16.</strong>पेंशन प्रकरणों की संख्या जो
                                तैयार कर पेंशन स्वीकृतकर्ता को भेजे गये और शेष
                                पेंशन प्रकरणों की संख्या :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) कितने प्रकरण भेजे गये :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="kitnePensionPrakarBhejeGai"
                                value={formData.kitnePensionPrakarBhejeGai}
                                onChange={handleInputChange} />
                              {errors.kitnePensionPrakarBhejeGai && (
                                <small className="text-danger">
                                  {errors.kitnePensionPrakarBhejeGai}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) कितने पेंशन प्रकरण अनिर्णीत शेष हैं :
                              </Label>
                              <Input type="number"
                                name="anirnitPensionPrakaran"
                                className="form-control"
                                value={formData.anirnitPensionPrakaran}
                                onChange={handleInputChange} />
                              {errors.anirnitPensionPrakaran && (
                                <small className="text-danger">
                                  {errors.anirnitPensionPrakaran}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>17.</strong>कितने अनुशासिक कार्यवाही के
                                मामले में जॉच की गई व कितने शेष हैं :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) वर्ष प्रारंभ होने से पूर्व चले आ रहे प्रकरण
                                :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="purvPrakaran"
                                value={formData.purvPrakaran}
                                onChange={handleInputChange} />
                              {errors.purvPrakaran && (
                                <small className="text-danger">
                                  {errors.purvPrakaran}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) वर्ष के दौरान संस्थित प्रकरण :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                value={formData.sansthitPrakran}
                                name="sansthitPrakran"
                                onChange={handleInputChange} />
                              {errors.sansthitPrakran && (
                                <small className="text-danger">
                                  {errors.sansthitPrakran}
                                </small>
                              )}
                            </Col>
                            <Col className="mt--4" md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (तीन) वर्ष के दौरान कार्यवाही समाप्त वाले प्रकरण
                                :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="karyaWahiPrakaran"
                                value={formData.karyaWahiPrakaran}
                                onChange={handleInputChange} />
                              {errors.karyaWahiPrakaran && (
                                <small className="text-danger">
                                  {errors.karyaWahiPrakaran}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (चार) वर्ष के अन्त में शेष प्रकरण :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="sheshPrakaran"
                                value={formData.sheshPrakaran}
                                onChange={handleInputChange} />
                              {errors.sheshPrakaran && (
                                <small className="text-danger">
                                  {errors.sheshPrakaran}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>18. </strong> वर्ष की अवधि में प्राचार्य
                                द्वारा स्वयं कुल कितने शिक्षण कार्य किये गये
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) स्नातक स्तर की कक्षाओं के पीरियड :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="ugperiods"
                                value={formData.ugperiods}
                                onChange={handleInputChange} />
                              {errors.ugperiods && (
                                <small className="text-danger">
                                  {errors.ugperiods}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) स्नातकोत्तर स्तर की कक्षाओं के पीरियेड :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="pgperiods"
                                value={formData.pgperiods}
                                onChange={handleInputChange} />
                              {errors.pgperiods && (
                                <small className="text-danger">
                                  {errors.pgperiods}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>19. </strong> महाविद्यालय में अध्ययनरत
                                छात्रों की कुल संख्या :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) स्नातक स्तर पर :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="studentNougStarPar"
                                value={formData.studentNougStarPar}
                                onChange={handleInputChange} />
                              {errors.studentNougStarPar && (
                                <small className="text-danger">
                                  {errors.studentNougStarPar}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) स्नातकोत्तर स्तर पर :
                              </Label>
                              <Input type="number"
                                className="form-control"
                                name="pgperistudentNoPgStarParods"
                                value={formData.pgperistudentNoPgStarParods}
                                onChange={handleInputChange} />
                              {errors.pgperistudentNoPgStarParods && (
                                <small className="text-danger">
                                  {errors.pgperistudentNoPgStarParods}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col>
                          <Row>
                            <Col>
                              <Label>
                                <strong>20.</strong>विगत शैक्षणिक वर्ष में
                                संस्था के छात्रों के परीक्षाफल का विश्लेषण :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (एक) स्नातक प्रथम वर्ष परीक्षा :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="ugFirstExamResult"
                                value={formData.ugFirstExamResult}
                                onChange={handleInputChange} />
                              {errors.ugFirstExamResult && (
                                <small className="text-danger">
                                  {errors.ugFirstExamResult}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (दो) स्नातक द्वितीय वर्ष परीक्षा :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="ugSecondExamResult"
                                value={formData.ugSecondExamResult}
                                onChange={handleInputChange} />
                              {errors.ugSecondExamResult && (
                                <small className="text-danger">
                                  {errors.ugSecondExamResult}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (तीन) स्नातक तृतीय वर्ष परीक्षा :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="ugThirdExamResult"
                                value={formData.ugThirdExamResult}
                                onChange={handleInputChange} />
                              {errors.ugThirdExamResult && (
                                <small className="text-danger">
                                  {errors.ugThirdExamResult}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (धार) स्नातकोत्तर पूर्वार्द्ध परीक्षा :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="pgFirstHalfExamResult"
                                value={formData.pgFirstHalfExamResult}
                                onChange={handleInputChange} />
                              {errors.pgFirstHalfExamResult && (
                                <small className="text-danger">
                                  {errors.pgFirstHalfExamResult}
                                </small>
                              )}
                            </Col>
                          </Row>
                          <Row className="mt-2">
                            <Col md="3">
                              <Label style={{ fontSize: "15px" }}>
                                (पांच) स्नातकोत्तर उत्तरार्द्ध परीक्षा :
                              </Label>
                              <Input type="text"
                                className="form-control"
                                name="pgSecondHalfExamResult"
                                value={formData.pgSecondHalfExamResult}
                                onChange={handleInputChange} />
                              {errors.pgSecondHalfExamResult && (
                                <small className="text-danger">
                                  {errors.pgSecondHalfExamResult}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-flex mt-3 mb-3">
                        <Col md="12">
                          <Row>
                            <Col>
                              <Label>
                                <strong>21. </strong> संस्था के छात्रों द्वारा
                                वर्ष के दौरान कोई विशिष्ट उपलब्धि प्राप्त की गई
                                थी तो उसका संक्षिप्त विवरण :
                              </Label>
                              <Input type="textarea"
                                className="form-control"
                                maxLength={300}
                                name="visisthUplabdhi"
                                value={formData.visisthUplabdhi}

                                onChange={handleInputChange} />
                              {errors.visisthUplabdhi && (
                                <small className="text-danger">
                                  {errors.visisthUplabdhi}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-flex mt-3 mb-3">

                        <Col md="12">
                          <Row>
                            <Col>
                              <Label>
                                <strong>22. </strong> संस्था के छात्रों का यदि
                                किसी महत्वपूर्ण शासकीय सेवा या अशासकीय संगठन में
                                किसी उच्च पद पर चयन हुआ हो तो उसका उल्लेख करें :
                              </Label>
                              <Input type="textarea" className="form-control"
                                name="mahatvapurnaSaskiyaSevaChayan"
                                maxLength={500}
                                value={formData.mahatvapurnaSaskiyaSevaChayan}
                                onChange={handleInputChange} />
                              {errors.mahatvapurnaSaskiyaSevaChayan && (
                                <small className="text-danger">
                                  {errors.mahatvapurnaSaskiyaSevaChayan}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                      <Row className="d-block mt-3 mb-3">
                        <Col md="12">
                          <Row>
                            <Col>
                              <Label>
                                <strong>23. </strong>
                                वर्ष के दौरान आपके द्वारा पढ़ी गई सबसे अच्छी तीन पुस्तकों के नाम लेखक व प्रकाशक का नाम :
                              </Label>
                            </Col>
                          </Row>
                          <Row>
                            <Col lg="12">
                              {books.map((book, index) => (
                                <Row key={index}>
                                  <Col>
                                    <FormGroup>
                                      <Label style={{ fontWeight: "700" }} for={`bookName${index}`}>
                                        पुस्तक {index + 1}
                                      </Label>
                                      <Input
                                        type="text"
                                        id={`bookName${index}`}
                                        value={book.name}
                                        onChange={(e) => handleChange(index, "name", e.target.value)}
                                        required
                                      />
                                    </FormGroup>
                                  </Col>
                                  <Col>
                                    <FormGroup>
                                      <Label style={{ fontWeight: "700" }} for={`bookWriter${index}`}>
                                        लेखक
                                      </Label>
                                      <Input
                                        type="text"
                                        id={`bookWriter${index}`}
                                        value={book.writer}
                                        onChange={(e) => handleChange(index, "writer", e.target.value)}
                                        required
                                      />
                                    </FormGroup>
                                  </Col>
                                  <Col>
                                    <FormGroup>
                                      <Label style={{ fontWeight: "700" }} for={`bookPublisher${index}`}>
                                        प्रकाशक
                                      </Label>
                                      <Input
                                        type="text"
                                        id={`bookPublisher${index}`}
                                        value={book.publisher}
                                        onChange={(e) => handleChange(index, "publisher", e.target.value)}
                                        required
                                      />
                                    </FormGroup>
                                  </Col>
                                </Row>
                              ))}
                              {/* Disable the button if there are already 3 books */}
                              {books.length < 3 ? (
                                <Button color="primary" onClick={() => { }}>
                                  Add Another Book
                                </Button>
                              ) : (
                                <p className="text-danger">You can only enter three books.</p>
                              )}
                            </Col>
                          </Row>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                  <Button color="success" onClick={handleFinalSubmit}>
                    Submit
                  </Button>
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

ACRFormUGPG.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormUGPG;
