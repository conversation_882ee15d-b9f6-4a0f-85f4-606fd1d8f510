    import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import TransferTimeline from "../../views/Admin/TransferTimeline.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import { BsDownload, BsEye } from "react-icons/bs";

import formatDate from "../../utils/formateDate";
import {
  Col,
  ButtonGroup,
  Button,
  Container,
  Row,
  Card,
  Input,
  CardHeader,
  CardBody,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Badge,
} from "reactstrap";

const TransferedEmpListByCollege = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const clgId = sessionStorage.getItem("id");
  const [empData, setEmpData] = useState([]);
  const [allEmpData, setAllEmpData] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [timelineModal, setTimelineModal] = useState(false);
  const toggleTimeModal = () => setTimelineModal(!timelineModal);
  const [selectedEmployee, setSelectedEmployee] = useState("");
  const [startDate, setStartDate] = useState(""); // start date filter
  const [endDate, setEndDate] = useState(""); // end date filte

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/transfered-data-byClgId/${clgId}`,
          {
            headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setEmpData(response.data);
          setAllEmpData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token, clgId]);

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);
    console.log(filter, "Getting filter");

    let filteredData;
    if (filter === "all") {
      filteredData = allEmpData;
    } else if (filter === "incoming") {
      filteredData = allEmpData.filter(
        (emp) => emp.transferToCollege._id.toString() === clgId.toString()
      );
    } else if (filter === "outgoing") {
      filteredData = allEmpData.filter(
        (emp) => emp.transferFromCollege._id.toString() === clgId.toString()
      );
    }
    setEmpData(filteredData);
  };

  useEffect(() => {
    let filtered = allEmpData;
    if (searchTerm.trim() !== "") {
      filtered = filtered.filter((item) =>
        Object.keys(item).some((key) =>
          String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
    if (startDate) {
      filtered = filtered.filter(
        (item) => new Date(item.transferOrderDate) >= new Date(startDate)
      );
    }
    if (endDate) {
      filtered = filtered.filter(
        (item) => new Date(item.transferOrderDate) <= new Date(endDate)
      );
    }
    setEmpData(filtered);
  }, [searchTerm, startDate, endDate, allEmpData, setEmpData]);

  const handleStartDateChange = (e) => {
    setStartDate(e.target.value);
  };
  const handleEndDateChange = (e) => {
    setEndDate(e.target.value);
  };

  const columns = [
    {
      name: "S.no",
      selector: (row, index) => index + 1,
      width: "100px",
      sortable: true,
    },
    {
      name: "Transfer ID / Date",
      selector: (row) => (
        <>
          <div className="font-weight-700">{row.transferID}</div>
          <div className="font-weight-700">({formatDate(row.createdAt)})</div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Transfer Order No / Date",
      width: "200px",
      selector: (row) => (
        <>
          <div>{row.transferOrderNo}</div>
          <div style={{ fontSize: "0.85em", color: "#555" }}>
            {formatDate(row.transferOrderDate)}
          </div>
          <div>
            <strong>Carreer Timeline</strong>{" "}
            <Button
              onClick={() => {
                setSelectedEmployee(row.employeeDetails._id);
                toggleTimeModal();
              }}
              className="btn-sm bg-warning text-white"
            >
              view
            </Button>
          </div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Employee Details",
      selector: (row) => (
        <div>
          <strong className="text-primary">{row.name || "N/A"}</strong>
          <br />
          <strong>Emp Code: </strong>
          {row.empCode || "N/A"}
          <br />
          <strong>S/o: </strong>
          {row.fatherName || "N/A"}
          <br />
          <strong>Designation: </strong>
          {row.designation || "N/A"}
          <br />
          <strong>Contact No: </strong>
          {row.mobileNo || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Institute Name",
      selector: (row) => (
        <div>
          <strong>From College: </strong>
          {row.transferFromCollege?.name || "N/A"}
          <br />
          <strong>Target College: </strong>
          {row.transferToCollege?.name || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Charge Relieving Details",
      selector: (row) => (
        <div>
          <strong>Relieving Status : </strong>
          {row.isChargeReleasingDone === true ? (
            <Badge className="bg-success text-white">Completed </Badge>
          ) : (
            <Badge className="bg-warning text-white">Pending </Badge>
          )}
          <br />
          {row.isChargeReleasingDone === true && (
            <>
              <strong>Order No :</strong>
              {row.chargeReleaseOrderNo || "N/A"}
              <br />
              <strong>Order Date :</strong>
              {formatDate(row.chargeReleaseOrderDate) || "N/A"}
              <br />
              <strong>Order Letter :</strong>
             
              <a
                href={`https://heonline.cg.nic.in/${row.copyOfChargeReleaseLetter}`}
                download
              >
                <Button className="btn-sm btn-primary">
                  {row.copyOfChargeReleaseLetter ? <BsDownload color="blue"  size={18} /> : "No File"}
                </Button>
              </a>
            </>
          )}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Charge Joining Details",
      selector: (row) => (
        <div>
          <strong>Joining Status : </strong>
          {row.isJoiningDone === true ? (
            <Badge className="bg-success text-white">Completed </Badge>
          ) : (
            <Badge className="bg-warning text-white">Pending </Badge>
          )}
          <br />
          {row.isJoiningDone === true && (
            <>
              <strong>Joining Date :</strong>
              {formatDate(row.chargeJoinDate) || "N/A"}
              <br />
              <strong>Joining Letter :</strong>
              <a
                href={`https://heonline.cg.nic.in/${row.chargeJoiningLetter}`}
                download
              >
                <Button className="btn-sm btn-primary">
                  {row.chargeJoiningLetter ? <BsDownload color="blue"  size={18} /> : "No File"}
                </Button>
              </a>
            </>
          )}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
  ];

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value); // Update searchTerm state
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="border-0 d-block justify-content-between align-items-center">
            <Row>
              <Col xs="3">
                <h3 className="mb-0">Transferd Employee List</h3>
              </Col>
              <Col md={3}>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  style={{ marginBottom: "10px" }}
                />
              </Col>
              <Col md={3}>
                <Input
                  type="date"
                  placeholder="Start Date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  style={{ marginBottom: "10px" }}
                  max={endDate || undefined} // prevent selecting after end date
                />
              </Col>
              <Col md={3}>
                <Input
                  type="date"
                  placeholder="End Date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  style={{ marginBottom: "10px" }}
                  min={startDate || undefined} // prevent selecting before start date
                />
              </Col>
            </Row>
            <Row>
              <Col className="d-flex align-items-center">
                <ButtonGroup>
                  <Button
                    color={
                      selectedFilter === "incoming" ? "success" : "secondary"
                    }
                    onClick={() => handleFilterClick("incoming")}
                  >
                    Incoming Transfer
                  </Button>
                  <Button
                    color={
                      selectedFilter === "outgoing" ? "success" : "secondary"
                    }
                    onClick={() => handleFilterClick("outgoing")}
                  >
                    Out-Going Transfer
                  </Button>
                  <Button
                    color={selectedFilter === "all" ? "success" : "secondary"}
                    onClick={() => handleFilterClick("all")}
                  >
                    All Transfer
                  </Button>
                </ButtonGroup>
                <Modal isOpen={timelineModal} toggle={toggleTimeModal}>
                  <ModalHeader toggle={toggleTimeModal}>
                    Carreer Timeline
                  </ModalHeader>
                  <ModalBody>
                    {selectedEmployee && (
                      <TransferTimeline data={selectedEmployee} />
                    )}
                  </ModalBody>

                  <ModalFooter>
                    <Button color="secondary" onClick={toggleTimeModal}>
                      Close
                    </Button>
                  </ModalFooter>
                </Modal>
              </Col>{" "}
            </Row>
          </CardHeader>
          <CardBody>
            <DataTable
              columns={columns}
              data={empData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              defaultSortField="name"
              defaultSortAsc={true}
              customStyles={{
                header: {
                  style: {
                    backgroundColor: "#f8f9fa",
                    fontWeight: "bold",
                  },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff",
                    borderBottom: "1px solid #ddd",
                  },
                  onHoverStyle: {
                    backgroundColor: "#ffff99",
                  },
                },
              }}
            />
          </CardBody>
        </Card>
      </Container>
    </>
  );
};

export default TransferedEmpListByCollege;
