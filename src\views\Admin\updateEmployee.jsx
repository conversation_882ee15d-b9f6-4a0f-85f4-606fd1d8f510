import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const defaultErrors = {
  name: "",
  empCode: "",
  email: "",
  contact: "",
  divison: "",
  district: "",
  vidhansabha: "",
  college: "",
  designation: "",
  class: "",
  address: "",
  workType: "",
  currentSalary: "",
  nextIncrementDate: "",
  gender: "",
  title: "",
  isInchargePrincipal: false,

};
const UpdateEmployee = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const { id } = useParams();
  const [errors, setErrors] = useState(defaultErrors);
  const inputStyle = {
    color: "gray", // Set the desired gray color
    borderColor: "#ccc", // Optional: Set border color for better visibility
  };
  const [formData, setFormData] = useState({
    name: "",
    empCode: "",
    email: "",
    contact: "",
    divison: "",
    district: "",
    vidhansabha: "",
    designation: "",
    class: "",
    address: "",
    workType: "",
    currentSalary: "",
    nextIncrementDate: "",
    applicableNextIncrement: "",
    gender: "",
    title: "",
    onDeputation: "false",
    deputedDesignation: "",
    postingLocation: "",
    employeeType: "",
    isInchargePrincipal: false,

  });

  const [showNextIncrementDate, setShowNextIncrementDate] = useState(false);
  // if(formData.applicableNextIncrement === 'Yes'){
  //   setShowNextIncrementDate(true);
  // }else{
  //   setShowNextIncrementDate(false);
  // }
  useEffect(() => {
    const getEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data; // Convert to YYYY-MM-DD
          if (data.applicableNextIncrement === 'Yes') {
            setShowNextIncrementDate(true);
          }
          setFormData({
            ...data,
            class: data.classData,
            divison: data.divison || "",
            vidhansabha: data.vidhansabha || "",
            district: data.district || "",
            designation: data.designation || formData.designation,
            onDeputation: String(data.onDeputation),
            nextIncrementDate: String(data.nextIncrementDate),
            applicableNextIncrement: String(data.applicableNextIncrement),
          });
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getEmployee(); // Call the function inside useEffect
  }, [id, endPoint, token]); // Dependencies
  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);
  const [division, setDivision] = useState([]);
  const [classData, setClassData] = useState([]);
  const [designationData, setDesignationData] = useState([]);

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };



    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchDesignation();
    fetchClassData();
    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const [employee, setEmployee] = useState([]);
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const data = response.data.getAllEmployeeCollegeWise;
          const filterd = data.filter((item) => item.designation_details.designation === "UG Principal" ||
            item.designation_details.designation === "PG Principal"
            || item.isInchargePrincipal === true);

            const expectPrincipal = filterd.filter((item)=> item._id != id)
            // console.log("Getting Expect Principal", expectPrincipal);
          setEmployee(expectPrincipal);


        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchCollege();
  }, [endPoint, token]);


  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
    if (name === "applicableNextIncrement") {
      if (value === "Yes") {
        setShowNextIncrementDate(true);
      } else {
        setShowNextIncrementDate(false);
      }
    }

    if (name === "designation" && (designationData.find((type) => type._id === formData.designation)?.designation != 'Assistant Professor' || designationData.find((type) => type._id === formData.designation)?.designation != 'Professor' )){
      setFormData({
        ...formData,
        [name]:value,
        isInchargePrincipal: false,
      });
    }
     

    if (name === "isInchargePrincipal") {
      setFormData({
        ...formData,
        [name]: value === "true",
      });
    }
  };

  const handleDivisionChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData((prevData) => ({
      ...prevData,
      divison: value,
    }));
  };

  useEffect(() => {
    const ChangeDeputation = async () => {
      if (formData.onDeputation === "false")
        setFormData({
          ...formData,
          deputedDesignation: "",
          postingLocation: "",
          employeeType: "",
        });
    };

    ChangeDeputation(); // Call the function inside useEffect
  }, [formData.onDeputation]);

  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({
      ...formData,
      district: value,
    });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
  };

  const validateFields = () => {
    const newErrors = {};

    Object.keys(formData).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if there are no errors
  };


  const [clgData, setClgData] = useState({});
  useEffect(() => {
    const getCollegeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-single-college/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setClgData(data);
        } else {
          SwalMessageAlert("Failed to load college data", "error");
        }
      } catch (error) {
        console.error("Error fetching college data:", error);
        SwalMessageAlert("Failed to load college data.", "error");
      }
    };
    getCollegeData();

  }, [collegeId, endPoint, token]);


  const handleSubmit = async (e) => {


    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    // if (!validateFields()) {
    //   return;
    // }

    // // console.log(1);
    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.name,
      formData.empCode,
      formData.email,
      formData.contact,
      formData.divison,
      formData.district,
      formData.vidhansabha,
      formData.designation,
      formData.class,
      formData.address,
      formData.workType,
      formData.currentSalary,
      formData.applicableNextIncrement &&
      formData.applicableNextIncrement === "Yes" &&
      formData.nextIncrementDate,
      formData.gender,
      formData.title,
      formData.onDeputation,
      ...(formData.onDeputation === "true"
        ? [
          formData.deputedDesignation,
          formData.postingLocation,
          formData.employeeType,
        ]
        : []),
    ];

    // console.log("Reaching Here");


    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );
    if (formData.applicableNextIncrement === "Yes") {
      setErrors((prevErrors) => ({
        ...prevErrors,
        nextIncrementDate: "Please enter next increment date",
      }));
    } else {
      setFormData((prevFormData) => ({
        ...prevFormData,
        nextIncrementDate: "",
      }));
    }

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    try {
      const body = {
        name: formData.name,
        empCode: String(formData.empCode),
        email: formData.email,
        contact: String(formData.contact),
        divison: String(formData.divison),
        district: String(formData.district),
        vidhansabha: String(formData.vidhansabha),
        college: String(collegeId),
        designation: formData.designation,
        classData: String(formData.class),
        workType: String(formData.workType),
        address: formData.address,
        currentSalary: String(formData.currentSalary),
        nextIncrementDate: String(formData.nextIncrementDate),
        applicableNextIncrement: String(formData.applicableNextIncrement),
        gender: String(formData.gender),
        title: String(formData.title),
        onDeputation: String(formData.onDeputation),
        deputedDesignation: String(formData.deputedDesignation),
        postingLocation: String(formData.postingLocation),
        employeeType: String(formData.employeeType),
        isInchargePrincipal: String(formData.isInchargePrincipal),
      };

      
      const response = await axios.put(
        `${endPoint}/api/employee/update/${id}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 201) {
        setFormData({
          name: "",
          empCode: "",
          email: "",
          contact: "",
          divison: "",
          district: "",
          vidhansabha: "",
          college: "",
          designation: "",
          class: "",
          address: "",
          workType: "",
          currentSalary: "",
          nextIncrementDate: "",
          applicableNextIncrement: "",
          gender: "",
          title: "",
          onDeputation: "",
          deputedDesignation: "",
          postingLocation: "",
          employeeType: "",
          isInchargePrincipal: false,

        });
        window.location.replace("admin/employee-list");
        SwalMessageAlert("Employee Updated Successfully","success")
      } else {
        alert("Updating Employee failed.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate("admin/university");
    }
  };

  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.divison}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.divison) fetchDistrict();
  }, [formData.divison, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.vidhansabha) fetchVidhansabha();
  }, [formData.vidhansabha, endPoint, token]);

  const handleClassInputChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/degisnation-class-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        let filteredData;
        if (clgData.principalType !== "Regular" || employee && employee.length > 0) {
          filteredData = data.filter((item) =>
            item.designation !== "UG Principal" && item.designation !== "PG Principal"
          );
        } else {
          filteredData = data;
        }
        setDesignationData(filteredData);
      } else {
        SwalMessageAlert("Designation Not Found", "error");

      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      class: value,
    });
  };

  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date)) return ''; // Check if the date is valid
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  return (
    <>
      <Header />

      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Edit Employee</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    {/* <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button> */}
                  </Col>
                </Row>
              </CardHeader>
              <CardBody className="">
                <Form role="form" >
                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="empCode" className="form-control-label">
                          Employee Code
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-hat-3" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="empCode"
                            name="empCode"
                            placeholder="Employee Code"
                            type="text"
                            readOnly
                            style={inputStyle}
                            onChange={handleInputChange}
                            value={formData.empCode}
                          />
                          {errors.empCode && (
                            <p style={{ color: "red" }}>{errors.empCode}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="contact" className="form-control-label">
                          Contact
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-mobile-button" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="contact"
                            name="contact"
                            placeholder="Contact"
                            type="tel"
                            style={inputStyle}
                            onChange={handleInputChange}
                            value={formData.contact}
                          />
                          {errors.contact && (
                            <p style={{ color: "red" }}>{errors.contact}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>

                    <Col xs="3">
                      <FormGroup>
                        <Label for="title" className="form-control-label">
                          Title
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="fas fa-user"></i>
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="title"
                            name="title"
                            type="select" // Change type to "select"
                            value={formData.title || ""}
                            style={{ ...inputStyle, padding: "10px" }} // Adjust inputStyle as needed
                            onChange={handleInputChange}
                          >
                            <option value="">-- Select Title --</option>
                            <option value="Mr.">Mr.</option>
                            <option value="Mrs.">Mrs.</option>
                            <option value="Ms.">Ms.</option>
                            <option value="Miss">Miss</option>
                            <option value="Dr.">Dr.</option>
                            <option value="Prof.">Prof.</option>
                            <option value="Hon.">Hon.</option>
                          </Input>
                          {errors.title && (
                            <p style={{ color: "red" }}>{errors.title}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="name" className="form-control-label">
                          Name
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-hat-3" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="name"
                            name="name"
                            placeholder="Name"
                            type="text"
                            style={inputStyle}
                            onChange={handleInputChange}
                            value={formData.name}
                          />
                          {errors.name && (
                            <p style={{ color: "red" }}>{errors.name}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                  </Row>

                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="gender" className="form-control-label">
                          Gender
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i
                                className="fas fa-male"
                                style={{ marginRight: "5px" }}
                              ></i>
                              <i className="fas fa-female"></i>
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="gender"
                            name="gender"
                            type="select" // Change type to "select"
                            value={formData.gender || ""}
                            style={inputStyle}
                            onChange={handleInputChange}
                          >
                            <option value="">-- Select Gender --</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                          </Input>
                          {errors.gender && (
                            <p style={{ color: "red" }}>{errors.gender}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="email" className="form-control-label">
                          Email
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-email-83" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="email"
                            placeholder="Email"
                            name="email"
                            type="email"
                            style={inputStyle}
                            autoComplete="new-email"
                            onChange={handleInputChange}
                            value={formData.email}
                          />
                          {errors.email && (
                            <p style={{ color: "red" }}>{errors.email}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>

                    <Col xs="3">
                      <FormGroup>
                        <Label for="division" className="form-control-label">
                          Select Division
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-map-big" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            name="divison"
                            id="input-division"
                            type="select"
                            value={formData.divison}
                            onChange={handleDivisionChange}
                            style={inputStyle}
                          >
                            <option value="">Select Division</option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.divisionCode}
                                  selected={
                                    Number(type.divisionCode) ===
                                    Number(formData.divison)
                                  }
                                >
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.divison && (
                            <p style={{ color: "red" }}>{errors.divison}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="district" className="form-control-label">
                          Select District
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-map-big" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            onChange={handleDistrictChange}
                            style={inputStyle}
                          >
                            <option value="">Select District</option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.LGDCode}
                                  selected={
                                    Number(type.LGDCode) ===
                                    Number(formData.district)
                                  }
                                >
                                  {type.districtNameEng}
                                </option>
                              ))}
                          </Input>
                          {errors.district && (
                            <p style={{ color: "red" }}>{errors.district}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                  </Row>

                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="vidhanSabha" className="form-control-label">
                          Select Vidhan Sabha
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-map-big" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            name="vidhansabha"
                            id="input-vidhansabha"
                            type="select"
                            value={formData.vidhansabha}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Vidhan Sabha</option>
                            {vidhansabha &&
                              vidhansabha.length > 0 &&
                              vidhansabha.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.ConstituencyNumber}
                                  selected={
                                    formData.vidhansabha ===
                                    type.ConstituencyNumber
                                  }
                                >
                                  {type.ConstituencyName}
                                </option>
                              ))}
                          </Input>
                          {errors.vidhansabha && (
                            <p style={{ color: "red" }}>{errors.vidhansabha}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>

                    <Col xs="3">
                      <FormGroup>
                        <Label for="vidhanSabha" className="form-control-label">
                          Work Type
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-map-big" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            name="workType"
                            id="input-workType"
                            type="select"
                            value={formData.workType}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Work Type</option>
                            <option value="TEACHING">TEACHING</option>
                            <option value="NON TEACHING">NON TEACHING</option>
                          </Input>
                          {errors.vidhansabha && (
                            <p style={{ color: "red" }}>{errors.vidhansabha}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="class" className="form-control-label">
                          Select Class
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-book-bookmark" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="class"
                            type="select"
                            name="class"
                            style={inputStyle}
                            onChange={handleClassInputChange}
                          >
                            <option value="">Select Class</option>
                            {classData &&
                              classData.length > 0 &&
                              classData.map((type, index) => (
                                <option
                                  key={index}
                                  value={type._id}
                                  selected={
                                    type._id === formData.class ? true : false
                                  }
                                >
                                  {type.className}
                                </option>
                              ))}
                          </Input>
                          {errors.class && (
                            <p style={{ color: "red" }}>{errors.class}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                    <Col xs="3">
                      <FormGroup>
                        <Label for="designation" className="form-control-label">
                          Designation
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-briefcase-24" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="designation"
                            type="select"
                            name="designation"
                            style={inputStyle}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Designation</option>
                            {designationData &&
                              designationData.length > 0 &&
                              designationData.map((type, index) => (
                                <option
                                  key={index}
                                  value={type._id}
                                  selected={
                                    String(type._id) ===
                                      String(formData.designation)
                                      ? true
                                      : false
                                  }
                                >
                                  {type.designation}
                                </option>
                              ))}
                          </Input>
                          {errors.designation && (
                            <p style={{ color: "red" }}>{errors.designation}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label
                          for="currentSalary"
                          className="form-control-label"
                        >
                          Basic Salary
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-money-coins" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="currentSalary"
                            name="currentSalary"
                            placeholder=" Basic Salary"
                            type="text"
                            style={inputStyle}
                            value={formData.currentSalary || ""}
                            onChange={handleInputChange}
                            maxLength={6}
                          />
                        </InputGroup>
                        {errors.currentSalary && (
                          <p style={{ color: "red", marginTop: "-15px" }}>
                            {errors.currentSalary}
                          </p>
                        )}
                      </FormGroup>
                    </Col>

                    <Col lg="6" md="12">
                      <Row>
                        <Col lg="6">
                          <FormGroup>
                            <Label
                              for="nextIncrementDate"
                              className="form-control-label"
                            >
                              Applicable For Next Increment
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-calendar-grid-58" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="applicableNextIncrement"
                                type="select"
                                disabled={
                                  formData.applicableNextIncrement === "false"
                                }
                                name="applicableNextIncrement"
                                style={{ maxWidth: "100%" }}
                                onChange={handleInputChange}
                                value={formData.applicableNextIncrement || ""}
                              >
                                <option value="">Select</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </Input>
                            </InputGroup>
                            {errors.applicableNextIncrement && (
                              <p style={{ color: "red", marginTop: "-15px" }}>
                                {errors.applicableNextIncrement}
                              </p>
                            )}
                          </FormGroup>
                        </Col>
                        <Col
                          lg="6"
                          style={{
                            display:
                              showNextIncrementDate !== true ? "none" : "",
                          }}
                        >
                          <FormGroup>
                            <Label
                              for="nextIncrementDate"
                              className="form-control-label"
                            >
                              Next Increment Date
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-calendar-grid-58" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="nextIncrementDate"
                                name="nextIncrementDate"
                                placeholder="Next Increment Date"
                                type="date"
                                style={inputStyle}
                                value={formatDateForInput(formData.nextIncrementDate)}
                                min={new Date().toISOString().split('T')[0]} // Ensures minimum date is today
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                            {errors.nextIncrementDate && (
                              <p style={{ color: "red", marginTop: "-15px" }}>
                                {errors.nextIncrementDate}
                              </p>
                            )}

                          </FormGroup>
                        </Col>
                        {clgData.principalType === "In-charge" && !(employee && employee.length > 0) &&  (designationData.find((type) => type._id === formData.designation)?.designation === 'Assistant Professor' || designationData.find((type) => type._id === formData.designation)?.designation === 'Professor' ) &&
                          <Col lg="6">
                            <FormGroup>
                              <Label
                                for="isInchargePrincipal"
                                className="form-control-label"
                              >
                                Is In-Charge Principal
                              </Label>
                              <div>
                                <Label
                                  style={{
                                    paddingLeft: "50px",
                                    paddingRight: "50px",
                                  }}
                                  check
                                >
                                  <Input
                                    type="radio"
                                    name="isInchargePrincipal"
                                    value={true}
                                    onChange={handleInputChange}
                                    checked={formData.isInchargePrincipal === true}
                                  />
                                  Yes
                                </Label>
                                <Label check>
                                  <Input
                                    type="radio"
                                    name="isInchargePrincipal"
                                    value={false}
                                    onChange={handleInputChange}
                                    checked={formData.isInchargePrincipal === false}
                                  />
                                  No
                                </Label>
                              </div>
                            </FormGroup>
                          </Col>
                        }
                      </Row>
                    </Col>

                    <Col xs="6">
                      <FormGroup>
                        <Label for="address" className="form-control-label">
                          Residential Address
                        </Label>
                        <InputGroup className="input-group-alternative mb-3">
                          <InputGroupAddon addonType="prepend">
                            <InputGroupText>
                              <i className="ni ni-map-pin" />
                            </InputGroupText>
                          </InputGroupAddon>
                          <Input
                            id="address"
                            placeholder="Address"
                            name="address"
                            type="text"
                            style={inputStyle}
                            onChange={handleInputChange}
                            value={formData.address}
                          />
                          {errors.address && (
                            <p style={{ color: "red" }}>{errors.address}</p>
                          )}
                        </InputGroup>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label
                          for="onDeputation"
                          className="form-control-label"
                        >
                          On Deputation
                        </Label>
                        <div>
                          <Label
                            style={{
                              paddingLeft: "50px",
                              paddingRight: "50px",
                            }}
                            check
                          >
                            <Input
                              type="radio"
                              name="onDeputation"
                              value="true"
                              onChange={handleInputChange}
                              checked={formData.onDeputation === "true"}
                            />
                            Yes
                          </Label>
                          <Label check>
                            <Input
                              type="radio"
                              name="onDeputation"
                              value="false"
                              onChange={handleInputChange}
                              checked={formData.onDeputation === "false"}
                            />
                            No
                          </Label>
                        </div>
                      </FormGroup>
                    </Col>

                    {formData.onDeputation === "true" && (
                      <>
                        <Col xs="3">
                          <FormGroup>
                            <Label
                              for="deputedDesignation"
                              className="form-control-label"
                            >
                              Deputed Designation
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-briefcase-24" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="deputedDesignation"
                                type="select"
                                name="deputedDesignation"
                                style={{ maxWidth: "100%" }}
                                disabled={formData.onDeputation === "false"}
                                value={formData.deputedDesignation}
                                onChange={handleInputChange}
                              >
                                <option value="">Select Designation</option>
                                {designationData &&
                                  designationData.length > 0 &&
                                  designationData.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.designation}
                                    </option>
                                  ))}
                              </Input>
                            </InputGroup>
                          </FormGroup>
                        </Col>
                        {/* Main Posting Location Field */}
                        <Col xs="3">
                          <FormGroup>
                            <Label
                              for="postingLocation"
                              className="form-control-label"
                            >
                              Posting Location
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-map-big" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="postingLocation"
                                type="text"
                                name="postingLocation"
                                style={inputStyle}
                                disabled={formData.onDeputation === "false"}
                                value={formData.postingLocation || ""}
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>

                        {/* Employee Type Field */}
                        <Col xs="3">
                          <FormGroup>
                            <Label
                              for="employeeType"
                              className="form-control-label"
                            >
                              Employee Type
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-briefcase-24" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="employeeType"
                                type="select"
                                disabled={formData.onDeputation === "false"}
                                value={formData.employeeType}
                                name="employeeType"
                                style={{ maxWidth: "100%" }}
                                onChange={handleInputChange}
                              >
                                <option value="">Select Employee Type</option>
                                <option value="DIRECTORATE">Directorate</option>
                                <option value="COMMISSIONER">
                                  Commissioner
                                </option>
                                <option value="INSTITUTE">Institute</option>
                              </Input>
                            </InputGroup>
                          </FormGroup>
                        </Col>
                      </>
                    )}
                  </Row>

                  <Row className="align-items-center">
                    <Col xs="8"></Col>
                    <Col className="text-right" xs="4">
                      <Button color="primary" onClick={handleSubmit}>
                        Submit
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateEmployee;
