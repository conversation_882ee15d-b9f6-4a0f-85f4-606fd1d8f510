import { useEffect, useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>er,
    <PERSON><PERSON>ooter,
    Container,
    Row,
    Col,
    Badge,
    Table,
    Pagination,
    PaginationItem,
    PaginationLink,
    Input,
    FormGroup,
    Label,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import Swal from "sweetalert2";
import { Link, useNavigate } from "react-router-dom";

const SectionList = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const navigate = useNavigate();
    const [section, setSection] = useState([]);
    const [filteredSection, setFilteredSection] = useState([]);
    const [searchTerm, setSearchTerm] = useState("");
    const [officeFilter, setOfficeFilter] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10; // Number of items per page for pagination
    const [sortConfig, setSortConfig] = useState({ key: "", direction: "asc" });

    useEffect(() => {
        const fetchSection = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/section/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });

                if (response.status === 200) {
                    const verifiedSection = response.data.filter(
                        (section) => section.isVerified === true
                    );

                    setSection(verifiedSection);
                    setFilteredSection(verifiedSection);
                } else {
                    alert("Failed to fetch Section. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchSection();
    }, [endPoint, token]);

    useEffect(() => {
        const filtered = section.filter((section) => {
            const matchesOffice =
                !officeFilter || String(section.office) === officeFilter;

            const matchesSearchTerm =
                !searchTerm ||
                (typeof section.sectionName === 'string' && section.sectionName.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (typeof section.sectionOfficerName === 'string' && section.sectionOfficerName.toLowerCase().includes(searchTerm.toLowerCase()));

            return matchesOffice && matchesSearchTerm;
        });
        setFilteredSection(filtered);
    }, [officeFilter, searchTerm, section]);


    const handleSort = (key) => {
        let direction = "asc";
        if (sortConfig.key === key && sortConfig.direction === "asc") {
            direction = "desc";
        }

        const sortedSection = [...filteredSection].sort((a, b) => {
            if (a[key] < b[key]) return direction === "asc" ? -1 : 1;
            if (a[key] > b[key]) return direction === "asc" ? 1 : -1;
            return 0;
        });

        setSortConfig({ key, direction });
        setFilteredSection(sortedSection);
    };

    const mapSectionOfficer = (id) => {
        Swal.fire({
            title: 'Are you sure?',
            text: "Please, Ensure Employee is onboarded Before Mapping as Section Officer",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, proceed!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                Swal.fire('Proceeding!', 'Your action has been confirmed.', 'success');
                navigate(`/admin/update-section/${id}`);
            } else {
                // Action on cancel
                Swal.fire('Cancelled', 'Your action has been cancelled.', 'info'); // Optional message
            }
        });
    };
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredSection.slice(
        indexOfFirstItem,
        indexOfLastItem
    );
    const totalPages = Math.ceil(filteredSection.length / itemsPerPage);

    const handlePageChange = (pageNumber) => setCurrentPage(pageNumber);

    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Row className="mt-5">
                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0">
                                <h3 className="mb-0">
                                    Section List{" "}  <span style={{ color: "blue" }}>( MAP SECTION OFFICER )</span>
                                    <span style={{ color: "red", fontSize: "12px" }}>
                                        (Only Verified Data will be shown here. Please verify.)
                                    </span>
                                </h3>
                                <Row className="mt-3">
                                    <Col md="6">
                                        <FormGroup>
                                            <Label for="searchSection">
                                                Search by Section
                                            </Label>
                                            <Input
                                                type="text"
                                                id="searchSection"
                                                placeholder="Enter Section name"
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                            />
                                        </FormGroup>
                                    </Col>
                                    <Col md="6">
                                        <FormGroup>
                                            <Label for="filterOffice">Filter by Office</Label>
                                            <Input
                                                type="select"
                                                value={officeFilter}
                                                onChange={(e) => setOfficeFilter(e.target.value)}
                                            >
                                                <option value="">All Offices</option>
                                                <option value="Directorate">Directorate</option>
                                                <option value="Institute">Institute</option>
                                            </Input>
                                        </FormGroup>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <Table
                                className="align-items-center table-flush"
                                hover
                                responsive
                            >
                                <thead className="thead-light">
                                    <tr>
                                        <th>S.No</th>
                                        <th onClick={() => handleSort("sectionName")}>
                                            Section Name{" "}
                                            {sortConfig.key === "sectionName"
                                                ? sortConfig.direction === "asc"
                                                    ? "↑"
                                                    : "↓"
                                                : ""}
                                        </th>
                                        <th>
                                            Section Officer
                                        </th>
                                        <th onClick={() => handleSort("office")}>
                                            Office{" "}
                                            {sortConfig.key === "office"
                                                ? sortConfig.direction === "asc"
                                                    ? "↑"
                                                    : "↓"
                                                : ""}
                                        </th>
                                        <th onClick={() => handleSort("createdAt")}>
                                            Created At{" "}
                                            {sortConfig.key === "createdAt"
                                                ? sortConfig.direction === "asc"
                                                    ? "↑"
                                                    : "↓"
                                                : ""}
                                        </th>
                                        <th onClick={() => handleSort("updatedAt")}>
                                            Updated At{" "}
                                            {sortConfig.key === "updatedAt"
                                                ? sortConfig.direction === "asc"
                                                    ? "↑"
                                                    : "↓"
                                                : ""}
                                        </th>
                                        <th>Edit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {currentItems.map((item, index) => (
                                        <tr key={index}>
                                            <td>{indexOfFirstItem + index + 1}</td>
                                            <td>{item.sectionName}</td>
                                            <td>{item.sectionOfficerName === undefined ? <strong> <Badge style={{ fontWeight: "bold", fontSize: "12px" }} className="bg-warning text-white "> Not Mapped </Badge></strong> : <strong>{item.sectionOfficerName}</strong>}</td>
                                            <td>{item.office}</td>
                                            <td>
                                                {new Date(item.createdAt).toLocaleDateString()}
                                            </td>
                                            <td>
                                                {new Date(item.updatedAt).toLocaleDateString()}
                                            </td>
                                            <td>
                                                {/* <Link to={`/admin/update-section/${item._id}`}> */}
                                                <button onClick={() => mapSectionOfficer(item._id)} className="btn btn-warning btn-sm">
                                                    Edit & Map
                                                </button>
                                                {/* </Link> */}
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <CardFooter className="py-4">
                                <nav aria-label="...">
                                    <Pagination className="pagination justify-content-end mb-0">
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem
                                                key={i}
                                                className={
                                                    currentPage === i + 1 ? "active" : ""
                                                }
                                            >
                                                <PaginationLink
                                                    href="#"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(i + 1);
                                                    }}
                                                >
                                                    {i + 1}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                    </Pagination>
                                </nav>
                            </CardFooter>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default SectionList;
