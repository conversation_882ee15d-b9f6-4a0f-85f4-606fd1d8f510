import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
const UpdateDistrict = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const { id } = useParams();
  const [formData, setFormData] = useState({
    sName: "",
    districtName: "",
    districtNameEng: "",
    LGDCode: "",
    division: "",
  });
  useEffect(()=>{
    const fetchDistrictData = async () => {
        try {
          const response = await axios.get(`${endPoint}/api/district/get-single-district/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
          if (response.status === 200) {  
            const data = response.data;// Convert to YYYY-MM-DD
            setFormData({
                ...data,
            });
          } else {
            alert("No District Found. Please try again.");
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          alert("An error occurred. Please try again later.");
        }
      };
      // Call the function
      fetchDistrictData();
  }, [id, endPoint, token])

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const [division, setDivision] = useState([]);
  useEffect(() => {
    const fetchDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          //   // console.log(response.data);

          setDivision(response.data);
        } else {
          alert("Failed to Division  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDivision();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();

    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.sName,
      formData.districtName,
      formData.districtNameEng,
      formData.LGDCode,
      formData.division,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    try {
      const body = {
        sName: String(formData.sName),
        districtName: String(formData.districtName),
        districtNameEng: String(formData.districtNameEng),
        LGDCode: String(formData.LGDCode),
        division: String(formData.division),
      };
      const response = await axios.put(
        `${endPoint}/api/district/update-district/${id}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
            sName: "",
            districtName: "",
            districtNameEng: "",
            LGDCode: "",
            division: "",
        });
        window.location.replace("admin/district");
      } else {
        alert("District Already Exists.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred while submitting the form:");
    }
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Update District</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-division"
                          >
                            Select Division
                          </label>
                          <Input
                            name="division"
                            id="input-division"
                            type="select"
                            value={formData.division}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Division</option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option key={index} value={type.divisionCode} selected={type.divisionCode === formData.division ? true : false}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-sName"
                          >
                            Short Name
                          </label>
                          <Input
                            name="sName"
                            id="input-sName"
                            placeholder="District Short Name"
                            type="text"
                            value={formData.sName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-districtName"
                          >
                            Name in Hindi
                          </label>
                          <Input
                            name="districtName"
                            id="input-districtName"
                            placeholder="District Name Hindi"
                            type="text"
                            value={formData.districtName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-districtNameEng"
                          >
                            Name in English
                          </label>
                          <Input
                            name="districtNameEng"
                            id="input-districtNameEng"
                            placeholder="District Name in English"
                            type="text"
                            value={formData.districtNameEng}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-LGDCode"
                          >
                            LGD Code
                          </label>
                          <Input
                            name="LGDCode"
                            id="input-LGDCode"
                            placeholder="LGD Code"
                            type="number"
                            autoComplete="pope"
                            value={formData.LGDCode}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateDistrict;
