import React, { useState, useEffect } from "react";
import axios from "axios";
import {
  <PERSON>,
  Card<PERSON>eader,
  CardBody,
  Input,
  Button,
  Container,
  Row,
  Col,
  Modal,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ody,
  ModalFooter,
} from "reactstrap";
import Header from "../../../components/Headers/Header";
import Select from "react-select";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";
const DirectorateACRMapping = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const [employees, setEmployees] = useState([]);
  const [section, setSection] = useState([]);
  const [acrMappedData, setACRMappedData] = useState([]);
  const [formData, setFormData] = useState([]);
  const [searchQuery, setSearchQuery] = useState(""); // Search query state
  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };
  const fetchEmployees = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/acr/director/get-employee`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const allEmployees = response.data || [];
        const sortedEmployees = allEmployees.sort((a, b) =>
          a.name.localeCompare(b.name)
        );
        setEmployees(
          sortedEmployees.map((emp) => ({
            ...emp,
            inputs: ["", "", ""],
            isChecked: false,
          }))
        );
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };
  const fetchSection = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/section/getAll`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        const data = response.data || [];
        setSection(data);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };
  const fetchMappedACR = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/acr/director/get-acr-mapping`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data || [];
        setACRMappedData(data);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };
  useEffect(() => {
    fetchEmployees();
    fetchSection();
    fetchMappedACR();
  }, []);
  const handleInputChange = (id, index, selectedOption) => {
    const selectedValue = selectedOption ? selectedOption.value : "";
    setEmployees((prev) =>
      prev.map((emp) =>
        emp._id === id
          ? {
            ...emp,
            inputs: emp.inputs.map((input, i) =>
              i === index ? selectedValue : input
            ),
          }
          : emp
      )
    );
  };
  const handleAdd = (id) => {
    const employee = employees.find((emp) => emp._id === id);
    if (employee) {
      // Ensure static values are set at the 2nd and 3rd indices
      const inputsWithDefaults = employee.inputs.map((input, index) => {
        if (index === 1) return "Additional Director"; // Set for 2nd index
        if (index === 2) return "Commissioner"; // Set for 3rd index
        return input || ""; // Use existing value or fallback to empty string
      });

      // Check if all inputs are valid
      const isValid = inputsWithDefaults.every((input) => input !== "");
      if (!isValid) {
        SwalMessageAlert("Please select all required fields", "error");
        return;
      }

      // Update the form data
      setFormData((prev) => [
        ...prev,
        {
          employeeId: employee._id,
          designation: employee.designationDetails.designationName,
          inputs: inputsWithDefaults,
        },
      ]);
    }
  };

  const handleFinalSubmit = async (e) => {
    try {
      e.target.disabled = true;

      const response = await axios.post(
        `${endPoint}/api/acr/submit-acr-mapping`,
        { formData },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Data submitted successfully!", "success");
        setFormData([]);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };
  const employeeOptions =
    (employees &&
      employees.length > 0 &&
      employees.map((emp) => ({
        value: emp._id,
        label: `${emp.name} (${emp.empCode})`,
      }))) ||
    [];

  const filteredEmployees = employees.filter(
    (emp) =>
      emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emp.empCode.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const [showMappingModal, setShowMappingModal] = useState(false);
  const [mappingData, setMappingData] = useState([]);
  const handleOpenModal = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/acr/director/get-acr-mapping`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setMappingData(data);
        setShowMappingModal(true);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };
  const toggleShowMappingModal = () => {
    setShowMappingModal(!showMappingModal);
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">
                Directorate Employee List For ACR Mapping
              </h3>
              <Button
                className="btn btn-sm btn-warning"
                onClick={handleOpenModal}
              >
                Show Mapping
              </Button>
            </Col>
            <Col className="text-right" xs="4">
              <Input
                type="text"
                placeholder="Search by Name or EmpCode"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Col>
            <Col className="text-right" xs="2">
              <Button
                color="primary"
                size="sm"
                onClick={handleFinalSubmit}
                disabled={formData.length === 0}
              >
                Final Submit
              </Button>
            </Col>
          </CardHeader>
          <CardBody>
            <Row>
              <Col lg="8">
                <table
                  border="1"
                  style={{ width: "100%", textAlign: "center" }}
                >
                  <thead>
                    <tr>
                      <th>S.No.</th>
                      <th>Basic Details</th>
                      <th>Reporting + Reviewing Officer</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredEmployees.map((emp, index) => (
                      <tr key={emp._id}>
                        <td>{index + 1}</td>
                        <td>
                          {`${emp.name} (${emp.empCode})`} <br />
                          {`${emp.classDetails.className} - ${emp.designationDetails.designation}`}{" "}
                          <br />
                          Section:{" "}
                          {section
                            .filter(
                              (a) => a?.sectionOfficerData?._id === emp._id
                            )
                            .map((a) => a.sectionName)
                            .join(", ")}
                        </td>
                        <td>
                          {emp.inputs.map((input, index) => {
                            const filteredOptions = employeeOptions.filter(
                              (option) => option.value !== emp._id
                            );

                            const staticValues = [
                              null, // No static value for the first input
                              {
                                value: "Additional Director",
                                label: "Additional Director",
                              },
                              { value: "Commissioner", label: "Commissioner" },
                            ];

                            const isStaticValue = staticValues[index] !== null;

                            return (
                              <Select
                                key={index}
                                value={
                                  isStaticValue
                                    ? staticValues[index]
                                    : filteredOptions.find(
                                      (option) => option.value === input
                                    )
                                }
                                options={isStaticValue ? [] : filteredOptions} // Empty options for static values
                                onChange={(selectedOption) =>
                                  handleInputChange(
                                    emp._id,
                                    index,
                                    selectedOption
                                  )
                                }
                                placeholder={
                                  index === 0
                                    ? "प्रथम मतांकन किसके द्वारा किया जायेगा चूने"
                                    : index === 1
                                      ? "द्वितीय मतांकन किसके द्वारा किया जायेगा चूने"
                                      : "अंतिम मतांकन किसके द्वारा किया जायेगा चूने"
                                }
                                disabled={
                                  !emp.isChecked || index === 1 || index === 2
                                } // Disable for static values
                              />
                            );
                          })}
                        </td>
                        <td>
                          <button
                            onClick={() => handleAdd(emp._id)}
                            className="btn btn-sm btn-warning"
                          >
                            Add
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </Col>
              <Col lg="4">
                <h3>Mapped Data:</h3>
                <table border="1">
                  <thead>
                    <tr>
                      <th>S.No.</th>
                      <th>Details</th>
                    </tr>
                  </thead>
                  <tbody>
                    {formData.length === 0 ? "No Data" : ""}
                    {formData.map((data, index) => (
                      <tr key={data.id}>
                        <td>{index + 1} </td>
                        <td>
                          Employee Name :{" "}
                          {
                            employees.find(
                              (emp) =>
                                String(emp._id) === String(data.employeeId)
                            )?.name
                          }
                          <br />
                          प्रथम मतांकन :{" "}
                          {
                            employees.find(
                              (emp) =>
                                String(emp._id) === String(data.inputs[0])
                            )?.name
                          }
                          <br />
                          द्वितीय मतांकन :{" "}{data.inputs[1]}

                          <br />
                          अंतिम मतांकन:{" "}{data.inputs[2]}

                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Container>

      <Modal
        style={{ maxWidth: "70%" }}
        isOpen={showMappingModal}
        toggle={toggleShowMappingModal}
      >
        <ModalHeader toggle={toggleShowMappingModal}>
          Directorate ACR Mapping
        </ModalHeader>
        <ModalBody>
          {mappingData &&
            mappingData.map((a, index) => (
              <>
                <Card className="shadow">
                  <CardHeader className=" align-items-center">
                    <Row>
                      <Col lg="5">
                        <h3 className="mb-0">
                          {a.name}{" "}
                          <strong className="badge badge-primary">
                            {a.empCode}
                          </strong>{" "}
                          <br />
                          <h5 style={{ color: "blue" }}>{a.designation}</h5>
                        </h3>
                      </Col>
                      <Col lg="7">
                        {a.levelDetails.map((b, index1) => (
                          <>
                            <Button
                              className="btn btn-sm btn-warning"
                              onClick={handleOpenModal}
                            >
                              Level {index1 + 1} <br />
                              {index1 === 0
                                ? "प्रथम मतांकन"
                                : index1 === 1
                                  ? "द्वितीय मतांकन"
                                  : "अंतिम मतांकन"}
                              <br />
                              {index1 === 0 ? b.employeeOption : b.selectOption}
                              <br />
                              {b.empCode}
                            </Button>
                          </>
                        ))}
                        <br />
                      </Col>
                    </Row>
                  </CardHeader>
                </Card>
              </>
            ))}
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={toggleShowMappingModal}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default DirectorateACRMapping;
