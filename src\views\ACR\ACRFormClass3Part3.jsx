import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Label,
  Button,
  Input,
  Table,
} from "reactstrap";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Swal from "sweetalert2";
import axios from "axios";

const ACRFormClass3Part3 = ({ employee }) => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;

  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);
  const [formData, setFormData] = useState({
    applicationId: employee[0].basicDetails[0].applicationId,
    employeeId: employee[0].basicDetails[0].employeeId,
    employeeName: employee[0].basicDetails[0].employeeName,
    collegeName: employee[0].basicDetails[0].collegeName,
    collegeId: employee[0].basicDetails[0].collegeId,
    shreniKaran1: "",
    remark1: "",
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.shreniKaran1)
      newErrors.shreniKaran1 = "This field is required.";
    if (!formData.remark1) newErrors.remark1 = "This field is required.";

    return newErrors;
  };

  const handleFinalSubmit = async (e) => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        e.target.disabled = true;

        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("द्वितीय मतांकन सफल हुआ", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  return (
    <>
      <Row>
        <Col>
          <Card className="shadow pb-4">
            <CardHeader
              className="bg-white border-0"
              style={{ textAlign: "center" }}
            >
              <h2 className="mb-0">प्रपत्र --- दो</h2>
              {employee[0].employeeDetails.classDetails.className ===
                "Class 3" ? (
                <>
                  <h3 className="mb-0">
                    तृतीय श्रेणी एवं अन्य लिपिक वर्गीय कर्मचारी के गोपनीय
                    प्रतिवेदन लिखे जाने का प्रपत्र 31 मार्च, {lastYearDate} को
                    समाप्त होने वाले वर्ष के लिए |
                  </h3>
                </>
              ) : (
                <>
                  <h3 className="mb-0">
                    चतुर्थ श्रेणी के शासकीय कर्मचारियों के संबंध में प्रतिवर्ष
                    माह अप्रैल के प्रथम सप्ताह में लिखी जाने वाली चरित्र पंजी का
                    फार्म 31 मार्च {lastYearDate}
                    को समाप्त होने वाले वर्ष के लिये
                  </h3>
                </>
              )}
            </CardHeader>
            <CardBody>
              <div className="mb-4">
                <Row className="mb-3">
                  <Table>
                    <tbody>
                      <tr>
                        <h2 style={{ textAlign: "center" }}>प्रतिवेदक</h2>
                      </tr>
                      {employee[0].employeeDetails.classDetails.className ===
                        "Class 3" ? (
                        <>
                          <tr>
                            <td>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>पूरा नाम श्री/श्रीमती / कुमारी</Label>
                                  <Input
                                    type="text"
                                    name="employeeName"
                                    value={formData.employeeName}
                                    readOnly
                                    className="form-control"
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पिता अथवा पति का नाम </Label>
                                  <Input
                                    type="text"
                                    name="fatherName"
                                    value={
                                      employee[0].basicDetails[0].fatherName
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.fatherName && (
                                    <small className="text-danger">
                                      {errors.fatherName}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>जन्म तिथि</Label>
                                  <Input
                                    type="date"
                                    name="dateofBirth"
                                    value={
                                      employee[0].basicDetails[0].dateofBirth
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.dateofBirth && (
                                    <small className="text-danger">
                                      {errors.dateofBirth}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>
                                    धारित पदनाम (मूल / स्थानापन्न / अस्थायी)
                                    वर्तमान
                                  </Label>
                                  <Input
                                    type="text"
                                    name="designation"
                                    value={
                                      employee[0].basicDetails[0].designation
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                </Col>
                              </Row>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>महाविद्यालय का नाम</Label>
                                  <Input
                                    type="text"
                                    name="collegeName"
                                    value={
                                      employee[0].basicDetails[0].collegeName
                                    } // Use employee data here
                                    readOnly
                                    className="form-control"
                                    onChange={handleInputChange}
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>वरिष्ठता सूची क्रमांक </Label>
                                  <Input
                                    type="number"
                                    name="varishtSuchiNumber"
                                    value={
                                      employee[0].basicDetails[0]
                                        .varishtSuchiNumber
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.varishtSuchiNumber && (
                                    <small className="text-danger">
                                      {errors.varishtSuchiNumber}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>प्रथम नियमित नियुक्ति दिनांक</Label>
                                  <Input
                                    type="date"
                                    name="firstNiyamitNiyuktiDate"
                                    value={
                                      employee[0].basicDetails[0]
                                        .firstNiyamitNiyuktiDate
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.firstNiyamitNiyuktiDate && (
                                    <small className="text-danger">
                                      {errors.firstNiyamitNiyuktiDate}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>
                                    वर्तमान पद पर पदोन्नति दिनांक (यदि हो तो)
                                  </Label>
                                  <Input
                                    type="date"
                                    name="currentPadonatiDate"
                                    value={
                                      employee[0].basicDetails[0]
                                        .currentPadonatiDate
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.currentPadonatiDate && (
                                    <small className="text-danger">
                                      {errors.currentPadonatiDate}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-4">
                                <Col md="3">
                                  <Label>वेतन</Label>
                                  <Input
                                    type="number"
                                    name="salary"
                                    value={employee[0].basicDetails[0].currentSalary} // Use employee data here
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                </Col>
                                <Col md="9">
                                  <Label>कर्तव्यों का संक्षिप्त विवरण </Label>
                                  <Input
                                    type="textarea"
                                    name="kartavyaKaVivran"
                                    value={
                                      employee[0].basicDetails[0]
                                        .kartavyaKaVivran
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.kartavyaKaVivran && (
                                    <small className="text-danger">
                                      {errors.kartavyaKaVivran}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                            </td>
                          </tr>
                        </>
                      ) : (
                        <>
                          <tr>
                            <td>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>कर्मचारी का नाम</Label>
                                  <Input
                                    type="text"
                                    name="employeeName"
                                    value={
                                      employee[0].basicDetails[0].employeeName
                                    }
                                    readOnly
                                    className="form-control"
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पिता अथवा पति का नाम </Label>
                                  <Input
                                    type="text"
                                    name="fatherName"
                                    value={
                                      employee[0].basicDetails[0].fatherName
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.fatherName && (
                                    <small className="text-danger">
                                      {errors.fatherName}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>जन्म तिथि</Label>
                                  <Input
                                    type="date"
                                    name="dateofBirth"
                                    value={
                                      employee[0].basicDetails[0].dateofBirth
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.dateofBirth && (
                                    <small className="text-danger">
                                      {errors.dateofBirth}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>शैक्षणिक योग्यता, यदि कोई हो</Label>
                                  <Input
                                    type="text"
                                    name="shenikYogyata"
                                    value={
                                      employee[0].basicDetails[0].shenikYogyata
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.shenikYogyata && (
                                    <small className="text-danger">
                                      {errors.shenikYogyata}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>वरिष्ठता सूची क्रमांक </Label>
                                  <Input
                                    type="number"
                                    name="varishtSuchiNumber"
                                    value={
                                      employee[0].basicDetails[0]
                                        .varishtSuchiNumber
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.varishtSuchiNumber && (
                                    <small className="text-danger">
                                      {errors.varishtSuchiNumber}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>वेतन</Label>
                                  <Input
                                    type="number"
                                    name="salary"
                                    value={employee[0].basicDetails[0].currentSalary} // Use employee data here
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पदनाम / स्थायी / अस्थायी </Label>
                                  <Input
                                    type="text"
                                    name="designation"
                                    value={
                                      employee[0].basicDetails[0].designation
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.kartavyaKaVivran && (
                                    <small className="text-danger">
                                      {errors.kartavyaKaVivran}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>नियमित नियुक्ति की तिथि </Label>
                                  <Input
                                    type="date"
                                    name="niyamitNiyukti"
                                    value={
                                      employee[0].basicDetails[0].niyamitNiyukti
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.niyamitNiyukti && (
                                    <small className="text-danger">
                                      {errors.niyamitNiyukti}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-4">
                                <Col md="6">
                                  <Label>महाविद्यालय का नाम</Label>
                                  <Input
                                    type="text"
                                    name="collegeName"
                                    value={
                                      employee[0].basicDetails[0].collegeName
                                    } // Use employee data here
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                </Col>
                                <Col md="6">
                                  <Label>
                                    अवधि जिसके लिए मत अंकित किया जा रहा है{" "}
                                  </Label>
                                  <Input
                                    type="textarea"
                                    name="avadhiAnkit"
                                    value={
                                      employee[0].basicDetails[0].avadhiAnkit
                                    }
                                    className="form-control"
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  {errors.avadhiAnkit && (
                                    <small className="text-danger">
                                      {errors.avadhiAnkit}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                            </td>
                          </tr>
                        </>
                      )}
                    </tbody>
                  </Table>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <h2 style={{ textAlign: "center" }}>प्रथम मतांकन</h2>
                  </Col>
                </Row>
                {employee[0].employeeDetails.classDetails.className ===
                  "Class 3" ? (
                  <>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>व्यक्तित्व एवं व्यवहार</Label>
                        <Input
                          type="textarea"
                          name="vektiVehwar"
                          value={employee[0].levelDetails[0].data.vektiVehwar}
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.vektiVehwar && (
                          <small className="text-danger">
                            {errors.vektiVehwar}
                          </small>
                        )}
                      </Col>
                      <Col md="6">
                        <Label>आचरण / चरित्र </Label>
                        <Input
                          type="textarea"
                          name="acharanCharitra"
                          value={
                            employee[0].levelDetails[0].data.acharanCharitra
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.acharanCharitra && (
                          <small className="text-danger">
                            {errors.acharanCharitra}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>प्रारूप और टीप लिखने की योग्यता</Label>
                        <Input
                          type="textarea"
                          name="prarupTip"
                          value={employee[0].levelDetails[0].data.prarupTip}
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.prarupTip && (
                          <small className="text-danger">
                            {errors.prarupTip}
                          </small>
                        )}
                      </Col>

                      <Col md="6">
                        <Label>
                          कार्यालय प्रक्रिया और नियमों का ज्ञान तथा प्रयास करने
                          की योग्यता
                        </Label>
                        <Input
                          type="textarea"
                          name="karyalayaPrakriya"
                          value={
                            employee[0].levelDetails[0].data.karyalayaPrakriya
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.karyalayaPrakriya && (
                          <small className="text-danger">
                            {errors.karyalayaPrakriya}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>प्रकरण के परीक्षण की क्षमता</Label>
                        <Input
                          type="textarea"
                          name="prakrankiParikhsha"
                          value={
                            employee[0].levelDetails[0].data.prakrankiParikhsha
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.prakrankiParikhsha && (
                          <small className="text-danger">
                            {errors.prakrankiParikhsha}
                          </small>
                        )}
                      </Col>
                      <Col md="6">
                        <Label>कार्य के निपटारे की तत्परता</Label>
                        <Input
                          type="textarea"
                          name="karyakeNiptare"
                          value={
                            employee[0].levelDetails[0].data.karyakeNiptare
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.karyakeNiptare && (
                          <small className="text-danger">
                            {errors.karyakeNiptare}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="6">
                        <Label>उपस्थिति की नियमितता और समय की पाबंदी</Label>
                        <Input
                          type="textarea"
                          name="samaykiPabandi"
                          value={
                            employee[0].levelDetails[0].data.samaykiPabandi
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.samaykiPabandi && (
                          <small className="text-danger">
                            {errors.samaykiPabandi}
                          </small>
                        )}
                      </Col>
                      <Col md="6">
                        <Label>उच्च अधिकारियों एवं सहभागियों से संबंध</Label>
                        <Input
                          type="textarea"
                          name="sahbhagiyonSeSambhand"
                          value={
                            employee[0].levelDetails[0].data
                              .sahbhagiyonSeSambhand
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.sahbhagiyonSeSambhand && (
                          <small className="text-danger">
                            {errors.sahbhagiyonSeSambhand}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="6">
                        <Label>
                          नित्य कार्य जैसे—असिस्टेंट की डायरी का रख-रखाव, गार्ड
                          फाइलें, रिकार्डिंग आदि का ध्यान रखा जाना
                        </Label>
                        <Input
                          type="textarea"
                          name="nityaKaryaJaise"
                          value={
                            employee[0].levelDetails[0].data.nityaKaryaJaise
                          } // Use employee data here
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.nityaKaryaJaise && (
                          <small className="text-danger">
                            {errors.nityaKaryaJaise}
                          </small>
                        )}
                      </Col>

                      <Col md="6">
                        <Label>सनिष्ठा</Label>
                        <Input
                          type="textarea"
                          name="sanishth"
                          value={employee[0].levelDetails[0].data.sanishth}
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.sanishth && (
                          <small className="text-danger">
                            {errors.sanishth}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="6">
                        <Label>
                          कर्मचारी द्वारा यदि कोई असाधारण या उल्लेखनीय कार्य
                          किया गया हो तो वह संक्षेप में बतावें ।
                        </Label>
                        <Input
                          type="textarea"
                          name="asadharanOrUlekhNiya"
                          value={
                            employee[0].levelDetails[0].data
                              .asadharanOrUlekhNiya
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.asadharanOrUlekhNiya && (
                          <small className="text-danger">
                            {errors.asadharanOrUlekhNiya}
                          </small>
                        )}
                      </Col>
                      <Col md="6">
                        <Label>पदोन्नति की उपयुक्तता</Label>
                        <Input
                          type="textarea"
                          name="padonatiKiUpyukta"
                          value={
                            employee[0].levelDetails[0].data.padonatiKiUpyukta
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.padonatiKiUpyukta && (
                          <small className="text-danger">
                            {errors.padonatiKiUpyukta}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="6">
                        <Label>श्रेणीकरण</Label>
                        <Input
                          name="shreniKaran"
                          type="select"
                          id="recordsPerPage"
                          value={employee[0].levelDetails[0].data.shreniKaran}
                          onChange={handleInputChange}
                          readOnly
                        >
                          <option value="उत्कृष्ट">उत्कृष्ट</option>
                          <option value="बहुत अच्छा">बहुत अच्छा</option>
                          <option value="अच्छा">अच्छा</option>
                          <option value="साधारण">साधारण</option>
                          <option value="घटिया">घटिया</option>
                        </Input>
                        {errors.shreniKaran && (
                          <small className="text-danger">
                            {errors.shreniKaran}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="12">
                        <Label>रिमार्क</Label>
                        <Input
                          name="remark"
                          type="textarea"
                          value={employee[0].levelDetails[0].data.remark}
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.remark && (
                          <small className="text-danger">{errors.remark}</small>
                        )}
                      </Col>
                    </Row>
                  </>
                ) : (
                  <>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>आचरण / व्यवहार तथा आज्ञाकारिता</Label>
                        <Input
                          type="textarea"
                          name="acharanVevhar"
                          maxLength={150}
                          value={employee[0].levelDetails[0].data.acharanVevhar}
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.acharanVevhar && (
                          <small className="text-danger">
                            {errors.acharanVevhar}
                          </small>
                        )}
                      </Col>
                      <Col md="6">
                        <Label>समय की पाबंदी </Label>
                        <Input
                          type="textarea"
                          name="samayKiPabandi"
                          maxLength={150}
                          value={
                            employee[0].levelDetails[0].data.samayKiPabandi
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.samayKiPabandi && (
                          <small className="text-danger">
                            {errors.samayKiPabandi}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>शारीरिक क्षमता</Label>
                        <Input
                          type="textarea"
                          name="sharirikShamta"
                          value={
                            employee[0].levelDetails[0].data.sharirikShamta
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.sharirikShamta && (
                          <small className="text-danger">
                            {errors.sharirikShamta}
                          </small>
                        )}
                      </Col>

                      <Col md="6">
                        <Label>सौपे गये कार्य को करने की समझ और योग्यता</Label>
                        <Input
                          type="textarea"
                          name="karyaKiYogyata"
                          value={
                            employee[0].levelDetails[0].data.karyaKiYogyata
                          }
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.karyaKiYogyata && (
                          <small className="text-danger">
                            {errors.karyaKiYogyata}
                          </small>
                        )}
                      </Col>
                    </Row>
                    <Row className="mb-3">
                      <Col md="6">
                        <Label>
                          स्थानांतरण, दण्ड आदि के संबंध में सामान्य मत
                        </Label>
                        <Input
                          type="textarea"
                          name="stanantaran"
                          value={employee[0].levelDetails[0].data.stanantaran}
                          className="form-control"
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.stanantaran && (
                          <small className="text-danger">
                            {errors.stanantaran}
                          </small>
                        )}
                      </Col>
                  
                      <Col md="6">
                        <Label>श्रेणीकरण</Label>
                        <Input
                          name="shreniKaran"
                          type="select"
                          id="recordsPerPage"
                          value={employee[0].levelDetails[0].data.shreniKaran}
                          onChange={handleInputChange}
                          readOnly
                        >
                          <option value="">चुने</option>
                          <option value="उत्कृष्ट">उत्कृष्ट</option>
                          <option value="बहुत अच्छा">बहुत अच्छा</option>
                          <option value="अच्छा">अच्छा</option>
                          <option value="साधारण">साधारण</option>
                          <option value="घटिया">घटिया</option>
                        </Input>
                        {errors.shreniKaran && (
                          <small className="text-danger">
                            {errors.shreniKaran}
                          </small>
                        )}
                      </Col>
                    </Row>

                    <Row className="mb-3">
                      <Col md="12">
                        <Label>रिमार्क</Label>
                        <Input
                          name="remark"
                          type="textarea"
                          style={{height:"100px"}}
                          value={employee[0].levelDetails[0].data.remark}
                          onChange={handleInputChange}
                          readOnly
                        />
                        {errors.remark && (
                          <small className="text-danger">{errors.remark}</small>
                        )}
                      </Col>
                    </Row>
                  </>
                )}

                <Row className="mb-3">
                  <Col>
                    <h2 style={{ textAlign: "center" }}>
                      समीक्षक अधिकारी की टिप्पणी
                    </h2>
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col md="3">
                    <Label>श्रेणीकरण</Label>
                    <Input
                      name="shreniKaran1"
                      type="select"
                      id="recordsPerPage"
                      value={formData.shreniKaran1}
                      onChange={handleInputChange}
                    >
                      <option value="">चुनें</option>
                      <option value="उत्कृष्ट">उत्कृष्ट</option>
                      <option value="बहुत अच्छा">बहुत अच्छा</option>
                      <option value="अच्छा">अच्छा</option>
                      <option value="साधारण">साधारण</option>
                      <option value="घटिया">घटिया</option>
                    </Input>
                    {errors.shreniKaran1 && (
                      <small className="text-danger">
                        {errors.shreniKaran1}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="12">
                    <Label>रिमार्क</Label>
                    <Input
                      name="remark1"
                      type="textarea"
                      maxLength={500}
                      style={{ height: "100px" }} // Set the height here
                      value={formData.remark1}
                      onChange={handleInputChange}
                    />
                    {errors.remark1 && (
                      <small className="text-danger">{errors.remark1}</small>
                    )}
                  </Col>
                </Row>
                <Button color="success" onClick={handleFinalSubmit}>
                  Submit
                </Button>
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row>
    </>
  );
};

ACRFormClass3Part3.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormClass3Part3;
