import React, { useState } from "react";
import { <PERSON>, Col, Card, CardHeader, CardBody, Container } from "reactstrap";
import { Button } from "react-bootstrap";
const DynamicPieChart = () => {
  const [data, setData] = useState([
    { name: "Group A", value: 40, color: "#0088FE" },
    { name: "Group B", value: 30, color: "#00C49F" },
    { name: "Group C", value: 20, color: "#FFBB28" },
    { name: "Group D", value: 10, color: "#FF8042" },
  ]);

  const [transitionData, setTransitionData] = useState(data);

  // Calculate the total value
  const totalValue = transitionData.reduce((acc, item) => acc + item.value, 0);

  // Generate Pie Chart Paths with Labels
  const generateChartPaths = () => {
    let cumulativeValue = 0;
    return transitionData.map((item, index) => {
      const startAngle = (cumulativeValue / totalValue) * 2 * Math.PI;
      const endAngle =
        ((cumulativeValue + item.value) / totalValue) * 2 * Math.PI;

      cumulativeValue += item.value;

      const x1 = Math.cos(startAngle) * 100;
      const y1 = Math.sin(startAngle) * 100;
      const x2 = Math.cos(endAngle) * 100;
      const y2 = Math.sin(endAngle) * 100;

      const largeArcFlag = endAngle - startAngle > Math.PI ? 1 : 0;

      // Calculate label position
      const labelAngle = (startAngle + endAngle) / 2;
      const labelX = Math.cos(labelAngle) * 60; // Position slightly inward
      const labelY = Math.sin(labelAngle) * 60;

      return (
        <g key={item.name}>
          {/* Pie slice */}
          <path
            d={`M 0 0 L ${x1} ${y1} A 100 100 0 ${largeArcFlag} 1 ${x2} ${y2} Z`}
            fill={item.color}
            style={{
              transition: "d 0.5s ease-out",
            }}
          />
          {/* Label */}
          <text
            x={labelX}
            y={labelY}
            textAnchor="middle"
            dominantBaseline="middle"
            fill="white"
            fontSize="10"
            fontWeight="bold"
          >
            {item.value.toFixed(2)}%
          </text>
        </g>
      );
    });
  };

  const handleUpdate = () => {
    const newData = [
      { name: "Group A", value: Math.random() * 100, color: "#0088FE" },
      { name: "Group B", value: Math.random() * 100, color: "#00C49F" },
      { name: "Group C", value: Math.random() * 100, color: "#FFBB28" },
      { name: "Group D", value: Math.random() * 100, color: "#FF8042" },
    ];
    setData(newData);

    // Delay transition to let animation render smoothly
    setTimeout(() => setTransitionData(newData), 0);
  };

  return (
      <Container className="mt--7" fluid>
        <Row>
          <Col sm="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h2 className="text-xl font-bold mb-4">
                      Attendance Yesterday Chart{" "}
                      <Button
                        className="btn btn-waring btn-sm"
                        onClick={handleUpdate}
                      >
                        Update Data
                      </Button>
                    </h2>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <svg
                  viewBox="-120 -120 240 240"
                  className="w-64 h-64"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {generateChartPaths()}
                </svg>
              </CardBody>
            </Card>
          </Col>
          <Col sm="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h2 className="text-xl font-bold mb-4">
                      Attendance Today Chart{" "}
                      <Button
                        className="btn btn-waring btn-sm"
                        onClick={handleUpdate}
                      >
                        Update Data
                      </Button>
                    </h2>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <svg
                  viewBox="-120 -120 240 240"
                  className="w-64 h-64"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  {generateChartPaths()}
                </svg>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
  );
};

export default DynamicPieChart;
