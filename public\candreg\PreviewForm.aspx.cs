﻿using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class candreg_PreviewForm : System.Web.UI.Page
{
    MyDbOps dbops = new MyDbOps();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["userID"] == null)
        {
            Response.Redirect("~//candreg_login.aspx");
        }
        else
        {
            if (!IsPostBack)
            {
                Txtmobileno.Text = Session["userID"].ToString();
                bindgender();
                bindcaste();
                bindstate();
                FillPermanentDistrict();
                dvhandicapetype.Visible = false;
                dvcategory.Visible = false;
                dvagevalid.Visible = false;
                GetData();
            }
        }
    }
    #region Methods
    public void GetData()
    {
        int id = 0;
        if (Request.QueryString["Id"] != null)
        {
            id = int.Parse(Request.QueryString["Id"]);
        }
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select RegistrationNo,First_nameH,Middle_nameH,Last_NameH,First_nameE,Middle_nameE,Last_NameE,
GenderID,DOB,age,Relative_name,Relative_nameE,Mother_name,Mother_nameE,CasteID,Email,StateCode,DistrictCCode,Address,PinCode,
Per_StateCode,Per_DistrictCCode,Per_Address,Per_Pincode,Photopath,Signpath,Marksheet10Path,Marksheet5Path,CastCertificatePath,
IsGovEmp,IsServiceEmp,maritalstatus,IsDisabled,DisabledType,attendantpost,otherpost,servantpriority,watchmanpriority,
sweeperpriority,is_intercastemarriagepromotion,is_govawards,is_defenceservices,is_bonafidecand,is_certifiedindian,
is_sepereated,is_excluded,is_terminated,is_guilty,dist_preferencecode,castesrno,castedist,castename,castedate
  FROM tbl_CandidateMaster where Id=@id";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@id", SqlDbType.Int).Value = id;
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                txtfnameh.Text = dtShops.Rows[0]["First_nameH"].ToString();
                txtmnameh.Text = dtShops.Rows[0]["Middle_nameH"].ToString();
                txtlnameh.Text = dtShops.Rows[0]["Last_NameH"].ToString();
                txtfnameE.Text = dtShops.Rows[0]["First_nameE"].ToString();
                txtMnameE.Text = dtShops.Rows[0]["Middle_nameE"].ToString();
                txtlnameE.Text = dtShops.Rows[0]["Last_NameE"].ToString();
                Txtrelativename.Text = dtShops.Rows[0]["Relative_name"].ToString();
                TxtrelativenameE.Text = dtShops.Rows[0]["Relative_nameE"].ToString();
                Txtmothername.Text = dtShops.Rows[0]["Mother_name"].ToString();
                TxtmothernameE.Text = dtShops.Rows[0]["Mother_nameE"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Photopath"].ToString()))
                {
                    a_photoUplodad.Visible = true;
                    string str = string.Empty;
                    if (dtShops.Rows[0]["Photopath"].ToString().Contains("candregfile/"))
                    {
                        str = dtShops.Rows[0]["Photopath"].ToString().Replace("candregfile/", "");
                    }
                    else if (dtShops.Rows[0]["Photopath"].ToString().Contains("candreg/"))
                    {
                        str = dtShops.Rows[0]["Photopath"].ToString().Replace("candreg/", "");
                    }
                    else
                    {
                        str = dtShops.Rows[0]["Photopath"].ToString();
                    }
                    a_photoUplodad.NavigateUrl = str;
                    Hiddenphoto.Value = dtShops.Rows[0]["Photopath"].ToString();
                }
                else
                {
                    a_photoUplodad.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Signpath"].ToString()))
                {
                    a_SignUpload.Visible = true;
                    string str = string.Empty;
                    if (dtShops.Rows[0]["Signpath"].ToString().Contains("candregfile/"))
                    {
                        str = dtShops.Rows[0]["Signpath"].ToString().Replace("candregfile/", "");
                    }
                    else if (dtShops.Rows[0]["Signpath"].ToString().Contains("candreg/"))
                    {
                        str = dtShops.Rows[0]["Signpath"].ToString().Replace("candreg/", "");
                    }
                    else
                    {
                        str = dtShops.Rows[0]["Photopath"].ToString();
                    }
                    a_SignUpload.NavigateUrl = str;
                    Hiddensign.Value = dtShops.Rows[0]["Signpath"].ToString();
                }
                else
                {
                    a_SignUpload.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["StateCode"].ToString()))
                {
                    ddlstate.SelectedValue = dtShops.Rows[0]["StateCode"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DistrictCCode"].ToString()))
                {
                    ddldistrict.SelectedValue = dtShops.Rows[0]["DistrictCCode"].ToString();
                }
                txtaddress.Text = dtShops.Rows[0]["Address"].ToString();
                Txtpincode.Text = dtShops.Rows[0]["PinCode"].ToString();
                //Chkboxaddress.Text = dtShops.Rows[0]["First_nameH"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Per_StateCode"].ToString()))
                {
                    perddlstate.SelectedValue = dtShops.Rows[0]["Per_StateCode"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Per_DistrictCCode"].ToString()))
                {
                    perddldistrict.SelectedValue = dtShops.Rows[0]["Per_DistrictCCode"].ToString();
                }
                pertxtadd.Text = dtShops.Rows[0]["Per_Address"].ToString();
                pertxtpincode.Text = dtShops.Rows[0]["Per_Pincode"].ToString();
                if (dtShops.Rows[0]["IsGovEmp"].ToString() == "Y")
                {
                    chkgov.Checked = true;
                }
                if (dtShops.Rows[0]["IsServiceEmp"].ToString() == "Y")
                {
                    chkservice.Checked = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["CasteID"].ToString()))
                {
                    ddlcategory.SelectedValue = dtShops.Rows[0]["CasteID"].ToString();
                    if (dtShops.Rows[0]["CasteID"].ToString() != "1")
                    {
                        dvcategory.Visible = true;
                    }
                    else
                    {
                        dvcategory.Visible = false;
                    }
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["maritalstatus"].ToString()))
                {
                    ddlmarriedstatus.SelectedValue = dtShops.Rows[0]["maritalstatus"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["GenderID"].ToString()))
                {
                    ddlgender.SelectedValue = dtShops.Rows[0]["GenderID"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DOB"].ToString()))
                {
                    txtdob.Text = Convert.ToDateTime(dtShops.Rows[0]["DOB"]).ToString("yyyy-MM-dd");
                }
                txtage.Text = dtShops.Rows[0]["age"].ToString();
                if (!string.IsNullOrEmpty(txtage.Text.Trim()))
                {
                    validateage(int.Parse(txtage.Text.Trim()));
                }
                txtcastesrno.Text = dtShops.Rows[0]["castesrno"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["castedist"].ToString()))
                {
                    ddlcastedist.SelectedValue = dtShops.Rows[0]["castedist"].ToString();
                }
                txtcastename.Text = dtShops.Rows[0]["castename"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["castedate"].ToString()))
                {
                    txtcastedate.Text = Convert.ToDateTime(dtShops.Rows[0]["castedate"]).ToString("yyyy-MM-dd");
                }
                Txtmobileno.Text = dtShops.Rows[0]["RegistrationNo"].ToString();
                Textemail.Text = dtShops.Rows[0]["Email"].ToString();
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["IsDisabled"].ToString()))
                {
                    ddlhandicape.SelectedValue = dtShops.Rows[0]["IsDisabled"].ToString();
                    if (dtShops.Rows[0]["IsDisabled"].ToString() == "Y")
                    {
                        dvhandicapetype.Visible = true;
                    }
                    else
                    {
                        dvhandicapetype.Visible = false;
                    }
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["DisabledType"].ToString()))
                {
                    ddlhandicapetype.SelectedValue = dtShops.Rows[0]["DisabledType"].ToString();
                }
                if (dtShops.Rows[0]["attendantpost"].ToString() == "Y")
                {
                    chklabattendant.Checked = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Marksheet10Path"].ToString()))
                {
                    a_file10thmarksheet.Visible = true;
                    string str = string.Empty;
                    if (dtShops.Rows[0]["Marksheet10Path"].ToString().Contains("candregfile/"))
                    {
                        str = dtShops.Rows[0]["Marksheet10Path"].ToString().Replace("candregfile/", "");
                    }
                    else if (dtShops.Rows[0]["Marksheet10Path"].ToString().Contains("candreg/"))
                    {
                        str = dtShops.Rows[0]["Marksheet10Path"].ToString().Replace("candreg/", "");
                    }
                    else
                    {
                        str = dtShops.Rows[0]["Marksheet10Path"].ToString();
                    }
                    a_file10thmarksheet.NavigateUrl = str;
                    HiddenMarksheet.Value = dtShops.Rows[0]["Marksheet10Path"].ToString();
                }
                else
                {
                    a_file10thmarksheet.Visible = false;
                }
                if (dtShops.Rows[0]["otherpost"].ToString() == "Y")
                {
                    chkpost.Checked = true;
                    dvpriority.Visible = true;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["Marksheet5Path"].ToString()))
                {
                    a_file5thmarksheet.Visible = true;
                    string str = string.Empty;
                    if (dtShops.Rows[0]["Marksheet5Path"].ToString().Contains("candregfile/"))
                    {
                        str = dtShops.Rows[0]["Marksheet5Path"].ToString().Replace("candregfile/", "");
                    }
                    else if (dtShops.Rows[0]["Marksheet5Path"].ToString().Contains("candreg/"))
                    {
                        str = dtShops.Rows[0]["Marksheet5Path"].ToString().Replace("candreg/", "");
                    }
                    else
                    {
                        str = dtShops.Rows[0]["Marksheet5Path"].ToString();
                    }
                    a_file5thmarksheet.NavigateUrl = str;
                    HiddenMarksheet5th.Value = dtShops.Rows[0]["Marksheet5Path"].ToString();
                }
                else
                {
                    a_file5thmarksheet.Visible = false;
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["servantpriority"].ToString()))
                {
                    ddlservantpriority.SelectedValue = dtShops.Rows[0]["servantpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["watchmanpriority"].ToString()))
                {
                    ddlwatchmanpriority.SelectedValue = dtShops.Rows[0]["watchmanpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["sweeperpriority"].ToString()))
                {
                    ddlsweeperpriority.SelectedValue = dtShops.Rows[0]["sweeperpriority"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_intercastemarriagepromotion"].ToString()))
                {
                    rdnis_intercastemarriagepromotion.SelectedValue = dtShops.Rows[0]["is_intercastemarriagepromotion"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_govawards"].ToString()))
                {
                    rdnis_govawards.SelectedValue = dtShops.Rows[0]["is_govawards"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_defenceservices"].ToString()))
                {
                    rdnis_defenceservices.SelectedValue = dtShops.Rows[0]["is_defenceservices"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_bonafidecand"].ToString()))
                {
                    rdnis_bonafidecand.SelectedValue = dtShops.Rows[0]["is_bonafidecand"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_certifiedindian"].ToString()))
                {
                    rdnis_certifiedindian.SelectedValue = dtShops.Rows[0]["is_certifiedindian"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_sepereated"].ToString()))
                {
                    rdnis_sepereated.SelectedValue = dtShops.Rows[0]["is_sepereated"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_excluded"].ToString()))
                {
                    rdnis_excluded.SelectedValue = dtShops.Rows[0]["is_excluded"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_terminated"].ToString()))
                {
                    rdnis_terminated.SelectedValue = dtShops.Rows[0]["is_terminated"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["is_guilty"].ToString()))
                {
                    rdnis_guilty.SelectedValue = dtShops.Rows[0]["is_guilty"].ToString();
                }
                if (!string.IsNullOrEmpty(dtShops.Rows[0]["dist_preferencecode"].ToString()))
                {
                    ddlpreference.SelectedValue = dtShops.Rows[0]["dist_preferencecode"].ToString();
                }
            }
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindgender()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select GenderID,GenderNameHin from GenderMaster";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlgender.DataSource = dtShops;
                ddlgender.DataTextField = "GenderNameHin";
                ddlgender.DataValueField = "GenderID";
                ddlgender.DataBind();
            }
            ddlgender.Items.Insert(0, new ListItem("--चुने--", "0"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindcaste()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select CasteID,CasteNameHin from casteMaster";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlcategory.DataSource = dtShops;
                ddlcategory.DataTextField = "CasteNameHin";
                ddlcategory.DataValueField = "CasteID";
                ddlcategory.DataBind();
            }
            ddlcategory.Items.Insert(0, new ListItem("--चुने--", "0"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindstate()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select StateCode,NameHin from tStateView  order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);

            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddlstate.DataSource = dtShops;
                ddlstate.DataTextField = "NameHin";
                ddlstate.DataValueField = "StateCode";
                ddlstate.DataBind();
                perddlstate.DataSource = dtShops;
                perddlstate.DataTextField = "NameHin";
                perddlstate.DataValueField = "StateCode";
                perddlstate.DataBind();
            }
            ddlstate.SelectedValue = "22";
            perddlstate.SelectedValue = "22";
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void binddistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", ddlstate.SelectedValue);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddldistrict.DataSource = dtShops;
                ddldistrict.DataTextField = "NameHin";
                ddldistrict.DataValueField = "LGDDistCode";
                ddldistrict.DataBind();
            }
            ddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddldistrict.Items.Remove(ddldistrict.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void bindperdistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", ddlstate.SelectedValue);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                perddldistrict.DataSource = dtShops;
                perddldistrict.DataTextField = "NameHin";
                perddldistrict.DataValueField = "LGDDistCode";
                perddldistrict.DataBind();
            }
            perddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            perddldistrict.Items.Remove(perddldistrict.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void FillPermanentDistrict()
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select LGDDistCode,NameHin from TDistrict where StateCode=@ddlstate order by NameHin";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@ddlstate", "22");
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                ddldistrict.DataSource = dtShops;
                ddldistrict.DataTextField = "NameHin";
                ddldistrict.DataValueField = "LGDDistCode";
                ddldistrict.DataBind();
                perddldistrict.DataSource = dtShops;
                perddldistrict.DataTextField = "NameHin";
                perddldistrict.DataValueField = "LGDDistCode";
                perddldistrict.DataBind();
                ddlcastedist.DataSource = dtShops;
                ddlcastedist.DataTextField = "NameHin";
                ddlcastedist.DataValueField = "LGDDistCode";
                ddlcastedist.DataBind();
                ddlpreference.DataSource = dtShops;
                ddlpreference.DataTextField = "NameHin";
                ddlpreference.DataValueField = "LGDDistCode";
                ddlpreference.DataBind();
            }
            ddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            perddldistrict.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddlcastedist.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddlpreference.Items.Insert(0, new ListItem("--चुने--", "0"));
            ddldistrict.Items.Remove(ddldistrict.Items.FindByValue("-1"));
            perddldistrict.Items.Remove(perddldistrict.Items.FindByValue("-1"));
            ddlcastedist.Items.Remove(ddlcastedist.Items.FindByValue("-1"));
            ddlpreference.Items.Remove(ddlpreference.Items.FindByValue("-1"));
            conn.Close();
            dtShops.Dispose();
        }
    }

    #endregion
    protected void ddlstate_SelectedIndexChanged(object sender, EventArgs e)
    {
        binddistrict();
    }

    protected void perddlstate_SelectedIndexChanged(object sender, EventArgs e)
    {
        bindperdistrict();
    }

    protected void ddlhandicape_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlhandicape.SelectedValue == "Y")
        {
            dvhandicapetype.Visible = true;
        }
        else
        {
            dvhandicapetype.Visible = false;
        }
    }
    public int CalculateAgeCorrect(DateTime birthDate, DateTime now)
    {
        int age = now.Year - birthDate.Year;

        if (now.Month < birthDate.Month || (now.Month == birthDate.Month && now.Day < birthDate.Day))
            age--;

        return age;
    }
    protected void txtdob_TextChanged(object sender, EventArgs e)
    {
        if (ddlcategory.SelectedValue == "0")
        {
            string jv = "<script>alert('Select Category!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlcategory.Focus();
            ddlcategory.BackColor = Color.Pink;
            return;
        }
        if (ddlgender.SelectedValue == "0")
        {
            string jv = "<script>alert('Select Gender!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            return;
        }
        if (txtdob.Text.Trim() == "")
        {
            string jv = "<script>alert('Enter DOB!');</script>";
            ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            ddlgender.Focus();
            ddlgender.BackColor = Color.Pink;
            return;
        }
        DateTime now = new DateTime(2023, 1, 1);
        int age = CalculateAgeCorrect(DateTime.Parse(txtdob.Text), now);
        txtage.Text = age.ToString();
        validateage(age);
    }
    protected void ddlcategory_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlcategory.SelectedValue != "1" && ddlcategory.SelectedValue != "0")
        {
            dvcategory.Visible = true;
        }
        else
        {
            dvcategory.Visible = false;
        }
    }

    protected void ddlhandicapetype_SelectedIndexChanged(object sender, EventArgs e)
    {
        if (ddlhandicapetype.SelectedValue != "OL" && ddlhandicapetype.SelectedValue != "HH")
        {
            dv10thmarksheet.Visible = false;
        }
        else
        {
            dv10thmarksheet.Visible = true;
        }
    }

    protected void ddlservantpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        int s = int.Parse(ddlservantpriority.SelectedValue);
        int w = int.Parse(ddlwatchmanpriority.SelectedValue);
        int p = int.Parse(ddlsweeperpriority.SelectedValue);
        if (s != 0 && p != 0 && w != 0)
        {
            if (s == w || s == p)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (w == p || w == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (p == w || p == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
        }
    }

    protected void ddlwatchmanpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        int s = int.Parse(ddlservantpriority.SelectedValue);
        int w = int.Parse(ddlwatchmanpriority.SelectedValue);
        int p = int.Parse(ddlsweeperpriority.SelectedValue);
        if (s != 0 && p != 0 && w != 0)
        {
            if (s == w || s == p)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (w == p || w == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (p == w || p == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
        }
    }

    protected void ddlsweeperpriority_SelectedIndexChanged(object sender, EventArgs e)
    {
        int s = int.Parse(ddlservantpriority.SelectedValue);
        int w = int.Parse(ddlwatchmanpriority.SelectedValue);
        int p = int.Parse(ddlsweeperpriority.SelectedValue);
        if (s != 0 && p != 0 && w != 0)
        {
            if (s == w || s == p)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (w == p || w == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
            if (p == w || p == s)
            {
                string jv = "<script>alert('Priority should be different!');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                ddlservantpriority.SelectedValue = "0";
                ddlwatchmanpriority.SelectedValue = "0";
                ddlsweeperpriority.SelectedValue = "0";
            }
        }
    }
    public bool CheckDoubleExtension(string fileName, byte[] fileBytes)
    {
        string ext = Path.GetExtension(
                        Path.GetFileNameWithoutExtension(
                            fileName));

        bool hasExecutableExtension = ext.Equals(".exe", StringComparison.InvariantCultureIgnoreCase);
        bool hasbatExtension = ext.Equals(".bat", StringComparison.InvariantCultureIgnoreCase);

        bool isExecutableFile = fileBytes.Length > 1 &&
                        fileBytes[0] == 0x4D &&
                        fileBytes[1] == 0x5A;

        //int count = photoUplodad.PostedFile.FileName.Split('.').Length - 1;
        int count = fileName.Split('.').Length - 1;

        if (hasExecutableExtension || isExecutableFile || hasbatExtension || count > 1)
            return true;
        else
            return false;
    }
    public string GetCandidateLoginId(string userID)
    {
        string id = string.Empty;
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select Id from tbl_Candidatelogin where MobileNo=@userid";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@userid", SqlDbType.NVarChar).Value = userID.Trim();
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                id = dtShops.Rows[0][0].ToString();
            }
            conn.Close();
            dtShops.Dispose();
        }
        return id;
    }
    public void validateage(int age)
    {
        bool is_ok = true;
        if (chkgov.Checked && ddlgender.SelectedValue == "1" && ddlcategory.SelectedValue == "1")
        {
            if (age > 40)
            {
                is_ok = false;
                string jv = "<script>alert('Age is not valid, Age limit is 40 !');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        else if (chkgov.Checked && ddlcategory.SelectedValue != "1")
        {
            if (age > 45)
            {
                is_ok = false;
                string jv = "<script>alert('Age is not valid, Age limit is 45 !');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        else if (!chkgov.Checked && ddlgender.SelectedValue == "1" && ddlcategory.SelectedValue == "1")
        {
            if (age > 40)
            {
                is_ok = false;
                string jv = "<script>alert('Age is not valid, Age limit is 40 !');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        else if (!chkgov.Checked)
        {
            if (age > 45)
            {
                is_ok = false;
                string jv = "<script>alert('Age is not valid, Age limit is 45 !');</script>";
                ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
            }
        }
        if (is_ok)
        {
            dvagevalid.Visible = true;
        }
        else
        {
            txtage.Text = string.Empty;
            txtdob.Text = string.Empty;
            dvagevalid.Visible = false;
        }
    }

    protected void btnback_Click(object sender, EventArgs e)
    {
        int id = 0;
        if (Request.QueryString["Id"] != null)
        {
            id = int.Parse(Request.QueryString["Id"]);
        }
        Response.Redirect("~/candreg/edit_registration.aspx?Id=" + id);
    }
}