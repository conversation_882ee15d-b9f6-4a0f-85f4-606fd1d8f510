h2.text-center {
    /* position: relative;
    display: inline-block; */
    font-size: 2rem; /* Adjust font size as needed */
    font-weight: bold;
    color: #333; /* Text color */
  }
  
  h2.text-center::after {
   
    background: linear-gradient( #e4e70b); /* Gradient underline effect */
    border-radius: 5px; /* Optional rounded corners for the underline */
   
    transform: translateX(-50%); /* This shifts the underline back to the center */
  }
  
 /* Custom modal position at the top */
.custom-modal .modal-dialog {
  top: 10px; /* Position it at the top */
  margin: 0 auto; /* Center the modal horizontally */
  max-width: 400px; /* Set a max-width to make the modal smaller */
}

/* Modal header (includes close button) */
.custom-modal .modal-header {
  padding: 10px;
  border-bottom: 1px solid #ddd;
}

/* Modal body */
.custom-modal .modal-body {
  padding: 10px;
  font-size: 14px; /* Smaller font size */
}

/* Modal footer */
.custom-modal .modal-footer {
  padding: 10px;
  border-top: 1px solid #ddd;
}

/* Buttons */
.custom-modal .modal-footer .btn {
  font-size: 14px; /* Smaller button text */
  padding: 5px 15px; /* Adjust button size */
}

/* Close button (X) */
.custom-modal .modal-header .close {
  font-size: 2px;
  color: #000;
}
