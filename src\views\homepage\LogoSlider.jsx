import React from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay,Navigation } from 'swiper/modules'; // Import Autoplay module
import { Container, Row, Col } from 'reactstrap';
import "swiper/css/navigation"; // Import Swiper navigation styles
// Import required Swiper styles
import 'swiper/css';
import 'swiper/css/autoplay';

const LogoSlider = () => {
  return (
    <Container  style={{ backgroundColor: 'white' }} fluid className="p-4" >
      {/* <h2 className="text-center " style={{color: '#234ADC' }}>Our Trusted Partners</h2> */}
      <Row>
        <Col md={1}></Col>
        <Col md={10}>
        <Swiper
  modules={[Autoplay, Navigation]} // Add Navigation module
  spaceBetween={30} // Increased space between slides
  slidesPerView={4}
  loop={true}
  autoplay={{
    delay: 1500,
    disableOnInteraction: false,
    pauseOnMouseEnter: true,
  }}
  navigation={true} // Enable navigation arrows
  breakpoints={{
    1024: { slidesPerView: 3, spaceBetween: 30 }, // More space on larger screens
    768: { slidesPerView: 2, spaceBetween: 50 },
    480: { slidesPerView: 1, spaceBetween: 30 },
  }}
  style={{ padding: "10px" }} // Added padding for better spacing
>
  <SwiperSlide>
    <a href="https://www.mygov.in/" target="_blank" rel="noopener noreferrer">
      <img src="/carousel/logo-5.png" alt="Logo 1" className="img-fluid"
        style={{
          objectFit: 'contain',
          // display: 'block',
          // margin: 'auto' // Center align images
        }} />
    </a>
  </SwiperSlide>
  <SwiperSlide>
    <a href="https://voters.eci.gov.in/" target="_blank" rel="noopener noreferrer">
      <img src="/carousel/voterportal.jpg" alt="Logo 2" className="img-fluid"
        style={{
          objectFit: 'contain',
          display: 'block',
          margin: 'auto',
          height:'50px',
        }} />
    </a>
  </SwiperSlide>
  <SwiperSlide>
    <a href="https://www.nic.in/" target="_blank" rel="noopener noreferrer">
      <img src="/carousel/nic.png" alt="Logo 3" className="img-fluid"
        style={{
          objectFit: 'contain',
          display: 'block',
          margin: 'auto',
          height:'50px',
        }} />
    </a>
  </SwiperSlide>
  <SwiperSlide>
    <a href="http://india.gov.in/" target="_blank" rel="noopener noreferrer">
      <img src="/carousel/logo-4.png" alt="Logo 4" className="img-fluid"
        style={{
          objectFit: 'contain',
          display: 'block',
          margin: 'auto'
        }} />
    </a>
  </SwiperSlide>
  <SwiperSlide>
    <a href="https://eduportal.cg.nic.in/Login.aspx" target="_blank" rel="noopener noreferrer">
      <img src="/carousel/chhattisgarh-shashan.png" alt="Logo 5" className="img-fluid"
        style={{
          objectFit: 'contain',
          display: 'block',
          margin: 'auto'
        }} />
    </a>
  </SwiperSlide>
</Swiper>
        </Col>
       
      </Row>
      <style>
  {`
    .swiper-button-prev, .swiper-button-next {
      color: lightblue !important; /* Change arrow color */
      width: 50px !important;
      height: 50px !important;
    }
    .swiper-button-prev {
      left: -12px !important; /* Move left arrow further left */
    }
    .swiper-button-next {
      right: -12px !important; /* Move right arrow further right */
    }
  `}
</style>
    </Container>
  );
};

export default LogoSlider;