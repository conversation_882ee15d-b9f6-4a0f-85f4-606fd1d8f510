import { useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ooter,
    Container,
    Input,
    FormGroup,
    Row,
    Table,
    Col,
    Badge,
    Pagination,
    PaginationItem,
    PaginationLink,
    Modal, ModalHeader, <PERSON>dalBody, ModalFooter,

} from "reactstrap";
import { BsDownload, BsEye, } from 'react-icons/bs';
import Swal from 'sweetalert2';
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import { Spinner } from "reactstrap";
import { Link } from 'react-router-dom';
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx"
import formatDate from "../../utils/formateDate.jsx";
const LeaveAction = () => {


    const endPoint = import.meta.env.VITE_API_URL;
    const ClgId = sessionStorage.getItem("id");
    const token = sessionStorage.getItem("authToken");

    //#region  For Pegination


    const [currentPage, setCurrentPage] = useState(1);
    const [itemsPerPage, setRecordsPerPage] = useState(5); // Default value

    const handleChange = (event) => {
        const value = Number(event.target.value);
        setRecordsPerPage(value);
    };






    const [applications, setApplications] = useState([]);
    const [filteredApplications, setFilteredApplications] = useState([]);
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredApplications.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(applications.length / itemsPerPage);
    const [selectedStatus, setSelectedStatus] = useState(null);
    const [modal, setModal] = useState(false);
    const [currentStep, setCurrentStep] = useState(0); // Track the current step
    const [selectedAction, setSelectedAction] = useState('');
    const [remark, setRemark] = useState('');
    const [rules, setRules] = useState([]);
    const [buttonClicked, setButtonClicked] = useState(false)
    const [selectedPendingApplication, setSelectedPendingApplication] = useState(null);
    const [modalPendingReqOpen, setModalPendingReqOpen] = useState(false);


    const [searchTerm, setSearchTerm] = useState('');


    const getMaxLeaveType = (leaveType) => {

        // console.log(leaveType, "Getting LEaveType in Case");


        switch (leaveType) {
            case 'Casual Leave':
                // console.log("Entering In CL");
                return rules[0]?.pAuthMaxCL || 0; // Use pAuthMaxCL for Casual Leave
            case 'Earned Leave':
                // console.log("Entering In EL");
                return rules[0]?.pAuthMaxEL || 0; // Use pAuthMaxEL for Earned Leave
            case 'Medical Leave':
                // console.log("Entering In ML");
                return rules[0]?.pAuthMaxMedicalLeave || 0; // Use pAuthMaxMedicalLeave for Medical Leave
            case 'Maternity Leave':
                // console.log("Entering In ML2");
                return rules[0]?.pAuthMaxMaternityLeave || 0; // Use pAuthMaxMaternityLeave for Maternity Leave
            case 'Paternity Leave':
                // console.log("Entering In PL");
                return rules[0]?.pAuthMaxPaternintyLeave || 0; // Use pAuthMaxPaternintyLeave for Paternity Leave
            case 'Child Care Leave':
                // console.log("Entering In CCL");
                return rules[0]?.pAuthCCL || 0; // Use pAuthCCL for Compensatory Leave
            case 'Half Pay Leave':
                // console.log("Entering In HPL");
                return rules[0]?.pAuthMaxHPL || 0; // Use pAuthMaxHPL for Half Pay Leave
            case 'No Pay Leave':
                // console.log("Entering In NPL");
                return rules[0]?.pAuthNPL || 0; // Use pAuthNPL for Non-Paid Leave
            case 'Restricted Holiday':
                return rules[0]?.pAuthMaxRH; // Use pAuthMaxRH for Restricted Holiday
            default:
                // console.log("Entering In Default");
                return 150; // Default to 0 if leave type is not recognized
        }
    };
    const [clgInfo, setClgInfo] = useState([]);

    useEffect(() => {
        const fetchCollegeInfo = async () => {

            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-college/${ClgId}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    setClgInfo(response.data);

                } else {
                    alert("College Not Found.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        }

        fetchCollegeInfo();
    }, [ClgId]);


    useEffect(() => {
        const fetchRuleInfo = async () => {
            if (selectedPendingApplication && selectedPendingApplication.empClass && selectedPendingApplication.workType) { // Check if user, college, and user ID are defined
                try {
                    const response = await axios.get(
                        `${endPoint}/api/leave/rule/listbyclass/${selectedPendingApplication.empClass}/${selectedPendingApplication.workType}`, // Adjusted endpoint
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        setRules(response.data);
                    } else {
                        alert("Rules Not Found.");
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                }
            }
        };
        fetchRuleInfo();
    }, [selectedPendingApplication]);


    const [movement, setMovement] = useState('');
    const [checkMovement, setCheckMovement] = useState(false);
    const [applicationForMovement, setApplicationForMovement] = useState([]);

    const toggleMovement = async (item) => {
        // console.log("Getting Application ID", item);
        setApplicationForMovement(item)

        try {
            const response = await axios.get(`${endPoint}/api/leave/movement/${item._id}`,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
            if (response.status === 200) {
                const data = response.data.reverse();
                // console.log(data, "Getting Data");
                setMovement(data)


            } else {
                alert("Data Not Fetched.");
            }
        } catch (error) {
            console.error("An error occurred while Getting Data:", error);
            alert("An error occurred. Please try again later.");
        }

        // setApplicationForMovement(item);
        setCheckMovement(!checkMovement);
    }



    useEffect(() => {
        const fetchApplications = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/leave/employees_leave/${ClgId}`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                if (response.status === 200) {
                    const data = response.data.reverse();
                    const PendingForActionPrinciple = data.filter(leaves => (leaves.leaveStatus === 1 || (leaves.leaveStatus === 6 && !(leaves.designation === 'UG Principal' || leaves.designation === 'PG Principal'))));
                    // // console.log(data, "Getting Data");
                    setApplications(PendingForActionPrinciple);
                    setFilteredApplications(PendingForActionPrinciple);
                } else {
                    alert("Data Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchApplications();
    }, [ClgId, endPoint, token]);


    useEffect(() => {
        const filteredItems = applications.filter(item => {
            return Object.keys(item).some(key =>
                String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
            );
        });
        setFilteredApplications(filteredItems);
        setCurrentPage(1); // Reset to first page on filter change
    }, [searchTerm, applications]);



    const handleFilterChange = (Id) => {
        const status = parseInt(Id);
        // console.log(status);

        setSelectedStatus(status);
        if (status) {
            const filtered = applications.filter((app) => app.leaveStatus === status);
            setFilteredApplications(filtered);
        } else {
            setFilteredApplications(applications); // Reset to all applications if no filter
        }
        setCurrentPage(1); // Reset to first page on filter change
    };


    const getStatusCount = (status) => {
        return applications.filter(app => app.leaveStatus === status).length;
    };






    const handlePageChange = (pageNumber) => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber);
        }
    };


    const toggle = (status) => {
        setModal(!modal);
        setSelectedStatus(status);
    };
    const statusCode = selectedStatus;


    useEffect(() => {
        if (statusCode >= 1 && statusCode <= 3) {
            setCurrentStep(statusCode - 1); // Convert status code to zero-based index
        }
    }, [statusCode]);

    const steps = [
        { label: "Employee", icon: "fa-user" },
        { label: "Office", icon: "fa-solid fa-briefcase" },
        { label: "Directorate", icon: "fa-solid fa-user-shield" }
    ];

    const handleShowDetails = (remark1, remark2, remark3) => {
        // console.log("Getting Users Remark,", remark1);
        // console.log("Getting Principle Remark,", remark2);
        // console.log("Getting Director Remark,", remark3);

        Swal.fire({
            html: `<div style="text-align: left; max-height: 300px; overflow-y: auto;"><h3><b>Remark By User</h3><p> ${remark1}<p>
           </br> <h3><b>Remark By Principal</b></h3><p> ${remark2}<p>
           </br> <h3><b>Remark By ByDirectorate</b></h3><p> ${remark3}<p></div>`,
            showCloseButton: true,
            // showCancelButton: false,
            confirmButtonText: 'Close',
        });
    };


    const handlePendingApplicationDetails = (item) => {
        setSelectedPendingApplication(item);
        setModalPendingReqOpen(true);
        // handleCheckLeaveLimit();
    };

    const [leaveLimit, setLeaveLimit] = useState(0);

    const handleLeaveLimit = () => {
        setButtonClicked(true);
        const Limit = getMaxLeaveType(selectedPendingApplication.leaveType);
        // console.log(Limit,"Get Leave Limit");
        
        setLeaveLimit(Limit);
        // console.log("Getting Leave Type", selectedPendingApplication.leaveType, Limit, rules[0]?.pAuthMaxEL, selectedPendingApplication.dayCount);

    };


    const toggleModal = () => {
        setButtonClicked(false)
        setModalPendingReqOpen(!modalPendingReqOpen);
    }

    const handleActionChange = (e) => {
        setSelectedAction(e.target.value);

        if (remark === '' || remark === "Forwarded" || remark === "Approved" || remark === "Rejected" || remark === "Cancelled" || remark === "Others") {
            setRemark(
                e.target.value === "2"
                    ? "Forwarded"
                    : e.target.value === "3"
                        ? "Approved"
                        : e.target.value === "4"
                            ? "Rejected"
                            : e.target.value === "5"
                                ? "Cancelled"
                                : "Others"
            );
        }
    };

    const handleRemarkChange = (e) => {
        setRemark(e.target.value);
    };

    const handleSaveClick = async () => {


        if (!selectedAction || !remark) {
            SwalMessageAlert("Please give a remark and select an action before saving.", "warning");
            return;
        }


        const appId = selectedPendingApplication._id;


        try {
            const response = await axios.put(`${endPoint}/api/leave/update-status/${appId}`, {
                action: selectedAction,
                remark: remark,
                institute: ClgId,
                principal: clgInfo.contactPerson,
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'web-url': window.location.href,
                    "Authorization": `Bearer ${token}`
                }
            });
            if (response.status === 200) {
                SwalMessageAlert("Leave status Updated.", "success");
                toggleModal(); // Close the modal after saving
                setTimeout(function () {
                    window.location.reload();
                }, 3000); // 4000 milliseconds = 4 seconds
            }
            else {
                SwalMessageAlert('Application updated Failed', "error");

            }
        } catch (error) {
            console.error('Error updating application:', error);
            alert('Failed to update the application');
        }
    };

    const statuses = [
        { valueOf: "", label: `All (${applications.length})`, color: "blue" },
        { valueOf: "1", label: `Pending (${getStatusCount(1)})`, color: "orange" },
        { valueOf: "2", label: `Forwarded (${getStatusCount(2)})`, color: "gray" },
        { valueOf: "3", label: `Approved (${getStatusCount(3)})`, color: "darkgreen" },
        { valueOf: "4", label: `Rejected (${getStatusCount(4)})`, color: "red" },
        { valueOf: "5", label: `Cancelled (${getStatusCount(5)})`, color: "skyblue" },
        { valueOf: "6", label: `Applied for Cancel (${getStatusCount(6)})`, color: "orange" },
    ];




    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Row>
                    <div className="col">
                        <Card className="shadow">
                            <CardHeader className="border-0">
                                {/* <button onClick={handleCheckLeaveLimit}>click</button> */}
                                <h3 className="mb-0">Pending For Action / कार्यवाही हेतु लंबित</h3>
                                <Row className="mt-3">
                                    <Col>
                                        {statuses.map((status) => (
                                            <button className="btn-sm "
                                                key={status.valueOf}
                                                onClick={() => handleFilterChange((status.valueOf))}
                                                style={{
                                                    backgroundColor: status.color,
                                                    color: "white",
                                                    border: "none",
                                                    borderRadius: "5px",
                                                    padding: "10px",
                                                    fontSize: "13px",
                                                    margin: "5px",
                                                    fontWeight: "bolder",
                                                    cursor: "pointer",
                                                }}
                                            >
                                                {status.label}
                                            </button>
                                        ))}
                                    </Col>
                                    <Col md={3}>
                                        <Input
                                            type="text"
                                            placeholder="Search..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            style={{ marginBottom: '10px' }}
                                        />
                                    </Col>
                                </Row>
                            </CardHeader>

                            <Table className="align-items-center table-flush" responsive>
                                <thead className="thead-light">
                                    <tr>

                                        <th style={{ color: "black", fontWeight: "bolder" }}>Sno. <br /> / क्र.</th>
                                        {/* <th>ApplicantId.<br />(आवेदक आई.डी.)</th> */}

                                        <th style={{ color: "black", fontWeight: "bolder" }}>File Movement / Remark / Action <br /> / View / Download</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Status <br />स्थिति </th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Application Details <br /> (आवेदन जानकारी )</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Leave Details <br /> (अवकाश जानकारी)</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Date <br /> Between <br /> (दिनांक से - तक)</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    {currentItems.map((application, index) => (
                                        <tr key={application.id}>
                                            <td>{indexOfFirstItem + index + 1}</td>
                                            {/* <td>{application.applicantId}</td> */}

                                            <td> <button
                                                className="btn-sm m-2 btn-info"
                                                onClick={() => toggleMovement(application)}
                                            >
                                                Movement
                                            </button>

                                                <button
                                                    className="btn-sm m-2 btn-primary"
                                                    onClick={() => handleShowDetails(application.remarkByUser, application.remarkByPrincipal, application.remarkByDirector)}>
                                                    Remark
                                                </button>

                                                <Button className="btn-sm m-2 btn-success" color="btn " onClick={() => handlePendingApplicationDetails(application)}>
                                                    Action
                                                </Button>

                                                <br />
                                                <Link to={`/admin/Preview/${application._id}`}>
                                                    <Button
                                                        className="btn-sm m-2"
                                                        style={{ backgroundColor: "yellow" }}
                                                    ><BsDownload size={18} /><BsEye size={18} /></Button>
                                                </Link>

                                                <a href={`https://heonline.cg.nic.in/${application.uploadFile}`} download>
                                                    <Button className="btn-sm m-2 btn-primary">

                                                        {application.uploadFile ? <BsDownload size={18} /> : "No File"}
                                                    </Button>
                                                </a>


                                            </td>
                                            <td>
                                                <h2>
                                                    <Badge

                                                        className="badge-sm"
                                                        onClick={application.leaveStatus === 1 || application.leaveStatus === 2 ? () => toggle(application.leaveStatus) : null}
                                                        style={{
                                                            fontWeight: 'bolder',
                                                            color: 'white',
                                                            backgroundColor:
                                                                application.leaveStatus === 3
                                                                    ? 'green'  // Approved background color
                                                                    : application.leaveStatus === 4
                                                                        ? 'red'   // Rejected background color
                                                                        : (application.leaveStatus === 1)
                                                                            ? '#f3c70c'  // Pending background color

                                                                            : application.leaveStatus === 5
                                                                                ? 'blue'
                                                                                : (application.leaveStatus === 2)
                                                                                    ? '#ff8a00'
                                                                                    : (application.leaveStatus === 6)
                                                                                        ? '#ff8a00'
                                                                                        : 'lavender'
                                                        }}
                                                    >
                                                        {application.leaveStatus === 3
                                                            ? 'Approved'
                                                            : application.leaveStatus === 4
                                                                ? 'Rejected'
                                                                : application.leaveStatus === 6
                                                                    ? 'approve Cancel'
                                                                    : (application.leaveStatus === 1)
                                                                        ? (
                                                                            <>
                                                                                <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                                Pending
                                                                            </>
                                                                        )
                                                                        : (application.leaveStatus === 2)
                                                                            ? (
                                                                                <>
                                                                                    {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                    Forwarded
                                                                                </>
                                                                            )
                                                                            : application.leaveStatus === 5
                                                                                ? 'Cancelled'
                                                                                : 'Error'}
                                                    </Badge>

                                                </h2>
                                            </td>
                                            <td> <strong> Name : </strong> {application.applicantName} <br /> <strong> Application ID : </strong>{application.applicationId} <br /><strong>Applied Date :</strong>{formatDate(application.appliedDate)} <br /><strong>Emp Code :</strong> {application.empCode}</td>
                                            <td><strong>Type : </strong>{application.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : application.leaveType} <br /><strong>Reason :</strong>{application.reason}</td>
                                            <td><strong>({formatDate(application.fromDate)}) <br /> to <br /> ({formatDate(application.tillDate)}) <br /> <span style={{ fontWeight: "bolder", color: "red" }}>{application.dayCount} Days</span></strong></td>


                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <Modal style={{ maxWidth: "1000px", minWidth: "400px" }} isOpen={checkMovement} toggle={toggleMovement}>
                                <ModalHeader toggle={toggleMovement}><h3>File Movement Of ({applicationForMovement.applicationId}) </h3></ModalHeader>
                                <ModalBody>

                                    <Table className="align-items-center table-flush" responsive bordered>
                                        <thead className="thead-light">
                                            <tr>
                                                <th>Sno./ क्र.</th>
                                                <th>Action/ <br />Sent By</th>
                                                <th>Section Name</th>
                                                <th>Action Date</th>
                                                <th>Day Taken</th>
                                                <th>Currently With</th>
                                                <th>Status</th>
                                                <th>Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {movement && movement.length > 0 ? movement.map((item, index) =>
                                            (
                                                <tr key={item._id}>
                                                    <td className="text-center">{index + 1}</td>
                                                    <td className="text-left">{item.directorName}</td>
                                                    <td className="text-left">{item.sectionName}</td>
                                                    <td className="text-center">{formatDate(item.actionDate)}</td>
                                                    <td className="text-center">{item.dayTaken}</td>
                                                    <td className="text-center"><strong style={{ fontWeight: "bolder", fontSize: "14px" }}>
                                                        {item.sendToDirector}
                                                    </strong>
                                                        {index === 0 && (
                                                            <i
                                                                className="fa fa-check fa-xl"
                                                                style={{ marginLeft: "10px", color: "green" }}
                                                                aria-hidden="true"
                                                            ></i>
                                                        )}
                                                    </td>
                                                    <td>
                                                        <h2>
                                                            <Badge

                                                                className="badge-sm"
                                                                onClick={item.status === 1 || item.status === 2 ? () => toggle(item.status) : null}
                                                                style={{
                                                                    fontWeight: 'bolder',
                                                                    color: 'white',
                                                                    backgroundColor:
                                                                        item.status === 3
                                                                            ? 'green'  // Approved background color
                                                                            : item.status === 4
                                                                                ? 'red'   // Rejected background color
                                                                                : (item.status === 1)
                                                                                    ? '#f3c70c'  // Pending background color
                                                                                    : (item.status === 6)
                                                                                        ? '#ff8a00'
                                                                                        : item.status === 5
                                                                                            ? 'blue'
                                                                                            : (item.status === 2)
                                                                                                ? '#ff8a00'
                                                                                                : 'lavender'
                                                                }}
                                                            >
                                                                {item.status === 3
                                                                    ? 'Approved'
                                                                    : item.status === 4
                                                                        ? 'Rejected'
                                                                        : item.status === 6
                                                                            ? 'approve Cancel'
                                                                            : (item.status === 1)
                                                                                ? (
                                                                                    <>
                                                                                        <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                                        Pending at principal
                                                                                    </>
                                                                                )
                                                                                : (item.status === 2)
                                                                                    ? (
                                                                                        <>
                                                                                            {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                            Forwarded
                                                                                        </>
                                                                                    )
                                                                                    : item.status === 5
                                                                                        ? 'Cancelled'
                                                                                        : 'Error'}
                                                            </Badge>

                                                        </h2>
                                                    </td>
                                                    <td className="text-left">{item.remarksFileMovment}</td>
                                                </tr>
                                            )) : <tr>
                                                <td colSpan="6" className="text-center">No Data Found</td>
                                            </tr>}
                                        </tbody>
                                    </Table>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggleMovement}>
                                        Close
                                    </Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <Modal isOpen={modal} toggle={toggle}>
                                <ModalHeader toggle={toggle}>Status</ModalHeader>
                                <ModalBody>
                                    <div className="main">
                                        <ul style={{ display: 'flex', justifyContent: 'space-around' }}>
                                            {steps.map((step, index) => (
                                                <li key={index} style={{ listStyle: 'none', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                    <i className={`icons awesome fa-solid ${step.icon}`} style={{ fontSize: '25px', color: '#1b761b' }}></i>
                                                    <div
                                                        className={`step ${index === currentStep ? 'active' : ''}`}
                                                        style={{
                                                            height: '30px',
                                                            width: '30px',
                                                            borderRadius: '50%',
                                                            backgroundColor: index <= currentStep ? '#1b761b' : '#d7d7c3',
                                                            margin: '16px 0 10px',
                                                            display: 'grid',
                                                            placeItems: 'center',
                                                            color: 'ghostwhite',
                                                            position: 'relative',
                                                            cursor: 'default' // Change cursor to default since clicking is disabled
                                                        }}
                                                    >
                                                        <p style={{ fontSize: '18px', display: index <= currentStep ? 'none' : 'block' }}>{index + 1}</p>
                                                        <i className="awesome fa-solid fa-check" style={{ display: index <= currentStep ? 'flex' : 'none' }}></i>
                                                    </div>
                                                    <p className="label" style={{ fontFamily: 'sans-serif', letterSpacing: '1px', fontSize: '14px', fontWeight: 'bold', color: '#1b761b' }}>{step.label}</p>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggle}>Close</Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <Modal style={{ maxWidth: "800px", minWidth: "400px" }} isOpen={modalPendingReqOpen} toggle={toggleModal}>
                                {selectedPendingApplication &&
                                    <ModalHeader toggle={toggleModal}><div className="d-flex"><h1>Application Details : </h1>{selectedPendingApplication.leaveStatus === 1 ? <h2 className="text-primary mt-1 pl-4">Application for Leave</h2> : selectedPendingApplication.leaveStatus === 6 ? <h2 className="text-primary mt-1 pl-4">Application for Cancelation</h2> : <h2 className="text-primary mt-1 pl-4">Application for Joining</h2>}</div></ModalHeader>}
                                <ModalBody>
                                    {selectedPendingApplication ? (
                                        <Container fluid>
                                            <Row className="mt--3">
                                                <Col sm={6}>
                                                    <p><strong className="text-primary" >Application ID:</strong> {selectedPendingApplication.applicationId}</p>
                                                </Col>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Applicant Name:</strong> {selectedPendingApplication.applicantName}</p>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Applied Date:</strong> {formatDate(selectedPendingApplication.appliedDate)}</p>
                                                </Col>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Leave Type:</strong> {selectedPendingApplication.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : selectedPendingApplication.leaveType}</p>
                                                </Col>

                                            </Row>
                                            <Row>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Leave Reason:</strong> {selectedPendingApplication.reason}</p>
                                                </Col>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Total Days:</strong> {selectedPendingApplication.dayCount}</p>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">From Date:</strong> {formatDate(selectedPendingApplication.fromDate)}</p>
                                                </Col>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Till Date:</strong> {formatDate(selectedPendingApplication.tillDate)}</p>
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Station Leave</strong> {selectedPendingApplication.permission === 1 ? "Yes" : "No"}</p>
                                                </Col>
                                                <Col sm={6}>
                                                    <p><strong className="text-primary">Address During Leave</strong> {selectedPendingApplication.stationAddress}</p>
                                                </Col>
                                            </Row>
                                        </Container>
                                    ) : (
                                        <p>No details available.</p>
                                    )}
                                </ModalBody>
                                <ModalFooter>
                                    <FormGroup>
                                        <Input
                                            type="textarea"
                                            style={{ color: "black", width: "340px", height: "40px" }}
                                            id="remarksTextArea"
                                            value={remark}
                                            row='1'
                                            onChange={handleRemarkChange}
                                            placeholder="Enter your remarks"
                                        />
                                    </FormGroup>
                                    {!buttonClicked && <Button color="primary" onClick={handleLeaveLimit}>Action</Button>}
                                    {buttonClicked && <> <FormGroup>

                                        <Input
                                            type="select"
                                            style={{ color: "Black" }}
                                            id="actionSelect"
                                            value={selectedAction}
                                            onChange={handleActionChange}>
                                            <option value="">Select an action</option>
                                            {selectedPendingApplication && selectedPendingApplication.leaveStatus !== 6 && (
                                                <>
                                                    {selectedPendingApplication.dayCount <= leaveLimit && (
                                                        <option style={{ backgroundColor: "green", color: "white" }} value="3">
                                                            Approve
                                                        </option>
                                                    )}
                                                    <option style={{ backgroundColor: "red", color: "white" }} value="4">Reject</option>
                                                    <option style={{ backgroundColor: "yellow", color: "black" }} value="2">Forwarded</option>
                                                </>
                                            )}
                                            {selectedPendingApplication && selectedPendingApplication.leaveStatus === 6 && (
                                                <>
                                                    <option style={{ backgroundColor: "green", color: "white" }} value="5">Cancel</option>
                                                </>
                                            )}

                                        </Input>
                                    </FormGroup>
                                        <Button color="primary" onClick={handleSaveClick}>Action</Button> </>}
                                    <Button color="secondary" onClick={toggleModal}>Close</Button>
                                </ModalFooter>
                            </Modal>
                            <CardFooter className="py-4">
                                <div>
                                    <label htmlFor="recordsPerPage"> <span style={{ fontWeight: "bold" }}> No. of Records Per Page: </span></label>
                                    <select
                                        id="recordsPerPage"
                                        value={itemsPerPage}
                                        onChange={handleChange}
                                    >
                                        <option value={5}>5</option>
                                        <option value={10}>10</option>
                                        <option value={20}>20</option>
                                        <option value={50}>50</option>
                                    </select>
                                </div>
                                <nav aria-label="...">
                                    <Pagination className="pagination justify-content-end mb-0">
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem key={i} className={currentPage === i + 1 ? 'active' : ''}>
                                                <PaginationLink
                                                    href="#"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(i + 1);
                                                    }}
                                                >
                                                    {i + 1}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                    </Pagination>
                                </nav>
                            </CardFooter>
                        </Card>
                    </div>
                </Row>
            </Container>
        </>
    );
};

export default LeaveAction;