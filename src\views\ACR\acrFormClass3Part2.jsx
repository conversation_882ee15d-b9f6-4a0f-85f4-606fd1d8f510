import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Label,
  Button,
  Input,
  Table,
} from "reactstrap";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Swal from "sweetalert2";
import axios from "axios";

const ACRFormClass3Part2 = ({ employee }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);
  const [formData, setFormData] = useState({
    applicationId: employee[0].basicDetails[0].applicationId,
    employeeId: employee[0].basicDetails[0].employeeId,
    employeeName: employee[0].basicDetails[0].employeeName,
    collegeName: employee[0].basicDetails[0].collegeName,
    collegeId: employee[0].basicDetails[0].collegeId,
    vektiVehwar: "",
    acharanCharitra: "",
    prarupTip: "",
    karyalayaPrakriya: "",
    prakrankiParikhsha: "",
    karyakeNiptare: "",
    samaykiPabandi: "",
    sahbhagiyonSeSambhand: "",
    nityaKaryaJaise: "",
    sanishth: "",
    asadharanOrUlekhNiya: "",
    padonatiKiUpyukta: "",
    shreniKaran: "",
    remark: "",
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.vektiVehwar.trim())
      newErrors.vektiVehwar = "This field is required.";
    if (!formData.acharanCharitra)
      newErrors.acharanCharitra = "This field is required.";
    if (!formData.prarupTip.trim())
      newErrors.prarupTip = "This field is required.";
    if (!formData.karyalayaPrakriya)
      newErrors.karyalayaPrakriya = "This field is required.";
    if (!formData.prakrankiParikhsha)
      newErrors.prakrankiParikhsha = "This field is required.";
    if (!formData.karyakeNiptare)
      newErrors.karyakeNiptare = "This field is required.";
    if (!formData.samaykiPabandi)
      newErrors.samaykiPabandi = "This field is required.";
    if (!formData.sahbhagiyonSeSambhand)
      newErrors.sahbhagiyonSeSambhand = "This field is required.";
    if (!formData.nityaKaryaJaise)
      newErrors.nityaKaryaJaise = "This field is required.";
    if (!formData.sanishth) newErrors.sanishth = "This field is required.";
    if (!formData.asadharanOrUlekhNiya)
      newErrors.asadharanOrUlekhNiya = "This field is required.";
    if (!formData.padonatiKiUpyukta)
      newErrors.padonatiKiUpyukta = "This field is required.";
    if (!formData.shreniKaran)
      newErrors.shreniKaran = "This field is required.";
    if (!formData.remark) newErrors.remark = "This field is required.";

    return newErrors;
  };

  const handleFinalSubmit = async (e) => {

    
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        e.target.disabled = true;

        setTimeout(() => {
          e.target.disabled = false;
        }, 5000);
        

        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("प्रथम मतांकन संपन्न", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  return (
    <>
      <Row>
        <Col>
          <Card className="shadow pb-4">
            <CardHeader
              className="bg-white border-0"
              style={{ textAlign: "center" }}
            >
              <h2 className="mb-0">प्रपत्र --- दो</h2>
              <h3 className="mb-0">
                तृतीय श्रेणी एवं अन्य लिपिक वर्गीय कर्मचारी के गोपनीय प्रतिवेदन
                लिखे जाने का प्रपत्र 31 मार्च, {lastYearDate} को समाप्त होने
                वाले वर्ष के लिए |
              </h3>
            </CardHeader>
            <CardBody>
              <div className="mb-4">
                <Row className="mb-3">
                  <Table>
                    <tbody>
                      <tr>
                        <h2 style={{ textAlign: "center" }}>प्रतिवेदक</h2>
                      </tr>
                      <tr>
                        <td>
                          <Row className="mb-3">
                            <Col md="3">
                              <Label>पूरा नाम श्री/श्रीमती / कुमारी</Label>
                              <Input
                                type="text"
                                name="employeeName"
                                value={formData.employeeName}
                                readOnly
                                className="form-control"
                              />
                            </Col>
                            <Col md="3">
                              <Label>पिता अथवा पति का नाम </Label>
                              <Input
                                type="text"
                                name="fatherName"
                                value={employee[0].basicDetails[0].fatherName}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.fatherName && (
                                <small className="text-danger">
                                  {errors.fatherName}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>जन्म तिथि</Label>
                              <Input
                                type="date"
                                name="dateofBirth"
                                value={employee[0].basicDetails[0].dateofBirth}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.dateofBirth && (
                                <small className="text-danger">
                                  {errors.dateofBirth}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>
                                धारित पदनाम (मूल / स्थानापन्न / अस्थायी) वर्तमान
                              </Label>
                              <Input
                                type="text"
                                name="designation"
                                value={employee[0].basicDetails[0].designation}
                                className="form-control"
                                readOnly
                              />
                            </Col>
                          </Row>
                          <Row className="mb-3">
                            <Col md="3">
                              <Label>महाविद्यालय का नाम</Label>
                              <Input
                                type="text"
                                name="collegeName"
                                value={employee[0].basicDetails[0].collegeName} // Use employee data here
                                readOnly
                                className="form-control"
                                onChange={handleInputChange}
                              />
                            </Col>
                            <Col md="3">
                              <Label>वरिष्ठता सूची क्रमांक </Label>
                              <Input
                                type="number"
                                name="varishtSuchiNumber"
                                value={employee[0].basicDetails[0].varishtSuchiNumber}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.varishtSuchiNumber && (
                                <small className="text-danger">
                                  {errors.varishtSuchiNumber}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>प्रथम नियमित नियुक्ति दिनांक</Label>
                              <Input
                                type="date"
                                name="firstNiyamitNiyuktiDate"
                                value={employee[0].basicDetails[0].firstNiyamitNiyuktiDate}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.firstNiyamitNiyuktiDate && (
                                <small className="text-danger">
                                  {errors.firstNiyamitNiyuktiDate}
                                </small>
                              )}
                            </Col>
                            <Col md="3">
                              <Label>
                                वर्तमान पद पर पदोन्नति दिनांक (यदि हो तो)
                              </Label>
                              <Input
                                type="date"
                                name="currentPadonatiDate"
                                value={employee[0].basicDetails[0].currentPadonatiDate}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.currentPadonatiDate && (
                                <small className="text-danger">
                                  {errors.currentPadonatiDate}
                                </small>
                              )}
                            </Col>
                          </Row>
                          <Row className="mb-4">
                            <Col md="3">
                              <Label>वेतन</Label>
                              <Input
                                type="number"
                                name="salary"
                                value={employee[0].basicDetails[0].currentSalary} // Use employee data here
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                            </Col>
                            <Col md="9">
                              <Label>कर्तव्यों का संक्षिप्त विवरण </Label>
                              <Input
                                type="textarea"
                                name="kartavyaKaVivran"
                                value={employee[0].basicDetails[0].kartavyaKaVivran}
                                className="form-control"
                                onChange={handleInputChange}
                                readOnly
                              />
                              {errors.kartavyaKaVivran && (
                                <small className="text-danger">
                                  {errors.kartavyaKaVivran}
                                </small>
                              )}
                            </Col>
                          </Row>
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Row>
                <Row className="mb-3">
                  <Col>
                    <h2 style={{ textAlign: "center" }}>प्रथम मतांकन </h2>
                  </Col>
                </Row>
                <Row className="mb-3">
                  <Col md="6">
                    <Label>व्यक्तित्व एवं व्यवहार</Label>
                    <Input
                      type="textarea"
                      name="vektiVehwar"
                      maxLength={200}
                      value={formData.vektiVehwar}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.vektiVehwar && (
                      <small className="text-danger">
                        {errors.vektiVehwar}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>आचरण / चरित्र </Label>
                    <Input
                      type="textarea"
                      name="acharanCharitra"
                      maxLength={200}

                      value={formData.acharanCharitra}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.acharanCharitra && (
                      <small className="text-danger">
                        {errors.acharanCharitra}
                      </small>
                    )}
                  </Col>

                </Row>
                <Row className="mb-3">
                  <Col md="6">
                    <Label>प्रारूप और टीप लिखने की योग्यता</Label>
                    <Input
                      type="textarea"
                      name="prarupTip"
                      maxLength={200}
                      value={formData.prarupTip}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.prarupTip && (
                      <small className="text-danger">{errors.prarupTip}</small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>
                      कार्यालय प्रक्रिया और नियमों का ज्ञान तथा प्रयास करने की
                      योग्यता
                    </Label>
                    <Input
                      type="textarea"
                      name="karyalayaPrakriya"
                      maxLength={200}

                      value={formData.karyalayaPrakriya}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.karyalayaPrakriya && (
                      <small className="text-danger">
                        {errors.karyalayaPrakriya}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col md="6">
                    <Label>प्रकरण के परीक्षण की क्षमता</Label>
                    <Input
                      type="textarea"
                      name="prakrankiParikhsha"
                      maxLength={200}
                      value={formData.prakrankiParikhsha}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.prakrankiParikhsha && (
                      <small className="text-danger">
                        {errors.prakrankiParikhsha}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>कार्य के निपटारे की तत्परता</Label>
                    <Input
                      type="textarea"
                      name="karyakeNiptare"
                      maxLength={200}
                      value={formData.karyakeNiptare}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.karyakeNiptare && (
                      <small className="text-danger">
                        {errors.karyakeNiptare}
                      </small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col md="6">
                    <Label>उपस्थिति की नियमितता और समय की पाबंदी</Label>
                    <Input
                      type="textarea"
                      name="samaykiPabandi"
                      value={formData.samaykiPabandi}
                      className="form-control"
                      maxLength={200}
                      onChange={handleInputChange}
                    />
                    {errors.samaykiPabandi && (
                      <small className="text-danger">
                        {errors.samaykiPabandi}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>उच्च अधिकारियों एवं सहभागियों से संबंध</Label>
                    <Input
                      type="textarea"
                      name="sahbhagiyonSeSambhand"
                      value={formData.sahbhagiyonSeSambhand}
                      className="form-control"
                      maxLength={200}
                      onChange={handleInputChange}
                    />
                    {errors.sahbhagiyonSeSambhand && (
                      <small className="text-danger">
                        {errors.sahbhagiyonSeSambhand}
                      </small>
                    )}
                  </Col>

                </Row>
                <Row className="mb-4">

                  <Col md="6">
                    <Label>
                      नित्य कार्य जैसे—असिस्टेंट की डायरी का रख-रखाव, गार्ड
                      फाइलें, रिकार्डिंग आदि का ध्यान रखा जाना
                    </Label>
                    <Input
                      type="textarea"
                      name="nityaKaryaJaise"
                      value={formData.nityaKaryaJaise} // Use employee data here
                      className="form-control"
                      maxLength={200}
                      onChange={handleInputChange}
                    />
                    {errors.nityaKaryaJaise && (
                      <small className="text-danger">
                        {errors.nityaKaryaJaise}
                      </small>
                    )}
                  </Col>

                  <Col md="6">
                    <Label>सनिष्ठा</Label>
                    <Input
                      type="textarea"
                      name="sanishth"
                      maxLength={200}
                      value={formData.sanishth}
                      className="form-control"
                      onChange={handleInputChange}
                    />
                    {errors.sanishth && (
                      <small className="text-danger">{errors.sanishth}</small>
                    )}
                  </Col>
                </Row>
                <Row className="mb-4">
                  <Col md="6">
                    <Label>
                      कर्मचारी द्वारा यदि कोई असाधारण या उल्लेखनीय कार्य किया
                      गया हो तो वह संक्षेप में बतावें ।
                    </Label>
                    <Input
                      type="textarea"
                      name="asadharanOrUlekhNiya"
                      value={formData.asadharanOrUlekhNiya}
                      className="form-control"
                      maxLength={200}
                      onChange={handleInputChange}
                    />
                    {errors.asadharanOrUlekhNiya && (
                      <small className="text-danger">
                        {errors.asadharanOrUlekhNiya}
                      </small>
                    )}
                  </Col>
                  <Col md="6">
                    <Label>पदोन्नति की उपयुक्तता</Label>
                    <Input
                      type="textarea"
                      name="padonatiKiUpyukta"
                      value={formData.padonatiKiUpyukta}
                      className="form-control"
                      maxLength={200}

                      onChange={handleInputChange}
                    />
                    {errors.padonatiKiUpyukta && (
                      <small className="text-danger">
                        {errors.padonatiKiUpyukta}
                      </small>
                    )}
                  </Col>
                </Row>

                <Row className="mb-4">
                  <Col md="6">
                    <Label>श्रेणीकरण</Label>
                    <Input
                      name="shreniKaran"
                      type="select"
                      id="recordsPerPage"
                      value={formData.shreniKaran}
                      onChange={handleInputChange}
                    >
                      <option value="">चुने</option>
                      <option value="उत्कृष्ट">उत्कृष्ट</option>
                      <option value="बहुत अच्छा">बहुत अच्छा</option>
                      <option value="अच्छा">अच्छा</option>
                      <option value="साधारण">साधारण</option>
                      <option value="घटिया">घटिया</option>
                    </Input>
                    {errors.shreniKaran && (
                      <small className="text-danger">
                        {errors.shreniKaran}
                      </small>
                    )}
                  </Col>
                </Row>

                <Row className="mb-4">
                  <Col md="12">
                    <Label>रिमार्क</Label>
                    <Input
                      name="remark"
                      type="textarea"
                      maxLength={500}
                      value={formData.remark}
                      onChange={handleInputChange}
                    />
                    {errors.remark && (
                      <small className="text-danger">{errors.remark}</small>
                    )}
                  </Col>
                </Row>
                <Button color="success" onClick={handleFinalSubmit}>
                  Submit
                </Button>
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row>
    </>
  );
};

ACRFormClass3Part2.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormClass3Part2;