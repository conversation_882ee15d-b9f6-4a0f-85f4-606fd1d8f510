import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>ooter,
  Container,
  Input,
  FormGroup,
  Row,
  Table,
  Badge,
  Pagination,
  PaginationItem,
  Col,
  PaginationLink,
  Modal,
  Label,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import { BsDownload, BsEye } from "react-icons/bs";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import { Spinner } from "reactstrap";
import { Link, useNavigate } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";

// Dummy Data - Array of Leave Applications
const LeaveActionAtDirector = () => {
  const token = sessionStorage.getItem("authToken");


  const designationPA = sessionStorage.getItem("designationName");
  const userTypeCommisioner = sessionStorage.getItem("type");
  const directorAtSession = sessionStorage.getItem("id");
  // console.log(directorAtSession, "directorAtSession");
  const navigate = useNavigate();
  const PA = designationPA === "Personal Assistant";
  const Commissioner = Number(userTypeCommisioner) === 6;

  // console.log(token, "Getting Token In Leave Action");
  const endPoint = import.meta.env.VITE_API_URL;

  const [modal, setModal] = useState(false);
  const [modalForward, setModalForward] = useState(false);
  const [checkMovement, setCheckMovement] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const [remark, setRemark] = useState("");
  const [selectedPendingApplication, setSelectedPendingApplication] =
    useState(null);

  const [modalPendingReqOpen, setModalPendingReqOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [applications, setApplications] = useState([]);
  const [filteredApplications, setFilteredApplications] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setRecordsPerPage] = useState(5); // Default value

  const handleChange = (event) => {
    const value = Number(event.target.value);
    setRecordsPerPage(value);
  };

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredApplications.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(applications.length / itemsPerPage);
  const [selectedAction, setSelectedAction] = useState("");
  const [selectedForForward, setSelectedForForward] = useState(null);
  const [directors, setDirectors] = useState([]);
  const [sectionData, setSectionData] = useState([]);
  const [classData, setClassData] = useState();

  const [movementData, setMovementData] = useState({
    director: '',
    section: '',
    remarkFileMovement: 'Forward',
  });

  const handleSelectedDirectorChange = (e) => {
    // const { name, value } = e.target;
    const selectedOption = e.target.options[e.target.selectedIndex];
    const directorId = selectedOption.value;
    const sectionId = selectedOption.getAttribute('data-section-id');

    setMovementData({
      ...movementData,
      director: directorId,
      section: sectionId
    });

  }

  const isSectionOfficer = sessionStorage.getItem("id");
  const SA = sectionData.some(section => section.sectionOfficer === isSectionOfficer);



  const handleDirectorChangePA = (e) => {
    const { value } = e.target;

    const directorId = value;
    // console.log(directorId, "directorId");

    const sectionId = sectionData && sectionData.length > 0 && sectionData.find((type) => type.sectionOfficer === value)?._id;
    // console.log(sectionId, "sectionId");


    setMovementData({
      ...movementData,
      director: directorId,
      section: sectionId
    });

  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setMovementData({ ...movementData, [name]: value });

  }

  const handleForward = async () => {

    if (!movementData.director) {
      alert("Please select an Authority.");
      return;
    }

    if (movementData.director === undefined) {
      alert("Section Officer Not Oppointed.");
      return;
    }


    const appId = selectedForForward._id;

    try {

      const response = await axios.put(
        `${endPoint}/api/leave/forward-file/${appId}`,
        {
          sendBy: directorAtSession,
          director: movementData.director,
          section: movementData.section,
          remarkFileMovement: movementData.remarkFileMovement,
        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 201) {
        SwalMessageAlert("File Forwarded.", "success");
        toggleModal();
        window.location.reload();
      } else {
        SwalMessageAlert("Failed to Forward the application.", "error");
      }
    } catch (error) {
      if (error.response) {
        const backendMessage = error.response.data.message;
        SwalMessageAlert(`${backendMessage}`, "error"); // For other types of errors
      }
    }
  };


  const genrateReciept = async (application) => {
    const appId = application._id;
    const action = application.actionByCommissioner;
    const remarkbyCommisioner = application.remarkByDirector;

    // console.log(remarkbyCommisioner, "Getting remarkbyCommisioner");
    // console.log(application.remarkByDirector, "Getting remarkbyCommisioner 2");

    // Show SweetAlert confirmation dialog
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to Genrate Action Letter For this ${application.applicationId} Application?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, Genrate',
      cancelButtonText: 'Cancel'
    });

    // If the user confirmed, proceed with the action
    if (result.isConfirmed) {
      try {
        const response = await axios.put(
          `${endPoint}/api/leave/update-status-section/${appId}`,
          {
            action: String(action),
            remark: remarkbyCommisioner,
            director: directorAtSession,
          },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          SwalMessageAlert("Application Genrated Successfuly.", "success");

          const timer = setTimeout(() => {
            navigate(`/admin/Genrated/${application._id}`);
          }, 3000);
          return () => clearTimeout(timer);
          // window.location.reload();
        } else {
          SwalMessageAlert("Failed to update the application.", "error");
        }
      } catch (error) {
        if (error.response) {
          const backendMessage = error.response.data.error;
          SwalMessageAlert(`${backendMessage}`, "error"); // For other types of errors
        }
      }
    } else {
      // If the user canceled, you can show a message or do nothing
      Swal.fire('Cancelled', 'The Action Letter Genration was cancelled.', 'info');
    }
  };


  useEffect(() => {

    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          const class1 = response.data.filter((item) => item.className === "Class 4");
          // console.log(class1[0]?._id, " Getting Class Data");

          setClassData(class1[0]?._id);

        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();

  }, [endPoint, token]);


  useEffect(() => {

    const fetchData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/section/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        const verifiedData = response.data.filter((item) => item.isVerified === true);
        // console.log(verifiedData, "verifiedData");
        setSectionData(verifiedData);
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, [endPoint, token]);

  useEffect(() => {
    const fetchDirector = async () => {

      try {
        const response = await axios.get(`${endPoint}/api/director/get-all-director`, {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.status === 200) {
          //   // console.log(response.data);
          const data = response.data.filter((item) => item._id !== directorAtSession && item.classData !== classData)
          setDirectors(data);


        } else {
          alert("Failed to director  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    }
    fetchDirector();

  }, [endPoint, token]);



  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/employees_Leave_all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data.reverse();
          const PendingForActionDirectorate = data.filter(
            (leaves) =>
              leaves.fileAt == directorAtSession && (leaves.leaveStatus === 2 || (leaves.leaveStatus === 6 && (leaves.designation === "PG Principal" || leaves.designation === "UG Principal")))
          );
          // console.log(PendingForActionDirectorate, "PendingForActionDirectorate");

          setApplications(PendingForActionDirectorate);
          setFilteredApplications(PendingForActionDirectorate);

          // window.location.replace("admin/Dashboard");
        } else {
          alert("Data Not Fetched.");
          // navigate('/auth/Register');
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
        // navigate('/auth/Register');
      }
    };
    fetchApplications();
  }, [endPoint, token]);

  useEffect(() => {
    const filteredItems = applications.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilteredApplications(filteredItems);
    setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, applications]);

  const handleFilterChange = (Id) => {
    const status = parseInt(Id);
    // console.log(status);

    setSelectedStatus(status);
    if (status) {
      const filtered = applications.filter((app) => app.leaveStatus === status);
      setFilteredApplications(filtered);
    } else {
      setFilteredApplications(applications); // Reset to all applications if no filter
    }
    setCurrentPage(1); // Reset to first page on filter change
  };

  const getStatusCount = (status) => {
    return applications.filter((app) => app.leaveStatus === status).length;
  };

  const toggle = (status) => {
    setModal(!modal);
    setSelectedStatus(status);
  };

  const toggleForward = () => {
    setModalForward(!modalForward);
  };

  const [movement, setMovement] = useState('');

  const [applicationForMovement, setApplicationForMovement] = useState([]);

  const toggleMovement = async (item) => {
    // console.log("Getting Application ID", item);
    setApplicationForMovement(item)

    try {
      const response = await axios.get(`${endPoint}/api/leave/movement/${item._id}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
      if (response.status === 200) {
        const data = response.data.reverse();
        // console.log(data, "Getting Data");
        setMovement(data)


      } else {
        alert("Data Not Fetched.");
      }
    } catch (error) {
      console.error("An error occurred while Getting Data:", error);
      alert("An error occurred. Please try again later.");
    }

    // setApplicationForMovement(item);
    setCheckMovement(!checkMovement);
  }




  // useEffect(() => {
  //   if (applicationForMovement !== '') {
  //     const fetchApplications = async () => {

  //     };
  //     fetchApplications();
  //   }
  // }, [endPoint, token,applicationForMovement]);


  const handlePageChange = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };

  const handlePendingApplicationDetails = (item) => {
    setSelectedPendingApplication(item);
    setModalPendingReqOpen(true);
  };

  const handleSelectForward = (item) => {
    setSelectedForForward(item);
    setModalForward(true);
  };

  const toggleModal = () => setModalPendingReqOpen(!modalPendingReqOpen);

  const handleActionChange = (e) => {
    setSelectedAction(e.target.value);

    if (remark === '' || remark === "Forwarded" || remark === "Approved" || remark === "Rejected" || remark === "Cancelled" || remark === "Others") {
      setRemark(
        e.target.value === "2"
          ? "Forwarded"
          : e.target.value === "3"
            ? "Approved"
            : e.target.value === "4"
              ? "Rejected"
              : e.target.value === "5"
                ? "Cancelled"
                : "Others"
      );
    }
  };

  const statusCode = selectedStatus;
  useEffect(() => {
    if (statusCode >= 1 && statusCode <= 3) {
      setCurrentStep(statusCode - 1); // Convert status code to zero-based index
    }
  }, [statusCode]);

  const steps = [
    { label: "Employee", icon: "fa-user" },
    { label: "Office", icon: "fa-solid fa-briefcase" },
    { label: "Directorate", icon: "fa-solid fa-user-shield" },
  ];

  const handleRemarkChange = (e) => {
    setRemark(e.target.value);
  };

  const handleShowDetails = (remark1, remark2, remark3) => {
    Swal.fire({
      html: `<div style="text-align: left; max-height: 300px; overflow-y: auto;"><h3><b>Remark By User</h3><p> ${remark1}<p>
           </br> <h3><b>Remark By Principal</b></h3><p> ${remark2}<p>
           </br> <h3><b>Remark By ByDirectorate</b></h3><p> ${remark3}<p></div>`,
      showCloseButton: true,
      confirmButtonText: "Close",
    });
  };

  const handleSaveClick = async () => {
    if (!selectedAction) {
      alert("Please select an action before saving.");
      return;
    }


    const appId = selectedPendingApplication._id;

    try {
      const response = await axios.put(
        `${endPoint}/api/leave/update-status-director/${appId}`,
        {
          action: selectedAction,
          remark: remark,
          director: directorAtSession,

        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Leave status Updated.", "success");
        toggleModal();
        setTimeout(function () {
          window.location.reload();
        }, 3000);
      } else {
        SwalMessageAlert("Failed to update the application.", "error");
      }
    } catch (error) {
      if (error.response) {
        const backendMessage = error.response.data.error;
        SwalMessageAlert(`${backendMessage}`, "error"); // For other types of errors

      }
    }
  };

  const statuses = [
    { valueOf: "", label: `All (${applications.length})`, color: "blue" },
    { valueOf: "2", label: `Pending (${getStatusCount(2)})`, color: "orange" },
    { valueOf: "3", label: `Approved (${getStatusCount(3)})`, color: "darkgreen" },
    { valueOf: "4", label: `Rejected (${getStatusCount(4)})`, color: "red" },
    { valueOf: "5", label: `Cancelled (${getStatusCount(5)})`, color: "skyblue" },
    { valueOf: "6", label: `Applied for Cancel (${getStatusCount(6)})`, color: "orange" },
  ];



  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <div className="col">
            <Card className="shadow">
              <CardHeader className="border-0">
                <h3 className="mb-0">
                  Pending For Action / कार्यवाही हेतु लंबित
                </h3>
                <Row className="mt-3">
                  <Col>
                    {statuses.map((status) => (
                      <button
                        className="btn-sm"
                        key={status.valueOf}
                        onClick={() => handleFilterChange((status.valueOf))}
                        style={{
                          backgroundColor: status.color,
                          color: "white",
                          border: "none",
                          fontWeight: "bolder",
                          fontSize: "14px",
                          borderRadius: "5px",
                          padding: "10px",
                          margin: "5px",
                          cursor: "pointer",
                        }}
                      >
                        {status.label}
                      </button>
                    ))}
                  </Col>
                  <Col md={3}>
                    <Input
                      type="text"
                      placeholder="Search..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    />
                  </Col>
                </Row>
              </CardHeader>
              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                    <th style={{ color: "black", fontWeight: "bolder" }}>Sno. <br /> / क्र.</th>
                    <th style={{ color: "black", fontWeight: "bolder" }}>Forward / Remark / Movement<br /> Action / View / Download </th>
                    {SA === true && <th style={{ color: "black", fontWeight: "bolder" }}>Genrate Letter</th>}
                    <th style={{ color: "black", fontWeight: "bolder" }}>Status <br />स्थिति </th>
                    <th style={{ color: "black", fontWeight: "bolder" }}>Application Details <br /> (आवेदन जानकारी )</th>
                    <th style={{ color: "black", fontWeight: "bolder" }}>Leave Details <br /> (अवकाश जानकारी)</th>
                    <th style={{ color: "black", fontWeight: "bolder" }}>Date <br /> Between <br /> (दिनांक से - तक)</th>

                  </tr>
                </thead>
                <tbody>
                  {currentItems.map((application, index) => (
                    <tr key={application.id}>
                      <td>{indexOfFirstItem + index + 1}</td>
                      <td> <Button
                        color="btn btn-sm m-2 btn-success"
                        onClick={() =>
                          handleSelectForward(application)
                        }
                      >
                        Forward
                      </Button>

                        <button
                          className="btn btn-sm m-2 btn-primary"
                          onClick={() => handleShowDetails(application.remarkByUser, application.remarkByPrincipal, application.remarkByDirector)}>
                          Remark
                        </button>

                        <button
                          className="btn-sm  btn m-2 btn-info"
                          onClick={() => toggleMovement(application)}
                        >
                          Movement
                        </button>

                        <br />

                        {Commissioner === true &&
                          <Button
                            color=" btn btn-sm m-2 btn-success"
                            onClick={() =>
                              handlePendingApplicationDetails(application)
                            }
                          >
                            Action
                          </Button>
                        }

                        <Link to={`/admin/Preview/${application._id}`}>
                          <Button className=" btn btn-sm m-2" style={{ backgroundColor: "yellow" }}>
                            <BsDownload size={18} />
                            <BsEye size={18} />
                          </Button>
                        </Link>

                        <a  href={`https://heonline.cg.nic.in/${application.uploadFile}`} download>
                          <Button className="btn-sm m-2 btn-primary">

                            {application.uploadFile ? <BsDownload size={18} /> : "No File"}
                          </Button>
                        </a>




                      </td>
                      {SA === true && <td>
                        {application.recieptGenrated === false && application.isCommisionerAction === false ?
                          <Badge style={{ backgroundColor: "green", fontSize: "12px", fontWeight: "bolder" }}
                            className="text-white bg-warning">Approval <br /> Pending</ Badge>
                          : application.recieptGenrated === false && application.isCommisionerAction === true ?
                            <button onClick={() => genrateReciept(application)} className="btn-sm btn-warning disabled">Generate Reciept</button> :
                            application.recieptGenrated === true && application.isCommisionerAction === true ?
                              <Link to={`/admin/Genrated/${application._id}`}>
                                <Button className=" btn btn-sm m-2" style={{ backgroundColor: "skyblue" }}>
                                  <BsDownload size={18} />
                                  <BsEye size={18} />
                                </Button>
                              </Link> : 'error'}</td>}
                      <td>
                        <h2>
                          <Badge

                            className="badge-sm"
                            onClick={application.leaveStatus === 1 || application.leaveStatus === 2 ? () => toggle(application.leaveStatus) : null}
                            style={{
                              fontWeight: 'bolder',
                              color: 'white',
                              backgroundColor:
                                application.leaveStatus === 3
                                  ? 'green'  // Approved background color
                                  : application.leaveStatus === 4
                                    ? 'red'   // Rejected background color
                                    : (application.leaveStatus === 1)
                                      ? '#f3c70c'  // Pending background color

                                      : application.leaveStatus === 5
                                        ? 'blue'
                                        : (application.leaveStatus === 2)
                                          ? '#ff8a00'
                                          : (application.leaveStatus === 6)
                                            ? '#ff8a00'
                                            : 'lavender'
                            }}
                          >
                            {application.leaveStatus === 3
                              ? 'Approved'
                              : application.leaveStatus === 4
                                ? 'Rejected'
                                : application.leaveStatus === 6
                                  ? 'approve Cancel'
                                  : (application.leaveStatus === 1)
                                    ? (
                                      <>
                                        <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                        Pending
                                      </>
                                    )
                                    : (application.leaveStatus === 2)
                                      ? (
                                        <>
                                          {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                          Forwarded
                                        </>
                                      )
                                      : application.leaveStatus === 5
                                        ? 'Cancelled'
                                        : 'Error'}
                          </Badge>

                        </h2>
                      </td>
                      <td> <strong> Name : </strong> {application.applicantName} <br /> <strong> Application ID : </strong>{application.applicationId} <br /><strong>Applied Date :</strong>{formatDate(application.appliedDate)} <br /><strong>Emp Code :</strong> {application.empCode}</td>
                      <td><strong>Type : </strong>{application.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : application.leaveType} <br /><strong>Reason :</strong>{application.reason}</td>
                      <td><strong>({formatDate(application.fromDate)}) <br /> to <br /> ({formatDate(application.tillDate)}) <br /> <span style={{ fontWeight: "bolder", color: "red" }}>{application.dayCount} Days</span></strong></td>


                    </tr>
                  ))}
                </tbody>
              </Table>
              <Modal isOpen={modal} toggle={toggle}>
                <ModalHeader toggle={toggle}>Status</ModalHeader>
                <ModalBody>
                  <div className="main">
                    <ul
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      {steps.map((step, index) => (
                        <li
                          key={index}
                          style={{
                            listStyle: "none",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <i
                            className={`icons awesome fa-solid ${step.icon}`}
                            style={{ fontSize: "25px", color: "#1b761b" }}
                          ></i>
                          <div
                            className={`step ${index === currentStep ? "active" : ""
                              }`}
                            style={{
                              height: "30px",
                              width: "30px",
                              borderRadius: "50%",
                              backgroundColor:
                                index <= currentStep ? "#1b761b" : "#d7d7c3",
                              margin: "16px 0 10px",
                              display: "grid",
                              placeItems: "center",
                              color: "ghostwhite",
                              position: "relative",
                              cursor: "default", // Change cursor to default since clicking is disabled
                            }}
                          >
                            <p
                              style={{
                                fontSize: "18px",
                                display:
                                  index <= currentStep ? "none" : "block",
                              }}
                            >
                              {index + 1}
                            </p>
                            <i
                              className="awesome fa-solid fa-check"
                              style={{
                                display: index <= currentStep ? "flex" : "none",
                              }}
                            ></i>
                          </div>
                          <p
                            className="label"
                            style={{
                              fontFamily: "sans-serif",
                              letterSpacing: "1px",
                              fontSize: "14px",
                              fontWeight: "bold",
                              color: "#1b761b",
                            }}
                          >
                            {step.label}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button color="secondary" onClick={toggle}>
                    Close
                  </Button>
                  {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                </ModalFooter>
              </Modal>
              <Modal style={{ maxWidth: "1000px", minWidth: "400px" }} isOpen={checkMovement} toggle={toggleMovement}>
                <ModalHeader toggle={toggleMovement}><h3>File Movement Of ({applicationForMovement.applicationId}) </h3></ModalHeader>
                <ModalBody>

                  <Table className="align-items-center table-flush" responsive bordered>
                    <thead className="thead-light">
                      <tr>
                        <th>Sno./ क्र.</th>
                        <th>Action/ <br />Sent By</th>
                        <th>Action Date</th>
                        <th>Day Taken</th>
                        <th>Status</th>
                        <th>Remarks</th>
                        <th>Currently With</th>
                      </tr>
                    </thead>
                    <tbody>
                      {movement && movement.length > 0 ? movement.map((item, index) =>
                      (
                        <tr key={item._id}>
                          <td className="text-center">{index + 1}</td>
                          <td className="text-left">{item.directorName}</td>
                          <td className="text-center">{formatDate(item.actionDate)}</td>
                          <td className="text-center">{item.dayTaken}</td>

                          <td>
                            <h2>
                              <Badge

                                className="badge-sm"
                                onClick={item.status === 1 || item.status === 2 ? () => toggle(item.status) : null}
                                style={{
                                  fontWeight: 'bolder',
                                  color: 'white',
                                  backgroundColor:
                                    item.status === 3
                                      ? 'green'  // Approved background color
                                      : item.status === 4
                                        ? 'red'   // Rejected background color
                                        : (item.status === 1)
                                          ? '#f3c70c'  // Pending background color
                                          : (item.status === 6)
                                            ? '#ff8a00'
                                            : item.status === 5
                                              ? 'blue'
                                              : (item.status === 2)
                                                ? '#ff8a00'
                                                : 'lavender'
                                }}
                              >
                                {
                                  item.status === 3 && item.recieptGenrated === true
                                    ? 'Approved'
                                    : item.status === 4 && item.recieptGenrated === true
                                      ? 'Rejected'
                                      : item.status === 6 && item.recieptGenrated === true
                                        ? 'Approve Cancel'
                                        : item.status === 1
                                          ? (
                                            <>
                                              <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                              Pending at principal
                                            </>
                                          )
                                          : item.status === 2
                                            ? 'Forwarded'
                                            : item.status === 5
                                              ? 'Cancelled'
                                              : item.status === 4 && item.isRecieptGenrated === false
                                                ? 'Rejected By Commissioner'
                                                : item.status === 3 && item.isRecieptGenrated === false
                                                  ? 'Approved By Commissioner'
                                                  : 'Error'
                                }
                              </Badge>

                            </h2>
                          </td>
                          <td className="text-left">{item.remarksFileMovment}</td>
                          <td className="text-center"><strong style={{ fontWeight: "bolder", fontSize: "14px" }}>
                            {item.sendToDirector}
                          </strong>
                            {index === 0 && (
                              <i
                                className="fa fa-check fa-xl"
                                style={{ marginLeft: "10px", color: "green" }}
                                aria-hidden="true"
                              ></i>
                            )} <br />
                            <strong className="text-primary">({item.sectionName})</strong>
                          </td>
                        </tr>
                      )) : <tr>
                        <td colSpan="6" className="text-center">No Data Found</td>
                      </tr>}
                    </tbody>
                  </Table>
                </ModalBody>
                <ModalFooter>
                  <Button color="secondary" onClick={toggleMovement}>
                    Close
                  </Button>
                  {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                </ModalFooter>
              </Modal>
              <Modal
                style={{ maxWidth: "800px", minWidth: "400px" }}
                isOpen={modalForward}
                toggle={toggleForward}
              >
                {selectedForForward && (
                  <ModalHeader toggle={toggleForward}>
                    <div className="d-flex">
                      <h1>Application Details : </h1>
                      {selectedForForward.leaveStatus === 2 ? (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Leave
                        </h2>
                      ) : selectedForForward.leaveStatus === 6 ? (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Cancelation
                        </h2>
                      ) : (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Joining
                        </h2>
                      )}
                    </div>
                  </ModalHeader>
                )}
                <ModalBody>
                  {selectedForForward ? (
                    <Container fluid>
                      <Row className="mt--3">
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Application ID:
                            </strong>{" "}
                            {selectedForForward.applicationId}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Applicant Name:
                            </strong>{" "}
                            {selectedForForward.applicantName}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Applied Date:
                            </strong>{" "}
                            {formatDate(
                              selectedForForward.appliedDate
                            )}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Leave Type:
                            </strong>{" "}
                            {selectedForForward.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : selectedForForward.leaveType}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Leave Reason:
                            </strong>{" "}
                            {selectedForForward.reason}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Total Days:
                            </strong>{" "}
                            {selectedForForward.dayCount}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">From Date:</strong>{" "}
                            {formatDate(
                              selectedForForward.fromDate
                            )}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">Till Date:</strong>{" "}
                            {formatDate(
                              selectedForForward.tillDate
                            )}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Station Leave
                            </strong>{" "}
                            {selectedForForward.permission === 1
                              ? "Yes"
                              : "No"}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Address During Leave
                            </strong>{" "}
                            {selectedForForward.stationAddress}
                          </p>
                        </Col>
                      </Row>
                    </Container>
                  ) : (
                    <p>No details available.</p>
                  )}
                </ModalBody>
                <ModalFooter className="d-block">
                  <Row>
                    <Col>
                      {PA === true && <FormGroup>
                        <Label><strong>Send To Section {PA}</strong> </Label>
                        <Input
                          id="director"
                          type="select"
                          name="director"
                          required
                          onChange={handleDirectorChangePA}
                        >
                          <option value="">Select Section</option>
                          {sectionData &&
                            sectionData.length > 0 &&
                            sectionData.map((type, index) => (
                              <option key={index} value={type.sectionOfficer}>
                                {type.sectionName}
                              </option>
                            ))}
                        </Input>
                      </FormGroup>}

                      {PA === false &&
                        <FormGroup>
                          <Label><strong>Send To Director {PA}</strong> </Label>
                          <Input
                            id="director"
                            type="select"
                            name="director"
                            required
                            onChange={handleSelectedDirectorChange}
                          >
                            <option value="">Select Designation</option>
                            {directors &&
                              directors.length > 0 &&
                              directors.map((type, index) => (
                                Array.isArray(type.section) && type.section.length > 0
                                  ? type.section.map((sectionId, sectionIndex) => {
                                    const sectionDetail = type.sectionData?.find(
                                      (data) => data._id === sectionId
                                    );
                                    return (
                                      <option
                                        key={`${index}-${sectionIndex}`}
                                        value={type._id}
                                        data-section-id={sectionId}
                                      >
                                        {type.name}<span className="text-danger">({type.designationName})</span> <strong>({sectionDetail.sectionName})</strong>
                                      </option>
                                    );
                                  })
                                  : (
                                    <option key={index} value={type._id} data-section-id={null}>
                                      {type.name} <strong>No sections available</strong>
                                    </option>
                                  )
                              ))}
                          </Input>
                        </FormGroup>}
                    </Col><Col>
                      <FormGroup>
                        <Label><strong>Remark</strong></Label>
                        <Input
                          type="textarea"
                          name="remarkFileMovement"
                          style={{ color: "black", width: "340px", height: "40px" }}
                          id="remarksTextArea"
                          value={movementData.remarkFileMovement}
                          row="1"
                          onChange={handleInputChange}
                          placeholder="Enter your remarks"
                          required
                        />
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Button color="success" onClick={handleForward}>
                        Forward
                      </Button>
                      <Button color="danger" onClick={toggleForward}>
                        Close
                      </Button>
                    </Col>
                  </Row>
                </ModalFooter>
              </Modal>
              <Modal
                style={{ maxWidth: "800px", minWidth: "400px" }}
                isOpen={modalPendingReqOpen}
                toggle={toggleModal}
              >
                {selectedPendingApplication && (
                  <ModalHeader toggle={toggleModal}>
                    <div className="d-flex">
                      <h1>Application Details : </h1>
                      {selectedPendingApplication.leaveStatus === 2 ? (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Leave
                        </h2>
                      ) : selectedPendingApplication.leaveStatus === 6 ? (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Cancelation
                        </h2>
                      ) : (
                        <h2 className="text-primary mt-1 pl-4">
                          Application for Joining
                        </h2>
                      )}
                    </div>
                  </ModalHeader>
                )}
                <ModalBody>
                  {selectedPendingApplication ? (
                    <Container fluid>
                      <Row className="mt--3">
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Application ID:
                            </strong>{" "}
                            {selectedPendingApplication.applicationId}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Applicant Name:
                            </strong>{" "}
                            {selectedPendingApplication.applicantName}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Applied Date:
                            </strong>{" "}
                            {formatDate(
                              selectedPendingApplication.appliedDate
                            )}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Leave Type:
                            </strong>{" "}
                            {selectedPendingApplication.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : selectedPendingApplication.leaveType}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Leave Reason:
                            </strong>{" "}
                            {selectedPendingApplication.reason}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Total Days:
                            </strong>{" "}
                            {selectedPendingApplication.dayCount}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">From Date:</strong>{" "}
                            {formatDate(
                              selectedPendingApplication.fromDate
                            )}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">Till Date:</strong>{" "}
                            {formatDate(
                              selectedPendingApplication.tillDate
                            )}
                          </p>
                        </Col>
                      </Row>
                      <Row>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Station Leave
                            </strong>{" "}
                            {selectedPendingApplication.permission === 1
                              ? "Yes"
                              : "No"}
                          </p>
                        </Col>
                        <Col sm={6}>
                          <p>
                            <strong className="text-primary">
                              Address During Leave
                            </strong>{" "}
                            {selectedPendingApplication.stationAddress}
                          </p>
                        </Col>
                      </Row>
                    </Container>
                  ) : (
                    <p>No details available.</p>
                  )}
                </ModalBody>
                <ModalFooter>
                  <FormGroup>
                    <Input
                      type="textarea"
                      style={{ color: "black", width: "340px", height: "40px" }}
                      id="remarksTextArea"
                      value={remark}
                      row="1"
                      onChange={handleRemarkChange}
                      placeholder="Enter your remarks"
                    />
                  </FormGroup>
                  <FormGroup>
                    <Input
                      type="select"
                      style={{ color: "Black" }}
                      id="actionSelect"
                      value={selectedAction}
                      onChange={handleActionChange}
                    >
                      <option value="">Select an action</option>
                      {selectedPendingApplication &&
                        selectedPendingApplication.leaveStatus !== 6 && (
                          <>
                            <option
                              style={{
                                backgroundColor: "green",
                                color: "white",
                              }}
                              value="3"
                            >
                              Approve
                            </option>
                            <option
                              style={{ backgroundColor: "red", color: "white" }}
                              value="4"
                            >
                              Reject
                            </option>
                          </>
                        )}
                      {selectedPendingApplication &&
                        selectedPendingApplication.leaveStatus === 6 && (
                          <>
                            <option
                              style={{
                                backgroundColor: "green",
                                color: "white",
                              }}
                              value="5"
                            >
                              Cancel
                            </option>
                          </>
                        )}
                    </Input>
                  </FormGroup>
                  <Button color="primary" onClick={handleSaveClick}>
                    Action
                  </Button>
                  <Button color="secondary" onClick={toggleModal}>
                    Close
                  </Button>
                </ModalFooter>
              </Modal>
              <CardFooter className="py-4">
                <div>
                  <label htmlFor="recordsPerPage"> <span style={{ fontWeight: "bold" }}> No. of Records Per Page: </span></label>
                  <select
                    id="recordsPerPage"
                    value={itemsPerPage}
                    onChange={handleChange}
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>
                <nav aria-label="...">
                  <Pagination className="pagination justify-content-end mb-0">
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem
                        key={i}
                        className={currentPage === i + 1 ? "active" : ""}
                      >
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(i + 1);
                          }}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </div>
        </Row>
      </Container >
    </>
  );
};

export default LeaveActionAtDirector;
