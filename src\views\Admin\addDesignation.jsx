import { useState, useEffect, useLayoutEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Table,
  CardFooter,
  Container,
  Row,
  Col,
  Label,
  Spinner,
} from "reactstrap";
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
const AddDesignation = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [formData, setFormData] = useState({
    designation: "",
    dClass: "",
  });
  const [classFormData, setclassFormData] = useState({
    className: "",
  });
  const [data, setData] = useState([]);
  const [classData, setClassData] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: "", direction: "asc" });
  const [searchTerm, setSearchTerm] = useState("");
  const [filterClass, setFilterClass] = useState("");
  const [currentPage, setCurrentPage] = useState(1);

  const entriesPerPage = 5;

  useEffect(() => {
    fetchData();
    fetchClassData();
  }, []);

  const fetchData = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/designation/getAll`, {
        headers: {

          'Content-Type': 'application/json',
           'web-url': window.location.href,
          Authorization: `Bearer ${token}` },
      });
      const unverifiedData = response.data.filter(
        (item) => item.isVerified === 0
      );
      setData(unverifiedData);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const fetchClassData = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/class/getAll`, {
        headers: { 
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}` },
      });
      if (response.status === 200) {
        setClassData(response.data);
      } else {
        SwalMessageAlert("No Class Data Found", "error");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setclassFormData({ ...formData, [name]: value });
  };

  const handleVerification = (item) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Please verify the details. If verification is not completed, the data will not be saved.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Verify",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        // Set the formData isVarified field to "1" after verification
        setFormData((prevData) => ({ ...prevData, isVarified: "1" }));

        // Make the PUT API call to update the designation
        try {
          const response = await axios.put(
            `${endPoint}/api/designation/update/${item._id}`,
            {
              designation: String(item.designation),
              dClass: String(item.class),
              isVerified: "1",
            },
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            Swal.fire(
              "Success!",
              "Designation updated successfully!",
              "success"
            );
            window.location.reload();
          } else {
            Swal.fire("Error", "Failed to update designation.", "error");
          }
        } catch (error) {
          console.error("An error occurred while updating the data:", error);
          Swal.fire(
            "Error",
            "An error occurred. Please try again later.",
            "error"
          );
        }
      } else {
        Swal.fire("Cancelled", "Verification was not completed.", "info");
      }
    });
  };

  const handleSubmitClass = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    e.preventDefault();
    try {
      const body = {
        className: classFormData.className,
      };
      const response = await axios.post(`${endPoint}/api/class/add`, body, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 201) {
        SwalMessageAlert("Class added successfully!", "success");
        setclassFormData({ className: "" });
        fetchData();
      } else {
        alert("Failed to add designation.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    try {
      const body = {
        dClass: String(formData.dClass),
        designation: String(formData.designation),
      };
      const response = await axios.post(
        `${endPoint}/api/designation/add`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 201) {
        SwalMessageAlert("Designation added successfully!", "success");
        setFormData({ designation: "", dClass: "" });
        fetchData();
      } else {
        alert("Failed to add designation.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const handleSort = (key) => {
    const direction =
      sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
    setSortConfig({ key, direction });
  };

  const sortedData = [...data].sort((a, b) => {
    if (sortConfig.key) {
      const order = sortConfig.direction === "asc" ? 1 : -1;
      return a[sortConfig.key] > b[sortConfig.key] ? order : -order;
    }
    return 0;
  });

  const filteredData = sortedData.filter((item) => {
    return (
      item.designation.toLowerCase().includes(searchTerm.toLowerCase()) &&
      (!filterClass || String(item.class) === filterClass)
    );
  });

  const paginatedData = filteredData.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  const totalPages = Math.ceil(filteredData.length / entriesPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        {/* Form Section */}
        <Row>
          <Col lg="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Add Class</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-className">Class Name</Label>
                          <Input
                            type="text"
                            name="className"
                            id="input-className"
                            placeholder="Enter Class Name"
                            value={classFormData.className}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button color="primary" onClick={handleSubmitClass}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
          <Col lg="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Add Designation</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-class">Class</Label>
                          <Input
                            type="select"
                            name="dClass"
                            id="input-class"
                            value={formData.dClass}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select Class</option>
                            {classData &&
                              classData.length > 0 &&
                              classData.map((type, index) => (
                                <option key={index} value={type._id}>
                                  {type.className}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-designation">Designation</Label>
                          <Input
                            type="text"
                            name="designation"
                            id="input-designation"
                            placeholder="Designation"
                            value={formData.designation}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button color="primary"  onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Table Section with Pagination */}
        <Row className="mt-4">
          <Col lg="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Class List</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Table responsive hover striped>
                  <thead>
                    <tr>
                      <th>Sno</th>
                      <th>Class Name</th>
                      <th>Last Updated Date</th>
                      <th>Created Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {classData.map((className, index) => (
                      <tr key={index}>
                        <td>{index + 1}</td>
                        <td>{className.className}</td>
                        <td>{formatDate(className.updatedAt)}</td>
                        <td>{formatDate(className.createdAt)}</td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
          <Col lg="6">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">
                      Verify Updates
                      <span style={{ color: "red", fontSize: "12px" }}>
                        ( Unverified Data will not be finalized, so please
                        verify. )
                      </span>
                    </h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Row className="mb-3">
                  <Col md="4">
                    <Input
                      type="text"
                      placeholder="Search by Designation"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </Col>
                  <Col md="4">
                    <Input
                      type="select"
                      value={filterClass}
                      onChange={(e) => setFilterClass(e.target.value)}
                    >
                      <option value="">Filter by Class</option>
                      {classData &&
                        classData.length > 0 &&
                        classData.map((type, index) => (
                          <option key={index} value={type._id}>
                            {type.className}
                          </option>
                        ))}
                    </Input>
                  </Col>
                </Row>
                <Table responsive hover striped>
                  <thead>
                    <tr>
                      <th>Sno</th>
                      <th>Verify</th>
                      <th
                        onClick={() => handleSort("designation")}
                        style={{ cursor: "pointer" }}
                      >
                        Designation{" "}
                        {sortConfig.key === "designation"
                          ? sortConfig.direction === "asc"
                            ? "▲"
                            : "▼"
                          : ""}
                      </th>
                      <th
                        onClick={() => handleSort("class")}
                        style={{ cursor: "pointer" }}
                      >
                        Class{" "}
                        {sortConfig.key === "class"
                          ? sortConfig.direction === "asc"
                            ? "▲"
                            : "▼"
                          : ""}
                      </th>
                      <th>Last Updated Date</th>
                     
                    </tr>
                  </thead>
                  <tbody>
                    {paginatedData.map((item, index) => (

                      <tr key={item.id}>
                        <td>
                          {index + 1 + (currentPage - 1) * entriesPerPage}
                        </td>
                        <td>
                          <Button
                            className="btn btn-warning btn-sm"
                            type="button"
                            onClick={() => handleVerification(item)}
                          >
                            <Spinner
                                  size="sm"
                                  color="white"
                                  style={{ marginRight: "8px" }}
                                />
                                Verify
                          </Button>
                        </td>
                        <td>{item.designation}</td>
                        <td>{classData.filter((items) => String(items._id) === String(item.class))[0]?.className}</td>
                        <td>{formatDate(item.updatedAt)}</td>
                       
                      </tr>
                    ))}
                  </tbody>
                </Table>
              </CardBody>
              <CardFooter>
                <div className="pagination">
                  {[...Array(totalPages)].map((_, index) => (
                    <Button
                      key={index}
                      color={
                        currentPage === index + 1 ? "primary" : "secondary"
                      }
                      onClick={() => handlePageChange(index + 1)}
                      size="sm"
                      className="mr-2"
                    >
                      {index + 1}
                    </Button>
                  ))}
                </div>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddDesignation;
