{"name": "lmsfrontend", "private": true, "version": "0.0.0", "type": "module", "proxy": "http://localhost:3001/lmsbackend", "homepage": "/", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "6.5.1", "@fortawesome/fontawesome-svg-core": "^6.7.1", "@loadable/component": "^5.16.4", "@react-pdf/renderer": "^4.0.0", "@tensorflow/tfjs": "^4.22.0", "ajv": "^8.16.0", "axios": "^1.7.9", "bootstrap": "^4.6.2", "chart.js": "2.9.4", "classnames": "2.3.2", "cors": "^2.8.5", "dotenv": "^16.4.5", "face-api.js": "^0.22.2", "file-saver": "^2.0.5", "framer-motion": "^12.4.2", "html2canvas": "^1.4.1", "html2pdf": "^0.0.11", "jspdf": "^3.0.1", "moment": "^2.29.4", "nouislider": "15.4.0", "pdfmake": "^0.2.14", "prop-types": "^15.8.1", "react": "^18.3.1", "react-big-calendar": "^1.15.0", "react-bootstrap": "^2.10.9", "react-bootstrap-icons": "^1.11.6", "react-calendar": "^5.0.0", "react-circular-progressbar": "^2.1.0", "react-copy-to-clipboard": "5.1.0", "react-data-table-component": "^7.6.2", "react-datetime": "3.2.0", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-modal": "^3.16.3", "react-multi-select-component": "^4.3.4", "react-router-dom": "6.21.1", "react-select": "^5.9.0", "react-toastify": "^11.0.5", "react-webcam": "^7.2.0", "reactstrap": "8.10.0", "sass": "1.69.5", "sweetalert2": "^11.14.3", "swiper": "^11.2.3", "translate": "^3.0.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.13.0", "@types/jspdf": "^1.3.3", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "eslint": "^9.13.0", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "globals": "^15.11.0", "vite": "^5.4.9"}}