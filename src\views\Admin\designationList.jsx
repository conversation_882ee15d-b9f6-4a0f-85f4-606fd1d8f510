import { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>ooter,
  Container,
  Row,
  Col,
  Table,
  Pagination,
  PaginationItem,
  PaginationLink,
  Input,
  FormGroup,
  Label,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";

const DesignationList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [filteredDesignations, setFilteredDesignations] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [classFilter, setClassFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page for pagination
  const [sortConfig, setSortConfig] = useState({ key: "", direction: "asc" });


  const [designations, setDesignations] = useState([]);
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const verifiedDesignations = response.data.filter(
            (designation) => designation.isVerified === 1
          );

          setDesignations(verifiedDesignations);
          setFilteredDesignations(verifiedDesignations);
        } else {
          alert("Failed to fetch designations. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchDesignations();
  }, [endPoint, token]);

  // Handle filtering by class and search term
  useEffect(() => {
    const filtered = designations.filter((designation) => {
      const matchesClass =
        !classFilter || String(designation.class) === classFilter;
      const matchesSearchTerm =
        !searchTerm ||
        designation.designation
          .toLowerCase()
          .includes(searchTerm.toLowerCase());

      return matchesClass && matchesSearchTerm;
    });
    setFilteredDesignations(filtered);
  }, [classFilter, searchTerm, designations]);

  // Sorting function
  const handleSort = (key) => {
    let direction = "asc";
    if (sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc";
    }

    const sortedDesignations = [...filteredDesignations].sort((a, b) => {
      if (a[key] < b[key]) return direction === "asc" ? -1 : 1;
      if (a[key] > b[key]) return direction === "asc" ? 1 : -1;
      return 0;
    });

    setSortConfig({ key, direction });
    setFilteredDesignations(sortedDesignations);
  };

  // Pagination calculations
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDesignations.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredDesignations.length / itemsPerPage);

  // Handle page change
  const handlePageChange = (pageNumber) => setCurrentPage(pageNumber);

  const [classData, setClassData] = useState([]);
  const [filterClass, setFilterClass] = useState("");
  useEffect(() => {
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
  }, [endPoint, token]);

  

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0">
                <h3 className="mb-0">
                  Designation List{" "}
                  <span style={{ color: "red", fontSize: "12px" }}>
                    (Only Verified Data will be Show here, so please verify. )
                  </span>
                </h3>
                <Row className="mt-3">
                  <Col md="6">
                    <FormGroup>
                      <Label for="searchDesignation">
                        Search by Designation
                      </Label>
                      <Input
                        type="text"
                        id="searchDesignation"
                        placeholder="Enter designation name"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                      />
                    </FormGroup>
                  </Col>
                  <Col md="6">
                    <FormGroup>
                      <Label for="filterClass">Filter by Class</Label>
                      <Input
                        type="select"
                        value={filterClass}
                        onChange={(e) => setFilterClass(e.target.value)}
                      >
                        <option value="">Filter by Class</option>
                        {classData &&
                          classData.length > 0 &&
                          classData.map((type, index) => (
                            <option key={index} value={type._id}>
                              {type.className}
                            </option>
                          ))}
                      </Input>
                    </FormGroup>
                  </Col>
                </Row>
              </CardHeader>
              <Table className="align-items-center table-flush" hover active responsive>
                <thead className="thead-light">
                  <tr>
                    <th>S.No</th>
                    <th scope="col" onClick={() => handleSort("designation")}>Designation {sortConfig.key === "designation" ? (sortConfig.direction === "asc" ? "↑" : "↓") : ""}</th>
                    <th scope="col" onClick={() => handleSort("class")}>Class {sortConfig.key === "class" ? (sortConfig.direction === "asc" ? "↑" : "↓") : ""}</th>
                    <th scope="col" onClick={() => handleSort("createdAt")}>Created At {sortConfig.key === "createdAt" ? (sortConfig.direction === "asc" ? "↑" : "↓") : ""}</th>
                    <th scope="col" onClick={() => handleSort("updatedAt")}>Updated At {sortConfig.key === "updatedAt" ? (sortConfig.direction === "asc" ? "↑" : "↓") : ""}</th>
                    {/* <th scope="col">Edit</th> */}
                  </tr>
                </thead>
                <tbody>
                  {currentItems.map((item, index) => (
                    <tr key={index}>
                      <td>{indexOfFirstItem + index + 1}</td>
                      <td>{item.designation}</td>
                      <td>{classData.find((a) => String(a._id) === String(item.class))?.className}</td>
                      <td>{new Date(item.createdAt).toLocaleDateString()}</td>
                      <td>{new Date(item.updatedAt).toLocaleDateString()}</td>
                      {/* <td>
                        <Link to={`/admin/update-designation/${item._id}`}>
                          <button className="btn btn-warning btn-sm">
                            Edit
                          </button>
                        </Link>
                      </td> */}
                    </tr>
                  ))}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <nav aria-label="...">
                  <Pagination className="pagination justify-content-end mb-0">
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem
                        key={i}
                        className={currentPage === i + 1 ? "active" : ""}
                      >
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(i + 1);
                          }}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default DesignationList;
