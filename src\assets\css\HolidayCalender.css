/* src/HolidayCalendar.css */

.custom-calendar {
    border: none; /* Remove default border */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 4px 20px rgba(78, 187, 5, 0.815); /* Box shadow */
    background-color: #f7f9fc; /* Background color */
    padding: 20px; /* Padding for better spacing */
    max-width: 400px; /* Max width for the calendar */
    margin: auto; /* Center the calendar */
}

.react-calendar {
    width: 100%; /* Full width */
    max-width: 350px; /* Max width */
    border: none; /* Remove border */
    border-radius: 10px; /* Rounded corners */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Light shadow */
    background-color: #ffffff; /* Background color */
}

.react-calendar__month-view__days__day {
    width: 40px; /* Width for each day */
    height: 40px; /* Height for each day */
    display: flex; /* Flexbox for centering */
    align-items: center; /* Center items vertically */
    justify-content: center; /* Center items horizontally */
    font-size: 1rem; /* Font size */
    border-radius: 50%; /* Circular shape */
    transition: background-color 0.3s; /* Smooth background color change */
}

.react-calendar__month-view__days__day--weekend {
    color: #ad9c9c; /* Color for weekend days */
}

.react-calendar__tile {
    padding: 10px; /* Padding for tiles */
    transition: background-color 0.3s; /* Smooth background color transition */
}

.react-calendar__tile--active {
    background-color: #007bff; /* Active day color */
    color: white; /* Active day text color */
    border-radius: 50%; /* Circular shape */
}

.react-calendar__tile--active:enabled:hover {
    background-color: #08b961; /* Darker color on hover */
}

.react-calendar__tile--now {
    border: 2px solid #00ff9d; /* Current day border */
}

.holiday-info {
    background-color: #ffffff; /* Background for holiday info */
    border-radius: 5px; /* Rounded corners */
    padding: 15px; /* Padding */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Light shadow */
    margin-top: 15px; /* Margin on top */
}

.holiday-info h5 {
    margin: 0; /* No margin */
    font-weight: bold; /* Bold text */
}

.holiday-info p {
    margin: 5px 0; /* Small margin */
    color: #be0000; /* Text color */
}

.text-primary {
    color: #007bff; /* Primary text color */
}

.text-center {
    text-align: center; /* Center text */
}
/* Highlight current date with a green circle */
/* Highlight current date with a medium green circle */
.current-day {
    background-color: green;
    color: white !important;
    border-radius: 50%;
    height: 20px; /* Adjusted height */
    width: 30px;  /* Adjusted width */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px; /* Adjust font size for better visibility */
}

/* Highlight holidays with a medium red circle */
.holiday {
    background-color: rgba(255, 123, 0, 0.973);
    color: white !important;
    border-radius: 50%;
    height: 20px; /* Adjusted height */
    width: 30px;  /* Adjusted width */
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px; /* Adjust font size for better visibility */
}

  
  /* Ensure other tiles look normal */
  .react-calendar__tile {
    padding: 10px;
    height: 40px;
    width: 40px;
  }
  ::-webkit-calendar-picker-indicator {
    filter: invert(1);
}
