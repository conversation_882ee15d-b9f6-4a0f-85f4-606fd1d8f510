
import { NavItem, NavLink, Nav, Container, <PERSON>, Col } from "reactstrap";

const Login = () => {
  return (
    <>
      <footer className="py-5">
        <Container>
          <Row className="align-items-center justify-content-xl-between">
            <Col xl="6">
              <div className="copyright text-center text-xl-left text-muted">
                © {new Date().getFullYear()}{" "}
                <a className="font-weight-bold ml-1" target="_blank">
                  NIC
                </a>
              </div>
            </Col>
            <Col xl="6">
              <Nav className="nav-footer justify-content-center justify-content-xl-end">
                <NavItem>
                  <NavLink
                    
                    target="_blank"
                  >
                    
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                
                    target="_blank"
                  >
                    
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                    
                    target="_blank"
                  >
                    
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                    
                    target="_blank"
                  >
                    
                  </NavLink>
                </NavItem>
              </Nav>
            </Col>
          </Row>
        </Container>
      </footer>
    </>
  );
};

export default Login;
