import { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardBody, Input, Button, Container, Row, Col, FormGroup, Label, FormText, Spinner } from "reactstrap";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import Swal from "sweetalert2";

const PhdEligibilityForm = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const empCode = sessionStorage.getItem("empId");
    const id = sessionStorage.getItem("id");
    const empIdCode = empCode?.trim() ? empCode : (id?.trim() ? id : null); // Extracting empIdCode from id or  empCode
    const [employeeInfo, setEmployee] = useState(null);
    const [errors, setErrors] = useState({});
    const [step, setStep] = useState(1);
    const [loading, setLoading] = useState(false);
    const [loadingDraft, setLoadingDraft] = useState(true);
    const [existingData, setExistingData] = useState(null);
    const [isEditable, setIsEditable] = useState(true);
    // const [allCollegeList,setCollege]=useState();
    const [sessionInfo, setSessionInfo] = useState();



    const [form, setForm] = useState({
        isPhdEnrolled: "",
        courseWorkDone: "",
        mphilDone: "",
        elCount: "",
        registrationNo: "",
        courseWorkCertificate: null,
        serviceBook: null,
        sodhNirdeshakAlloted: "",
        sodhNirdeshak: "",
        sodhKendra: "",
        satyaapitPatra: null,
        university: "",
        sodhKaVisay: "",
        vetanMan: "",
        kulSevaWadhi: "",
        shaikshanikYogyata: "",
        mphilCertificate: null,
        sodhPanjiyanTithi: "",
        sodhKaSirsak: "",
        pracharyaKaTipAnushansa: "",
        pracharyaSahmatiPatra: null,
    });

    // Fetch employee info based on empIdCode
    useEffect(() => {
        const fetchEmployee = async () => {
            if (!empIdCode || empIdCode) return;  // Prevent unnecessary API calls if empIdCode is missing

            try {
                const response = await axios.get(
                    `${endPoint}/api/get-employee-info/${empIdCode}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    // console.log("eeee",response.data)
                    setEmployee(response.data);   // assuming you have setEmployee in state
                } else {
                    alert("Failed to fetch employee data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching employee data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchEmployee();
    }, [endPoint, token, empIdCode,]);

    // Create separate variables for displaying employee information
    const employeeName = employeeInfo?.data?.name || "";
    const employeeDesignation = employeeInfo?.data?.designation || "";
    const employeeCollege = employeeInfo?.data?.college || "";
    const employeeDOB = employeeInfo?.data?.personalInformation?.employeeDOB
        ? employeeInfo.data.personalInformation.employeeDOB.split('T')[0]
        : "";
    const employeeJoiningDate = employeeInfo?.data?.personalInformation?.joiningDate
        ? employeeInfo.data.personalInformation.joiningDate.split('T')[0]
        : "";


    // For Submit And Draft Form 
    const handleSubmit = async (isFinalSubmit = false) => {
        // Check if user is authenticated
        if (!token) {
            SwalMessageAlert("❌ Please login to submit the form.", "error");
            return;
        }

        // Check if form is already finalized and not editable
        if (!isEditable) {
            SwalMessageAlert("❌ This form has been finalized and cannot be edited.", "error");
            return;
        }

        // For final submission, show confirmation dialog
        if (isFinalSubmit) {
            const result = await Swal.fire({
                title: 'Final Submission Confirmation',
                text: 'Are you sure you want to submit this form? Once submitted, you will not be able to edit it.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#dc3545',
                confirmButtonText: 'Yes, Submit Final Form',
                cancelButtonText: 'Cancel'
            });

            if (!result.isConfirmed) {
                return;
            }

            // Validate all required fields for final submission
            if (!validatePersonalFields()) {
                SwalMessageAlert("❌ कृपया सभी आवश्यक फ़ील्ड भरें।", "error");
                return;
            }
        }

        setLoading(true);
        try {
            const formData = new FormData();

            Object.entries(form).forEach(([key, value]) => {
                if (value !== null && value !== undefined && value !== "") {
                    formData.append(key, value);
                }
            });

            // Add emp Info for backend processing
            formData.append("empCode", employeeInfo.data.empCode);
            formData.append("sessionYear",sessionInfo.sessionYear);
            formData.append("isDrafted", isFinalSubmit ? false : true);
            formData.append("name",employeeInfo.data.name);
            formData.append("padKaNam",employeeInfo.data.designation);
            formData.append("padsthaMahavidhalay",employeeInfo.data.college);
            formData.append("janmTithi",employeeInfo.data.personalInformation?.employeeDOB);
            formData.append("niyuktiTithi",employeeInfo.data.personalInformation?.joiningDate);

            for (let [key, value] of formData.entries()) {
                console.log(key, ":", value);
            }

            const response = await axios.post(
                `${endPoint}/api/new-phd-form-submit`,
                formData,
                {
                    headers: {
                        "Content-Type": "multipart/form-data",
                        "web-url": window.location.href,
                        "Authorization": `Bearer ${token}`,
                    },
                }
            );

            if (response.status === 200 || response.status === 201) {
                SwalMessageAlert(
                    isFinalSubmit
                        ? "✅ Form submitted successfully!"
                        : "✅ Form saved as draft!",
                    "success"
                );
                if (isFinalSubmit) {
                    setIsEditable(false);
                    // Refresh the page after a short delay to show the locked state
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
                setTimeout(() => {
                    window.location.reload();
                }, 1500);

            } else {
                SwalMessageAlert("⚠️ Unexpected response from server.", "warning");
            }
        } catch (error) {
            if (error.response) {
                const errorData = error.response.data;
                let message = "Server error occurred";
                // Try to extract meaningful error message
                if (typeof errorData === 'string') {
                    message = errorData;
                } else if (errorData?.message) {
                    message = errorData.message;
                } else if (errorData?.msg) {
                    message = errorData.msg;
                } else if (errorData?.error) {
                    message = errorData.error;
                }

                const details = errorData?.details || errorData?.stack || "";
                SwalMessageAlert(`❌ ${message}${details ? ` - ${details}` : ""}`, "error");
            } else if (error.request) {
                SwalMessageAlert("❌ No response from server. Please check your connection.", "error");
            } else {
                SwalMessageAlert(`❌ ${error.message}`, "error");
            }
        } finally {
            setLoading(false);
        }
    };

    // List application Form 
    useEffect(() => {
        const fetchApplicationData = async () => {
            if (!token || !empCode) {
                setLoadingDraft(false);
                return;
            }

            try {
                const response = await axios.get(
                    `${endPoint}/api/phd-application/${empCode}`,
                    {
                        headers: {
                            "Authorization": `Bearer ${token}`,
                            "web-url": window.location.href,
                        },
                    }
                );

                if (response.status === 200 && response.data.success && Array.isArray(response.data.data)) {
                    const applicationsList = response.data.data;

                    // You can choose latest draft or latest final
                    const latestApplication = applicationsList.find(app => app.isEditable) || applicationsList[0];

                    if (latestApplication) {
                        const applicationData = latestApplication;
                        const editable = latestApplication.isEditable;

                        setExistingData(latestApplication);  // store full list if needed elsewhere

                        setIsEditable(editable);
                        setForm({
                            isPhdEnrolled: applicationData.isPhdEnrolled || "",
                            courseWorkDone: applicationData.courseWorkDone || "",
                            mphilDone: applicationData.mphilDone || "",
                            elCount: applicationData.elCount || "",
                            registrationNo: applicationData.registrationNo || "",
                            courseWorkCertificate: null,
                            serviceBook: null,
                            sodhNirdeshakAlloted: applicationData.sodhNirdeshakAlloted || "",
                            sodhNirdeshak: applicationData.sodhNirdeshak || "",
                            sodhKendra: applicationData.sodhKendra || "",
                            satyaapitPatra: null,
                            university: applicationData.university || "",
                            sodhKaVisay: applicationData.sodhKaVisay || "",
                            vetanMan: applicationData.vetanMan || "",
                            kulSevaWadhi: applicationData.kulSevaWadhi || "",
                            shaikshanikYogyata: applicationData.shaikshanikYogyata || "",
                            mphilCertificate: null,
                            sodhPanjiyanTithi: "",
                            sodhKaSirsak: "",
                            pracharyaKaTipAnushansa: "",
                            pracharyaSahmatiPatra: null,
                            // Note: name, padKaNam, padsthaMahavidhalay, janmTithi, niyuktiTithi
                        });
                    }
                }
            } catch (error) {
                console.log("No applications found or error fetching applications:", error);
            } finally {
                setLoadingDraft(false);
            }
        };

        fetchApplicationData();
    }, [token, empCode, endPoint]);


    useEffect(() => {
        const fetchPhdSessionInfo = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/phd-session-info`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            "web-url": window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200 && response.data?.success) {
                    const sessionData = response.data.data?.[0] || null; // get first item or null
                    setSessionInfo(sessionData);  // store session info directly
                } else {
                    alert("Failed to fetch session info. Please try again.");
                }
            } catch (error) {
                console.error("Error fetching session info:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchPhdSessionInfo();
    }, [endPoint, token]);


    const now = new Date();
    const openDate = new Date(sessionInfo?.openDate);
    const closeDate = new Date(sessionInfo?.closeDate);
    const isFormOpen = openDate instanceof Date && !isNaN(openDate) &&closeDate instanceof Date && !isNaN(closeDate) &&now >= openDate && now <= closeDate;
   
    
    // All Validations 
    const validateFieldOnChange = (field, value) => {
        let error = "";
        // Removed readonly fields: name, padKaNam, padsthaMahavidhalay, janmTithi, niyuktiTithi
        if (["sodhKaVisay", "vetanMan", "kulSevaWadhi", "shaikshanikYogyata", "mphilCertificate", "sodhPanjiyanTithi", "sodhKaSirsak", "pracharyaKaTipAnushansa"].includes(field) && (!value || value.toString().trim() === "") || (field === "pracharyaSahmatiPatra" && !existingData?.pracharyaSahmatiPatra && (!value || value.toString().trim() === ""))) error = "This Field Is Required";

        if (field === "elCount" && form.mphilDone === "no") {
            if (!value || Number(value) < 100) {
                error = "Minimum 100 EL required if M.Phil not completed.";
            }
        }

        setErrors((prev) => ({ ...prev, [field]: error }));
    };


    const handleChange = (field) => (e) => {
        const value = e.target.type === "file" ? e.target.files[0] : e.target.value;

        setForm((prev) => ({ ...prev, [field]: value }));

        validateFieldOnChange(field, value); // 🔁 Live field validation
    };

    const handleNext = () => {
        if (step === 1 && validateStep1()) setStep(2);
        else if (step === 2 && validateFinalResearchFields()) setStep(3);
    };

    const handlePrevious = () => {
        if (step > 1) setStep(step - 1);
    };




    useEffect(() => {
        if (form.isPhdEnrolled === "no") {
            setForm((prevState) => ({
                ...prevState,
                courseWorkDone: "no",
            }));
        }
        if (form.courseWorkDone === "yes") {
            setForm((prevState) => ({
                ...prevState,
                mphilDone: "",
            }));
        }
    }, [form.isPhdEnrolled, form.courseWorkDone]);



    const validateStep1 = () => {
        const err = {};

        if (!form.isPhdEnrolled) {
            err.isPhdEnrolled = "PhD enrollment status is required.";
        } else if (form.isPhdEnrolled === "yes") {
            if (!form.courseWorkDone) {
                err.courseWorkDone = "Coursework status is required.";
            }
            else if (form.courseWorkDone === "yes") {
                if (!form.registrationNo) {
                    err.registrationNo = "If Enrolled in Ph.D & Coursework Done, Registration Number Is Required.";
                }

                if ((!existingData?.courseWorkCertificate || existingData.courseWorkCertificate === "") && !form.courseWorkCertificate) {
                    err.courseWorkCertificate = "Coursework Certificate upload is required.";
                }
            }
            else {
                // Course work not done, check M.Phil condition
                if (!form.mphilDone) {
                    err.mphilDone = "M.Phil status is required.";
                } else if (form.mphilDone === "no") {
                    if (!form.elCount || Number(form.elCount) < 100) {
                        err.elCount = "Minimum 100 EL required if M.Phil not completed.";
                    } else {
                        if ((!existingData?.serviceBook || existingData.serviceBook === "") && !form.serviceBook) {
                            err.serviceBook = "Service book upload is required.";
                        }
                    }
                } else if (form.mphilDone === "yes") {
                    if ((!existingData?.serviceBook || existingData.serviceBook === "") && !form.serviceBook) {
                        err.serviceBook = "Service book upload is required.";
                    }
                    if ((!existingData?.mphilCertificate || existingData.mphilCertificate === "") && !form.mphilCertificate) {
                        err.mphilCertificate = "M.Phil certificate upload is required.";
                    }
                }
            }

        } else if (form.isPhdEnrolled === "no") {
            // Not enrolled in PhD, check M.Phil condition
            if (!form.mphilDone) {
                err.mphilDone = "M.Phil status is required.";
            } else if (form.mphilDone === "no") {
                if (!form.elCount || Number(form.elCount) < 100) {
                    err.elCount = "Minimum 100 EL required if M.Phil not completed.";
                } else {
                    if ((!existingData?.serviceBook || existingData.serviceBook === "") && !form.serviceBook) {
                        err.serviceBook = "Service book upload is required.";
                    }
                }
            } else if (form.mphilDone === "yes") {
                if ((!existingData?.serviceBook || existingData.serviceBook === "") && !form.serviceBook) {
                    err.serviceBook = "Service book upload is required.";
                }

                if ((!existingData?.mphilCertificate || existingData.mphilCertificate === "") && !form.mphilCertificate) {
                    err.mphilCertificate = "M.Phil certificate upload is required.";
                }
            }
        }
        setErrors(err);
        return Object.keys(err).length === 0;
    };


    const validateFinalResearchFields = () => {
        const err = {};
        if (form.sodhNirdeshakAlloted !== "yes") {
            err.sodhNirdeshakAlloted = "शोध निर्देशक आवंटित नहीं किया गया है।";
        }
        if (!form.sodhNirdeshak.trim()) err.sodhNirdeshak = "शोध निर्देशक का नाम आवश्यक है";
        if (!form.sodhKendra.trim()) err.sodhKendra = "शोध केन्द्र का नाम आवश्यक है";
        if (!form.university.trim()) err.university = "University आवश्यक है";
        if (!existingData?.satyaapitPatra && !form.satyaapitPatra) {
            err.satyaapitPatra = "स्थापित पत्र अपलोड करना अनिवार्य है।";
        }

        setErrors(err);
        return Object.keys(err).length === 0;
    };

    const validatePersonalFields = () => {
        const err = {};

        // Validate form fields (editable fields)
        const requiredFields = [
            "sodhKaVisay", "vetanMan", "kulSevaWadhi", "shaikshanikYogyata",
        ];
        requiredFields.forEach((field) => {
            if (!form[field]) err[field] = "यह फ़ील्ड आवश्यक है";
        });

        // Validate employee info fields (readonly fields)
        if (!employeeName) err.name = "Employee name is required";
        if (!employeeDesignation) err.padKaNam = "Employee designation is required";
        if (!employeeCollege) err.padsthaMahavidhalay = "Employee college is required";
        if (!employeeDOB) err.janmTithi = "Employee date of birth is required";
        if (!employeeJoiningDate) err.niyuktiTithi = "Employee joining date is required";

        setErrors(err);
        return Object.keys(err).length === 0;
    };




    if (loadingDraft) {
        return (
            <Container className="mt--7" fluid>
                <Header />
                <Row>
                    <Col xl="12">
                        <Card className="bg-transparent  shadow">
                            <CardBody className="text-center py-5">
                                <Spinner color="primary" />
                                <p className="mt-3">Loading form data...</p>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        );
    }

    return (
        <Container className="mt--7" fluid>
            <Header />
            <Row>
                <Col xl="12">
                    <Card className="bg-secondary shadow">
                        <CardHeader className="bg-white border-0">
                            <div className="d-flex justify-content-between align-items-center">
                                <h3 className="mb-0">PHD. पात्रता आवेदन / PHD Eligibility Application</h3>

                            </div>

                        </CardHeader>

                        <CardBody>
                            <h3 className="m-3 text-primary text-center fw-bold"> PHD. पात्रता आवेदन / PHD Eligibility Application Form</h3>

                            {isFormOpen ? (
                                <>


                                    {/* Progress Indicator */}
                                    <div className="mb-4">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className={`text-center ${step >= 1 ? 'text-primary' : 'text-muted'}`}>
                                                <div className={`rounded-circle d-inline-flex align-items-center justify-content-center ${step >= 1 ? 'bg-primary text-white' : 'bg-light'}`} style={{ width: '40px', height: '40px' }}>
                                                    {step > 1 ? '✓' : '1'}
                                                </div>
                                                <div className="mt-2 small">Academic Status</div>
                                            </div>
                                            <div className={`flex-grow-1 mx-3 ${step > 1 ? 'bg-primary' : 'bg-light'}`} style={{ height: '2px' }}></div>
                                            <div className={`text-center ${step >= 2 ? 'text-primary' : 'text-muted'}`}>
                                                <div className={`rounded-circle d-inline-flex align-items-center justify-content-center ${step >= 2 ? 'bg-primary text-white' : 'bg-light'}`} style={{ width: '40px', height: '40px' }}>
                                                    {step > 2 ? '✓' : '2'}
                                                </div>
                                                <div className="mt-2 small">Research Details</div>
                                            </div>
                                            <div className={`flex-grow-1 mx-3 ${step > 2 ? 'bg-primary' : 'bg-light'}`} style={{ height: '2px' }}></div>
                                            <div className={`text-center ${step >= 3 ? 'text-primary' : 'text-muted'}`}>
                                                <div className={`rounded-circle d-inline-flex align-items-center justify-content-center ${step >= 3 ? 'bg-primary text-white' : 'bg-light'}`} style={{ width: '40px', height: '40px' }}>
                                                    3
                                                </div>
                                                <div className="mt-2 small">Personal Info</div>
                                            </div>
                                        </div>
                                    </div>
                                    {step === 1 && (
                                        <>
                                            <div className="mb-4">
                                                <h5 className="text-primary mb-3">📚 Step 1: PhD Enrollment & Academic Status</h5>
                                                <div className="bg-light p-3 rounded">
                                                    <small className="text-indigo">
                                                        Please provide information about your current PhD enrollment status and academic qualifications.
                                                    </small>
                                                </div>
                                            </div>

                                            <FormGroup>
                                                <Label>Are you enrolled in PhD? <strong className="text-danger">*</strong></Label>
                                                <div className="d-flex gap-4">
                                                    {["yes", "no"].map((opt) => (
                                                        <FormGroup check inline key={opt}>
                                                            <Input
                                                                type="radio"
                                                                name="isPhdEnrolled"
                                                                value={opt}
                                                                checked={form.isPhdEnrolled === opt}
                                                                onChange={handleChange("isPhdEnrolled")}
                                                                disabled={!isEditable}
                                                            />
                                                            <Label check className="ms-2 text-capitalize">{opt}</Label>
                                                        </FormGroup>
                                                    ))}
                                                </div>
                                                {errors.isPhdEnrolled && <FormText color="danger">{errors.isPhdEnrolled}</FormText>}
                                            </FormGroup>

                                            {form.isPhdEnrolled === "yes" && (
                                                <>
                                                    <FormGroup>
                                                        <Label>Completed Coursework? <strong className="text-danger">*</strong></Label>
                                                        <div className="d-flex gap-4">
                                                            {["yes", "no"].map((opt) => (
                                                                <FormGroup check inline key={opt}>
                                                                    <Input
                                                                        type="radio"
                                                                        name="courseWorkDone"
                                                                        value={opt}
                                                                        checked={form.courseWorkDone === opt}
                                                                        onChange={handleChange("courseWorkDone")}
                                                                        disabled={!isEditable}
                                                                    />
                                                                    <Label check className="ms-2 text-capitalize">{opt}</Label>
                                                                </FormGroup>
                                                            ))}
                                                        </div>
                                                        {errors.courseWorkDone && <FormText color="danger">{errors.courseWorkDone}</FormText>}
                                                    </FormGroup>

                                                    {form.courseWorkDone === "yes" && (
                                                        <>
                                                            <Label className="mt-2">Registration No <strong className="text-danger">*</strong></Label>
                                                            <Input placeholder="Registration No" value={form.registrationNo} onChange={handleChange("registrationNo")} disabled={!isEditable} />
                                                            {errors.registrationNo ? (
                                                                <FormText color="danger">{errors.registrationNo}</FormText>
                                                            ) : (
                                                                <FormText color="muted">
                                                                    <small className="text-orange">
                                                                        If Enrolled in Ph.D & Coursework Done, Enter Registration Number.
                                                                    </small>
                                                                </FormText>
                                                            )}

                                                            <Label className="mt-2">Upload Coursework Certificate <strong className="text-danger">*</strong></Label>
                                                            <Input type="file" onChange={handleChange("courseWorkCertificate")} disabled={!isEditable} />
                                                            {errors.courseWorkCertificate && <FormText color="danger">{errors.courseWorkCertificate}</FormText>}
                                                        </>
                                                    )}
                                                </>
                                            )}

                                            {(form.isPhdEnrolled === "yes" || form.isPhdEnrolled === "no") && (form.courseWorkDone !== "yes" || form.courseWorkDone === "no") && (
                                                <>
                                                    <FormGroup>
                                                        <Label>Completed M.Phil? <strong className="text-danger">*</strong></Label>
                                                        <div className="d-flex gap-4">
                                                            {["yes", "no"].map((opt) => (
                                                                <FormGroup check inline key={opt}>
                                                                    <Input
                                                                        type="radio"
                                                                        name="mphilDone"
                                                                        value={opt}
                                                                        checked={form.mphilDone === opt}
                                                                        onChange={handleChange("mphilDone")}
                                                                        disabled={!isEditable}
                                                                    />
                                                                    <Label check className="ms-2 text-capitalize">{opt}</Label>
                                                                </FormGroup>
                                                            ))}
                                                        </div>
                                                        {errors.mphilDone && <FormText color="danger">{errors.mphilDone}</FormText>}
                                                    </FormGroup>
                                                    <label className="mt-2">Enter Total Earn Leave <strong className="text-danger">*</strong></label>
                                                    <Input
                                                        type="number"
                                                        pattern="[0-9]*"
                                                        inputMode="numeric"
                                                        placeholder="Enter EL (Leave Count)"
                                                        value={form.elCount}
                                                        onChange={handleChange("elCount")}
                                                        min={0}
                                                        disabled={!isEditable}
                                                    />

                                                    {errors.elCount ? (
                                                        <FormText color="danger">{errors.elCount}</FormText>
                                                    ) : (
                                                        <FormText color="muted">
                                                            <small className="text-orange">Minimum 100 EL required if M.Phil not completed.</small>
                                                        </FormText>
                                                    )}


                                                    {(form.mphilDone === "yes" || (form.mphilDone === "no" && Number(form.elCount) >= 100)) && (
                                                        <Row className="align-items-end">
                                                            <Col md="6" sm="12" className="mb-3">
                                                                <FormGroup>
                                                                    <Label className="mt-2">Upload Service Book Copy <strong className="text-danger">*</strong></Label>
                                                                    <Input type="file" onChange={handleChange("serviceBook")} disabled={!isEditable} />
                                                                    {errors.serviceBook && <FormText color="danger">{errors.serviceBook}</FormText>}
                                                                </FormGroup>
                                                            </Col>

                                                            {form.mphilDone === "yes" && (
                                                                <Col md="6" sm="12" className="mb-3">
                                                                    <FormGroup>
                                                                        <Label className="mt-2">Upload M.Phil Certificate <strong className="text-danger">*</strong></Label>
                                                                        <Input type="file" onChange={handleChange("mphilCertificate")} disabled={!isEditable} />
                                                                        {errors.mphilCertificate && <FormText color="danger">{errors.mphilCertificate}</FormText>}
                                                                    </FormGroup>
                                                                </Col>
                                                            )}
                                                        </Row>
                                                    )}

                                                </>
                                            )}

                                            <div className="text-end mt-3 d-flex justify-content-between">
                                                <Button color="primary" onClick={handleNext} disabled={loading}>Next ➡️</Button>
                                                {isEditable && (
                                                    <Button
                                                        color="info"
                                                        onClick={() => handleSubmit(false)}
                                                        disabled={loading}
                                                    >
                                                        {loading ? <Spinner size="sm" /> : "💾"} Save as Draft
                                                    </Button>
                                                )}
                                            </div>
                                        </>
                                    )}

                                    {step === 2 && (
                                        <>
                                            <div className="mb-4">
                                                <h5 className="text-primary mb-3">🔬 Step 2: Research Details & Supervisor Information</h5>
                                                <div className="bg-light p-3 rounded">
                                                    <small className="text-indigo">
                                                        Provide details about your research supervisor, research center, and university information.
                                                    </small>
                                                </div>
                                            </div>
                                            <Row >
                                                <Col md="12 mb-3">
                                                    <Label>शोध निर्देशक आवंटित किया गया है? <strong className="text-danger">*</strong></Label>
                                                    <div className="d-flex gap-3">
                                                        {["yes", "no"].map((opt) => (
                                                            <FormGroup check inline key={opt}>
                                                                <Input
                                                                    type="radio"
                                                                    name="sodhNirdeshakAlloted"
                                                                    value={opt}
                                                                    checked={form.sodhNirdeshakAlloted === opt}
                                                                    onChange={handleChange("sodhNirdeshakAlloted")}
                                                                    disabled={!isEditable}
                                                                />
                                                                <Label check className="ms-2 text-capitalize">{opt}</Label>
                                                            </FormGroup>
                                                        ))}
                                                    </div>
                                                    {errors.sodhNirdeshakAlloted && <FormText color="danger">{errors.sodhNirdeshakAlloted}</FormText>}
                                                </Col>
                                                {form.sodhNirdeshakAlloted === "yes" && (
                                                    <>
                                                        <Col md="3">
                                                            <Label>शोध निर्देशक का नाम <strong className="text-danger">*</strong></Label>
                                                            <Input value={form.sodhNirdeshak} onChange={handleChange("sodhNirdeshak")} disabled={!isEditable} />
                                                            {errors.sodhNirdeshak && <FormText color="danger">{errors.sodhNirdeshak}</FormText>}
                                                        </Col>
                                                        <Col md="4">
                                                            <Label>शोध केन्द्र का नाम <strong className="text-danger">*</strong></Label>
                                                            <Input value={form.sodhKendra} onChange={handleChange("sodhKendra")} disabled={!isEditable} />
                                                            {errors.sodhKendra && <FormText color="danger">{errors.sodhKendra}</FormText>}
                                                        </Col>
                                                        <Col md="5">
                                                            <Label>University का नाम <strong className="text-danger">*</strong> </Label>
                                                            <Input value={form.university} onChange={handleChange("university")} disabled={!isEditable} />
                                                            {errors.university && <FormText color="danger">{errors.university}</FormText>}
                                                        </Col>


                                                        <Col md="3" sm="6" xs="12" className="mb-3">
                                                            <FormGroup>
                                                                <Label>
                                                                    स्थापित पत्र अपलोड करें <strong className="text-danger">*</strong>
                                                                </Label>
                                                                <Input
                                                                    type="file"
                                                                    accept=".pdf,.jpg,.jpeg,.png"  // optional: limit file types if required
                                                                    onChange={handleChange("satyaapitPatra")}
                                                                    disabled={!isEditable}
                                                                />
                                                                {errors.satyaapitPatra && (
                                                                    <FormText color="danger">{errors.satyaapitPatra}</FormText>
                                                                )}
                                                            </FormGroup>
                                                        </Col>

                                                    </>
                                                )}
                                            </Row>

                                            <div className="d-flex justify-content-between mt-4">
                                                <Button color="secondary" onClick={handlePrevious} disabled={loading}>⬅️ Previous</Button>
                                                {isEditable && (
                                                    <Button
                                                        color="info"
                                                        onClick={() => handleSubmit(false)}
                                                        disabled={loading}
                                                    >
                                                        {loading ? <Spinner size="sm" /> : "💾"} Save as Draft
                                                    </Button>
                                                )}
                                                <Button color="primary" onClick={handleNext} disabled={loading}>Next ➡️</Button>
                                            </div>
                                        </>
                                    )}

                                    {step === 3 && (
                                        <>
                                            <div className="mb-4">
                                                <h5 className="text-primary mb-3">👤 Step 3: Personal & Official Information</h5>
                                                <div className="bg-light p-3 rounded">
                                                    <small className="text-indigo">
                                                        Complete your personal details, official information, and service records.
                                                    </small>
                                                </div>
                                            </div>

                                            <Row >
                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label> कर्मचारी का नाम <strong className="text-danger">*</strong></Label>
                                                        <Input
                                                            type="text"
                                                            value={employeeName}
                                                            readOnly
                                                            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                                                        />
                                                        <FormText color="muted">
                                                            <small>This information is automatically fetched from employee records</small>
                                                        </FormText>
                                                    </FormGroup>
                                                </Col>

                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>पद का नाम <strong className="text-danger">*</strong></Label>
                                                        <Input
                                                            type="text"
                                                            value={employeeDesignation}
                                                            readOnly
                                                            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                                                        />
                                                        <FormText color="muted">
                                                            <small>This information is automatically fetched from employee records</small>
                                                        </FormText>
                                                    </FormGroup>
                                                </Col>

                                                <Col md="6" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>पदस्थ महाविद्यालय <strong className="text-danger">*</strong></Label>
                                                        <Input
                                                            type="text"
                                                            value={employeeCollege}
                                                            readOnly
                                                            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                                                        />
                                                        <FormText color="muted">
                                                            <small>This information is automatically fetched from employee records</small>
                                                        </FormText>
                                                    </FormGroup>
                                                </Col>

                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>जन्मतिथि <strong className="text-danger">*</strong></Label>
                                                        <Input
                                                            type="date"
                                                            value={employeeDOB}
                                                            readOnly
                                                            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                                                        />
                                                        <FormText color="muted">
                                                            <small>This information is automatically fetched from employee records</small>
                                                        </FormText>
                                                    </FormGroup>
                                                </Col>

                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>प्रथम नियुक्ति तिथि <strong className="text-danger">*</strong></Label>
                                                        <Input
                                                            type="date"
                                                            value={employeeJoiningDate}
                                                            readOnly
                                                            style={{ backgroundColor: '#f8f9fa', cursor: 'not-allowed' }}
                                                        />
                                                        <FormText color="muted">
                                                            <small>This information is automatically fetched from employee records</small>
                                                        </FormText>
                                                    </FormGroup>
                                                </Col>

                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>कुल सेवा अवधि  वर्ष - माह - दिवस में <strong className="text-danger">*</strong></Label>
                                                        <Input type="text" value={form.kulSevaWadhi} onChange={handleChange("kulSevaWadhi")} disabled={!isEditable} />
                                                        {errors.kulSevaWadhi && <FormText color="danger">{errors.kulSevaWadhi}</FormText>}
                                                    </FormGroup>
                                                </Col>
                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>शैक्षणिक योग्यता <strong className="text-danger">*</strong></Label>
                                                        <Input type="text" value={form.shaikshanikYogyata} onChange={handleChange("shaikshanikYogyata")} disabled={!isEditable} />
                                                        {errors.shaikshanikYogyata && <FormText color="danger">{errors.shaikshanikYogyata}</FormText>}
                                                    </FormGroup>
                                                </Col>

                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>वेतनमान <strong className="text-danger">*</strong></Label>
                                                        <Input type="text" value={form.vetanMan} onChange={handleChange("vetanMan")} disabled={!isEditable} />
                                                        {errors.vetanMan && <FormText color="danger">{errors.vetanMan}</FormText>}
                                                    </FormGroup>
                                                </Col>



                                                <Col md="3" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>
                                                            प्राचार्य सहमति पत्र <strong className="text-danger">*</strong>
                                                        </Label>
                                                        <Input
                                                            type="file"
                                                            className="form-control"
                                                            id="pracharyaSahmatiPatra"
                                                            onChange={handleChange("pracharyaSahmatiPatra")}
                                                            disabled={!isEditable}
                                                        />
                                                        {errors.pracharyaSahmatiPatra && (
                                                            <FormText color="danger">{errors.pracharyaSahmatiPatra}</FormText>
                                                        )}
                                                    </FormGroup>
                                                </Col>

                                                <Col md="12" sm="6" xs="12" className="mb-3">
                                                    <FormGroup>
                                                        <Label>
                                                            प्राचार्य का टीप/अनुशंसा <strong className="text-danger">*</strong>
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            rows="4"
                                                            maxLength="1000"  // Approximate 150 words (as word count can't be exactly enforced in maxLength)
                                                            placeholder="यहाँ प्राचार्य का टीप या अनुशंसा लिखें (अधिकतम 150 शब्द)"
                                                            id="pracharyaKaTipAnushansa"
                                                            value={form.pracharyaKaTipAnushansa}
                                                            onChange={handleChange("pracharyaKaTipAnushansa")}
                                                            disabled={!isEditable}
                                                        />
                                                        {errors.pracharyaKaTipAnushansa && (
                                                            <FormText color="danger">{errors.pracharyaKaTipAnushansa}</FormText>
                                                        )}
                                                    </FormGroup>
                                                </Col>
                                                <Col md="3">
                                                    <Label>शोध पंजीयन तिथि <strong className="text-danger">*</strong></Label>
                                                    <Input
                                                        type="date"
                                                        value={form.sodhPanjiyanTithi}
                                                        onChange={handleChange("sodhPanjiyanTithi")}
                                                        disabled={!isEditable}
                                                    />
                                                    {errors.sodhPanjiyanTithi && <FormText color="danger">{errors.sodhPanjiyanTithi}</FormText>}
                                                </Col>
                                                <Col md="3">
                                                    <Label>शोध का विषय <strong className="text-danger">*</strong></Label>
                                                    <Input
                                                        type="text"
                                                        value={form.sodhKaVisay}
                                                        onChange={handleChange("sodhKaVisay")}
                                                        disabled={!isEditable}
                                                    />
                                                    {errors.sodhKaVisay && <FormText color="danger">{errors.sodhKaVisay}</FormText>}
                                                </Col>

                                                <Col md="6">
                                                    <Label>शोध का शीर्षक <strong className="text-danger">*</strong></Label>
                                                    <Input
                                                        type="text"
                                                        value={form.sodhKaSirsak}
                                                        onChange={handleChange("sodhKaSirsak")}
                                                        disabled={!isEditable}
                                                    />
                                                    {errors.sodhKaSirsak && <FormText color="danger">{errors.sodhKaSirsak}</FormText>}
                                                </Col>


                                            </Row>

                                            <div className="d-flex justify-content-between mt-4">
                                                <Button color="secondary" onClick={handlePrevious} disabled={loading}>⬅️ Previous</Button>
                                                {isEditable && (
                                                    <Button
                                                        color="info"
                                                        onClick={() => handleSubmit(false)}
                                                        disabled={loading}
                                                    >
                                                        {loading ? <Spinner size="sm" /> : "💾"} Save as Draft
                                                    </Button>
                                                )}
                                                <Button
                                                    color="success"
                                                    onClick={() => handleSubmit(true)}
                                                    disabled={loading}
                                                >
                                                    {loading ? <Spinner size="sm" /> : "✅"} Form Final Submit
                                                </Button>
                                            </div>
                                        </>
                                    )}

                                </>) : (
                                <div className="text-danger fw-bold text-center fw-bold">
                                    PHD. पात्रता आवेदन फ़ॉर्म वर्तमान में उपलब्ध नहीं है (PHD Eligibility Application Form is not currently available).
                                </div>)}
                        </CardBody>
                    </Card>
                </Col>
            </Row>
        </Container>
    );
};

export default PhdEligibilityForm;