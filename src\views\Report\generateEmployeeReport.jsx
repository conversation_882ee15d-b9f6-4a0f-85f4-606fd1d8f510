import { useEffect, useState } from "react";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Button,
  Table,
} from "reactstrap";
import Select from "react-select";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
const GenerateEmployeeReport = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const userType = sessionStorage.getItem("type");
  const collegeId = sessionStorage.getItem("id");
  const [allCollege, setAllCollege] = useState([]);
  const [employee, setEmployee] = useState([]);
  const [error, setError] = useState("");
  useEffect(() => {
    const fetchAllCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setAllCollege(response.data);
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchAllCollege();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setError("");
      setEmployee(null);
      const queryParams = {
        college: collegeInput,
      };

      const response = await axios.get(
        `${endPoint}/api/report/college-employee`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setEmployee(response.data);
    } catch (err) {
      setError(err.response?.data?.message || "Failed to fetch attendance.");
    }
  };

  const [collegeInput, setCollegeInput] = useState("");
  const collegeOptions =
    (allCollege &&
      allCollege.length > 0 &&
      allCollege?.map((type) => ({
        value: type._id,
        label: `${type.name} (${type.aisheCode})`,
      }))) ||
    [];

  const [verifiedData, setVerifiedData] = useState([]);
  const exportToExcel = async () => {
    try {
      const queryParams = {
        college: collegeInput,
      };
      const response = await axios.get(
        `${endPoint}/api/report/college-employee`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      const fetchedData = response.data; // Replace with your actual data field
      if (fetchedData.length > 0) {
        let tableHTML = `
          <table>
            <thead>
              <tr>
                <th>Sno</th>
                <th>Employee Name</th>
                <th>Employee Code</th>
                <th>Email</th>
                <th>Contact</th>
                <th>Division</th>
                <th>District</th>
                <th>Vidhansabha</th>
                <th>Work Type</th>
                <th>Class</th>
                <th>Designation</th>
                <th>Address</th>
              </tr>
            </thead>
            <tbody>
        `;

        fetchedData.forEach((college, index) => {
          tableHTML += `
              <tr>
                <th colspan="12" style={color: "black", backgroundColor: "orange"}><b>${college.collegeName}</b></th>
              </tr>
            `;

          if (
            college.allCollegeEmployee &&
            college.allCollegeEmployee.length > 0
          ) {
            // Sort employees by name in alphabetical order
            const sortedEmployees = [...college.allCollegeEmployee].sort(
              (a, b) => (a.name || "").localeCompare(b.name || "")
            );

            sortedEmployees.forEach((details, subIndex) => {
              tableHTML += `
                  <tr>
                    <td>${subIndex + 1}</td>
                    <td>${details.name}</td>
                    <td>${details.empCode}</td>
                    <td>${details.email}</td>
                    <td>${details.contact}</td>
                    <td>${getDivisionName(details.divison)}</td>
                    <td>${details.districtName}</td>
                    <td>${details.vidhansabhaName}</td>
                    <td>${details.workType}</td>
                    <td>${details.classDetails?.className || "N/A"}</td>
                    <td>${details.designationDetails?.designation || "N/A"}</td>
                    <td>${details.address}</td>
                  </tr>
                `;
            });
          }
        });

        tableHTML += "</tbody></table>";

        const excelFileContent = `
          <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
            <head><!--[if gte mso 9]><xml>
              <x:ExcelWorkbook>
                <x:ExcelWorksheets>
                  <x:ExcelWorksheet>
                    <x:Name>Employees</x:Name>
                    <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                  </x:ExcelWorksheet>
                </x:ExcelWorksheets>
              </x:ExcelWorkbook>
            </xml><![endif]--></head>
            <body>${tableHTML}</body>
          </html>
        `;

        const blob = new Blob([excelFileContent], {
          type: "application/vnd.ms-excel;charset=utf-8;",
        });
        const date = new Date().toLocaleDateString();
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = `Employee_Report_${date}.xls`;
        downloadLink.click();
      } else {
        SwalMessageAlert("No data available for export.", "warning");
      }
    } catch (error) {
      SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
    }
  };

  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <Col xs="6">
                  <h3 className="mb-0">
                    Generate Employee Report{" "}
                    <h5 style={{ color: "red" }}>
                      (All College Wise and Single College Wise)
                    </h5>
                    <h5 style={{ color: "red" }}>
                      (For All College Report Direct Click On Generate Report
                      Button)
                    </h5>
                  </h3>
                </Col>
                <Col className="text-right" xs="4">
                  <Button
                    color="primary"
                    size="sm"
                    onClick={exportToExcel}
                  >
                    Export To Excel
                  </Button>
                </Col>
              </CardHeader>
              <CardBody>
                <Form onSubmit={handleSubmit}>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="4">
                        <FormGroup>
                          <Label htmlFor="input-className">College</Label>
                          <Select
                            options={collegeOptions}
                            onChange={(selectedOption) =>
                              setCollegeInput(selectedOption?.value || "")
                            }
                            placeholder="Select College"
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <button className="btn btn-sm btn-primary" type="submit">
                      Generate Report
                    </button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Attendance List</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Table responsive hover striped>
                  <thead>
                    <tr>
                      <th colSpan="10">College Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    {error && <p className="error">{error}</p>}
                    <table>
                      {employee &&
                        employee.map((college, index) => (
                          <>
                            <thead>
                              <tr key={index}>
                                <th colSpan="10" style={{color: "black", backgroundColor: "orange"}}><b>{college.collegeName}</b></th>
                              </tr>
                            </thead>
                            <tbody>
                              <td key={index}>
                                <tr>
                                  <th>Sno</th>
                                  <th>Employee Name</th>
                                  <th>Employee Code</th>
                                  <th>Email</th>
                                  <th>Contact</th>
                                  <th>Division</th>
                                  <th>District</th>
                                  <th>Vidhansabha</th>
                                  <th>WorkType</th>
                                  <th>Class</th>
                                  <th>Designation</th>
                                  <th>Address</th>
                                </tr>
                                {college.allCollegeEmployee &&
                                  [...college.allCollegeEmployee] // Create a copy to avoid mutating the original array
                                    .sort((a, b) =>
                                      a.name.localeCompare(b.name)
                                    ) // Sort alphabetically by name
                                    .map((details, subIndex) => (
                                      <tr key={subIndex}>
                                        <td>{subIndex + 1}</td>
                                        <td style={{textWrap: "wrap"}}>{details.name}</td>
                                        <td>{details.empCode}</td>
                                        <td style={{textWrap: "wrap"}}>{details.email}</td>
                                        <td>{details.contact}</td>
                                        <td>
                                          {getDivisionName(details.divison)}
                                        </td>
                                        <td style={{textWrap: "wrap"}}>{details.districtName}</td>
                                        <td style={{textWrap: "wrap"}}>{details.vidhansabhaName}</td>
                                        <td>{details.workType}</td>
                                        <td>
                                          {details.classDetails.className}
                                        </td>
                                        <td>
                                          {
                                            details.designationDetails
                                              .designation
                                          }
                                        </td>
                                        <td style={{textWrap: "wrap"}}>{details.address}</td>
                                      </tr>
                                    ))}
                              </td>
                            </tbody>
                          </>
                        ))}
                    </table>
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default GenerateEmployeeReport;
