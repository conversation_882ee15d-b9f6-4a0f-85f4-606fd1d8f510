import { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  Container,
  CardBody,
  Col,
  Row,
  Button,
  Input,
  <PERSON>dal,
  Badge,
  <PERSON><PERSON>Header,
  <PERSON>dal<PERSON><PERSON>,
  ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import ACRFormClass3Part2 from "./acrFormClass3Part2.jsx";
import ACRFormClass3Part3 from "./aCRFormClass3Part3.jsx";
import ACRFormClass3Part4 from "./acrFormClass3Part4.jsx";
import ACRFormClass4Part2 from "./class4/acrFormClass4Part2.jsx";
import ACRFormProfessorPart3 from "./professor/acrFormProfessorPart3.jsx";
import ACRFormProfessorPart2 from "./professor/acrFormProfessorPart2.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import ACRFormClass3or4FinalForm from "./acrClass3or4FinalForm.jsx";
import { useNavigate } from "react-router-dom";
import ACRFormProfessorPart4 from "./professor/acrFormProfessorPart4.jsx";
import ACRFormProfessorFinal from "./professor/acrFormProfessorFinal.jsx";
import ACRFormUGPGPart2 from "./principal/ACRFormUGPGPart2.jsx";
import ACRFormUGandPGPart3 from "./principal/acrFormUGPGPart3.jsx";
import ACRFormUGandPGPart4 from "./principal/acrFormUGPGPart4.jsx";
import ACRForLibrerianPart2 from "./librerian/acrFormLibrerianPart2.jsx";
import ACRForLibrerianPart3 from "./librerian/acrFormLibrerianPart3.jsx";
import ACRForLibrerianPart4 from "./librerian/acrFormLibrerianPart4.jsx";
import ACRForSportOfficerPart2 from "./sportsOfficer/acrFormSportsOfficerPart2.jsx";
import ACRForSportsOfficerPart3 from "./sportsOfficer/acrFormSportsOfficerPart3.jsx";
import ACRForSportsOfficerPart4 from "./sportsOfficer/acrFormSportOfficerPart4.jsx";
import ACRformForRegistrarPart2 from "./Registrar/acrFormRegistrarPart2.jsx";
import ACRformForRegistrarPart3 from "./Registrar/acrFormRegistrarPart3.jsx";
import ACRformForRegistrarPart4 from "./Registrar/acrFormRegistrarPart4.jsx";
import ACRForDirectorPart2 from "./directorateACR/acrFormForDirecotorsPart2.jsx";
import ACRForDirectorPart3 from "./directorateACR/acrFormForDirecotorsPart3.jsx";
import ACRForDirectorPart4Final from "./directorateACR/ACRForDirectorPart4Final.jsx";
const ACRFormDirectorList = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const id = sessionStorage.getItem("id");
  let userType = sessionStorage.getItem("userType");
  let designation = sessionStorage.getItem("designationName");
  const navigate = useNavigate();
  const [employees, setEmployees] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isLevel2ModalOpen, setIsLevel2ModalOpen] = useState(false);
  const [isLevel3ModalOpen, setIsLevel3ModalOpen] = useState(false);
  const [isFinalForm, setFinalForm] = useState(false);
  const [className, setClassName] = useState(null);
  const [designationName, setDesignationName] = useState(null);

  if (designation === "Additional Director") {
    userType = "Additional Director"
  }

  useEffect(() => {
    fetchACRFormList();
  }, []);

  // Generic error handler
  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };
  const types = {
    Director: "Director",
    "Additional Director": "Additional Director"
  };

  // Fetch employee list based on user type


  // Filtered employee list based on search query
  const filteredEmployees = employees.filter(
    (emp) =>
      emp.employeeName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emp.empCode.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle modals
  const toggleModal = async (
    level,
    classData,
    designation = null,
    id = null
  ) => {
    setClassName(classData);
    setDesignationName(designation);

    if (id !== null) {
      try {
        const response = await axios
          .get(`${endPoint}/api/acr/get-single-acr/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          })
          .then((res) => {
            if (res.status === 200) {
              setEmployees([res.data]); // Set employee data directly
              if (level === 1) {
                setIsModalOpen((prev) => !prev);
              }
              if (level === 2) {
                setIsLevel2ModalOpen((prev) => !prev);
              }
              if (level === 3) {
                setIsLevel3ModalOpen((prev) => !prev);
              }
            } else {
              SwalMessageAlert(res.data?.msg || "Data fetch failed.", "error");
            }
          });
      } catch (error) {
        handleApiError(error);
      }
    } else {
      if (level === 1) {
        setIsModalOpen((prev) => !prev);
      }
      if (level === 2) {
        setIsLevel2ModalOpen((prev) => !prev);
      }
      if (level === 3) {
        setIsLevel3ModalOpen((prev) => !prev);
      }
    }
  };

  const [finalFormData, setFinalFormData] = useState([]);
  const toggleFinal = async (id = null, classData, designation = null) => {
    if (id !== null) {
      try {
        setClassName(classData);
        setDesignationName(designation);

        const response = await axios.get(`${endPoint}/api/acr/get-acr-form-view?type=${types[userType]}&id=${id}`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {

          setFinalFormData(response.data); // Set employee data directly
          setFinalForm((prev) => !prev);
        } else {
          SwalMessageAlert(response.data?.msg || "Data fetch failed.", "error");
        }
      } catch (error) {
        console.error("Error fetching ACR form view:", error); // Log the error for debugging
        handleApiError(error);
      }
    } else {
      setFinalForm((prev) => !prev);
    }
  };
  // Render buttons or status messages
  const renderAction = (row) => {


    // const levelIndexes = {
    //   Director: row.preDefinedLevels[0].status === false && row.preDefinedLevels[0].selectOption === id ? 0 : row.preDefinedLevels[1].status === false && row.preDefinedLevels[1].selectOption === "Additional Director" ? 1 : row.preDefinedLevels[2].status === false && row.preDefinedLevels[2].selectOption === "Commissioner" ? 2 : (<span className="text-success">मतांकन पूरा हुआ</span>),
    // };

    const levelIndexes = {
      Director: row.preDefinedLevels.findIndex(
        (level) => level.selectOption === id
      ),

      "Additional Director": row.preDefinedLevels.findIndex(
        (level) => level.selectOption === "Additional Director"
      ),

      Commissioner: row.preDefinedLevels.findIndex(
        (level) => level.selectOption === "Commissioner"
      ),
    };



    const levelIndex = levelIndexes[userType];


    const allLevelsCompleted = row.preDefinedLevels.every(
      (level) => level.status === true
    );
    // if (allLevelsCompleted) {
    //   return <span className="text-success">मतांकन पूरा हुआ</span>;
    // }
    const isButtonVisible = (index) => {
      const className = row.employeeDetails.classDetails.className;
      const designationName =
        row.employeeDetails.designationDetails.designation;
      const validClass = ["Class 3", "Class 4"].includes(className);
      const validDesignation = ["Professor", "Assistant Professor", "UG Principal", "PG Principal", "Librarian", "Sports Officer", "Registrar", 'Additional Director', 'Joint Director (F)', 'Joint Director', 'Assistant Director (F)'].includes(
        designationName
      );
      if (index === 1) {
        return (
          (validClass || validDesignation) &&
          row.preDefinedLevels[1]?.status === false &&
          row.preDefinedLevels[0]?.status === true
        );
      }
      if (index === 2) {
        return (
          (validClass || validDesignation) &&
          !row.preDefinedLevels[2]?.status &&
          row.preDefinedLevels[0]?.status &&
          row.preDefinedLevels[1]?.status
        );
      }
      return (
        (validClass || validDesignation) && !row.preDefinedLevels[0]?.status
      );
    };
    const statusMessages = [
      "प्रथम मतांकन किया गया",
      "द्वितीय मतांकन किया गया",
      "अंतिम मतांकन किया गया",
    ];


    // console.log(isButtonVisible(levelIndex), "isButtonVisible(levelIndex)");


    if (isButtonVisible(levelIndex) === false) {
      return <span className="text-danger">{statusMessages[levelIndex]}</span>;
    }

    const labels = ["प्रथम मतंकन करें", "द्वितीय मतांकन करें", "अंतिम मतांकन"];
    const classes = ["primary", "warning", "danger"];

    return (
      <button
        className={`btn btn-${classes[levelIndex]} btn-sm`}
        onClick={() =>
          toggleModal(
            levelIndex + 1,
            row.employeeDetails.classDetails.className,
            row.employeeDetails.designationDetails.designation,
            row._id
          )
        }
      >
        {labels[levelIndex]}
      </button>
    );
  };


  const fetchACRFormList = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/acr/get-acr-form-director?type=${types[userType]}&id=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200 && Array.isArray(response.data)) {
        const sortedEmployees = response.data.sort((a, b) =>
          a.employeeName.localeCompare(b.employeeName)
        );
        setEmployees(sortedEmployees);
      } else {
        SwalMessageAlert(response.data?.msg || "Data fetch failed.", "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };


  // Table columns
  const columns = [
    {
      name: "S.No.",
      selector: (row, index) => index + 1,
      sortable: true,
      width: "80px",
    },
    {
      name: "View",
      cell: (row) => (
        <span
          className="fa fa-eye btn btn-success btn-sm"
          onClick={() =>
            toggleFinal(
              row._id,
              row.employeeDetails.classDetails.className,
              row.employeeDetails.designationDetails.designation,
            )
          }
        ></span>
      ),
      selector: (row, index) => index + 1,
      sortable: true,
      width: "80px",
    },
    {
      name: "Status",
      cell: (row) => {
        const length = row.levelDetails.length;
        if (length <= 0) {
          return <Badge className="text-white bg-success" style={{ fontSize: "12px" }}>स्व-मतांकन संपन्न</Badge>;
        } else if (length === 1) {
          return <Badge className="text-white bg-warning" style={{ fontSize: "12px" }} >प्रथम मतांकन संपन्न</Badge>;
        } else if (length === 2) {
          return <Badge className="text-white bg-primary" style={{ fontSize: "12px" }}>द्वितीय मतांकन संपन्न</Badge>;
        } else if (length === 3) {
          return <Badge className="text-white bg-danger" style={{ fontSize: "12px" }}>अंतिम मतांकन संपन्न</Badge>;
        } else {
          return <Badge >Error</Badge>;
        }
      },
      sortable: true,
    },
    {
      name: "Name / Employee Code",
      cell: (row) => (
        <div>
          <div>{row.employeeName}</div>
          <div><b>({row.empCode})</b></div>
        </div>
      ),
      sortable: true,
    },
    {
      name: "Class",
      selector: (row) => row.employeeDetails.classDetails.className,
      sortable: true,
    },
    {
      name: "Designation",
      selector: (row) => row.employeeDetails.designationDetails.designation,
      sortable: true,
    },
    {
      name: "Work Type",
      selector: (row) => row.employeeDetails.workType,
      sortable: true,
    },
    { name: "Action", cell: renderAction, sortable: true },
  ];
  const handlePrint = () => {
    const printContainer = document.getElementById("print-container");
    if (!printContainer) {
      console.error("Element with ID 'print-container' not found.");
      return;
    }

    const printWindow = window.open("", "_blank");
    const printContent = printContainer.innerHTML;
    const printDocument = printWindow.document;

    printDocument.write(`
        <html>
          <head>
            <title>Print Form</title>
            <style>
              @page { 
                size: A4 landscape; /* Set to A4 landscape */
                margin: 20px; 
              }
              body { font-family: Arial, sans-serif; }
              .print-container {
                width: 29.7cm; /* Width for landscape */
                height: 21cm; /* Height for landscape */
                background: #fff;
              }
              .form-field {
                margin-bottom: 15px;
              }
              .form-field label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
              }
              .form-field input {
                width: 100%;
                padding: 8px;
                box-sizing: border-box;
              }
              h1 {
                font-size: 24px;
                margin-bottom: 20px;
              }
              p {
                font-size: 16px;
                margin-bottom: 10px;
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              ${printContent}
            </div>
          </body>
        </html>
      `);
    printDocument.close();
    printWindow.print();
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">ACR Form List</h3>
            </Col>
            <Col xs="4" className="text-right">
              <Input
                type="text"
                placeholder="Search by Name or EmpCode"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Col>
          </CardHeader>
          <CardBody>
            <Row>
              <Col lg="12">
                <DataTable
                  columns={columns}
                  data={filteredEmployees}
                  pagination
                  highlightOnHover
                  striped
                  responsive
                />
              </Col>
            </Row>
          </CardBody>
        </Card>

        {/* Modals */}
        <Modal
          isOpen={isModalOpen}
          toggle={() => toggleModal(1)}
          backdrop="static"
          style={{
            maxWidth: "90%",
            width: "100%",
          }}
        >
          <ModalHeader toggle={() => toggleModal(1)} />
          <ModalBody>
            {className === "Class 3" && (
              <ACRFormClass3Part2 employee={employees} />
            )}
            {className === "Class 4" && (
              <ACRFormClass4Part2 employee={employees} />
            )}
            {(designationName === "Assistant Professor" ||
              designationName === "Professor") && (
                <ACRFormProfessorPart2 employee={employees} />
              )}
            {(designationName === "Librarian") && (
              <ACRForLibrerianPart2 employee={employees} />
            )}
            {(designationName === "Sports Officer") && (
              <ACRForSportOfficerPart2 employee={employees} />
            )}
            {(designationName === "Registrar") && (
              <ACRformForRegistrarPart2 employee={employees} />
            )}

            {(designationName === 'Additional Director' ||
              designationName === 'Joint Director (F)' ||
              designationName === 'Joint Director' ||
              designationName === 'Assistant Director (F)' ||
              designationName === 'Deputy Director') && (
                <ACRForDirectorPart2 employee={employees} />
              )}


            {(designationName === "UG Principal" ||
              designationName === "PG Principal") && (
                <ACRFormUGPGPart2 employee={employees} />
              )}
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => toggleModal(1)}>
              Close
            </Button>
          </ModalFooter>
        </Modal>

        <Modal
          isOpen={isLevel2ModalOpen}
          toggle={() => toggleModal(2)}
          backdrop="static"
          style={{
            maxWidth: "90%",
            width: "100%",
          }}
        >
          <ModalHeader toggle={() => toggleModal(2)} />
          <ModalBody>
            {(className === "Class 3" || className === "Class 4") && (
              <ACRFormClass3Part3 employee={employees} />
            )}
            {(designationName === "Assistant Professor" ||
              designationName === "Professor") && (
                <ACRFormProfessorPart3 employee={employees} />
              )}
            {(designationName === "Librarian") && (
              <ACRForLibrerianPart3 employee={employees} />
            )}
            {(designationName === "Sports Officer") && (
              <ACRForSportsOfficerPart3 employee={employees} />
            )}
            {(designationName === "Registrar") && (
              <ACRformForRegistrarPart3 employee={employees} />
            )}
            {(designationName === "UG Principal" ||
              designationName === "PG Principal") && (
                <ACRFormUGandPGPart3 employee={employees} />
              )}

            {(designationName === 'Additional Director' ||
              designationName === 'Joint Director (F)' ||
              designationName === 'Joint Director' ||
              designationName === 'Assistant Director (F)' ||
              designationName === 'Deputy Director') && (
                <ACRForDirectorPart3 employee={employees} />
              )}
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => toggleModal(2)}>
              Close
            </Button>
          </ModalFooter>
        </Modal>

        <Modal
          isOpen={isLevel3ModalOpen}
          toggle={() => toggleModal(3)}
          backdrop="static"
          style={{
            maxWidth: "90%",
            width: "100%",
          }}
        >
          <ModalHeader toggle={() => toggleModal(3)} />
          <ModalBody>
            {(className === "Class 3" || className === "Class 4") && (
              <ACRFormClass3Part4 employee={employees} />
            )}
            {(designationName === "Assistant Professor" ||
              designationName === "Professor") && (
                <ACRFormProfessorPart4 employee={employees} />
              )}
            {(designationName === "Librarian") && (
              <ACRForLibrerianPart4 employee={employees} />
            )}
            {(designationName === "Sports Officer") && (
              <ACRForSportsOfficerPart4 employee={employees} />
            )}
            {(designationName === "Registrar") && (
              <ACRformForRegistrarPart4 employee={employees} />
            )}
            {(designationName === "UG Principal" ||
              designationName === "PG Principal") && (
                <ACRFormUGandPGPart4 employee={employees} />
              )}
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={() => toggleModal(3)}>
              Close
            </Button>
          </ModalFooter>
        </Modal>
        <Modal
          isOpen={isFinalForm}
          toggle={() => toggleFinal()}
          backdrop="static"
          style={{
            maxWidth: "90%",
            width: "100%",
          }}
        >
          <ModalHeader toggle={() => toggleFinal()} />
          <ModalBody>
            <Button
              color="primary"
              onClick={() => {
                handlePrint();
                navigate(-1);
              }}
            >
              Print
            </Button>

            <div id="print-container">
              {(className === "Class 3" || className === "Class 4") && (
                <ACRFormClass3or4FinalForm employee={finalFormData} />
              )}
              {(designationName === "Assistant Professor" ||
                designationName === "Professor") && (
                  <ACRFormProfessorFinal employee={finalFormData} />
                )}
              {(designationName === 'Additional Director' ||
                designationName === 'Joint Director (F)' ||
                designationName === 'Joint Director' ||
                designationName === 'Assistant Director (F)' ||
                designationName === 'Deputy Director') && (
                  <ACRForDirectorPart4Final employee={finalFormData} />
                )}
            </div>
          </ModalBody>
          <ModalFooter>
            <Button
              color="secondary"
              onClick={() => toggleFinal()}
            >
              Close
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default ACRFormDirectorList;