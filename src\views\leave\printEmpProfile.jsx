import { useState, useEffect } from "react";
import {
  Row,
  Col,
  But<PERSON>,
  <PERSON>,
  <PERSON><PERSON>ead<PERSON>,
  CardHeader,
  Table,
  CardBody,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import logo from "../../assets/img/brand/CGGov.png";

import axios from "axios";
import { useParams, useNavigate } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import "../../assets/css/empProfile.css";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const PrintEmpProfile = () => {
  const token = sessionStorage.getItem("authToken");
  const name = sessionStorage.getItem("name");

  const endPoint = import.meta.env.VITE_API_URL;
  const { id } = useParams();
  const [user, setUser] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const navigate = useNavigate();
  const [data, setUserProfile] = useState([]);
  const [designationData, setDesignationData] = useState([]);
  const [district, setDistrict] = useState([]);
  const [classData, setClassData] = useState([]);
  const [districtAll, setDistrictAll] = useState([]);

  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);

  const [basicPay, setBasicPay] = useState([]);

  useEffect(() => {
    const getBasicPay = async () => {
      try {
        let response;
        // console.log("Enterd Here 1111", data.employeeInformation.payScaleType);

        if (data.employeeInformation?.payScaleType === "UGC") {
          //   console.log("Enterd Here", data.employeeInformation?.payScaleType);

          response = await axios.get(
            `${endPoint}/api/get-all-basic-pay-teaching`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
        } else {
          //   console.log("Enterd Here 222");

          response = await axios.get(`${endPoint}/api/get-all-basic-pay`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
        }
        if (response.status === 200) {
          setBasicPay(response.data);
        }
      } catch (error) {
        console.error("Error fetching Basic Pay data:", error);
        alert("Failed to load Basic Pay data.");
      }
    };

    
      getBasicPay();
  }, [endPoint, token, data]);

  const fetchData = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-emp-profile?empId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setUserProfile(data);
        handlePreview();
      } else {
        SwalMessageAlert("PROFILE NOT COMPLETED", "warning");
        navigate("/admin/employee-list");
      }
    } catch (error) {
      console.error("Error fetching Employee data:", error);
      alert("Failed to load Employee data.");
    }
  };
  useEffect(() => {
    fetchData();
  }, [id]);

  const fetchVidhansabha = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${Number(
          data?.academicInformation?.workplaceDistrict
        )}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
      } else {
        alert("Failed to fetch district data.");
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
    }
  };
  useEffect(() => {
    if (
      data &&
      data.academicInformation &&
      data.academicInformation.workplaceDistrict
    ) {
      fetchVidhansabha();
    }
  }, [endPoint, token, data]);

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
          SwalMessageAlert;
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchDesignation();
    fetchClassData();
    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]);

  useEffect(() => {
    const getDistrictAll = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrictAll(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 1.");
      }
    };
    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 2.");
      }
    };

    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
    getDistrict();
    getDistrictAll();
    getDistrictAllStateWise();
  }, [endPoint, token]);
  const [stateWiseDistrict, setstateWiseDistrict] = useState([]);

  const getDistrictAllStateWise = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-alldistrict-state`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setstateWiseDistrict(data);
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
      alert("Failed to load district data 3.");
    }
  };

  const [stateName, setStateName] = useState([]); // For table data
  useEffect(() => {
    const fetchStateList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-all-state`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setStateName(response.data);
        } else {
          SwalMessageAlert("No State Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchStateList();
  }, [endPoint, token]);

  const getClassName = (value) => {
    // console.log(value, classData, "adfjkahsd");
    const classObj = classData.find((classItem) => classItem._id === value);
    return classObj ? classObj.className : "Unknown Class Name";
  };
  useEffect(() => {
    const getEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee-byId/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          setUser(data);
        }
      } catch (error) {
        console.error("Error fetching Employee data:", error);
        alert("Failed to load Employee data.");
      }
    };
    getEmployee();
  }, [endPoint, token, id]);

  const handlePreview = () => {
    setShowPreview(true);
  };

  const handlePrint = () => {
    const printWindow = window.open("", "_blank");
    const printContent = document.getElementById("print-container").innerHTML;
    const printDocument = printWindow.document;

    printDocument.write(`
            <html>
              <head>
                <title>Print Form</title>
                <style>
                  @page { 
                    size: A4; 
                    margin: 20px; 
                  }
                  body { 
                    font-family: Arial, sans-serif; 
                    position: relative; 
                  }
                  .print-container {
                    width: 100%;
                    min-height: 29.7cm;
                    background: #fff;
                    position: relative;
                    padding-bottom: 50px; /* Space for footer */
                  }
                  .form-field {
                    margin-bottom: 15px;
                  }
                  .form-field label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                  }
                  .form-field input {
                    width: 100%;
                    padding: 8px;
                    box-sizing: border-box;
                  }
                  h1 {
                    font-size: 24px;
                    margin-bottom: 20px;
                  }
                  p {
                    font-size: 16px;
                    margin-bottom: 10px;
                  }
                  .header, .footer {
                    position: fixed;
                    left: 0;
                    right: 0;
                    text-align: center;
                    font-size: 12px;
                  }
                  .header {
                    top: 0;
                  }
                  .footer {
                    bottom: 0;
                  }
                  .page-number {
                    display: inline-block;
                  }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>Header Title</h1>
                </div>
                <div class="print-container">
                  ${printContent}
                </div>
                <div class="footer" style="position: fixed; top:28cm; left: 0; right: 0; bottom: 0; text-align: center; padding: 10px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <div class="page-number" style="flex: 1; text-align: center;">Page 1 of 3</div>
        <h4 style="margin: 0; text-align: right;">Printed at : ${name}</h4>
    </div>
</div>
               <script>
              // Function to update page numbers
              function updatePageNumbers() {
                const totalPages = Math.ceil(document.body.scrollHeight / window.innerHeight);
                const pageNumberElements = document.querySelectorAll('.page-number');
                pageNumberElements.forEach((el, index) => {
                  el.innerHTML = "Page " + (index + 1) + " of " + totalPages;
                });
              }
              window.onload = function() {
                updatePageNumbers();
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
              </body>
            </html>
        `);
    printDocument.close();
  };

  // const currentDateTime = new Date().toLocaleString('en-IN', {
  //     day: '2-digit',
  //     month: '2-digit',
  //     year: 'numeric',
  //     hour: '2-digit',
  //     minute: '2-digit',
  //     second: '2-digit',
  //     hour12: true, // Use 12-hour clock
  // });

  return (
    <>
      <Header />
      <Modal
        isOpen={showPreview}
        toggle={() => setShowPreview(false)}
        backdrop="static"
        style={{ maxWidth: "70%" }}
      >
        <ModalHeader style={{ height: "20px", paddingTop: "5px" }}>
          <Row style={{ display: "flex" }}>
            <Col lg={10} style={{ width: 1002 }}>
              {" "}
              Print Preview
            </Col>
            <Col lg={2}>
              <Button
                style={{ paddingTop: "12px" }}
                close
                onClick={() => {
                  handlePreview();
                  navigate("/admin/dashboard");
                }}
              />
            </Col>
          </Row>
        </ModalHeader>
        <ModalBody style={{ marginTop: "0px" }}>
          <div id="print-container">
            <CardHeader>
              <Row
                style={{
                  textAlign: "center", // Center horizontally
                  display: "block", // Use flexbox for alignment
                }}
              >
                <Col
                  style={{
                    textAlign: "left",
                    display: "flex", // Center horizontally
                  }}
                >
                  {logo && (
                    <img
                      alt="logo"
                      height="50px"
                      className="navbar-brand-img"
                      style={{ marginRight: "20px" }}
                      src={logo}
                    />
                  )}
                  <span
                    style={{
                      textAlign: "left",
                      fontSize: "16px",
                      color: "darkblue",
                      fontWeight: "bolder",
                      display: "block", // Ensure block-level behavior
                    }}
                  >
                    DEPARTMENT OF <br /> HIGHER EDUCATION
                  </span>
                </Col>
              </Row>
              <Row
                style={{
                  justifyContent: "center",
                  display: "flex",
                  marginTop: "10px", // Add some spacing between rows
                }}
              >
                <Col md="12">
                  <div style={{ textAlign: "center" }}>
                    {user &&
                    user.encodedImage &&
                    user.faceVerified !== false &&
                    user.encodedImage !== "" ? (
                      <img
                        src={`data:image/png;base64,${user.encodedImage}`}
                        style={{
                          width: "220px",
                          height: "220px",
                          borderRadius: "100%",
                          objectFit: "cover",
                        }}
                        alt="User Profile"
                      />
                    ) : (
                      <img
                        src={NavImage}
                        style={{
                          width: "220px",
                          height: "220px",
                          borderRadius: "100%",
                          objectFit: "cover",
                        }}
                        alt="Default Profile"
                      />
                    )}
                  </div>
                </Col>
              </Row>
            </CardHeader>
            <CardBody style={{ marginTop: "1px" }}>
              <div style={{ textAlign: "center" }}>
                <h3>
                  {user.title}
                  {user.name} <br />
                </h3>
              </div>
              <div style={{ textAlign: "center" }}>
                {user.designationDetails?.designation}
              </div>
              <div style={{ textAlign: "center" }}>
                <h3>
                  <span
                    style={{
                      color: "darkblue",
                      fontSize: "14px",
                      textAlign: "center",
                    }}
                  >
                    Employee Code: - {user.empCode}
                  </span>
                  <br />
                  <span style={{ color: "orange", fontSize: "14px" }}>
                    {user.collegeDetails?.name}
                  </span>
                  <br></br>
                  <span>
                    {user.activeStatus === true ? (
                      <span style={{ fontSize: "14px", color: "green" }}>
                        Active
                      </span>
                    ) : (
                      <span style={{ fontSize: "18px", color: "red" }}>
                        Inactive
                      </span>
                    )}
                  </span>
                </h3>
              </div>

              <div style={{ textAlign: "center" }}>
                <div>
                  {" "}
                  {/* Reduced margin-bottom */}
                  Email - <a href={`mailto:${user.email}`}>{user.email}</a>
                </div>
                <div>
                  {" "}
                  {/* Reduced margin-bottom */}
                  Contact - <a>{user.contact}</a>
                </div>
              </div>

              <div style={{ textAlign: "center" }}>
                <div>Address - {user.address}</div>

                <div>
                  <div>
                    <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                      Registered: <span>{formatDate(user.createdAt)}</span>
                    </span>
                  </div>
                  <div>
                    <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                      Last Updated : <span>{formatDate(user.updatedAt)}</span>
                    </span>
                  </div>
                </div>
              </div>
            </CardBody>
            <hr />
            <Row>
              <div className="profile-table">
                <table style={{ width: "99%", borderCollapse: "collapse" }}>
                  <tbody>
                    <tr
                      style={{
                        border: "3px solid black",
                        fontSize: "14px",
                        textAlign: "center",
                        padding: "10px",
                        backgroundColor: "lightgray",
                        width: "100%",
                        verticalAlign: "middle",
                      }}
                    >
                      <td colspan="100%" style={{ padding: "1px" }}>
                        <strong
                          style={{
                            fontSize: "18px",
                            textAlign: "center",
                            textJustify: "center",
                          }}
                        >
                          Personal Information
                        </strong>
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        textAlign: "left",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Employee Code:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.empCode}
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Employee Name ({user.title}):
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.name}
                      </td>
                    </tr>

                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Email Id:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.email}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Working Mobile No:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.MobileNo}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Date of Birth:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.employeeDOB}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Gender:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.employeeGender}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Category:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.employeeCategory}
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Home State:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.homeDistrict}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Permanent Home Address:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.permanentHomeAddress}
                      </td>
                      {data?.personalInformation?.homeDistrict === "other" && (
                        <>
                          <Col
                            md="4"
                            className=" p-2"
                            style={{ fontSize: "13px" }}
                          >
                            <strong> State : </strong>{" "}
                            <strong className=" text-primary ml-2">
                              {stateName.find(
                                (type) =>
                                  String(type.STATEID) ===
                                  String(data?.personalInformation?.state)
                              )?.STATTENAME || "N/A"}
                            </strong>
                          </Col>
                          <Col
                            md="4"
                            className=" p-2"
                            style={{ fontSize: "13px" }}
                          >
                            <strong>Other District:</strong>{" "}
                            <strong className=" text-primary ml-2">
                              {stateWiseDistrict.find(
                                (type) =>
                                  String(type._id) ===
                                  String(
                                    data?.personalInformation?.otherDistrict
                                  )
                              )?.allDistrictName || "N/A"}
                            </strong>
                          </Col>
                        </>
                      )}
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Disability:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.disability === "true"
                          ? "Yes"
                          : "No"}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "1px solid black",
                        fontSize: "14px",
                        padding: "10px",
                      }}
                    >
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          fontWeight: "bold",
                        }}
                      >
                        Type of Disability:
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontSize: "16px",
                          color: "darkblue",
                        }}
                      >
                        {data?.personalInformation?.disabiltyTypes}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "3px solid black",
                        fontSize: "14px",
                        textAlign: "center",
                        padding: "10px",
                        backgroundColor: "lightgray",
                        width: "100%",
                        verticalAlign: "middle",
                      }}
                    >
                      <td colspan="100%" style={{ padding: "1px" }}>
                        <strong
                          style={{
                            fontSize: "18px",
                            textAlign: "center",
                            textJustify: "center",
                          }}
                        >
                          Academic Information
                        </strong>
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Category, In Which Service Joined
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.academicInformation?.appointmentCategory ||
                          "N/A"}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Qualification
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.academicInformation?.educationQualification ||
                          "N/A"}
                      </td>
                    </tr>
                    <tr>
                      {data?.academicInformation?.educationQualification ===
                        "Other" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Specify Other Qualification
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.academicInformation?.qualificationDetails ||
                              "N/A"}
                          </td>
                        </>
                      )}
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Workplace Division
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {
                          division.find(
                            (type) =>
                              String(type.divisionCode) ===
                              String(
                                data?.academicInformation?.workplaceDivision
                              )
                          )?.name
                        }
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Workplace District
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {
                          district.find(
                            (type) =>
                              String(type.LGDCode) ===
                              String(
                                data?.academicInformation?.workplaceDistrict
                              )
                          )?.districtNameEng
                        }
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Workplace Vidhansabha
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {
                          vidhansabha.find(
                            (type) =>
                              String(type.ConstituencyNumber) ===
                              String(
                                data?.academicInformation?.workplaceVidhansabha
                              )
                          )?.ConstituencyName
                        }
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "3px solid black",
                        fontSize: "14px",
                        textAlign: "center",
                        padding: "10px",
                        backgroundColor: "lightgray",
                        width: "100%",
                        verticalAlign: "middle",
                      }}
                    >
                      <td colspan="100%" style={{ padding: "1px" }}>
                        <strong
                          style={{
                            fontSize: "18px",
                            textAlign: "center",
                            textJustify: "center",
                          }}
                        >
                          Employee Information
                        </strong>
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Employee / Work Type
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data.employeeInformation?.employeeType || "N/A"}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Pay Scale Type
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data.employeeInformation?.payScaleType || "N/A"}
                      </td>
                    </tr>

                    {data.employeeInformation?.payScaleType === "UGC" && (
                      <tr>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "10px",
                            fontWeight: "bold",
                          }}
                        >
                          Current AGP (According to 6th Pay) (if UGC)
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "10px",
                            color: "darkblue",
                          }}
                        >
                          {data.employeeInformation?.CurrentAGP || "N/A"}
                        </td>
                      </tr>
                    )}
                    {data.employeeInformation?.CurrentAGP === "7000" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting AGP 7000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.dateOfAGPSevenThous ||
                              "N/A"}
                          </td>
                        </tr>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Basic pay in 7th Pay Scale after getting AGP 7000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.basicPayscaleSevenAGP ||
                              "N/A"}
                          </td>
                        </tr>
                      </>
                    )}
                    {data.employeeInformation?.CurrentAGP === "8000" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting AGP 8000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data.employeeInformation?.dateOfAGPEightThous
                            ) || "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Basic pay in 7th Pay Scale after getting AGP 8000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.basicPayscaleEightAGP ||
                              "N/A"}
                          </td>
                        </tr>
                      </>
                    )}
                    {data.employeeInformation?.CurrentAGP === "9000" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting AGP 9000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.dateOfAGPNineThous ||
                              "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Basic pay in 7th Pay Scale after getting AGP 9000
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.basicPayscaleNineAGP ||
                              "N/A"}
                          </td>
                        </tr>
                      </>
                    )}
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Retirement Date
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {formatDate(data.employeeInformation?.retirementDate)}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Class of First Regular Appointment
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {getClassName(data.employeeInformation?.employeeClass)}
                      </td>
                    </tr>

                    <tr>
                      {" "}
                      {designationData && designationData.length > 0 && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Designation of First Regular Appointment
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {designationData.find(
                              (type) =>
                                String(type._id) ===
                                String(
                                  data.employeeInformation
                                    ?.firstRegularAppointmentDesignation
                                )
                            )?.designation || "N/A"}
                          </td>
                        </>
                      )}
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Appointment Type
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data.employeeInformation?.appointmentType || "N/A"}
                      </td>
                    </tr>
                    <tr>
                      {" "}
                      {data.employeeInformation?.appointmentType ===
                        "Samviliyan" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of Samviliyan
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.dateOfSam || "N/A"}
                          </td>
                        </>
                      )}
                      {data.employeeInformation?.appointmentType ===
                        "Anukampa" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Typing Pass
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.TypingPass === "true"
                              ? "Yes"
                              : "No"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of Anukampa Niyukti
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(data.employeeInformation?.dateOfAnu) ||
                              "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      {" "}
                      {data.employeeInformation?.appointmentType ===
                        "Ad hoc" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of Adhoc Appointment
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.dateOfAdhoc || "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of regular appointment
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation
                              ?.dateofRegularApponitment || "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      {data.employeeInformation?.TypingPass === "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Upload Typing Pass Certificate
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation
                              ?.uploadTypingPassCertificate
                              ? "Uploaded"
                              : "Not Uploaded"}
                          </td>
                        </>
                      )}
                      {getClassName(data.employeeInformation?.employeeClass) ===
                        "Class 3" ||
                      getClassName(data.employeeInformation?.employeeClass) ===
                        "Class 4" ? (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Upload 12th Marksheet (if class 3 or 4)
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.uploadsMarksheet
                              ? "Uploaded"
                              : "Not Uploaded"}
                          </td>
                        </>
                      ) : null}
                    </tr>
                    <tr>
                      {getClassName(data.employeeInformation?.employeeClass) ===
                        "Class 3" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Accounts Trained (if class 3)
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation?.IsAccountsTrained ===
                            "true"
                              ? "Yes"
                              : "No"}
                          </td>
                        </>
                      )}
                      {data.employeeInformation?.IsAccountsTrained ===
                        "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Accounts Training Pass
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data.employeeInformation
                              ?.IsAccountsTrainingPass === "true"
                              ? "Yes"
                              : "No"}
                          </td>
                        </>
                      )}
                    </tr>
                    {data.employeeInformation?.IsAccountsTrainingPass ===
                      "true" && (
                      <tr>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "10px",
                            fontWeight: "bold",
                          }}
                        >
                          Upload Accounts Training Certificate
                        </td>
                        <td
                          style={{
                            border: "1px solid black",
                            padding: "10px",
                            color: "darkblue",
                          }}
                        >
                          {data.employeeInformation?.uploadAccTCertificate
                            ? "Uploaded"
                            : "Not Uploaded"}
                        </td>
                      </tr>
                    )}
                    <tr
                      style={{
                        border: "3px solid black",
                        fontSize: "14px",
                        textAlign: "center",
                        padding: "10px",
                        backgroundColor: "lightgray",
                        width: "100%",
                        verticalAlign: "middle",
                      }}
                    >
                      <td colspan="100%" style={{ padding: "1px" }}>
                        <strong
                          style={{
                            fontSize: "18px",
                            textAlign: "center",
                            textJustify: "center",
                          }}
                        >
                          Present Information
                        </strong>
                      </td>
                    </tr>

                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Present Post (Designation)
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data.presentInformation?.presentPost || "N/A"}
                      </td>
                      {data?.presentInformation
                        ?.firstRegularAppointmentDesignation !==
                        data?.presentInformation?.presentPost && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of Joining Present Post
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.presentInformation?.dateOfJoiningPresentPost
                            ) || "N/A"}
                          </td>
                        </>
                      )}{" "}
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Date of First Appointment
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {formatDate(
                          data?.presentInformation?.appointmentDate
                        ) || "N/A"}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Date of Posting in Present College
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {formatDate(
                          data?.presentInformation?.dateOfPresentClgPosting
                        ) || "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Name of Present College
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {user.collegeDetails?.name || "N/A"}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Probation Completion Status
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.presentInformation?.probationCompleteStatus ||
                          "N/A"}
                      </td>
                    </tr>
                    <tr>
                      {data?.presentInformation?.probationCompleteStatus ===
                        "onProbation" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Police Verification (if on Probation)
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.presentInformation?.policeVerification ||
                              "N/A"}
                          </td>
                        </>
                      )}
                      {data?.presentInformation?.probationCompleteStatus ===
                        "completion" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of Completion
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.presentInformation?.dateofCompletion ||
                              "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      {data?.presentInformation?.policeVerification ===
                        "Completed" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Upload Police Verification (if completed)
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.presentInformation?.uploadPoliceverification
                              ? "Uploaded"
                              : "Not Uploaded"}
                          </td>
                        </>
                      )}

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Present Pay Level (7th Pay)
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.presentInformation?.presentPayLevel || "N/A"}
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Present Basic Pay (7th Pay)
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {basicPay.find(
                          (type) =>
                            String(type._id) ===
                            String(data.presentInformation?.presentBasicpay)
                        )?.basicPayScale || "N/A"}
                      </td>
                    </tr>
                    <tr
                      style={{
                        border: "3px solid black",
                        fontSize: "14px",
                        textAlign: "center",
                        padding: "10px",
                        backgroundColor: "lightgray",
                        width: "100%",
                        verticalAlign: "middle",
                      }}
                    >
                      <td colspan="100%" style={{ padding: "1px" }}>
                        <strong
                          style={{
                            fontSize: "18px",
                            textAlign: "center",
                            textJustify: "center",
                          }}
                        >
                          Other Informations
                        </strong>
                      </td>
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Availed 1st TimeScale Pay
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.firstTimeScale === "true"
                          ? "Yes"
                          : "No"}
                      </td>

                      {data?.otherInformation?.firstTimeScale === "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting 1st TimeScale Pay
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.firstScaleDate
                            ) || "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Availed 2nd Time Scale Pay
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.secondTimeScale === "true"
                          ? "Yes"
                          : "No"}
                      </td>

                      {data?.otherInformation?.secondTimeScale === "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting 2nd TimeScale Pay
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.secondScaleDate
                            ) || "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Availed 3rd Time Scale Pay
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.thirdTimeScale === "true"
                          ? "Yes"
                          : "No"}
                      </td>

                      {data?.otherInformation?.thirdTimeScale === "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Date of getting 3rd TimeScale Pay
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.thirdScaleDate
                            ) || "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Latest Seniority List (Anitm) Sr. No.{" "}
                        {data?.otherInformation?.employeeType}
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.latestSeniorityList || "N/A"}
                      </td>
                    </tr>
                    {data.employeeInformation?.employeeType === "TEACHING" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Subject
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.otherInformation?.subject || "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            M. Phil
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.otherInformation?.Mphil === "true"
                              ? "Yes"
                              : "No"}
                          </td>
                        </tr>
                        {data?.otherInformation?.Mphil === "true" && (
                          <>
                            <tr>
                              <td
                                style={{
                                  border: "1px solid black",
                                  padding: "10px",
                                  fontWeight: "bold",
                                }}
                              >
                                Subject of M. Phil
                              </td>
                              <td
                                style={{
                                  border: "1px solid black",
                                  padding: "10px",
                                  color: "darkblue",
                                }}
                              >
                                {data?.otherInformation?.subjectOfMphil ||
                                  "N/A"}
                              </td>

                              <td
                                style={{
                                  border: "1px solid black",
                                  padding: "10px",
                                  fontWeight: "bold",
                                }}
                              >
                                M. Phil Award Date
                              </td>
                              <td
                                style={{
                                  border: "1px solid black",
                                  padding: "10px",
                                  color: "darkblue",
                                }}
                              >
                                {formatDate(
                                  data?.otherInformation?.MphilAwardDate
                                ) || "N/A"}
                              </td>
                            </tr>
                          </>
                        )}
                      </>
                    )}
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        PhD
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.phd === "true" ? "Yes" : "No"}
                      </td>
                    </tr>
                    {data?.otherInformation?.phd === "true" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Subject of PhD
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.otherInformation?.subjectOfPhd || "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            PhD Notification Date
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.phdNotifyDate
                            ) || "N/A"}
                          </td>
                        </tr>
                      </>
                    )}
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Dsc / DLit
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.DscOrDlit === "true"
                          ? "Yes"
                          : "No"}
                      </td>
                    </tr>
                    {data?.otherInformation?.DscOrDlit === "true" && (
                      <>
                        <tr>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Subject of Dsc / DLit
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {data?.otherInformation?.subjectOfDscOrDlit ||
                              "N/A"}
                          </td>

                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            Dsc / DLit Notification Date
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.DscOrDlitNotifyDate
                            ) || "N/A"}
                          </td>
                        </tr>
                      </>
                    )}
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        NET / SET
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.NetSet === "true"
                          ? "Yes"
                          : "No"}
                      </td>

                      {data?.otherInformation?.NetSet === "true" && (
                        <>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              fontWeight: "bold",
                            }}
                          >
                            NET / SET Certificate Date
                          </td>
                          <td
                            style={{
                              border: "1px solid black",
                              padding: "10px",
                              color: "darkblue",
                            }}
                          >
                            {formatDate(
                              data?.otherInformation?.NetSetCertificateDate
                            ) || "N/A"}
                          </td>
                        </>
                      )}
                    </tr>
                    <tr>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Court Case Status
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.courtCaseStatus || "N/A"}
                      </td>

                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          fontWeight: "bold",
                        }}
                      >
                        Pending Departmental Enquiry / Prosecution Case
                      </td>
                      <td
                        style={{
                          border: "1px solid black",
                          padding: "10px",
                          color: "darkblue",
                        }}
                      >
                        {data?.otherInformation?.PendingDeptEnquireCase ===
                        "true"
                          ? "Yes"
                          : "No"}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </Row>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              handlePreview();
              navigate("/admin/dashboard");
            }}
          >
            Close
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handlePrint();
              navigate(-1);
            }}
          >
            Print
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default PrintEmpProfile;
