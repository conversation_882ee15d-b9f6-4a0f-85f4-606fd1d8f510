﻿<%@ Page Title="" Language="C#" MasterPageFile="~/CandMaster.master" AutoEventWireup="true" CodeFile="PreviewForm.aspx.cs" Inherits="candreg_PreviewForm" %>

<%@ Register Assembly="AjaxControlToolkit" Namespace="AjaxControlToolkit" TagPrefix="cc2" %>
<asp:Content ID="Content1" ContentPlaceHolderID="cpHeaderSection" runat="Server">
    <script src="myscript.js"></script>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="mainContent" runat="Server">
    <asp:ScriptManager ID="ScriptManager1" runat="server"></asp:ScriptManager>
    <div class="container">
        <div class="panel-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="sirow clrleft mb_10">
                        <asp:Button ID="btnback" runat="server" CssClass="btn btn-info" Text="Back" OnClick="btnback_Click" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container">
        <div class="panel panel-primary paneles" style="margin-top: 50px;">
            <div class="panel-heading">
                <h5 style="font-size: large">आवेदन भरें - </h5>
            </div>
            <asp:UpdatePanel ID="UpdatePanel2" runat="server">
                <ContentTemplate>

                    <div class="panel-body">
                        <div class="row">
                            <div class="col-lg-12">
                                <div id="divDetl" runat="server" class="sirow clrleft cform_group reg-details mt_20">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="firstnameH">प्रथम नाम (हिंदी में)</label>
                                                <asp:Label ID="Label7" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="txtfnameh" class="form-control" placeholder="प्रथम नाम (हिंदी में)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="MnameH">मध्य नाम (हिंदी में)</label>
                                                <asp:Label ID="Label10" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text=""></asp:Label>
                                                <asp:TextBox ID="txtmnameh" class="form-control" placeholder="मध्य नाम (हिंदी में)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="lastnameH">अंतिम नाम (हिंदी में)</label>
                                                <asp:Label ID="Label9" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="txtlnameh" class="form-control" placeholder="अंतिम नाम (हिंदी में)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="firstnameE">प्रथम नाम (अंग्रेजी में)</label>
                                                <asp:Label ID="Label11" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="txtfnameE" class="form-control" Style="text-transform: uppercase;" placeholder="First Name" onkeypress="return AllowAlphabet(event)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="MiddleNameE">मध्य नाम (अंग्रेजी में)</label>
                                                <asp:Label ID="Label12" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text=""></asp:Label>
                                                <asp:TextBox ID="txtMnameE" class="form-control" Style="text-transform: uppercase;" placeholder="Middle Name" onkeypress="return AllowAlphabet(event)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="lastnameE">अंतिम नाम (अंग्रेजी में)</label>
                                                <asp:Label ID="Label13" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="txtlnameE" class="form-control" placeholder="Last Name" Style="text-transform: uppercase;" onkeypress="return AllowAlphabet(event)" runat="server" ReadOnly="true">></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="relativename">पिता / पति का नाम (हिंदी में)</label>
                                                <asp:Label ID="Label14" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="Txtrelativename" class="form-control" placeholder="पिता /पति का नाम" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="relativename">पिता / पति का नाम (अंग्रेजी में)</label>
                                                <asp:Label ID="Label15" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="TxtrelativenameE" class="form-control" placeholder="Father /Husband Name" Style="text-transform: uppercase;" onkeypress="return AllowAlphabet(event)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="mothername">माता का नाम (हिंदी में)</label>
                                                <asp:Label ID="Label16" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="Txtmothername" class="form-control" placeholder="माता का नाम" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="mothernameE">माता का नाम (अंग्रेजी में)</label>
                                                <asp:Label ID="Label17" runat="server" Font-Bold="True" Font-Size="16pt" ForeColor="Red" Text="*"></asp:Label>
                                                <asp:TextBox ID="TxtmothernameE" class="form-control" placeholder="Mother Name" Style="text-transform: uppercase;" onkeypress="return AllowAlphabet(event)" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <asp:HyperLink runat="server" ID="a_photoUplodad" Target="_blank" Visible="false" style="font-weight: bold;color: #5cb85c;">फोटो देखें</asp:HyperLink>
                                                <asp:HiddenField ID="Hiddenphoto" runat="server" />
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <asp:HyperLink runat="server" ID="a_SignUpload" Target="_blank" Visible="false" style="font-weight: bold;color: #5cb85c;">हस्ताक्षर देखें</asp:HyperLink>

                                                <asp:HiddenField ID="Hiddensign" runat="server" />
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-12">
                                            <div class="sirow clrleft mb_10">
                                                <label style="color: #1151c7; font-weight: bold; font-size: 19px;">वर्तमान पता</label>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="state">राज्य</label>
                                                <asp:DropDownList ID="ddlstate" CssClass="form-control" AutoPostBack="true" OnSelectedIndexChanged="ddlstate_SelectedIndexChanged" runat="server" Enabled="false"></asp:DropDownList>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="District">जिला</label>
                                                <asp:UpdatePanel ID="UpdatePanel3" runat="server">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="ddldistrict" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="Address">पता</label>
                                                <asp:TextBox ID="txtaddress" TextMode="MultiLine" Height="75" MaxLength="200" onkeypress="return isAlphaNumeric(event)" class="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="pincode">पिनकोड</label>
                                                <asp:TextBox ID="Txtpincode" class="numeric" MaxLength="6" onkeypress="return numeric(event)" CssClass="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-12">
                                            <div class="sirow clrleft mb_10">
                                                <label style="color: #1151c7; font-weight: bold; font-size: 19px;">स्थाई पता</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="state">राज्य </label>
                                                <asp:DropDownList ID="perddlstate" AutoPostBack="true" OnSelectedIndexChanged="perddlstate_SelectedIndexChanged" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="District">जिला</label>
                                                <asp:UpdatePanel ID="UpdatePanel1" runat="server">
                                                    <ContentTemplate>
                                                        <asp:DropDownList ID="perddldistrict" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                                    </ContentTemplate>
                                                </asp:UpdatePanel>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="Address">पता</label>
                                                <asp:TextBox ID="pertxtadd" TextMode="MultiLine" Height="75" MaxLength="200" onkeypress="return isAlphaNumeric(event)" class="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="pincode">पिनकोड</label>
                                                <asp:TextBox ID="pertxtpincode" MaxLength="6" onkeypress="return numeric(event)" class="numeric" CssClass="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-12" style="text-align: center">
                                            <div class="sirow clrleft">
                                                <br />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label></label>
                                                <asp:CheckBox ID="chkgov" Text="शासकीय सेवा में है?" runat="server" CssClass="text-space check_line" Enabled="false" />
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label></label>
                                                <asp:CheckBox ID="chkservice" Text="क्या आप भूतपूर्व सैनिक है?" runat="server" CssClass="text-space check_line" Enabled="false" />
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="category">जाति संवर्ग</label>
                                                <asp:DropDownList ID="ddlcategory" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlcategory_SelectedIndexChanged" Enabled="false"></asp:DropDownList>
                                                <asp:Label ID="lblRequDdlcategory" runat="server" Visible="false" Text="*सेलेक्ट करें"></asp:Label>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-6">
                                            <div class="sirow clrleft mb_10">
                                                <label for="staus">वैवाहिक स्थिति</label>
                                                <asp:DropDownList ID="ddlmarriedstatus" CssClass="form-control" runat="server" Enabled="false">
                                                    <asp:ListItem Value="0">--चुने--</asp:ListItem>
                                                    <asp:ListItem Value="1">विवाहित</asp:ListItem>
                                                    <asp:ListItem Value="2">अविवाहित</asp:ListItem>
                                                </asp:DropDownList>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-12">
                                            <div class="sirow clrleft mb_10">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="gender">लिंग</label>
                                                <asp:DropDownList ID="ddlgender" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                                <asp:Label ID="lblRequDdlgender" runat="server" Visible="false" Text="*सेलेक्ट करें"></asp:Label>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="dob">जन्म दिनांक</label>
                                                <asp:TextBox ID="txtdob" TextMode="Date" class="form-control" runat="server" AutoPostBack="true" OnTextChanged="txtdob_TextChanged" onkeypress="return false;" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-4">
                                            <div class="sirow clrleft mb_10">
                                                <label for="age">आयु (as on 01.01.2023)</label>
                                                <asp:TextBox ID="txtage" class="form-control age" runat="server" ReadOnly="true"></asp:TextBox>
                                            </div>
                                            <br />
                                        </div>
                                        <div class="col-md-12">
                                            <div class="sirow clrleft mb_10">
                                            </div>
                                        </div>
                                        <div runat="server" id="dvagevalid">
                                            <div runat="server" id="dvcategory">
                                                <div class="col-md-12">
                                                    <div class="sirow clrleft mb_10">
                                                        <label style="color: #1151c7; font-weight: bold; font-size: 19px;">If SC/ST/OBC - </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="castesrno">प्रमाण पत्र क्रमांक</label>
                                                        <asp:TextBox ID="txtcastesrno" CssClass="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="castedist">प्रमाण पत्र जारी करने वाले जिला का नाम</label>
                                                        <asp:DropDownList ID="ddlcastedist" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="castename">प्रमाण पत्र किसके द्वारा जारी किया गया </label>
                                                        <asp:TextBox ID="txtcastename" CssClass="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                                        <asp:Label ID="lblreqcastename" runat="server" Visible="false" Text="*प्रमाण पत्र किसके द्वारा जारी किया गया भरे"></asp:Label>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="castedate">प्रमाण पत्र जारी करने की तिथि</label>
                                                        <asp:TextBox ID="txtcastedate" TextMode="Date" CssClass="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                                        <asp:Label ID="lblreqcastedate" runat="server" Visible="false" Text="*प्रमाण पत्र जारी करने की तिथि भरे"></asp:Label>
                                                    </div>
                                                    <br />
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="mobileno">मोबाइल न.</label>
                                                    <asp:TextBox ID="Txtmobileno" onkeypress="return numeric(event)" class="numeric" ReadOnly="true" CssClass="form-control" runat="server"></asp:TextBox>
                                                    <asp:RegularExpressionValidator ID="revMobileno" ControlToValidate="Txtmobileno" Text="*Enter Valid Mobile number" ForeColor="Red" SetFocusOnError="true" ErrorMessage="Enter valid mobile no" ValidationExpression="^([6789]{1})([0-9]{1})([0-9]{8})$" runat="server"></asp:RegularExpressionValidator>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="email">ईमेल</label>
                                                    <asp:TextBox ID="Textemail" TextMode="Email" class="form-control" runat="server" ReadOnly="true"></asp:TextBox>
                                                    <asp:RegularExpressionValidator ID="RegularExpressionValidator2" ValidationGroup="Save" ForeColor="Red" ErrorMessage="सही ईमेल आई डी डाले !!" ControlToValidate="Textemail" ValidationExpression="^([\w-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([\w-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$" runat="server"></asp:RegularExpressionValidator>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="handicape">क्या आप शारीरिक रूप से असक्षम (पी एच / दिव्यांग) हैं? (हाँ / नहीं)</label>
                                                    <asp:DropDownList ID="ddlhandicape" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlhandicape_SelectedIndexChanged" Enabled="false">
                                                        <asp:ListItem Value="0">--चुने--</asp:ListItem>
                                                        <asp:ListItem Value="Y">हाँ</asp:ListItem>
                                                        <asp:ListItem Value="N">नहीं</asp:ListItem>
                                                    </asp:DropDownList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6" runat="server" id="dvhandicapetype">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="handicapetype">हाँ तो विकलांगता का प्रकार - </label>
                                                    <asp:DropDownList ID="ddlhandicapetype" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlhandicapetype_SelectedIndexChanged" Enabled="false">
                                                        <asp:ListItem Value="0">--चुने--</asp:ListItem>
                                                        <asp:ListItem Value="OL">OL</asp:ListItem>
                                                        <asp:ListItem Value="HH">HH</asp:ListItem>
                                                        <asp:ListItem Value="OA">OA</asp:ListItem>
                                                        <asp:ListItem Value="B">B</asp:ListItem>
                                                        <asp:ListItem Value="LV">LV</asp:ListItem>
                                                    </asp:DropDownList>
                                                    <asp:Label ID="lblreqddlhandicapetype" runat="server" Visible="false" Text="*सलेक्ट करे"></asp:Label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                    <label style="color: #1151c7; font-weight: bold; font-size: 19px;">पद हेतु आवेदन - </label>
                                                </div>
                                            </div>
                                            <div runat="server" id="dv10thmarksheet">
                                                <div class="col-md-12">
                                                    <div class="sirow clrleft mb_10">
                                                        <label></label>
                                                        <asp:CheckBox ID="chklabattendant" Text="प्रयोगशाला परिचारक" runat="server" CssClass="text-space check_line" Enabled="false" />
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <span>10th मार्कशीट अपलोड करें</span>
                                                    <div class="sirow clrleft mb_10">
                                                        <label class="file srow">
                                                            <asp:HyperLink runat="server" ID="a_file10thmarksheet" Target="_blank" Visible="false" style="font-weight: bold;color: #5cb85c;">10th मार्कशीट देखें</asp:HyperLink>

                                                            <span class="file-custom"></span>
                                                        </label>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-8">
                                                    <label></label>
                                                    <br />

                                                    <asp:HiddenField ID="HiddenMarksheet" runat="server" />
                                                    <br />
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                    <label></label>
                                                    <asp:CheckBox ID="chkpost" Text="भृत्य/चौकीदार/स्वीपर" runat="server" CssClass="text-space check_line" Enabled="false" />
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <span>5th मार्कशीट अपलोड करें</span>
                                                <div class="sirow clrleft mb_10">
                                                    <label class="file srow">
                                                        <asp:HyperLink runat="server" ID="a_file5thmarksheet" Target="_blank" Visible="false" style="font-weight: bold;color: #5cb85c;">5th मार्कशीट</asp:HyperLink>

                                                        <span class="file-custom"></span>
                                                    </label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-8">
                                                <label></label>
                                                <br />
                                                <asp:HiddenField ID="HiddenMarksheet5th" runat="server" />
                                                <br />
                                            </div>
                                            <div runat="server" id="dvpriority" visible="false">
                                                <div class="col-md-12">
                                                    <div class="sirow clrleft mb_10">
                                                        <label style="color: #1151c7; font-weight: bold; font-size: 19px;">
                                                            Select Priority – (1, 2, 3) 
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="servantpriority">भृत्य</label>
                                                        <asp:DropDownList ID="ddlservantpriority" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlservantpriority_SelectedIndexChanged" Enabled="false">
                                                            <asp:ListItem Value="">--चुने--</asp:ListItem>
                                                            <asp:ListItem Value="0">0</asp:ListItem>
                                                            <asp:ListItem Value="1">1</asp:ListItem>
                                                            <asp:ListItem Value="2">2</asp:ListItem>
                                                            <asp:ListItem Value="3">3</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="watchmanpriority">चौकीदार</label>
                                                        <asp:DropDownList ID="ddlwatchmanpriority" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlwatchmanpriority_SelectedIndexChanged" Enabled="false">
                                                            <asp:ListItem Value="">--चुने--</asp:ListItem>
                                                            <asp:ListItem Value="0">0</asp:ListItem>
                                                            <asp:ListItem Value="1">1</asp:ListItem>
                                                            <asp:ListItem Value="2">2</asp:ListItem>
                                                            <asp:ListItem Value="3">3</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </div>
                                                    <br />
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="sirow clrleft mb_10">
                                                        <label for="sweeperpriority">स्वीपर</label>
                                                        <asp:DropDownList ID="ddlsweeperpriority" CssClass="form-control" runat="server" AutoPostBack="true" OnSelectedIndexChanged="ddlsweeperpriority_SelectedIndexChanged" Enabled="false">
                                                            <asp:ListItem Value="">--चुने--</asp:ListItem>
                                                            <asp:ListItem Value="0">0</asp:ListItem>
                                                            <asp:ListItem Value="1">1</asp:ListItem>
                                                            <asp:ListItem Value="2">2</asp:ListItem>
                                                            <asp:ListItem Value="3">3</asp:ListItem>
                                                        </asp:DropDownList>
                                                    </div>
                                                    <br />
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="preference">Exam centre Preference District - </label>
                                                    <asp:DropDownList ID="ddlpreference" CssClass="form-control" runat="server" Enabled="false"></asp:DropDownList>
                                                    <asp:Label ID="lblreqddlpreference" runat="server" Visible="false" Text="*सलेक्ट करे"></asp:Label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                    <label style="color: #ff0000; font-weight: bold; font-size: 12px;">
                                                        Note- परीक्षा केंद्र का जिला विभाग द्वारा परिवर्तित किया जा सकता है |
                                                    </label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_intercastemarriagepromotion">क्या आवेदक को कभी छत्तीसगढ़ शासन द्वारा अंतर-जातीय विवाह प्रोत्साहन के लिए सम्मानित किया गया है?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_intercastemarriagepromotion" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_govawards">क्या आवेदक को छत्तीसगढ़ शासन द्वारा निम्नलिखित में से किसी पुरस्कार से सम्मानित किया गया है- (राजीव पांडे पुरस्कार/गुन्डाधुर पुरस्कार/ महाराजा प्रवीर चंद्र भंजदेव पुरस्कार या राष्ट्रीय युवा पुरस्कार) ?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_govawards" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_defenceservices">क्या आप एक रक्षा सेवा कर्मी हैं, जो किसी विदेशी देश के साथ शत्रुता के दौरान या अशांत क्षेत्र में संचालन में अक्षम हैं और जिसके परिणामस्वरूप उन्हें रिहा किया गया है?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_defenceservices" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_bonafidecand">
                                                        क्या आप एक ऐसे अभ्यर्थी हैं जो वियतनाम से भारतीय मूल (भारतीय पासपोर्ट 
                                            धारक) का एक वास्तविक प्रत्यावर्तन है और साथ ही वियतनाम में भारतीय दूतावास द्वारा उसे जारी किया गया 
                                            आपातकालीन प्रमाण पत्र रखने वाले अभ्यर्थी हैं और जो जुलाई, 1975 से पहले वियतनाम से भारत पहुंचे हैं?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_bonafidecand" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-12">
                                                <div class="sirow clrleft mb_10">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_certifiedindian">
                                                        क्या आप भारतीय हैं?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_certifiedindian" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_sepereated">
                                                        क्या आप अलग या विधवा हैं?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_sepereated" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_excluded">
                                                        क्या आपको कभी किसी प्राधिकारी/संस्था द्वारा परीक्षा/चयन से बाहर रखा गया है?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_excluded" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_terminated">
                                                        क्या अभ्यर्थी को सेवा अवधि के दौरान कभी दंडित किया गया या सेवा समाप्त की गई है?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_terminated" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <label for="is_guilty">
                                                        क्या आपको कभी किसी आपराधिक मामले में दोषी घोषित किया गया है?</label>
                                                </div>
                                                <br />
                                            </div>
                                            <div class="col-md-6">
                                                <div class="sirow clrleft mb_10">
                                                    <asp:RadioButtonList ID="rdnis_guilty" RepeatDirection="Horizontal" Style="width: 15%" runat="server" Enabled="false">
                                                        <asp:ListItem Value="Y" Text="हाँ"></asp:ListItem>
                                                        <asp:ListItem Value="N" Text="नहीं" Selected="True"></asp:ListItem>
                                                    </asp:RadioButtonList>
                                                </div>
                                                <br />
                                            </div>
                                        </div>
                                        <br />

                                        <div class="col-md-12">
                                            <div class="sirow clrleft mb_10">
                                                <label></label>
                                                <asp:CheckBox ID="chktrue" Text="मैं एतद्द्वारा घोषणा करता हूं कि मैंने प्रासंगिक विज्ञापन और उसमें दर्शाए गए सभी नियमों और शर्तों को ध्यान से पढ़ा है और 
चयन प्रक्रिया के किसी भी चरण में भाग लेने के लिए विज्ञापन के नियमों और शर्तों का पालन करने का वचन देता हूं। यहां मेरे 
द्वारा प्रदान की गई जानकारी मेरी सर्वोत्तम जानकारी के अनुसार सत्य और सही है। मैं यह भी वचन देता हूं कि मैं किसी भी 
श्रेणी/उपश्रेणी/छूट/आरक्षण के किसी भी लाभ का दावा नहीं करूंगा जिसे मैंने इस ऑनलाइन आवेदन पत्र में विशेष रूप से इंगित 
नहीं किया है। इसके अलावा, मुझे पता है कि यदि मेरे द्वारा प्रदान की गई जानकारी किसी भी स्तर पर गलत/अधूरी साबित होती है, 
तो मैं उचित पैनल कार्रवाई के लिए उत्तरदायी होऊंगा और परीक्षा प्रक्रिया से अयोग्य घोषित कर दिया जाऊंगा। मैं अपने द्वारा प्रदान
की गई गलत/भ्रामक/अधूरी जानकारी के साथ अपने आवेदन की स्वीकृति के आधार पर किसी छूट या लाभ का दावा नहीं करूंगा। 
मैं विफलता या किसी सत्यापन नियम की कमी पर सही/गलत डेटा के साथ लागू पद के लिए पात्रता के रूप में अपने आवेदन की 
स्वीकृति का दावा नहीं करूंगा।
"
                                                    runat="server" CssClass="text-space check_line" Enabled="false" />
                                            </div>
                                        </div>
                                        <br />

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </ContentTemplate>
            </asp:UpdatePanel>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cpFooter" runat="Server">
    <script type="text/javascript" src="gtransapi.js"></script>
    <script type="text/javascript">
        google.load("elements", "1", { packages: "transliteration" });
        var control;
        function onLoad() {
            var options = {
                //Source Language
                sourceLanguage: google.elements.transliteration.LanguageCode.ENGLISH,
                // Destination language to Transliterate
                destinationLanguage: [google.elements.transliteration.LanguageCode.HINDI],
                shortcutKey: 'ctrl+g',
                transliterationEnabled: true
            };
            control = new google.elements.transliteration.TransliterationControl(options);
            control.makeTransliteratable(['<%=txtfnameh.ClientID%>']);
            control.makeTransliteratable(['<%=txtmnameh.ClientID%>']);
            control.makeTransliteratable(['<%=txtlnameh.ClientID%>']);
            control.makeTransliteratable(['<%=Txtrelativename.ClientID%>']);
            control.makeTransliteratable(['<%=Txtmothername.ClientID%>']);
        }
        google.setOnLoadCallback(onLoad);
</script>

    <script type="text/javascript">         
        $(document).ready(function () {
            $('.dob').Zebra_DatePicker({
                format: 'd-m-Y',
                inside: true,
                view: 'years',
                onSelect: function (view) {
                    var today = new Date();
                    var dateParts = $(this).val().split("-");
                    var dateObject = new Date(+dateParts[2], dateParts[1] - 1, +dateParts[0]);
                    var birthDate = new Date(dateObject);
                    var age = today.getFullYear() - birthDate.getFullYear();
                    var m = today.getMonth() - birthDate.getMonth();
                    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
                        age--;
                    }
                    if (age < 16) {
                        alert('Your age does not fulfill minimum age criteria (i.e. 16 years)');
                        $(this).val('');
                    }
                }
            });
            $('.YearofPass').Zebra_DatePicker({
                format: 'Y',
                direction: false
            });

        });
        function Showalert() {
            alert('Call JavaScript function from codebehind');
        }
        function readURL1(input, image) {
            var file = URL.createObjectURL(event.target.files[0]);
            $("#" + image).show();
            $("#" + image).attr("href", file);
            $("#" + image).text(event.target.files[0].name);
        }
        function readURL(input, image) {
            if (input.files && input.files[0]) {
                var reader = new FileReader();
                reader.onload = function (e) {
                    $('#' + image).attr('src', e.target.result);
                }
                reader.readAsDataURL(input.files[0]);
            }
        }
        $("[id$=txtPassage]").keyup(function () {
            if ($(this).hasClass("krutiDev") || $(this).hasClass("Devlys"))
                convert_to_unicode(sourceCtl, targetCtl);
            else
                $("#" + targetCtl).val($("#" + sourceCtl).val());
        });
</script>
</asp:Content>

