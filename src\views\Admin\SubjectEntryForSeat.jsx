import React, { useState, useEffect } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  Card,
  CardHeader,
  CardBody,
  Input,
  Button,
  Container,
  Row,
  Col,
  Table,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import { useNavigate } from "react-router-dom";

const SubjectEntryForSeat = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");

  const [showForm, setShowForm] = useState(true);
  const currentYear = new Date().getFullYear();
  const navigate = useNavigate();

  const [formId, setFormId] = useState(null);

  const currentSessionYear = `${currentYear}-${currentYear + 1}`;
  const [subjectData, setSubjectData] = useState([]);
  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: {
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token]);

  const [courseData, setCourseData] = useState([]);
  useEffect(() => {
    const fetchCourseList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-course`, {
          headers: {
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setCourseData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    fetchCourseList();
  }, [endPoint, token]);

  useEffect(() => {
    const checkAlreadyExist = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/seat-current-session/college-wise?id=${collegeId}&session=${currentSessionYear}`,
          {
            headers: {
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200 && response.data.msg === "Data Found") {
          setShowForm(false);
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    checkAlreadyExist();
  }, [endPoint, token]);

  const [rows, setRows] = useState([
    {
      sessionYear: `${currentYear}-${currentYear + 1}`,
      subjectList: "",
      courseList: "",
      subjectTypes: "",
      applicationNo: "",
      CurrentSeatNo: 0,
      RequestedSeatNo: 0,
      afterExtensionTotalSeatNo: 0,
      resoucesAvailability: "",
    },
  ]);

  // Add new row
  const addRow = () => {
    setRows([
      ...rows,
      {
        sessionYear: `${currentYear}-${currentYear + 1}`,
        subjectList: "",
        courseList: "",
        subjectTypes: "",
        applicationNo: "",
        CurrentSeatNo: 0,
        RequestedSeatNo: 0,
        afterExtensionTotalSeatNo: 0,
        resoucesAvailability: "",
      },
    ]);
  };
  const removeRow = (index) => {
    const updatedRows = rows.filter((_, i) => i !== index);
    setRows(updatedRows);
  };

  const handleInputChange = (index, event) => {
    const { name, value } = event.target;
    const updatedRows = [...rows];
    updatedRows[index][name] = value;
    if (name === "CurrentSeatNo" || name === "RequestedSeatNo") {
      const currentSeats = parseInt(updatedRows[index].CurrentSeatNo || 0);
      const requestedSeats = parseInt(updatedRows[index].RequestedSeatNo || 0);
      updatedRows[index].afterExtensionTotalSeatNo =
        currentSeats + requestedSeats;
    }
    setRows(updatedRows);
  };

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };

  const [SeatSubjectEnableData, setSeatSubjectEnableData] = useState([]);
  const seatStatus = SeatSubjectEnableData.find(
    (item) => item.type === "seat"
  )?.status;

  useEffect(() => {
    const getSeatSubjectEnableData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/seat-subject/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setSeatSubjectEnableData(response.data);

          if (response.data[0].status === false) {
            SwalMessageAlert("सीट वृद्धि  हेतु आवेदन अभी बंद है", "error");
            // navigate("/admin/dashboard")
          }
        } else {
          SwalMessageAlert("No Data Found", "error");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    getSeatSubjectEnableData();
  }, [endPoint, token]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    let hasError = false;
    const errors = [];
    rows.forEach((row, index) => {
      const requiredFields = [
        "sessionYear",
        "subjectList",
        "courseList",
        "subjectTypes",
        "applicationNo",
        "CurrentSeatNo",
        "RequestedSeatNo",
        "afterExtensionTotalSeatNo",
        "resoucesAvailability",
      ];
      requiredFields.forEach((field) => {
        if (!row[field] || row[field].toString().trim() === "") {
          hasError = true;
          errors.push(`Row ${index + 1}: ${field} is required.`);
        }
      });
      if (row["CurrentSeatNo"] && row["RequestedSeatNo"]) {
        const totalSeats =
          parseInt(row["CurrentSeatNo"]) + parseInt(row["RequestedSeatNo"]);
        if (parseInt(row["afterExtensionTotalSeatNo"]) !== totalSeats) {
          hasError = true;
          errors.push(
            `Row ${
              index + 1
            }: afterExtensionTotalSeatNo must equal CurrentSeatNo + RequestedSeatNo.`
          );
        }
      }
    });
    if (hasError) {
      alert("Validation Errors:\n" + errors.join("\n"));
      return;
    }
    try {
      const formseatId = formId !== null ? formId : "";
      const response = await axios.post(
        `${endPoint}/api/subject-seat/add/${collegeId}?formId=${formseatId}`,
        { ...rows },
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Form Submitted Successfully!", "success");
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      }
    } catch (error) {
      // console.log(error);
      handleApiError(error);
    }
  };
  const [seatExtensionList, setSeatExtensionList] = useState([]);

  useEffect(() => {
    const getSeatExtensionListCollegeWise = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/seat-extension-list/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setSeatExtensionList(response.data);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    getSeatExtensionListCollegeWise();
  }, [endPoint, token]);
  const columns = [
    {
      name: "Session Year",
      cell: (seatExtensionList) => (
        <div>
          <div className="mb-2">{seatExtensionList.sessionYear}</div>
        </div>
      ),
      wrap: true,
      width: "200px",
    },
    {
      name: "Seat Details",
      cell: (seatExtensionList) => (
        <div>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr>
                <th className="border border-gray-300 px-2 py-1">
                  संकाय का नाम
                </th>
                <th className="border border-gray-300 px-2 py-1">विषय</th>
                <th className="border border-gray-300 px-2 py-1">
                  संचालन का प्रकार
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  आवेदनों की संख्या
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  वर्तमान सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  मांग की गई सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  वृद्धि पश्चात् सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  भौतिक संसाधन
                </th>
              </tr>
            </thead>
            <tbody>
              {seatExtensionList &&
                seatExtensionList.subjectDetails.map((a) => (
                  <tr key={a._id || a.applicationNo}>
                    <td className="border border-gray-300 px-2 py-1">
                      {
                        courseData.find((b) => String(b._id) === a.courseList)
                          ?.courseName
                      }
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {
                        subjectData.find((b) => String(b._id) === a.subjectList)
                          ?.subjectName
                      }
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.subjectTypes}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.applicationNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.CurrentSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.RequestedSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.afterExtensionTotalSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.resoucesAvailability ? "Yes" : "No"}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Action",
      cell: (seatExtensionList, index) => (
        <div>
          <div className="mb-2">
            {seatExtensionList.status === true ? (
              <>
                <Button className="btn btn-sm btn-success">Finalized</Button>
              </>
            ) : (
              <>
                <Button
                  className="btn btn-sm btn-warning"
                  onClick={() => handleEdit(index)}
                >
                  Edit
                </Button>
              </>
            )}
          </div>
        </div>
      ),
      wrap: true,
      width: "150px",
    },
  ];
  const [isEditable, setIsEditable] = useState(false);
  const handleEdit = (index) => {
    const seatDetails = seatExtensionList[index];
    setShowForm(true);
    setIsEditable(true);
    setFormId(seatExtensionList[index]._id);
    const mappedRows =
      seatDetails &&
      seatDetails.subjectDetails.map((item) => ({
        sessionYear: item.sessionYear || `${currentYear}-${currentYear + 1}`,
        subjectList: item.subjectList || "",
        courseList: item.courseList || "",
        subjectTypes: item.subjectTypes || "",
        applicationNo: item.applicationNo || "",
        CurrentSeatNo: item.CurrentSeatNo || 0,
        RequestedSeatNo: item.RequestedSeatNo || 0,
        afterExtensionTotalSeatNo: item.afterExtensionTotalSeatNo || 0,
        resoucesAvailability: item.resoucesAvailability || "",
      }));
    setRows(mappedRows);
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col className="order-xl-1" xl="12">
            <Card
              className="bg-secondary shadow"
              style={{ display: showForm !== true ? "none" : "" }}
            >
              <CardHeader className="bg-white border-0">
                <Row
                  className="align-items-center"
                  style={{ textAlign: "center" }}
                >
                  <Col xs="12">
                    <h2 className="mb-0">निर्धारित प्रपत्र</h2>
                    <h3 className="mb-0">
                      शैक्षणिक सत्र - {currentYear}-{currentYear + 1}
                    </h3>
                    <h5 className="mb-0">
                      शासकीय महाविद्यालय में संचालित कक्षा/संकाय/पाठ्यक्रम में
                      सीट वृद्धि हेतु ऑनलाइन आवेदन पत्र
                    </h5>
                    <h1>
                      {seatStatus
                        ? ""
                        : "❌ Seat Extension is Currently Closed"}
                    </h1>
                  </Col>
                </Row>
              </CardHeader>
              {seatStatus && seatStatus === true && (
                <CardBody>
                  <form onSubmit={handleSubmit}>
                    <Table bordered responsive>
                      <thead>
                        <tr>
                          <th>संकाय का नाम</th>
                          <th>विषय</th>
                          <th>संचालन का प्रकार</th>
                          <th>आवेदनों की संख्या</th>
                          <th>वर्तमान सीट</th>
                          <th>मांग की गई सीट</th>
                          <th>वृद्धि पश्चात् सीट</th>
                          <th>भौतिक संसाधन</th>
                          <th>Actions</th>
                        </tr>
                      </thead>
                      <tbody>
                        {rows.map((row, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="courseList"
                                value={row.courseList}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              >
                                <option value="">-- चूने --</option>
                                {courseData &&
                                  courseData.length > 0 &&
                                  courseData.map((a) => (
                                    <option key={a._id} value={a._id}>
                                      {a.courseName}
                                    </option>
                                  ))}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="subjectList"
                                value={row.subjectList}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              >
                                <option value="">-- चूने --</option>
                                {subjectData &&
                                  subjectData.length > 0 &&
                                  subjectData.map((a) => (
                                    <option key={a._id} value={a._id}>
                                      {a.subjectName}
                                    </option>
                                  ))}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="subjectTypes"
                                value={row.subjectTypes}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              >
                                <option value="">-- प्रकार --</option>
                                <option value="Government">शासन स्तर</option>
                                <option value="SelfFinancing">
                                  स्ववित्तीय मद
                                </option>
                                <option value="PublicParticipation">
                                  जनभागीदारी मद
                                </option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="applicationNo"
                                value={row.applicationNo}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="CurrentSeatNo"
                                value={row.CurrentSeatNo}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="RequestedSeatNo"
                                value={row.RequestedSeatNo}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="afterExtensionTotalSeatNo"
                                value={row.afterExtensionTotalSeatNo}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              />
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="resoucesAvailability"
                                value={row.resoucesAvailability}
                                onChange={(e) => handleInputChange(index, e)}
                                style={{
                                  width: "100px",
                                  fontSize: "12px",
                                  padding: "5px",
                                }}
                              >
                                <option value="">-- चयन करें --</option>
                                <option value="true">हां</option>
                                <option value="false">नहीं</option>
                              </Input>
                            </td>
                            <td>
                              <span
                                className="btn btn-danger btn-sm fa fa-trash"
                                onClick={() => removeRow(index)}
                              ></span>
                            </td>
                          </tr>
                        ))}
                        <tr>
                          <td colSpan="10" className="text-center">
                            <Button color="primary" onClick={addRow}>
                              Add Row
                            </Button>
                          </td>
                        </tr>
                      </tbody>
                    </Table>
                    <Button color="success" type="submit">
                      {isEditable !== true ? "Submit" : "Update"}
                    </Button>
                  </form>
                  <br></br>
                </CardBody>
              )}
            </Card>
            <Card
              className="shadow"
              style={{ display: showForm !== false ? "none" : "" }}
            >
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <Col xs="11">
                  <h3 className="mb-0">Seat Extension List </h3>
                  <small>
                    <strong
                      className={seatStatus ? "text-success" : "text-danger"}
                    >
                      {seatStatus
                        ? ""
                        : "❌ Seat Extension is Currently Closed"}
                    </strong>
                  </small>
                </Col>

                {/* <Col className="text-right" xs="4">
                  <Button
                    color="primary"
                    href="#"
                    onClick={exportToExcel}
                    size="sm"
                  >
                    Generate Verified Report
                  </Button>
                </Col> */}
                {/* <FormControl
                  type="text"
                  placeholder="Search Institute..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                /> */}
              </CardHeader>
              <CardBody>
                <DataTable
                  columns={columns}
                  data={seatExtensionList}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  defaultSortField="name" // Sort by the 'name' column initially
                  defaultSortAsc={true} // Ascending order
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa", // Light background color for header
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff", // Row color
                        borderBottom: "1px solid #ddd",
                      },
                      // Apply hover effect through the :hover pseudo-class directly in custom CSS
                      onHoverStyle: {
                        backgroundColor: "#ffff99", // Hover color
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default SubjectEntryForSeat;
