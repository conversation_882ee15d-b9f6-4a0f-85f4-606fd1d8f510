/* Marquee Wrapper */
.marquee {
    width: 100%;
    overflow: hidden;
    position: relative;
    background: linear-gradient(90deg, #0033cc, #0099ff, #33ccff); /* Smooth gradient */
    padding: 10px 0;
    white-space: nowrap;
    text-align: center;
  }
  
  /* Marquee Content */
  .marquee-content {
    display: flex;
    align-items: center;
    white-space: nowrap;
    animation: marquee-scroll 45s linear infinite;
  }
  .marquee-content:hover {
  animation-play-state: paused;
  cursor: pointer;
}
  /* News Item */
  .news-item {
    display: inline-block;
    padding: 0 15px;
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    font-family: "Arial", sans-serif;
    color: #ffffff; /* White Text for Better Visibility */
  }
  
  /* Image Styling (Fixing Blurriness) */
  .news-item img {
    height: 30px;
    width: auto;
    image-rendering: crisp-edges;
    object-fit: contain;
  }
  
  /* Marquee Running Animation */
  @keyframes marquee-scroll {
    from { transform: translateX(100%); }
    to { transform: translateX(-100%); }
  }
  