/* Wrapper for the table with top scrollbar */
.table-scroll-top-wrapper {
    position: relative;
  }
  
  .table-scroll-top-wrapper .scrollbar {
    overflow-x: auto;
    overflow-y: hidden;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 10px;
  }
  
  .table-scroll-top-wrapper .scrollbar::-webkit-scrollbar {
    height: 8px;
  }
  
  .table-scroll-top-wrapper .scrollbar::-webkit-scrollbar-thumb {
    background-color: #d1d5db; /* Gray */
    border-radius: 4px;
  }
  
  .table-scroll-top-wrapper .scrollbar::-webkit-scrollbar-track {
    background-color: #f3f4f6; /* Light gray */
  }
  
  /* Hide native scrollbar for the actual table container */
  .table-scroll-container {
    overflow-x: auto;
    overflow-y: hidden;
  }
  
  .table-scroll-container::-webkit-scrollbar {
    display: none;
  }
  
  /* Ensure the table is fully scrollable */
  .table-scroll-container > table {
    min-width: 100%;
    border-collapse: collapse;
  }
  
  .table-filters {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 0.5em;
	
	a {
		color: #222;
		font-size: 16px;
		font-weight: 500;
		margin-right: 1em;
		display: inline-block;		
		&:hover {
			text-decoration: none;
		}
	}
	
	select {
		background: #fff;
		font-family: 'Be Vietnam', sans-serif;
		font-size: 16px;
		font-weight: 500;
		width: 8em;
		height: 1.5em;
	}
}