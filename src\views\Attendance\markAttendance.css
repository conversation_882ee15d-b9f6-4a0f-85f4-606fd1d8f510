.camera-popup {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.camera-circle {
  width: 300px;
  height: 300px;
  border-radius: 50%;
  overflow: hidden;
  position: relative;
  border: 5px solid #ccc;
  display: flex;
  justify-content: center;
  align-items: center;
}

.camera-circle.green {
  border-color: green;
}

.camera-circle.red {
  border-color: red;
}

.status {
  margin-top: 20px;
  font-size: 16px;
}

.status.error {
  color: red;
}

.blink-count {
  margin-top: 10px;
  font-size: 14px;
  color: #555;
}
