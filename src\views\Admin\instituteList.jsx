import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormControl,
  Col,
  Button,
  Row,
} from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import { Link, useNavigate, useParams } from "react-router-dom";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { Label } from "reactstrap";
const InstituteList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [division, setDivision] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [college, setCollege] = useState([]);

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
         
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
           
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
  }, [endPoint, token]);


  const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
  const [nameFilter, setNameFilter] = useState("");
  const [selectedDivision, setSelectedDivision] = useState("");
 
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {

          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setDivision(response.data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision();
  }, [endPoint, token]);

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };
  const handleClearFilters = () => {
    setIsVerifiedFilter("");
    setNameFilter("");
    setSelectedDivision("");
    setFilterText("");

    // Optionally refresh data
    // fetchFilteredData({ isVerified: "", name: "", division: "", text: "" });
  };

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const params = {};

        if (nameFilter.trim() !== "") params.districtName = nameFilter.trim();

        if (isVerifiedFilter === "true" || isVerifiedFilter === "false" || isVerifiedFilter !== "") {
          params.status = isVerifiedFilter === "true" ? true : isVerifiedFilter === "false" ? false : undefined;
        }
       if (selectedDivision.trim() !== "") params.divison = selectedDivision.trim();
        const response = await axios.get(
          `${endPoint}/api/get-college-new`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
            params,
          }
        );
        if (response.status === 200) {
          setCollege(response.data);

        } else {
          setCollege([]);
          // SwalMessageAlert("Failed to fetch College data. Please try again." ,"error");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
  }, [endPoint, token, isVerifiedFilter, nameFilter, selectedDivision]);



  const columns = [
    {
      name: "Action",
      cell: (row) => (
        <>
          <a href={`/admin/update-institute/${row._id}`}>
            <button className="btn btn-warning btn-sm" title="Edit Details">
              Edit
            </button>
          </a>
          &nbsp; &nbsp;
          <button
            className="btn btn-primary btn-sm"
            title="Change Password"
            onClick={() => changePassword(row._id)}
          >
            <span className="fa fa-lock"></span>
          </button>
          {row.status === true ? (
            <span className="btn btn-success btn-sm">Verified</span>
          ) : (
            <span className="btn btn-danger btn-sm">Not Verified</span>
          )}

          <Link to={`/admin/college-print/${row._id}`}>
            <Button
              className="btn btn-sm mr-2"
              style={{ backgroundColor: "orange" }}
            >
              <i className="fas fa-print"></i>
            </Button>
          </Link>


        </>
      ),
    },
    {
      name: "Basic Details",
      cell: (row) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {row.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {row.collegeEmail}
          </div>
          <div className="mb-2">
            <strong>AISHE Code: </strong>
            <strong className="badge-primary">{row.aisheCode}</strong>
          </div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Contact Details",
      cell: (row) => (
        <div>
          <div className="mb-2">
            <strong>Contact Person: </strong>
            {row.contactPerson}
          </div>
          <div className="mb-2">
            <strong>Mobile: </strong>
            {row.contactNumber}
          </div>
          <div className="mb-2">
            <strong>Institute Type: </strong>
            {row.collegeType === "1" ? "GOVERNMENT" : ""}
          </div>
        </div>
      ),
      wrap: true,
    },
    // { name: "University", selector: (row) => row.universityName },
    {
      name: "Division/District",
      selector: (row) =>
        `${getDivisionName(row.divison)} / ${row.districtName}`,
    },
  ];

  // Function to sort data alphabetically by name
  const sortedData = college.sort((a, b) => {
    const nameA = a.districtName.toLowerCase();
    const nameB = b.districtName.toLowerCase();
    if (nameA < nameB) return -1; // a comes before b
    if (nameA > nameB) return 1;  // a comes after b
    return 0; // a and b are equal
  });


  const changePassword = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/change-password?id=${id}&key=College`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        copyToClipboard(response.data.password);
        SwalMessageAlert("Password Change Successfully", "success");
      } else {
        alert("Failed to fetch College data. Please try again.");
        SwalMessageAlert("Password Change Failed", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  function copyTextFallback(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      // console.log("Text copied using fallback!");
    } catch (err) {
      console.error("Fallback copy failed:", err);
    }
    document.body.removeChild(textArea);
  }
  const copyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => console.log("Text copied to clipboard!"))
        .catch((err) => console.error("Failed to copy text:", err));
    } else {
      copyTextFallback(text);
    }
  };



  const [district, setDistrict] = useState([]);
  useEffect(() => {
    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 2.");
      }
    };



    getDistrict();

  }, [endPoint, token]);


  const filteredData = college.filter((item) => {
    const filterTextLower = filterText.toLowerCase();

    // Check for "Verified" and "Not Verified"
    if (filterTextLower === "verified") {
      return item.status === true; // Only include verified employees
    } else if (filterTextLower === "not verified") {
      return item.status === false; // Only include not verified employees
    } else if (filterTextLower === "not") {
      return item.status === false; // Only include not verified employees
    }
    // Default filtering for name, empCode, and contact
    return (
      (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
      (item.empCode && item.empCode.toLowerCase().includes(filterTextLower)) ||
      (item.contact &&
        item.contact.toString().toLowerCase().includes(filterTextLower)) ||
      (item.aisheCode && item.aisheCode.toLowerCase().includes(filterTextLower)) ||
      (item.collegeEmail && item.collegeEmail.toLowerCase().includes(filterTextLower)) ||
      (item.contactPerson && item.contactPerson.toLowerCase().includes(filterTextLower)) ||
      (item.districtName && item.districtName.toLowerCase().includes(filterTextLower)) ||
      (item.contactNumber && item.contactNumber.toString().toLowerCase().includes(filterTextLower)) ||
      ((item.collegeType === "1" ? "government" : "private").includes(filterTextLower))
    );
  });

  const [verifiedData, setVerifiedData] = useState([]);

  const exportToExcel = async (e) => {
    e.preventDefault();
    await axios
      .get(`${endPoint}/api/college/get-all-verified-college`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      })
      .then((response) => {
        const fetchedData = response.data.collegeData; // Use response data directly
        setVerifiedData(fetchedData); // Update the state for future reference

        if (fetchedData.length > 0) {
          let tableHTML = `
          <table>
            <thead>
              <tr>
                <th>Division</th>
                <th>District</th>
                <th>College Name</th>
                <th>Principal Name</th>
                <th>Principal Mobile No.</th>
                <th>AISHE Code</th>
                <th>Email</th>
              </tr>
            </thead>
            <tbody>
        `;
          fetchedData.sort((a, b) => {
            // Ensure case-insensitive comparison for Division
            const divisionA = a.Division.toLowerCase();
            const divisionB = b.Division.toLowerCase();
            if (divisionA < divisionB) return -1; // a comes before b
            if (divisionA > divisionB) return 1;  // a comes after b
            return 0; // a and b are equal
          }).forEach((a) => {
            tableHTML += "<tr>";
            tableHTML += `<td>${a.Division}</td>`;
            tableHTML += `<td>${a.District}</td>`;
            tableHTML += `<td>${a["College Name"]}</td>`;
            tableHTML += `<td>${a["Principal Name"]}</td>`;
            tableHTML += `<td>${a["Principal Mobile No."]}</td>`;
            tableHTML += `<td>${a["AISHE Code"]}</td>`;
            tableHTML += `<td>${a.Email}</td>`;
            tableHTML += "</tr>";
          });
          tableHTML += "</tbody></table>";

          const excelFileContent = `
          <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
            <head><!--[if gte mso 9]><xml>
              <x:ExcelWorkbook>
                <x:ExcelWorksheets>
                  <x:ExcelWorksheet>
                    <x:Name>Sheet1</x:Name>
                    <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                  </x:ExcelWorksheet>
                </x:ExcelWorksheets>
              </x:ExcelWorkbook>
            </xml><![endif]-->
            </head>
            <body>${tableHTML}</body>
          </html>
        `;
          const blob = new Blob([excelFileContent], {
            type: "application/vnd.ms-excel;charset=utf-8;",
          });
          const date = new Date().toLocaleDateString();
          const downloadLink = document.createElement("a");
          downloadLink.href = URL.createObjectURL(blob);
          downloadLink.download = `Verified_College_Report_${date}.xls`;
          downloadLink.click();
        } else {
          SwalMessageAlert("No data available for export.", "warning");
        }
      })
      .catch((error) => {
        SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
      });
  };
  const exportAllToExcel = () => {
    try {
      const dataToExport = filteredData.map((details, index) => ({
        "S.No": index + 1,
        "Division": division.find((d) => d.divisionCode === details.divison)?.name || "N/A",
        "District": details.districtName || "N/A",
        "College Name": details.name,
        "Principal Name": details.contactPerson,
        "Principal Mobile No.": details.contactNumber || "N/A",
        "AISHE Code": details.aisheCode,
        "Email": details.collegeEmail,
        "Verified": details.status ? "Yes" : "No"
      }));

      const worksheet = XLSX.utils.json_to_sheet(dataToExport);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Employees");

      const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
      const blob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      });

      saveAs(blob, "Institute_Report.xlsx");
    } catch (error) {
      console.error("Excel export failed:", error);
      // alert("Failed to export data. See console for details.");
    }
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="border-0 d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">Institute List</h3>


            </Col>

            <Col className="text-right" xs="4">
              <Button
                color="primary"
                href="#"
                onClick={exportToExcel}
                size="sm"
              >
                Generate Verified Report
              </Button>
              <Button
                color="primary"
                onClick={exportAllToExcel}
                size="sm"
              >
                Export to Excel All Report
              </Button>
            </Col>
            <FormControl
              type="text"
              placeholder="Search Institute..."
              className="ml-auto"
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              style={{ width: "250px", borderRadius: "30px" }}
            />
          </CardHeader>
          <CardBody>
            <hr></hr>
            <Row>
              <Col xs="2">
                <Label>Filter by Verify</Label>
                <select value={isVerifiedFilter}
                  onChange={(e) => setIsVerifiedFilter(e.target.value)} className="form-control">
                  <option value=""><strong>All</strong></option>
                  <option value="true"><strong>Verified</strong></option>
                  <option value="false"><strong>Not Verified</strong></option>
                </select>
              </Col>
              <Col xs="2">
                <Label>Filter by District</Label>

                <select value={nameFilter} onChange={(e) => setNameFilter(e.target.value)} className="form-control">
                  <option value="">All District</option>
                  {district &&
                    district.length > 0 &&
                    district.map((type, index) => (
                      <option key={index} value={type.districtNameEng}>
                        {type.districtNameEng}
                      </option>
                    ))}
                </select>


              </Col>
              <Col xs="2">
                <Label>Filter by Division</Label>

                <select value={selectedDivision} onChange={(e) => setSelectedDivision(e.target.value)} className="form-control">
                  <option value="">Select Division</option>
                  {division &&
                    division.length > 0 &&
                    division.map((type, index) => (
                      <option key={index} value={type.divisionCode}>
                        {type.name}
                      </option>
                    ))}
                </select>
              </Col>
              <Col xs="2">
                <br></br>
                <Button onClick={handleClearFilters} className="btn btn-primary mt-1">
                  Clear Filters
                </Button>

              </Col>
            </Row>
            <hr></hr>
            <DataTable
              columns={columns}
              data={filteredData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              stripedsortable={sortedData}
              defaultSortField="name" // Sort by the 'name' column initially
              defaultSortAsc={true} // Ascending order
              customStyles={{
                header: {
                  style: {
                    backgroundColor: "#f8f9fa", // Light background color for header
                    fontWeight: "bold",
                  },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff", // Row color
                    borderBottom: "1px solid #ddd",
                  },
                  // Apply hover effect through the :hover pseudo-class directly in custom CSS
                  onHoverStyle: {
                    backgroundColor: "#ffff99", // Hover color
                  },
                },
              }}
            />
          </CardBody>
        </Card>
      </Container>
    </>
  );
};

export default InstituteList;
