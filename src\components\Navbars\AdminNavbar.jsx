import { Link, useNavigate } from "react-router-dom";
import { useEffect, useState, useRef } from "react";
// reactstrap components
import {
  DropdownMenu,
  DropdownItem,
  UncontrolledDropdown,
  DropdownToggle,
  Navbar,
  Button,
  Nav,
  Container,
  Form,
  FormGroup,
  Modal,
  ModalBody,
  ModalFooter,
  ModalHeader,
  Col,
  Media,
  Label,
  Input,
  InputGroup,
  InputGroupAddon,
  InputGroupText,
  Row,
  Card,
  CardHeader,
  CardTitle,
} from "reactstrap";
import axios from "axios";
import NavImage from "../../assets/img/theme/user-icon.png";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Sidebar from "../Sidebar/Sidebar.jsx";
import { useSidebar } from "../Sidebar/SidebarContext.jsx";
import Swal from "sweetalert2";
// import warningSound from "../../assets/Session_sound/popAlertAudio.mp3"
const AdminNavbar = () => {
  const { setIsSidebarVisible, isSidebarVisible } = useSidebar();
  const userType = sessionStorage.getItem("type");
  const type = sessionStorage.getItem("userType");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const id = sessionStorage.getItem("id");
  const navigate = useNavigate();

  //Session Timeout  
  const logoutTimer = useRef(null);
  const warningTimer = useRef(null);
  const SESSION_TIMEOUT = 15 * 60 * 1000;
  const WARNING_TIME = 14 * 60 * 1000;

  const resetTimers = () => {
    clearTimeout(logoutTimer.current);
    clearTimeout(warningTimer.current);
    warningTimer.current = setTimeout(() => {
      let timerInterval;
      Swal.fire({
        title: "Session Expire Alert!",
        html: "You will be logged out in 1 minute due to inactivity. <br>Count Down : <b></b> seconds",
        timer: 60000,
        timerProgressBar: true,
        didOpen: () => {
          Swal.showLoading();
          const timer = Swal.getPopup().querySelector("b");
          timerInterval = setInterval(() => {
            const timeLeftInMs = Swal.getTimerLeft();
            const timeLeftInSec = Math.ceil(timeLeftInMs / 1000);
            timer.textContent = `${timeLeftInSec}`;
          }, 1000); // Update every second
        },
        willClose: () => {
          clearInterval(timerInterval);
        }
      }).then((result) => {
        if (result.dismiss === Swal.DismissReason.timer) {
          console.log("I was closed by the timer");
        }
      });
    }, WARNING_TIME);
    logoutTimer.current = setTimeout(() => {
      sessionStorage.clear();
      SwalMessageAlert("Session expired.", "error");
      navigate('/');
      window.location.reload();
    }, SESSION_TIMEOUT);
  };

  useEffect(() => {
    const events = ['mousemove', 'keydown', 'mousedown', 'touchstart'];
    events.forEach(event =>
      window.addEventListener(event, resetTimers)
    );
    resetTimers();
    return () => {
      events.forEach(event =>
        window.removeEventListener(event, resetTimers)
      );
      clearTimeout(logoutTimer.current);
      clearTimeout(warningTimer.current);
    };
  }, []);

  const handleLogout = (e) => {
    e.preventDefault();
    sessionStorage.clear();
    // navigate("/");
    navigate("/");
    window.location.reload();
  };


  const goToChangePassword = () => {
    navigate("/admin/change-password");
  };
  const [formData, setFormData] = useState({
    lat: "",
    long: "",
    area: "",
  });
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  useEffect(() => {
    if (userType === "3" || userType === "2") {
      const fetchDesignation = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-single-college/${id}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setFormData({
              lat: response.data.lat,
              long: response.data.long,
              area: response.data.area,
            });
          } else {
            alert("Designation Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      };
      fetchDesignation();
    }
  }, []);
  const handleSubmit = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    try {
      const body = {
        lat: formData.lat,
        long: formData.long,
        area: formData.area,
      };
      const response = await axios.put(
        `${endPoint}/api/update-college-area/${id}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        SwalMessageAlert(data.msg, "success");
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert("Area Updation Failed", "error");
    }
  };

  const [updateFieldEmployee, setUpdateFieldEmployee] = useState(false);
  const toggleUpdateFieldModalEmployee = () =>
    setUpdateFieldEmployee(!updateFieldEmployee);

  const [employeeForm, setEmployeeForm] = useState({
    gender: "",
    title: "",
  });

  const handleInputFieldChange = (e) => {
    const { name, value } = e.target;
    setEmployeeForm({ ...employeeForm, [name]: value });
  };

  useEffect(() => {
    if (type === "Director" && userType === "5") {
      const getEmployeeData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/employee/get-employee/${id}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data;
            if (data.title === undefined) {
              setUpdateFieldEmployee(true);
            }
          } else {
            SwalMessageAlert("Failed to load Employee data", "error");
          }
        } catch (error) {
          console.error("Error fetching Employee data:", error);
          SwalMessageAlert("Failed to load Employee data.", "error");
        }
      };
      getEmployeeData();
    }
  }, [id, endPoint, token]);




  const handleEmployeeFieldUpdateSubmit = async (e) => {
    e.preventDefault();
    try {
      const body = {
        gender: employeeForm.gender,
        title: employeeForm.title,
      };
      const response = await axios.put(
        `${endPoint}/api/employee/update-field/${id}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("Employee Updated Successfully.", "success");
        toggleUpdateFieldModalEmployee(false);
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        SwalMessageAlert("Updating for Employee failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  useEffect(() => {
    const fetchIprDate = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/ipr-date/logs`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const date = response.data[0]["dateTo"];
          const dateTo = new Date(date);
          const currentDate = new Date();
          const yearDateTo = dateTo.getFullYear();
          const yearCurrent = currentDate.getFullYear();
          if (yearDateTo !== yearCurrent) {
            Swal.fire({
              title: "IPR Date Alert",
              text: "Kindly Update IPR Filling Date.",
              icon: "info",
              confirmButtonText: "Go",
            }).then((result) => {
              if (result.isConfirmed) {
                // Redirect to another page
                navigate("/admin/ipr-date-form/"); // Replace with your URL
              }
            });
          }
        } else if (response.status === 201) {
          Swal.fire({
            title: "IPR Date Alert",
            text: "Kindly Update IPR Filling Date.",
            icon: "info",
            confirmButtonText: "Go",
          }).then((result) => {
            if (result.isConfirmed) {
              // Redirect to another page
              navigate("/admin/ipr-date-form/"); // Replace with your URL
            }
          });
        }
      } catch (error) {
        if (error.response) {
          // Extract the error message from the API response
          const errorMessage =
            error.response.data?.msg ||
            "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          // Handle no response from server (network issues, server down, etc.)
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          // Handle other unexpected errors
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }
    };
    if (userType === "1") {
      fetchIprDate();
    }
  }, [endPoint, token]);

  useEffect(() => {
    const fetchACRDate = async () => {
      const isCurrentDateInRange = (startDate, endDate) => {
        const currentDate = new Date();
        return (
          currentDate >= new Date(startDate) && currentDate <= new Date(endDate)
        );
      };

      let dateRangeACR = null;

      try {
        const response = await axios.get(
          `${endPoint}/api/acr-date/logs?limit=1`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          dateRangeACR = isCurrentDateInRange(data.dateFrom, data.dateTo);
          // // console.log("Date Range ACR:", dateRangeACR);
          if (dateRangeACR === true) {
            sessionStorage.setItem("dateRangeACR", dateRangeACR);
          }
        } else {
          // console.log("No Date Range Added. Please Contact to State Admin!", "error");
        }
      } catch (error) {
        console.error("Error fetching date range:", error);
      }
    };
    fetchACRDate();
  }, [endPoint, token, userType]);

  const IprAlert = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/ipr-date/logs`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        const data = response.data[0];
        const startDate = new Date(data.dateFrom); // Convert to Date object
        const endDate = new Date(data.dateTo); // Convert to Date object
        const today = new Date();

        // Set all dates to the same time (midnight)
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(0, 0, 0, 0);
        today.setHours(0, 0, 0, 0);

        // // console.log(startDate, "Start");
        // // console.log(endDate, "End Date");
        // // console.log(today, "Today");

        const currentYear = new Date().getFullYear - 1;

        if (today.getTime() === startDate.getTime()) {
          Swal.fire(
            "IPR Filling Allowed From Today",
            `Kindly Fill Your IPR of Year ${currentYear}`,
            "info"
          );
        } else if (today.getTime() === endDate.getTime()) {
          Swal.fire(
            "Today is the Last day!",
            "Make sure to Fill Your IPR Form.",
            "warning"
          );
        } else if (today >= startDate && today <= endDate) {
          Swal.fire("Please", "Make sure to Fill Your IPR Form.", "warning");
        }
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        Swal.fire("Error", errorMessage, "error");
      } else if (error.request) {
        Swal.fire(
          "Error",
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        Swal.fire(
          "Error",
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };

  useEffect(() => {
    const fetchFiledIpr = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/ipr/get-employee-wise/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          if (data.iprDraft === false) {
            if (userType === "4") {
              IprAlert();
            }
          }
        } else if (response.status === 201) {
          if (userType === "4") {
            IprAlert();
          }
        }
      } catch (error) {
        if (error.response) {
          const errorMessage =
            error.response.data?.msg ||
            "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }
    };
    fetchFiledIpr();
  }, [token, endPoint, id]);

  const [chatMessageData, setChatMessageData] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const toggleIsOpen = () => setIsOpen(!isOpen);
  useEffect(() => {
    const handleCheckIncomingMessage = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/error-log/chat-message-status/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          if (response.data.length > 0) {
            setChatMessageData(response.data);
            setIsOpen(true);
          }
        }
      } catch (error) {
        if (error.response) {
          const errorMessage =
            error.response.data?.msg ||
            "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }
    };
    handleCheckIncomingMessage();
  }, [endPoint, token]);

  const handleMarkAsRead = async (ids) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/error-log/masrk-as-read/${ids}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Read Marked Successfully", "success");
      } else {
        SwalMessageAlert("Read Mark Failed", "error");
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };
  return (
    <>
      {" "}
      <Modal isOpen={isOpen} toggle={toggleIsOpen}>
        <ModalHeader toggle={toggleIsOpen}>
          <h2>Message Received</h2>
        </ModalHeader>
        <ModalBody>
          {chatMessageData &&
            chatMessageData.map((ticket) => (
              <>
                <Card style={{ backgroundColor: "cyan" }}>
                  <div key={ticket._id}>
                    <h3>
                      Ticket No. :- {ticket.ticketNo}{" "}
                      <span
                        className="btn btn-sm btn-warning"
                        onClick={() => handleMarkAsRead(ticket._id)}
                      >
                        Mark As Read
                      </span>
                    </h3>
                    <div>
                      {ticket.messages.map((msg, index) => (
                        <p key={index}>
                          Message{index + 1} : {msg.message}
                        </p>
                      ))}
                    </div>
                  </div>
                </Card>
                <br />
              </>
            ))}
        </ModalBody>
      </Modal>
      <Modal
        isOpen={updateFieldEmployee}
        toggle={toggleUpdateFieldModalEmployee}
      >
        <ModalHeader toggle={toggleUpdateFieldModalEmployee}>
          Update Information
        </ModalHeader>
        <ModalBody>
          <Form >
            <Row>
              <Col lg="6" md="12">
                <FormGroup>
                  <Label for="title" className="form-control-label">
                    Title
                  </Label>
                  <InputGroup className="input-group-alternative mb-3">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i className="fas fa-user"></i>
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      id="title"
                      name="title"
                      type="select" // Change type to "select"
                      value={employeeForm.title}
                      onChange={handleInputFieldChange}
                    >
                      <option value="">-- Select Title --</option>
                      <option value="Mr.">Mr.</option>
                      <option value="Mrs.">Mrs.</option>
                      <option value="Ms.">Ms.</option>
                      <option value="Miss">Miss</option>
                      <option value="Dr.">Dr.</option>
                      <option value="Prof.">Prof.</option>
                      <option value="Hon.">Hon.</option>
                    </Input>
                  </InputGroup>
                </FormGroup>
              </Col>

              <Col lg="6" md="12">
                <FormGroup>
                  <Label for="gender" className="form-control-label">
                    Gender
                  </Label>
                  <InputGroup className="input-group-alternative mb-3">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i
                          className="fas fa-male"
                          style={{ marginRight: "5px" }}
                        ></i>
                        <i className="fas fa-female"></i>
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      id="gender"
                      name="gender"
                      type="select" // Change type to "select"
                      value={employeeForm.gender}
                      onChange={handleInputFieldChange}
                    >
                      <option value="">-- Select Gender --</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </Input>
                  </InputGroup>
                </FormGroup>
              </Col>
            </Row>
            <ModalFooter>
              <Button color="primary" type="submit">
                Submit
              </Button>
              <Button
                color="secondary"
                onClick={toggleUpdateFieldModalEmployee}
              >
                Cancel
              </Button>
            </ModalFooter>
          </Form>
        </ModalBody>
      </Modal>
      <Navbar
        className="navbar-top navbar-dark"
        expand="md"
        id="navbar-main"
        style={{
          backgroundColor: "deepskyblue", // Example color
          padding: "10px", // Example padding
          boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          position: "sticky",
        }}
      >
        <Container fluid>
          <Row style={{ width: "100%" }}>
            <Col sm="1">
              <Button
                className="btn-sm"
                title="Menu"
                style={{
                  marginTop: "9px",
                  backgroundColor: "white",
                  paddingBottom: "-30px", // Remove margin
                  border: "none", // Optional: remove border
                  borderRadius: "5px", // Optional: add slight rounding
                }}
                onClick={() => setIsSidebarVisible((prev) => !prev)}
              >
                <i
                  style={{
                    paddingTop: "2px",
                    paddingRight: "4px",
                    paddingLeft: "4px",
                    color: "black",
                    fontSize: "19px", // Keep the icon size large
                  }}
                  className="fas fa-bars"
                ></i>
              </Button>
            </Col>
            <Col sm="5" className="pt-1">
              <Link
                className="h4 mb-1  text-white text-uppercase d-none d-lg-inline-block"
                style={{ textDecoration: "none" }}
                to="/"
              >
                <span style={{ fontSize: "25px", fontWeight: "bold" }}>
                  {" "}
                  DEPARTMENT OF HIGHER EDUCATION
                </span>
              </Link>
            </Col>
            <Col sm="4">
              <Row>
                {userType === "3" || userType === "2" ? (
                  <>
                    <input
                      name="lat"
                      placeholder="Latitude"
                      type="text"
                      value={formData.lat}
                      autoComplete="off"
                      style={{
                        height: "20px",
                        width: "70px",
                        marginTop: "10px",
                      }}
                      onChange={handleInputChange}
                    />
                    <input
                      name="long"
                      placeholder="Longitude"
                      type="text"
                      value={formData.long}
                      autoComplete="off"
                      style={{
                        height: "20px",
                        width: "70px",
                        marginTop: "10px",
                        marginLeft: "5px",
                      }}
                      onChange={handleInputChange}
                    />
                    <input
                      name="area"
                      placeholder="Area in Meter"
                      type="text"
                      value={formData.area}
                      autoComplete="off"
                      style={{
                        height: "20px",
                        width: "70px",
                        marginTop: "10px",
                        marginLeft: "10px",
                      }}
                      onChange={handleInputChange}
                    />
                    <div className="text-center">
                      <button
                        className="btn btn-sm btn-primary"
                        color="primary"
                        type="button"
                        style={{
                          marginTop: "6px",
                          marginLeft: "10px",
                          height: "30px",
                          borderRadius: "30px",
                        }}
                        onClick={handleSubmit}
                      >
                        Submit
                      </button>
                    </div>
                  </>
                ) : (
                  ""
                )}
                {userType === "3" || userType === "2" || userType === "4" ? (
                  <div className="text-center">
                    <a
                      href="https://drive.google.com/drive/folders/1S_OUN-7gloV7bhYcbx6qjA-XlQajQ_jT"
                      target="_blank"
                      className="btn btn-sm btn-warning"
                      color="primary"
                      type="button"
                      style={{
                        marginTop: "6px",
                        marginLeft: "20px",
                        height: "30px",
                        borderRadius: "30px",
                      }}
                    >
                      Download Apk
                    </a>
                  </div>
                ) : (
                  ""
                )}
              </Row>
            </Col>

            <Col sm="2">
              <Nav className="align-items-center d-none d-md-flex" navbar>
                <div
                  style={{
                    display: "flex",
                    justifyContent: "flex-end",
                    alignItems: "center",
                  }}
                >
                  {/* Media Section */}
                  <UncontrolledDropdown className="pr-3" nav>
                    <DropdownToggle className="pr-0" title="DropDown" nav>
                      <i
                        className="ni ni-bold-down"
                        style={{ fontSize: "1.2rem" }}
                      />
                    </DropdownToggle>

                    <DropdownMenu
                      right
                      style={{ marginRight: "10px", marginTop: "45px" }}
                    >
                      {" "}
                      {/* Aligns Dropdown to the right */}
                      <DropdownItem className="noti-title" header tag="div">
                        <h6
                          className="text-overflow m-0"
                          style={{ color: "black" }}
                        >
                          Welcome!
                        </h6>
                      </DropdownItem>
                      {userType === "4" && (
                        <DropdownItem to="/admin/userProfile" tag={Link}>
                          <i className="ni ni-single-02" />
                          <span>My Profile</span>
                        </DropdownItem>
                      )}
                       {userType === "3" && (
                        <DropdownItem to="/admin/update/institute-profile" tag={Link}>
                          <i className="ni ni-single-02" />
                          <span>My Profile</span>
                        </DropdownItem>
                      )}
                      {userType === "3" && (
                        <DropdownItem to="/admin/profile-institute" tag={Link}>
                          <i className="ni ni-single-02" />
                          <span>College Profile</span>
                        </DropdownItem>
                      )}                    
                      <DropdownItem onClick={goToChangePassword}>
                        <i className="ni ni-lock-circle-open" />
                        <span>Change Password</span>
                      </DropdownItem>
                      <DropdownItem to="/admin/settings" tag={Link}>
                        <i className="ni ni-settings-gear-65" />
                        <span>Settings</span>
                      </DropdownItem>
                      <DropdownItem to="/admin/activity" tag={Link}>
                        <i className="ni ni-calendar-grid-58" />
                        <span>Activity</span>
                      </DropdownItem>
                      <DropdownItem to="/admin/support" tag={Link}>
                        <i className="ni ni-support-16" />
                        <span>Support</span>
                      </DropdownItem>
                      <DropdownItem divider />
                      <DropdownItem onClick={handleLogout}>
                        <i className="ni ni-user-run" />
                        <span>Logout</span>
                      </DropdownItem>
                    </DropdownMenu>
                  </UncontrolledDropdown>
                  <Media className="align-items-center mr-3">
                    <span className="avatar avatar-sm rounded-circle">
                      <img
                        alt="User Avatar"
                        src={NavImage}
                        onError={(e) => (e.target.src = "default-avatar.png")} // Fallback image
                      />
                    </span>
                    <Media className="ml-2 d-none d-lg-block">
                      <span className="mb-0 text-sm font-weight-bold">
                        {sessionStorage.getItem("name") || "Guest"}{" "}
                        {/* Fallback for null */}
                      </span>
                    </Media>
                  </Media>

                  {/* Dropdown Section */}
                </div>
              </Nav>
            </Col>
          </Row>
        </Container>
      </Navbar>
    </>
  );
};

export default AdminNavbar;
