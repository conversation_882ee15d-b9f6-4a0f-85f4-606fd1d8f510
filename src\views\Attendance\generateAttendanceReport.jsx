
// #By RX200
import { useState, useEffect } from "react";
import axios from "axios";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Table,
  Container,
  Row,
  Col,
  Label,
  Button,
  Modal,
  ModalHeader,
  ModalBody,
} from "reactstrap";
import loadable from "@loadable/component";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import { FormControl } from "react-bootstrap";
const Header = loadable(() => import("../../components/Headers/Header.jsx"));
import DataTable from "react-data-table-component";
import NavImage from "../../assets/img/theme/user-icon.png";
const GenerateAttendanceReport = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");

  const todate = new Date().toISOString().split("T")[0]; // "YYYY-MM-DD"

  const [empId, setEmpId] = useState("");
  const [name, setName] = useState("");
  const [empCode, setEmpCode] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState(todate);
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [attendanceData, setAttendanceData] = useState([]);
  const [attendanceData2, setAttendanceData2] = useState([]);

  const [headers, setHeaders] = useState([]);
  const [attendanceModal, setAttendanceModal] = useState(false);
  const toggleAttendanceModal = () => setAttendanceModal(!attendanceModal);
  const [attendanceEmployeeData, setAttendanceEmployeeData] = useState([]);
  const [employeeProfile, setEmployeeProfile] = useState([]);
  const [filterTextModal, setFilterTextModal] = useState("");
  const [filter, setFilter] = useState("");
  const [base64Image, setBase64Image] = useState("");

 


  const viewEmployeeWise = async (empId, empCode) => {
    console.log(empId, "Getting EmpID");
    console.log(empCode, "Getting EmpCode");

    try {
      const response = await axios.get(
        `${endPoint}/api/employee/get-single-employee?empCode=${empCode}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const data = response.data;

        // Set profile and image
        if (data[0]) {
          handleConvertAndDisplay(data[0].encodedImage);
          setEmployeeProfile(data[0]);
        }

        // Get attendance records for that employee
        const employeeAttendanceData = attendanceData.filter(
          (rec) => rec.empId === empId
        );
        console.log(employeeAttendanceData, "ifidfoidf");
        // Set state only if we found matching attendance
        if (employeeAttendanceData.length > 0) {
          setEmpId(empId);
          setAttendanceEmployeeData(employeeAttendanceData);
        } else {
          setAttendanceEmployeeData([]); // fallback if no records
        }

        setAttendanceModal(true);
      } else {
        SwalMessageAlert("Employee Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };

  function handleConvertAndDisplay(base64) {
    if (base64 === undefined) {
      setBase64Image(NavImage);
    } else {
      const sampleBase64Image = `data:image/png;base64,${base64}`;
      setBase64Image(sampleBase64Image);
    }
  }
  const handleSubmit = async (e) => {
    e.preventDefault();
    setAttendanceData([]);
    try {
      const queryParams = {};
      if (name.trim()) queryParams.name = name.trim();
      if (empCode.trim()) queryParams.empCode = empCode.trim();
      if (fromDate.trim()) queryParams.fromDate = fromDate.trim();
      if (toDate.trim()) queryParams.toDate = toDate.trim();

      const response = await axios.get(
        `${endPoint}/api/attendance/generate-college-wise-filter/${id}`,
        {
          params: queryParams,
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data?.msg) {
        SwalMessageAlert(response.data.msg, "info");
        return;
      }

      console.log(response.data, "");

      setAttendanceData(response.data);
      setHeaders(getTableHeaders());
    } catch (err) {
      SwalMessageAlert(
        err.response?.data?.error || "Failed to fetch attendance.",
        "error"
      );
    }
  };

  const handleDateSubmit = async (e) => {

    e.preventDefault();

    try {
      if (filter === "") {
        SwalMessageAlert("Please Select Filter", "error");
      }
      const queryParams = {
        filter,
        fromDate: filter === "dateRange" ? fromDate : undefined,
        toDate: filter === "dateRange" ? toDate : undefined,
      };

      const response = await axios.get(
        `${endPoint}/api/generate-attendance/employee-wise-filter/${empId}`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        console.log(data, "handle date submit");
        setAttendanceEmployeeData(data);
      } else {
        SwalMessageAlert("Attendance Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };
  const columnsModal = [
    {
      name: "LogIN Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.loginTime
            ? new Date(attendanceEmployeeData.loginTime).toLocaleString()
            : "No LogIn Time"}
        </>
      ),
    },
    {
      name: "LogOUT Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.logoutTime ? (
            new Date(attendanceEmployeeData.logoutTime).toLocaleString()
          ) : (
            <span style={{ color: "red" }}>No Logout Time</span>
          )}
        </>
      ),
    },
    {
      name: "Stay Duration",
      selector: (attendanceEmployeeData) => (
        <>
          {calculateStayDuration(
            attendanceEmployeeData.loginTime,
            attendanceEmployeeData.logoutTime
          )}
        </>
      ),
    },
  ];
  const filteredDataModal = Array.isArray(attendanceEmployeeData)
    ? attendanceEmployeeData.filter((item) => {
      const filterTextLowerModal = filterTextModal
        ? filterTextModal.toLowerCase()
        : "";
      return (
        (item.name &&
          typeof item.name === "string" &&
          item.name.toLowerCase().includes(filterTextLowerModal)) ||
        (item.empCode &&
          typeof item.empCode === "string" &&
          item.empCode.toLowerCase().includes(filterTextLowerModal))
      );
    })
    : [];
  const getTableHeaders = () => {
    const headers = [];
    if (fromDate && toDate) {
      const start = new Date(fromDate);
      const end = new Date(toDate);
      while (start <= end) {
        headers.push(formatDate(start));
        start.setDate(start.getDate() + 1);
      }
    }
    return headers;
  };

  const formatDate = (dateInput) => {
    const date = new Date(dateInput);
    const year = date.getFullYear();
    const month = `${date.getMonth() + 1}`.padStart(2, "0");
    const day = `${date.getDate()}`.padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const formatTime = (isoString) =>
    isoString
      ? new Date(isoString).toLocaleTimeString("en-US", {
        hour: "2-digit",
        minute: "2-digit",
        second: "2-digit",
        hour12: true,
      })
      : "N/A";

  const calculateStayDuration = (loginTime, logoutTime) => {
    if (!loginTime || !logoutTime) return "N/A";
    const duration = new Date(logoutTime) - new Date(loginTime);
    if (duration <= 0) return "Invalid times";

    const hrs = Math.floor(duration / 3600000);
    const mins = Math.floor((duration % 3600000) / 60000);
    const secs = Math.floor((duration % 60000) / 1000);
    return `${hrs}h ${mins}m ${secs}s`;
  };


  const [groupedJson, setGroupedJson] = useState("");

  useEffect(() => {
    const groupedData = {};

    Array.isArray(attendanceData) && attendanceData.forEach((rec) => {
      const empId = rec.empId;
      const dateKey = formatDate(rec.loginTime);

      if (!groupedData[empId]) {
        groupedData[empId] = {
          empCode: rec.empCode,
          name: rec.employeeDetails?.name || rec.name,
          designation: rec.designationDetails?.designation || "N/A",
          college: rec.collegeDetails?.name || "N/A",
          aisheCode: rec.collegeDetails?.aisheCode || "N/A",
          records: {},
        };
      }

      groupedData[empId].records[dateKey] = rec;
    });

    setGroupedJson(JSON.stringify(groupedData));
  }, [attendanceData]);

  const exportToExcel = () => {
    if (!attendanceData.length || !headers.length) {
      SwalMessageAlert("No data available to export.", "info");
      return;
    }

    let tableHTML = `
      <table border="1">
        <thead>
          <tr>
            <th>Employee Details</th>
            ${headers.map((h) => `<th>${h}</th>`).join("")}
          </tr>
        </thead>
        <tbody>
    `;

    const groupedData = {};

    attendanceData && attendanceData.forEach((rec) => {
      const empId = rec.empId;
      const dateKey = formatDate(rec.loginTime);

      if (!groupedData[empId]) {
        groupedData[empId] = {
          empCode: rec.empCode,
          name: rec.employeeDetails?.name || rec.name,
          designation: rec.designationDetails?.designation || "N/A",
          college: rec.collegeDetails?.name || "N/A",
          aisheCode: rec.collegeDetails?.aisheCode || "N/A",
          records: {},
        };
      }

      groupedData[empId].records[dateKey] = rec;
    });

    for (const emp of Object.values(groupedData)) {
      tableHTML += `<tr>
        <td>
          Name: ${emp.name}<br/>
          Emp Code: ${emp.empCode}<br/>
          Designation: ${emp.designation}<br/>
          College: ${emp.college}<br/>
          AISHE Code: ${emp.aisheCode}
        </td>
      `;

      headers.forEach((date) => {
        const rec = emp.records[date];
        tableHTML += rec
          ? `<td>
              Login: ${formatTime(rec.loginTime)}<br/>
              Logout: ${formatTime(rec.logoutTime)}<br/>
              Stay: ${calculateStayDuration(rec.loginTime, rec.logoutTime)}
            </td>`
          : `<td style="color:red;">No Attendance</td>`;
      });

      tableHTML += "</tr>";
    }

    tableHTML += "</tbody></table>";

    const blob = new Blob(
      [
        `<html xmlns:x="urn:schemas-microsoft-com:office:excel">
          <head><meta charset="UTF-8"></head>
          <body>${tableHTML}</body>
        </html>`,
      ],
      { type: "application/vnd.ms-excel" }
    );

    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = "AttendanceData.xls";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <h3 className="mb-0">Attendance Filter</h3>
              </CardHeader>
              <CardBody>
                <Form onSubmit={handleSubmit}>
                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label>Name</Label>
                        <Input
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          placeholder="Enter Name"
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>Employee Code</Label>
                        <Input
                          type="text"
                          value={empCode}
                          onChange={(e) => setEmpCode(e.target.value)}
                          placeholder="Enter Employee Code"
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>From Date</Label>
                        <Input
                          type="date"
                          max={new Date().toISOString().split("T")[0]}
                          value={fromDate}
                          onChange={(e) => setFromDate(e.target.value)}
                          
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label>To Date</Label>
                        <Input
                          type="date"
                          max={new Date().toISOString().split("T")[0]}
                          value={toDate}
                          onChange={(e) => setToDate(e.target.value)}
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="4" className="d-flex align-items-end">
                      <Button color="primary" disabled={fromDate === "" || toDate === ""} type="submit">
                        Get Attendance
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0 d-flex justify-content-between">
                <h3 className="mb-0">Attendance List</h3>
                <Button
                  color="primary"
                  size="sm"
                  onClick={exportToExcel}
                  disabled={!attendanceData.length}
                >
                  Export to Excel
                </Button>
              </CardHeader>
              <CardBody>
                {attendanceData.length > 0 ? (
                  <Table responsive hover striped>
                    <thead>
                      {/* <tr>
                        <th className="fw-bold ">Employee Details</th>
                        {headers.map((header, idx) => (
                          <th key={idx}>{header}</th>
                        ))}
                      </tr> */}
                      <tr>
                        <th className="fw-bold text-primary"><strong>Employee Details</strong></th>
                        {headers.map((header, idx) => (
                          <th key={idx}>
                            {/\d{4}-\d{2}-\d{2}/.test(header)
                              ? new Date(header).toLocaleDateString("en-GB")
                              : header}
                          </th>
                        ))}
                      </tr>


                    </thead>
                    <tbody>
                      {Object.entries(
                        attendanceData.reduce((acc, rec) => {
                          const empId = rec.empId;
                          const dateKey = formatDate(rec.loginTime); // formatDate should match headers format

                          if (!acc[empId]) {
                            acc[empId] = {
                              empCode: rec.empCode,
                              empId: rec.empId,
                              title: rec.employeeDetails?.title || rec.title,
                              name: rec.employeeDetails?.name || rec.name,
                              designation: rec.designationDetails?.designation || "N/A",
                              college: rec.collegeDetails?.name || "Not Available",
                              aisheCode: rec.collegeDetails?.aisheCode || "N/A",
                              records: {},
                            };
                          }

                          acc[empId].records[dateKey] = rec;
                          return acc;
                        }, {})
                      ).map(([empId, emp]) => (
                        <tr key={empId}>
                          <td>
                            <strong>Name:</strong> {emp.title} {emp.name} <br />
                            <strong>Emp Code:</strong> {emp.empCode} <br />
                            <strong>Designation:</strong> {emp.designation} <br />
                            <strong>College:</strong> {emp.college} <br />
                            <strong>AISHE Code:</strong> {emp.aisheCode}<br />
                            <strong>View EmployeeWise:</strong> <button
                                          className="btn btn-primary btn-sm"
                                          title="Change Password"
                                          onClick={() =>
                                            viewEmployeeWise(
                                              emp.empId,
                                              emp.empCode
                                            )
                                          }
                                        >
                                       <span className="fa fa-eye"></span>
                                        </button>
                          </td>
                          {headers.map((date, i) => {
                            const rec = emp.records[date];
                            return (
                              <td key={i}>
                                {rec ? (
                                  <>
                                    Login: {formatTime(rec.loginTime)} <br />
                                    Logout: {formatTime(rec.logoutTime)} <br />
                                    Stay:{" "}
                                    {calculateStayDuration(rec.loginTime, rec.logoutTime)}
                                  </>
                                ) : (
                                  <span style={{ color: "red", fontWeight: "bold" }}>No Attendance</span>
                                )}
                              </td>
                            );
                          })}
                        
                           
                            
                        </tr>
                      ))}
                    </tbody>
                  </Table>
                ) : (
                  <p className="text-center text-danger fw-bold mb-0 ">No attendance data found.</p>
                )}
              </CardBody>

            </Card>
          </Col>
        </Row>
        <Modal
          isOpen={attendanceModal}
          toggle={toggleAttendanceModal}
          style={{ maxWidth: "90%" }}
        >
          <ModalHeader toggle={toggleAttendanceModal}>
            {/* <h2>Attendance Filter For : {employeeProfile.name}</h2> */}
          </ModalHeader>
          <ModalBody>
            <section style={{ backgroundColor: "#eee" }}>
              <div className="container py-5">
                <div className="row">
                  <div className="col-lg-4">
                    <div className="card mb-4">
                      <div className="card-body text-center">
                        <img
                          src={base64Image}
                          alt="avatar"
                          className="rounded-circle img-fluid"
                          style={{ width: "150px", height: "150px" }}
                        />

                        <h5 className="my-3">
                          <b>{employeeProfile.name}</b>({" "}
                          {employeeProfile ? employeeProfile.empCode : ""} )
                        </h5>
                        <p className="text-muted mb-1">
                          {employeeProfile && employeeProfile.length > 0
                            ? employeeProfile.designationDetails.designation
                            : ""}
                        </p>
                        <p className="text-muted mb-4">
                          {employeeProfile ? employeeProfile.address : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-8">
                    <div className="card mb-4">
                      <div className="card-body">
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Full Name</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.name : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Email</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.email : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Mobile</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.contact : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-12">
                        <div className="card mb-4 mb-md-0">
                          <div className="card-body">
                            <Form >
                              <div className="pl-lg-4">
                                <Row>
                                  <Col lg="3">
                                    <FormGroup>
                                      <Label htmlFor="input-className">
                                        Filter
                                      </Label>
                                      <Input
                                        type="select"
                                        value={filter}
                                        onChange={(e) =>
                                          setFilter(e.target.value)
                                        }
                                      >
                                        <option value="">Select Filter</option>
                                        <option value="today">Today</option>
                                        <option value="weekly">
                                          Current Week
                                        </option>
                                        <option value="monthly">
                                          Current Month
                                        </option>
                                        <option value="yearly">
                                          Current Year
                                        </option>
                                        <option value="dateRange">
                                          Date Range
                                        </option>
                                      </Input>
                                    </FormGroup>
                                  </Col>
                                  <Col lg="3">
                                    <FormGroup>
                                      {filter === "dateRange" && (
                                        <div>
                                          <label>Start Date:</label>
                                          <Input
                                            type="date"
                                            value={fromDate}
                                            onChange={(e) => setFromDate(e.target.value)}
                                          />
                                        </div>
                                      )}
                                    </FormGroup>
                                  </Col>
                                  <Col lg="3">
                                    <FormGroup>
                                      {filter === "dateRange" && (
                                        <div>
                                          <label>End Date:</label>
                                          <Input
                                            type="date"
                                            value={toDate}
                                            onChange={(e) => setToDate(e.target.value)}
                                          />
                                        </div>
                                      )}
                                    </FormGroup>
                                  </Col>
                                </Row>
                                <Button
                                  className="btn btn-sm btn-primary"
                                  onClick={handleDateSubmit}
                                >
                                  Get Attendance
                                </Button>
                              </div>
                            </Form>
                          </div>
                        </div>
                      </div>
                    </div>
                    <br />
                  </div>
                  <Col lg="12">
                    <Card className="shadow">
                      <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                        <h3 className="mb-0">Attendance List</h3>
                        <FormControl
                          type="text"
                          placeholder="Search..."
                          className="ml-auto"
                          value={filterTextModal}
                          onChange={(e) => setFilterTextModal(e.target.value)}
                          style={{ width: "250px", borderRadius: "30px" }}
                        />
                      </CardHeader>
                      <CardBody>
                        <DataTable
                          columns={columnsModal}
                          data={filteredDataModal}
                          pagination
                          paginationPerPage={10}
                          highlightOnHover
                          striped
                          customStyles={{
                            header: {
                              style: {
                                backgroundColor: "#f8f9fa",
                                fontWeight: "bold",
                              },
                            },
                            rows: {
                              style: {
                                backgroundColor: "#fff",
                                borderBottom: "1px solid #ddd",
                              },
                              onHoverStyle: {
                                backgroundColor: "#ffff99",
                              },
                            },
                          }}
                        />
                      </CardBody>
                    </Card>
                  </Col>
                </div>
              </div>
            </section>
            <br />
          </ModalBody>
        </Modal>
      </Container>
    </>
  );
};

export default GenerateAttendanceReport;
