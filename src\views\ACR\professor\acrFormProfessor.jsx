import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Container,
  Row,
  Col,
  Label,
  Button,
  Input,
  Table,
} from "reactstrap";
import Swal from "sweetalert2";
import axios from "axios";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";

const ACRFormProfessor = ({ employee }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);

  const [samikshaAwadhiKarya, setSamikshaAwadhiKarya] = useState([
    {
      level: 'स्नातक',
      sectionCount: '',
      studentCount: '',
      vyakhyan: '',
      prayogik: '',
      tutorial: '',
      visheshCoaching: '',
    },
    {
      level: 'स्नातकोत्तर',
      sectionCount: '',
      studentCount: '',
      vyakhyan: '',
      prayogik: '',
      tutorial: '',
      visheshCoaching: '',
    },
  ]);

  const [formData, setFormData] = useState({
    employeeId: employee._id,
    employeeName: employee.name,
    designation: employee.designationDetails.designation,
    collegeName: employee.collegeDetails.name,
    collegeId: employee.collegeDetails._id,
    currentSalary: employee.currentSalary,
    title: employee.title,
    gender: employee.gender,
    fatherName: "",
    dateofBirth: "",
    employeeSecondaryName: "",
    shenikVarsh: "",
    shenikSubject: "",
    payscale: "",
    prathamNiyuktiDesignation: "",
    prakar: "",
    prathanNiyuktiDate: "",
    niyamitNiyuktiDate: "",
    vartamanNiyuktiDate: "",
    padastOne: "",
    padastTwo: "",
    padastThree: "",
    samikshaAwadhiKarya: samikshaAwadhiKarya,
    panjiNiyamit: "",
    panjiPracharya: "",
    shodKaryaKaVivran: "",
    prakashitKaryaVivran: "",
    mphilStudentCount: "",
    phdStudentCount: "",
    weakStudent: "",
    bookName: "",
    leaveStatus: "",
    ncc: "",
    nss: "",
    parikshaSanchalan: "",
    collegeWork: "",
    otherWork: "",
  });
  const [errors, setErrors] = useState({});



  const handleArrayInputChange = (index, e) => {
    const { name, value } = e.target;
    const updatedData = [...samikshaAwadhiKarya];
    updatedData[index][name] = value; // Update the specific field in the specific row
    setSamikshaAwadhiKarya(updatedData); // Update the state
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    const regex = /^[a-zA-Z\s'-]*$/;

    // Validate the input for father's name
    if ((name === "employeeSecondaryName" || name === "fatherName") && !regex.test(value)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "Do Not Use Special Character।", // "Please do not use special characters."
      }));
      return; // Exit the function if the input is invalid
    } else {
      // Clear the error if the input is valid
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "",
      }));
    }

    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.fatherName.trim())
      newErrors.fatherName = "This field is required.";
    if (!formData.dateofBirth)
      newErrors.dateofBirth = "This field is required.";
    if (!formData.shenikSubject.trim())
      newErrors.shenikSubject = "This field is required.";
    if (!formData.payscale.trim())
      newErrors.payscale = "This field is required.";
    if (!formData.prathamNiyuktiDesignation.trim())
      newErrors.prathamNiyuktiDesignation = "This field is required.";
    if (!formData.prakar.trim())
      newErrors.prakar = "This field is required.";
    if (!formData.prathanNiyuktiDate.trim())
      newErrors.prathanNiyuktiDate = "This field is required.";
    if (!formData.niyamitNiyuktiDate.trim())
      newErrors.niyamitNiyuktiDate = "This field is required.";
    if (!formData.vartamanNiyuktiDate.trim())
      newErrors.vartamanNiyuktiDate = "This field is required.";
    if (!formData.padastOne.trim())
      newErrors.padastOne = "This field is required.";
    if (!formData.padastTwo.trim())
      newErrors.padastTwo = "This field is required.";
    if (!formData.padastThree.trim())
      newErrors.padastThree = "This field is required.";
    if (!formData.panjiNiyamit.trim())
      newErrors.panjiNiyamit = "This field is required.";
    if (!formData.panjiPracharya.trim())
      newErrors.panjiPracharya = "This field is required.";
    if (!formData.shodKaryaKaVivran.trim())
      newErrors.shodKaryaKaVivran = "This field is required.";
    if (!formData.prakashitKaryaVivran.trim())
      newErrors.prakashitKaryaVivran = "This field is required.";
    if (!formData.mphilStudentCount.trim())
      newErrors.mphilStudentCount = "This field is required.";
    if (!formData.phdStudentCount.trim())
      newErrors.phdStudentCount = "This field is required.";
    if (!formData.weakStudent.trim())
      newErrors.weakStudent = "This field is required.";
    if (!formData.bookName.trim())
      newErrors.bookName = "This field is required.";
    if (!formData.leaveStatus.trim())
      newErrors.leaveStatus = "This field is required.";
    if (!formData.ncc.trim())
      newErrors.ncc = "This field is required.";
    if (!formData.nss.trim())
      newErrors.nss = "This field is required.";
    if (!formData.parikshaSanchalan.trim())
      newErrors.parikshaSanchalan = "This field is required.";
    if (!formData.collegeWork.trim())
      newErrors.collegeWork = "This field is required.";
    if (!formData.otherWork.trim())
      newErrors.otherWork = "This field is required.";
    return newErrors;
  };

  const [payLevel, setPayLevel] = useState([]); // For table data


  useEffect(() => {
    const fetchPayLevel = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-pay-level`, {
          headers: { 
             "Content-Type": "application/json",
              'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setPayLevel(response.data); // ✅ Correctly updating PayLevel state
        } else {
          SwalMessageAlert("No Pay Level Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching Pay Level data:", error);
      }
    };

    fetchPayLevel();
  }, [endPoint, token]); // Dependencies

  const handleFinalSubmit = async (e) => {


    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
    e.target.disabled = true;

        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("स्व-प्रतिवेदन सफल हुआ", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const [designations, setDesignations] = useState([]);
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const verifiedDesignations = response.data.filter(
            (designation) => designation.isVerified === 1
          );

          setDesignations(verifiedDesignations);
        } else {
          alert("Failed to fetch designations. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchDesignations();
  }, [endPoint, token]);

  const [appointment, setAppointment] = useState([]);
  useEffect(() => {
    const getAppointment = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-appointment-type`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setAppointment(data);
        }
      } catch (error) {
        console.error("Error fetching appointment data:", error);
        alert("Failed to load appointment data.");
      }
    };

    getAppointment(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies



  const currentDate = new Date();
  const currentToday = currentDate.toISOString().split('T')[0];

  const minDate = new Date();
  minDate.setFullYear(currentDate.getFullYear() - 18);

  // Calculate the maximum date (65 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(currentDate.getFullYear() - 65);

  // Format dates to YYYY-MM-DD for the input
  const formattedMinDate = minDate.toISOString().split('T')[0];
  const formattedMaxDate = maxDate.toISOString().split('T')[0];


  return (
    <>
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="shadow pb-4">
              <CardHeader
                className="bg-white border-0"
                style={{ textAlign: "center" }}
              >
                <h2 className="mb-0">
                  कार्यालय, आयुक्त, उच्च शिक्षा, छत्तीसगढ़ रायपुर
                </h2>
                <h3 className="mb-0">
                  गोपनीय प्रतिवेदन प्रपत्र
                  <br />
                  31 मार्च {lastYearDate} को समाप्त होने वाले वर्ष के लिये
                  <br />
                  प्रतिवेदित अधिकारी प्राध्यापक / सहायक प्राध्यापक द्वारा भरा
                  जाए
                </h3>
              </CardHeader>
              <CardBody>
                <div className="mb-4">
                  <Row className="mb-3">
                    <Col md="3">
                      <Label>पूरा नाम</Label>
                      <Input
                        type="text"
                        name="employeeName"
                        value={formData.employeeName}
                        readOnly
                        className="form-control"
                      />
                    </Col>

                    {formData.gender === "Female" &&

                      <Col md="3">
                        <Label>
                          महिला अधिकारी विवाह के पूर्व का नाम भी लिखें
                        </Label>
                        <Input
                          type="text"
                          name="employeeSecondaryName"
                          className="form-control"
                          value={formData.employeeSecondaryName}
                          onChange={handleInputChange}
                        />
                        {errors.employeeSecondaryName && (
                          <small className="text-danger">
                            {errors.employeeSecondaryName}
                          </small>
                        )}
                      </Col>}
                    <Col md="3">
                      <Label>पिता अथवा पति का नाम </Label>
                      <Input
                        type="text"
                        name="fatherName"
                        value={formData.fatherName}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.fatherName && (
                        <small className="text-danger">
                          {errors.fatherName}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>जन्म तिथि</Label>
                      <Input
                        type="date"
                        name="dateofBirth"
                        value={formData.dateofBirth}
                        className="form-control"
                        onChange={handleInputChange}
                        min={formattedMaxDate} // Set the minimum date to 65 years ago
                        max={formattedMinDate}
                      />
                      {errors.dateofBirth && (
                        <small className="text-danger">
                          {errors.dateofBirth}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-3">
                    <Col md="6">
                      <Label style={{ fontWeight: "bold" }}>शैक्षणिक अर्हता एवं वर्ष</Label>

                      <Row>
                        <>
                          <div className="col-sm-6">
                            <Label>स्नातक</Label>
                            <Input
                              type="text"
                              name="shenikVarsh"
                              value={formData.shenikVarsh}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.shenikVarsh && (
                              <small className="text-danger">
                                {errors.shenikVarsh}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-6">
                            <Label>एम.फिल</Label>
                            <Input
                              type="text"
                              name="shenikSubject"
                              value={formData.shenikSubject}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.shenikSubject && (
                              <small className="text-danger">
                                {errors.shenikSubject}
                              </small>
                            )}
                          </div>
                        </>
                        <>
                          <div className="col-sm-6">
                            <Label>स्नातकोत्तर</Label>
                            <Input
                              type="text"
                              name="shenikVarsh"
                              value={formData.shenikVarsh}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.shenikVarsh && (
                              <small className="text-danger">
                                {errors.shenikVarsh}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-6">
                            <Label>पीएच.डी</Label>
                            <Input
                              type="text"
                              name="shenikSubject"
                              value={formData.shenikSubject}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.shenikSubject && (
                              <small className="text-danger">
                                {errors.shenikSubject}
                              </small>
                            )}
                          </div>
                        </>

                      </Row>
                    </Col>
                    <Col md="6">
                      <Label style={{ fontWeight: "bold" }}>वेतन व वेतनमान</Label>
                      <Row>
                        <div className="col-sm-6">
                          <Label>वेतन</Label>
                          <Input
                            type="number"
                            name="currentSalary"
                            value={formData.currentSalary}
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.currentSalary && (
                            <small className="text-danger">
                              {errors.currentSalary}
                            </small>
                          )}
                        </div>
                        <div className="col-sm-6">
                          <Label>वेतनमान</Label>
                          <Input
                            type="select"
                            name="payscale"
                            value={formData.payscale}
                            className="form-control"
                            onChange={handleInputChange}
                          >  <option

                            value={""} //  Use type.payID (Number)
                          >
                              Choose an option
                            </option>
                            {payLevel.length > 0 &&
                              payLevel.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.payID} //  Use type.payID (Number)
                                  selected={
                                    Number(type.payID) ===
                                    Number(formData.presentPayLevel)
                                  }
                                >
                                  {type.payLevel}
                                </option>
                              ))}
                          </Input>
                          {errors.payscale && (
                            <small className="text-danger">
                              {errors.payscale}
                            </small>
                          )}
                        </div>
                      </Row>

                    </Col>
                  </Row>
                  <hr />
                  <Row className="mt-3 mb-3">
                    <Col>
                      <h3>
                        <u> महाविद्यालयीन सेवा प्रारंभ करने की जानकारी</u>
                      </h3>
                    </Col>
                  </Row>
                  <Row>
                    <Col md="12">
                      <Label>प्रथम नियुक्ति का पद, प्रकार एवं दिनांक :</Label>
                      <div className="col-sm-12">
                        <Row>
                          <div className="col-sm-4">
                            <Label>पद</Label>
                            <Input
                              type="select"
                              name="prathamNiyuktiDesignation"
                              value={formData.prathamNiyuktiDesignation}
                              className="form-control"
                              onChange={handleInputChange}
                            >
                              <option

                                value={""} //  Use type.payID (Number)
                              >
                                Choose an option
                              </option>
                              {designations.length > 0 &&
                                designations.map((type, index) => (
                                  <option
                                    key={index}
                                    value={type.designation} //  Use type.payID (Number)
                                  >
                                    {type.designation}
                                  </option>
                                ))}
                            </Input>
                            {errors.prathamNiyuktiDesignation && (
                              <small className="text-danger">
                                {errors.prathamNiyuktiDesignation}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-4">
                            <Label>प्रकार</Label>
                            <Input
                              type="select"
                              name="prakar"
                              value={formData.prakar}
                              className="form-control"
                              onChange={handleInputChange}
                            ><option value="">
                                No options choosen
                              </option>
                              {appointment &&
                                appointment.length > 0 &&
                                appointment.map((type, index) => (
                                  <option
                                    key={index}
                                    value={type.appointment}
                                  >
                                    {type.appointment}
                                  </option>
                                ))}
                            </Input>
                            {errors.prakar && (
                              <small className="text-danger">
                                {errors.prakar}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-4">
                            <Label>दिनांक</Label>
                            <Input
                              type="date"
                              name="prathanNiyuktiDate"
                              value={formData.prathanNiyuktiDate}
                              className="form-control"
                              max={currentToday}
                              onChange={handleInputChange}
                            />
                            {errors.prathanNiyuktiDate && (
                              <small className="text-danger">
                                {errors.prathanNiyuktiDate}
                              </small>
                            )}
                          </div>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="4">
                      <Label>नियमित नियुक्ति का दिनाँक :</Label>
                      <div className="col-sm-12">
                        <Row>
                          <div className="col-sm-12">
                            <Label>दिनांक</Label>
                            <Input
                              type="date"
                              name="niyamitNiyuktiDate"
                              value={formData.niyamitNiyuktiDate}
                              className="form-control"
                              max={currentToday}
                              onChange={handleInputChange}
                            />
                            {errors.niyamitNiyuktiDate && (
                              <small className="text-danger">
                                {errors.niyamitNiyuktiDate}
                              </small>
                            )}
                          </div>
                        </Row>
                      </div>
                    </Col>
                    <Col md="8">
                      <Label>वर्तमान पद एवं नियुक्ति दिनांक :</Label>
                      <div className="col-sm-12">
                        <Row>
                          <div className="col-sm-6">
                            <Label>पद</Label>
                            <Input
                              type="text"
                              name="designation"
                              value={formData.designation}
                              className="form-control"
                              readOnly
                              onChange={handleInputChange}
                            />
                            {errors.designation && (
                              <small className="text-danger">
                                {errors.designation}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-6">
                            <Label>दिनांक</Label>
                            <Input
                              type="date"
                              name="vartamanNiyuktiDate"
                              value={formData.vartamanNiyuktiDate}
                              className="form-control"
                              max={currentToday}
                              onChange={handleInputChange}
                            />
                            {errors.vartamanNiyuktiDate && (
                              <small className="text-danger">
                                {errors.vartamanNiyuktiDate}
                              </small>
                            )}
                          </div>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="12">
                      <Label style={{ fontWeight: "bolder" }}>
                        वर्ष में किस-किस संस्था में पदस्थ रहे, अवधि का भी उल्लेख
                        करें :

                      </Label><br />
                      <Label>(यदि एक से अधिक संस्था में कार्य किया हो तो प्रत्येक
                        संस्था की कार्य अवधि के लिए पृथक फार्म भरा जाये)</Label>
                      <div className="col-sm-12">
                        <Row>
                          <div className="col-sm-4">
                            <Label>(i)</Label>
                            <Input
                              type="textarea"
                              maxLength={200}
                              name="padastOne"
                              value={formData.padastOne}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.padastOne && (
                              <small className="text-danger">
                                {errors.padastOne}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-4">
                            <Label>(ii)</Label>
                            <Input
                              type="textarea"
                              maxLength={200}
                              name="padastTwo"
                              value={formData.padastTwo}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.padastTwo && (
                              <small className="text-danger">
                                {errors.padastTwo}
                              </small>
                            )}
                          </div>
                          <div className="col-sm-4">
                            <Label>(iii)</Label>
                            <Input
                              type="textarea"
                              name="padastThree"
                              maxLength={200}
                              value={formData.padastThree}
                              className="form-control"
                              onChange={handleInputChange}
                            />
                            {errors.padastThree && (
                              <small className="text-danger">
                                {errors.padastThree}
                              </small>
                            )}
                          </div>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="12">
                      <Label style={{ fontWeight: "bolder" }}>
                        प्रतिवेदित अधिकारी द्वारा समीक्षा अवधि में किये गए कार्य
                        की जानकारी : <br />
                      </Label>
                      <div className="col-sm-12">
                        <Row>
                          <Table>
                            <thead>
                              <tr>
                                <th>क्र.</th>
                                <th>पढ़ाई गई कक्षा का स्तर</th>
                                <th>सेक्शन की संख्या</th>
                                <th>कुल छात्र संख्या</th>
                                <th>व्याख्यान</th>
                                <th>प्रायोगिक</th>
                                <th>टिटोरियल</th>
                                <th>विशेष कोचिंग</th>
                              </tr>
                            </thead>
                            <tbody>
                              {samikshaAwadhiKarya.map((data, index) => (
                                <tr key={index}>
                                  <th>{index + 1}</th>
                                  <th>{data.level}</th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="sectionCount"
                                      value={data.sectionCount}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="studentCount"
                                      value={data.studentCount}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="vyakhyan"
                                      value={data.vyakhyan}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="prayogik"
                                      value={data.prayogik}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="tutorial"
                                      value={data.tutorial}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                  <th>
                                    <Input
                                      type="number"
                                      name="visheshCoaching"
                                      value={data.visheshCoaching}
                                      className="form-control"
                                      onChange={(e) => handleArrayInputChange(index, e)}
                                    />
                                  </th>
                                </tr>
                              ))}
                            </tbody>
                          </Table>
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="3">
                      <Label>क्या उपस्थिति पंजी नियमित भरी गई</Label>
                      <Input
                        type="select" // Change the type to "select"
                        name="panjiNiyamit"
                        value={formData.panjiNiyamit} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      >
                        <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                        <option value="Han">हाँ</option> {/* Yes option */}
                        <option value="Nahi">नहीं</option> {/* No option */}
                      </Input>
                      {errors.panjiNiyamit && (
                        <small className="text-danger">
                          {errors.panjiNiyamit}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>क्या उपस्थिति पंजी प्राचार्य को सौंपी गई</Label>
                      <Input
                        type="select"
                        name="panjiPracharya"
                        value={formData.panjiPracharya} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      >
                        <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                        <option value="Han">हाँ</option> {/* Yes option */}
                        <option value="Nahi">नहीं</option> {/* No option */}
                      </Input>
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="12">
                      <Label>
                        वर्ष के दौरान आपके द्वारा किये गए शोध कार्य का विवरण
                      </Label>
                      <Input
                        type="textarea"
                        name="shodKaryaKaVivran"
                        style={{ height: "100px" }}
                        maxLength={300}
                        value={formData.shodKaryaKaVivran}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.shodKaryaKaVivran && (
                        <small className="text-danger">
                          {errors.shodKaryaKaVivran}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="12">
                      <Label>
                        प्रकाशित कार्य का विवरण
                      </Label>
                      <Input
                        type="textarea"
                        maxLength={300}
                        name="prakashitKaryaVivran"
                        value={formData.prakashitKaryaVivran}
                        className="form-control"
                        onChange={handleInputChange}
                      />

                      {errors.prakashitKaryaVivran && (
                        <small className="text-danger">
                          {errors.prakashitKaryaVivran}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <br />
                  <Row>
                    <Col md="12">
                      <Label style={{ fontWeight: "bolder" }}>
                        कितने छात्रों को शोध कार्य हेतु मार्गदर्शन किये
                      </Label>
                      <Row>
                        <Col>
                          <Label>एम. फिल. के कितने छात्रों को</Label>
                          <Input
                            type="number"
                            name="mphilStudentCount"
                            value={formData.mphilStudentCount}
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.mphilStudentCount && (
                            <small className="text-danger">
                              {errors.mphilStudentCount}
                            </small>
                          )}
                        </Col>
                        <Col>
                          <Label>पीएच.डी. के कितने छात्रों को</Label>
                          <Input
                            type="number"
                            name="phdStudentCount"
                            value={formData.phdStudentCount}
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.phdStudentCount && (
                            <small className="text-danger">
                              {errors.phdStudentCount}
                            </small>
                          )}
                        </Col>
                        <Col md="4">
                          <Label>कितने कमजोर छात्रों को विशेष कोचिंग दी</Label>
                          <Input
                            type="number"
                            name="weakStudent"
                            value={formData.weakStudent} // Use employee data here
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.weakStudent && (
                            <small className="text-danger">
                              {errors.weakStudent}
                            </small>
                          )}
                        </Col>
                      </Row>

                    </Col>

                  </Row>
                  <br />
                  <Row>
                    <Col md="6">
                      <Label>वर्ष में कितनी नई पुस्तकों का अध्ययन किया (पुस्तकों का नाम व लेखकों का नाम लिखे )</Label>
                      <div className="col-sm-12">
                        <Row>
                          <Label></Label>
                          <Input
                            type="textarea"
                            maxLength={400}
                            style={{ height: "150px" }}
                            name="bookName"
                            value={formData.bookName} // Use employee data here
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.bookName && (
                            <small className="text-danger">
                              {errors.bookName}
                            </small>
                          )}
                        </Row>
                      </div>
                    </Col>
                    <Col md="6">
                      <Label>
                        वर्ष के दौरान लिए गए अवकाश की प्रकृति एवं दिवस
                      </Label>
                      <div className="col-sm-12">
                        <Row>
                          <Input
                            type="textarea"
                            maxLength={400}
                            style={{ height: "150px" }}
                            name="leaveStatus"
                            value={formData.leaveStatus} // Use employee data here
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.leaveStatus && (
                            <small className="text-danger">
                              {errors.leaveStatus}
                            </small>
                          )}
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <br />
                  <Row className="mt-3 mb-3">
                    <Col>
                      <h3>
                        <u> शैक्षणेत्तर कार्यों का संक्षिप्त विवरण</u>
                      </h3>
                    </Col>
                  </Row>
                  <Row>
                    <Col md="6">
                      <Label>एन.सी.सी.</Label>
                      <div className="col-sm-12">
                        <Row>
                          <Input
                            type="textarea"
                            name="ncc"
                            maxLength={200}
                            value={formData.ncc} // Use employee data here
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.ncc && (
                            <small className="text-danger">
                              {errors.ncc}
                            </small>
                          )}
                        </Row>
                      </div>
                    </Col>

                    <Col md="6">
                      <Label>एन. एस. एस.</Label>
                      <div className="col-sm-12">
                        <Row>
                          <Input
                            type="textarea"
                            name="nss"
                            maxLength={200}
                            value={formData.nss} // Use employee data here
                            className="form-control"
                            onChange={handleInputChange}
                          />
                          {errors.nss && (
                            <small className="text-danger">
                              {errors.nss}
                            </small>
                          )}
                        </Row>
                      </div>
                    </Col>
                  </Row>
                  <Row className="mt-3">
                    <Col md="12" >
                      <Label style={{ fontWeight: "bolder" }}>
                        परीक्षा संचालन </Label>
                      <br />
                      <Label>

                        (महाविद्यालयीन परीक्षा संचालन में क्या कार्य किया कार्य
                        की प्रकृति एवं कितने दिन इस कार्य का संचालन किया)
                      </Label>
                      <Input
                        type="textarea"
                        name="parikshaSanchalan"
                        style={{ height: "100px" }}
                        maxLength={500}
                        value={formData.parikshaSanchalan} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.parikshaSanchalan && (
                        <small className="text-danger">
                          {errors.parikshaSanchalan}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <br />

                  <Row>
                    <Col md="6">
                      <Label>
                        महाविद्यालय प्रशासन के लिए किये गए कार्य
                        (जैसे अनुशासन, जांच कार्य, छात्र संघ आदि)
                      </Label>

                      <Input
                        type="textarea"
                        name="collegeWork"
                        maxLength={250}
                        value={formData.collegeWork} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.collegeWork && (
                        <small className="text-danger">
                          {errors.collegeWork}
                        </small>
                      )}

                    </Col>
                    <Col md="6">
                      <Label>
                        अन्य कार्य  (जैसे- खेल संबंधी, सेमीनार आदि)
                      </Label>
                      <Input
                        type="textarea"
                        name="otherWork"
                        maxLength={250}
                        value={formData.otherWork} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.otherWork && (
                        <small className="text-danger">
                          {errors.otherWork}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <br />
                  <Button color="success" onClick={handleFinalSubmit}>
                    Submit
                  </Button>
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

ACRFormProfessor.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    gender: PropTypes.string,
    title: PropTypes.string,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormProfessor;