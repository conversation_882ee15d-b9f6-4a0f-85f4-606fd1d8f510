import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Container,
  Row,
  Col,
  Form,
  Input,
  Label,
  Badge,

} from "reactstrap";
import moment from 'moment';

// import { Document, Page, Text, View, pdf } from "@react-pdf/renderer";


import Swal from "sweetalert2";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useNavigate, useParams } from "react-router-dom";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx"







const EditAppliedLeave = () => {
  const token = sessionStorage.getItem('authToken');
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [application, setApplication] = useState([]);
  const [isTribal, setIsTribal] = useState(false);
  const [dayGap, setDayGap] = useState(0);
  const [holiday, setHoliday] = useState([]);

  const [message, setMessage] = useState({
    message: "",
    color: "",
  });
  const [newLeaveBalance, setNewLeaveBalance] = useState([]);
  const [maxLeave, setMaxLeave] = useState(0);
  const endPoint = import.meta.env.VITE_API_URL;
  const { id } = useParams();
  const empId = sessionStorage.getItem('id')
  const navigate = useNavigate();
  const [pendingJoining, setPendingJoining] = useState([]);
  const [appliedLeave, setAppliedLeave] = useState([]);
  const [rules, setRules] = useState([]);
  const [user, setUser] = useState("");




  // console.log(id, "Getting Id Here");



  useEffect(() => {
    const fetchLeaveRules = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/leave/rule/list`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setRules(response.data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchLeaveRules();
  }, [token, endPoint]);

  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUser(data);


        } else {
          alert("Data Not Fetched.");

        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");

      }
    };
    fetchEmployeeData();
  }, [token, endPoint, empId]);

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/leaveBalance/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setIsTribal(data[0]?.isTribal);
          setLeaveBalance(data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchApplications();
  }, [empId, token, endPoint]);

  useEffect(() => {
    const fetchHoliday = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/holiday/get-all-holiday`,
          {
            headers: {
              'Content-Type': 'application/json',
              'web-url': window.location.href,
              "Authorization": `Bearer ${token}`
            }
          });
        if (response.status === 200) {
          const govtHoliday = response.data.filter(item => item.type === 'Govt')
          // const restrictedHoliday = response.data.filter(item => item.type === 'Restricted')
          setHoliday(govtHoliday);
          // setRestrictedHoliday(restrictedHoliday);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    }
    fetchHoliday();
  }, [token, endPoint]);

  const [formData, setFormData] = useState({
    applicantId: '',
    applicationType: '',
    reason: '',
    leaveType: '',
    maxLeave: '',
    fromDate: '',
    tillDate: '',
    dayCount: '',
    permission: '0',
    stationAddress: '',
    remarkByUser: '',
    uploadFile: null
  });



  useEffect(() => {
    const getApplication = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/getSingleLeave/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setApplication(data)
        }
      } catch (error) {
        console.error("Error fetching Application data:", error);
        alert("Failed to Application data.");
      }
    };

    getApplication(); // Call the function inside useEffect
  }, [id, endPoint, token]); // Dependencies

  useEffect(() => {
    // console.log("Hitting here");
    // console.log(application, "Geting Application ID");

    const formatDateForInput = (dateString) => {
      if (!dateString) return ''; // Return empty if no date
      const date = new Date(dateString);
      if (isNaN(date)) return ''; // Check if the date is valid
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    };

    setFormData({
      applicantId: application.applicantId || '',
      applicationType: application.applicationType || '',
      reason: application.reason || '',
      leaveType: String(application.leaveCode),
      maxLeave: application.maxLeave || '',
      fromDate: formatDateForInput(application.fromDate),
      tillDate: formatDateForInput(application.tillDate),
      dayCount: application.dayCount || '',
      permission: application.permission === 0 ? "0" : application.permission === 1 ? "1" : "",
      stationAddress: application.stationAddress || '',
      remarkByUser: application.remarkByUser || '',
      uploadFile: application.uploadFile || null,
    });

    // Function to format date as MM/DD/YYYY


  }, [empId, token, application]);


  // const [activeLeavePeriod, setActiveLeavePeriod] = useState([]);
  useEffect(() => {
    const fetchAppliedLeave = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/applied_Leaves/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const data = response.data;
          setAppliedLeave(data);
          const today = new Date().toISOString().split("T")[0];
          const currentApplicationID = application && application.applicationId;

          if (currentApplicationID) {
            const activeLeavePeriod = data.filter((leave) =>
              (leave.leaveStatus === 3 || leave.leaveStatus === 2 || leave.leaveStatus === 1) &&
              leave.fromDate > today &&
              leave.applicationId !== currentApplicationID
            );

            // console.log(activeLeavePeriod, "Log active Leave Period");
            // setActiveLeavePeriod(activeLeavePeriod);
            // Check leave period availability after setting activeLeavePeriod
            const { fromDate, tillDate } = formData;
            if (fromDate && tillDate) {
              // console.log(fromDate, tillDate, "Getting From Date and Till Date");

              // Convert incoming string dates to Date objects
             

              const isLeavePeriodAvailable = () => {
                const from = new Date(fromDate);
                const till = new Date(tillDate);
                return !activeLeavePeriod.some(leave => {
                  if ([1, 2, 3].includes(leave.leaveStatus)) {
                    const leaveFrom = new Date(leave.fromDate);
                    const leaveTill = new Date(leave.tillDate);

                    return (from <= leaveTill && till >= leaveFrom);
                  }
                  return false;
                });
              };

              if (isLeavePeriodAvailable()) {
                // console.log("Leave can be applied for this period.", fromDate, tillDate);
              } else {
                // console.log("Leave cannot be applied for this period due to overlapping with existing leaves.", fromDate, tillDate);
                setFormData((prevData) => ({
                  ...prevData,
                  fromDate: '',
                  tillDate: '',
                  dayCount: 0,
                }));
                SwalMessageAlert("Your leave request cannot be processed for this period due to existing leaves for same Duration.", "error");
              }
            }
          }

          const joiningPending = data.filter(
            (leave) =>
              leave.leaveStatus === 3 &&
              leave.isJoined === 0 &&
              (leave.leaveType === "Earned Leave" ||
                leave.leaveType === "Medical Leave" ||
                leave.leaveType === "Half Pay Leave")
          );
          setPendingJoining(joiningPending);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchAppliedLeave();
  }, [empId, endPoint, token, application, formData.fromDate, formData.tillDate]);





  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value
    }));
  };

  const maxSizeInBytes = 0.1 * 1024 * 1024; // 2 MB



  const handleFileChange = (e) => {
    const getFile = e.target.files[0];
    if (getFile) {
      // setFile(getFile);
      if (getFile.size > maxSizeInBytes) {
        setMessage({
          message: 'File size exceeds the limit of 100 KB.',
          color: 'red',
        });
        e.target.value = ''; // Clear the input
      } else {
        setMessage({
          message: 'File is within the size limit.',
          color: 'green',
        });
      }
    }
  };

  const [selectedReason, setSelectedReason] = useState('');
  const [manualReason, setManualReason] = useState('');
  const [showManualInput, setShowManualInput] = useState(false);


  const handleReasonChange = (e) => {
    const value = e.target.value;
    setSelectedReason(value);
    setFormData((prevData) => ({
      ...prevData,
      remarkByUser: e.target.value,
    }));

    if (value === "Other") {
      setShowManualInput(true);
    } else {
      setShowManualInput(false);
      setManualReason("");
      setFormData((prevData) => ({
        ...prevData,
        reason: value,
      }));
    }
  };

  const handleManualReasonChange = (e) => {
    const value = e.target.value;
    setManualReason(value);
    setFormData((prevData) => ({
      ...prevData,
      reason: manualReason,
    }));
  };



  const handleLeaveTypeChange = (e) => {
    const value = e.target.value;
    setFormData((prevData) => ({
      ...prevData,
      leaveType: value,
      fromDate: '',
      tillDate: '',
      dayCount: 0,
    }));
    setDayGap(0);
  };


  const handleDateChange = (e) => {
    const { name, value } = e.target;
    const today = new Date().toISOString().split("T")[0];

    // Check for backdates
    // // console.log(typeof formData.leaveType,formData.leaveType,"Getting Leave Type");
    // // console.log(value,"Getting Value");
    // // console.log(today,"Getting today");

    // const check = value < today ;
    // const check2 = formData.leaveType !== "1" || formData.leaveType !== "12"

    // // console.log("Checking ",check,check2);



    if ((formData.leaveType !== "1" && formData.leaveType !== "12") && value < today) {
      SwalMessageAlert("Backdates are Not Allowed. Please select a valid date.", "warning");
      return;
    }

    setFormData((prevData) => {
      let updatedData = { ...prevData, [name]: value };

      if (name === "fromDate") {
        updatedData = {
          ...updatedData,
          tillDate: "",
          dayCount: 0,
        };
        setDayGap(0);
      }

      // Recalculate day count if both dates are set
      if (updatedData.fromDate && updatedData.tillDate) {
        const fromDate = new Date(updatedData.fromDate);
        const tillDate = new Date(updatedData.tillDate);
        const dayCount = (tillDate - fromDate) / (1000 * 3600 * 24) + 1;
        updatedData.dayCount = dayCount > 0 ? dayCount : 0;
      }

      return updatedData;
    });

    // Perform holiday calculations only when valid date range exists
    const updatedData = { ...formData, [name]: value };
    if (name === "fromDate" || (updatedData.fromDate && updatedData.tillDate)) {
      const start = moment.utc(updatedData.fromDate);
      const end = moment.utc(updatedData.tillDate);

      // console.log("Start Date (UTC):", start.format());
      // console.log("End Date (UTC):", end.format());

      let totalHolidays = 0;
      const holidayDates = holiday.map((holiday) => moment.utc(holiday.date));

      // console.log("Holiday Dates (UTC):", holidayDates.map(date => date.format("YYYY-MM-DD")));

      if (updatedData.fromDate && updatedData.tillDate) {
        let current = start.clone();

        while (current.isSameOrBefore(end, "day")) {
          const isSunday = current.day() === 0;
          const isSaturday = current.day() === 6;

          const isHoliday = holidayDates.some((holidayDate) => holidayDate.isSame(current, "day"));

          // console.log(`Comparing ${current.format("YYYY-MM-DD")} with holidays: ${isHoliday}`);

          if (user.workType === "TEACHING") {
            if (isSunday || isHoliday) totalHolidays++;
          } else if (user.workType === "NON TEACHING") {
            if (isSunday || isSaturday || isHoliday) totalHolidays++;
          }

          current.add(1, "day");
        }
      }

      // console.log("Total Holidays:", totalHolidays);
      setDayGap(totalHolidays);
    }
  };


  const handleUpdate = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    try {
      // Assuming you have an ID for the leave application you want to update
      const leaveApplicationId = application._id; // Make sure this is set in your formData

      let body = {
        applicantId: String(empId),
        applicationType: String(formData.applicationType),
        reason: formData.reason,
        leaveType: String(formData.leaveType),
        fromDate: String(formData.fromDate),
        tillDate: String(formData.tillDate),
        dayCount: String(formData.dayCount),
        permission: String(formData.permission),
        stationAddress: formData.stationAddress,
        remarkByUser: formData.remarkByUser,
      };

      // console.log('Form updated:', formData);

      // Update the endpoint to reflect the update operation
      const response = await axios.put(`${endPoint}/api/leave/update_leave/${leaveApplicationId}`, body, {
        headers: {
          'Content-Type': 'application/json',
          'web-url': window.location.href,
          "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) { // Typically, a successful update returns a 200 status
        SwalMessageAlert("Leave application updated successfully.", "success");
        navigate("/admin/apply-leave");
        setShowManualInput(false);
      } else {
        SwalMessageAlert("Updating leave application failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while updating the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const mandatory = {

    color: "red",
    fontSize: "14px",
    fontWeight: "700",
    paddingLeft: "5px"
  }


  useEffect(() => {
    // Only run this effect if both fields have values
    // console.log(formData.maxLeave, "form Data max Leave");
    // console.log(maxLeave, "max Leave");

    if (formData.dayCount && maxLeave) {
      const dayCountValue = parseInt(formData.dayCount, 10);
      const maxLeaveValue = parseInt(maxLeave, 10);

      // Check if dayCount exceeds maxLeave
      if (dayCountValue > maxLeaveValue) {
        Swal.fire({
          icon: 'warning', // Set the warning icon
          title: 'Warning', // Title of the alert
          text: "Select Date is More than Max Limit.",
          position: 'top'
        });

        setFormData((prevData) => ({
          ...prevData,
          dayCount: '',
          tillDate: ''
        }));
      }
    }
    if (formData.fromDate && formData.tillDate) {
      const FromDate = formData.fromDate;
      const TillDate = formData.tillDate;

      // Check if dayCount exceeds maxLeave
      if (FromDate > TillDate) {
        Swal.fire({
          icon: 'warning', // Set the warning icon
          title: 'Warning', // Title of the alert
          text: 'Till Date can\'t be earlier than From Date.',
          position: 'top'
        });
        setFormData((prevData) => ({
          ...prevData,
          dayCount: '',
          tillDate: ''
        }));
      }
    }
  }, [formData.dayCount, formData.maxLeave]);







  useEffect(() => {
    let newCL = leaveBalance[0]?.casualLeave;
    let newEL = leaveBalance[0]?.earnedLeave;
    let newRH = leaveBalance[0]?.restrictedHoliday;
    let newHPL = leaveBalance[0]?.halfPayLeave;
    let newTCL = leaveBalance[0]?.tribalCasualLeave;
    let newTEL = leaveBalance[0]?.tribalEarnedLeave;


    // console.log(newCL);
    // console.log(newEL);
    // console.log(newHPL);
    // console.log(newRH);

    // Update the appropriate leave balance based on leaveType
    if (formData.leaveType === '1') {
      setMaxLeave(newCL);
    } else if (formData.leaveType === '2') {
      setMaxLeave(newEL);
    } else if (formData.leaveType === '3') {
      // Check if RH is greater than 0, then set max limit to 1
      if (newRH > 0) {
        setMaxLeave(1);
      } else {
        setMaxLeave(newRH);
      }
    } else if (formData.leaveType === '4') {
      setMaxLeave(newHPL / 2);
    } else if (formData.leaveType === '5') {
      setMaxLeave(newHPL);
    }
    else if (formData.leaveType === "12") {
      setMaxLeave(newTCL);
    }
    else if (formData.leaveType === "13") {
      setMaxLeave(newTEL);
    } else {
      setMaxLeave('');
    }
  }, [formData.leaveType, leaveBalance]);


  useEffect(() => {
    if (formData.dayCount && dayGap >= 0 && (formData.leaveType === '1' || formData.leaveType === '3' || formData.leaveType === '12')) {
      setFormData(prevData => ({
        ...prevData,
        dayCount: Math.max(0, prevData.dayCount - dayGap),
      }));
    }
  }, [dayGap, formData.tillDate]);

  useEffect(() => {
    if (formData.dayCount && maxLeave) {
      const dayCount = parseInt(formData.dayCount, 10);
      const maxLeaveLimit = parseInt(maxLeave, 10);

      if (formData.leaveType === '1' || formData.leaveType === '3' || formData.leaveType === '12') {
        if (dayCount > maxLeaveLimit + dayGap) {
          showLeaveLimitWarning("Casual Leave Date is More than Max Limit.");
        }
      } else if (dayCount > maxLeaveLimit) {
        showLeaveLimitWarning("Selected Date is More than Max Limit.");
      }
    }

    if (formData.fromDate && formData.tillDate && formData.fromDate > formData.tillDate) {
      Swal.fire({
        icon: "warning",
        title: "Warning",
        text: "Till Date can't be earlier than From Date.",
        position: "top",
      });
      resetInvalidDates();
    }
  }, [formData.dayCount, formData.maxLeave, dayGap]);

  const showLeaveLimitWarning = (message) => {
    Swal.fire({
      icon: "warning",
      title: "Warning",
      text: message,
      position: "top",
    });

    setFormData(prevData => ({
      ...prevData,
      dayCount: "",
      tillDate: "",
    }));
  };

  const resetInvalidDates = () => {
    setFormData(prevData => ({
      ...prevData,
      dayCount: "",
      tillDate: "",
    }));
  };



  useEffect(() => {
    // console.log("hitted Handle Change");
    // Calculate new leave balances based on the updated formData
    let newCL = leaveBalance[0]?.casualLeave;
    let newEL = leaveBalance[0]?.earnedLeave;
    let newRH = leaveBalance[0]?.restrictedHoliday;
    let newHPL = leaveBalance[0]?.halfPayLeave;
    let newTCL = leaveBalance[0]?.tribalCasualLeave;
    let newTEL = leaveBalance[0]?.tribalEarnedLeave;

    // console.log(newCL);
    // console.log(newEL);
    // console.log(newHPL);
    // console.log(newRH);
    // // console.log("leaveType",formData.leaveType);

    // console.log(newCL, "Getting New CL HERE");
    // Update the appropriate leave balance based on leaveType
    if (formData.leaveType === '1') {
      newCL -= formData.dayCount;
    } else if (formData.leaveType === '2') {
      newEL -= formData.dayCount;
    } else if (formData.leaveType === '3') {
      newRH -= formData.dayCount;
    } else if (formData.leaveType === '4') {
      newHPL -= formData.dayCount * 2;
    } else if (formData.leaveType === '5') {
      newHPL -= formData.dayCount;
    } else if (formData.leaveType === "12") {
      newTCL -= formData.dayCount;
    } else if (formData.leaveType === "13") {
      newTEL -= formData.dayCount;
    }
    else {
      // console.log("Direct enterd Here");

    }

    // console.log("leaveType", formData.leaveType);
    // console.log("day coutnt", formData.dayCount);


    const newLeaveBalance = [{ CL: newCL, EL: newEL, RH: newRH, HPL: newHPL, TCL: newTCL, TEL: newTEL }];

    setNewLeaveBalance(newLeaveBalance);
  }, [leaveBalance, formData.dayCount]);





  return (
    <>
      <Header />
      <Container className="mt--7" fluid>

        {newLeaveBalance && <Card className="bg-secondary mb-3" style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}>
          <CardHeader className="bg-white border-1">
            <Row>
              <Col>
                <h1>Leave Balance</h1>

              </Col>
            </Row>
          </CardHeader>
          <CardBody className="bg-white d-flex">
            <Col>
              <h1>
                <Badge style={{ backgroundColor: '#28a745', color: 'white', width: "100%" }}>CL - {newLeaveBalance[0]?.CL}</Badge>
              </h1>
            </Col>
            <Col>
              <h1>
                <Badge style={{ backgroundColor: '#007bff', color: 'white', width: "100%" }}>OL - {newLeaveBalance[0]?.RH}</Badge>
              </h1>
            </Col>
            <Col>
              <h1>
                <Badge style={{ backgroundColor: '#ffc107', color: 'black', width: "100%" }}>EL - {newLeaveBalance[0]?.EL}</Badge>
              </h1>
            </Col>
            <Col>
              <h1>
                <Badge style={{ backgroundColor: '#17a2b8', color: 'white', width: "100%" }}>HPL - {newLeaveBalance[0]?.HPL}</Badge>
              </h1>
            </Col>
            {isTribal && isTribal === true && <>
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#0d5348",
                      color: "white",
                      width: "100%",
                    }}
                  >
                    TCL - {newLeaveBalance[0]?.TCL}
                  </Badge>
                </h1>
              </Col>
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#623b0b",
                      color: "white",
                      width: "100%",
                    }}
                  >
                    TEL - {newLeaveBalance[0]?.TEL}
                  </Badge>
                </h1>
              </Col>  </>}
          </CardBody>

        </Card>

        }
        <Row>
          <Col>
            <Card className="bg-secondary" style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}>
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">EDIT LEAVE APPLICATION/ अवकाश आवेदन सुधार    </h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button color="primary" size="sm">
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Application Type(आवेदन के प्रकार)<span style={mandatory}>*</span></Label>
                        <Input
                          className="text-"
                          type="select"
                          name="applicationType"
                          value={formData.applicationType}
                          onChange={handleChange}
                        >
                          <option value="">--चयन करें--</option>
                          <option value="1">एकल अवकाश (Single Leave)</option>
                          <option value="2">एकाधिक अवकाश (Multiple Leave)</option>
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Reason(कारण)<span style={mandatory}>*</span></Label>
                        <Input
                          type="select"
                          name="reason"
                          value={selectedReason}
                          onChange={handleReasonChange}
                        >
                          <option value={application.reason}>{application.reason}</option>
                          <option value="व्यक्तिगत कारण">व्यक्तिगत कारण (Personal Reason)</option>
                          <option value="स्वास्थ्य कारण">स्वास्थ्य कारण (Health Reason)</option>
                          <option value="परिवारिक कार्यक्रम">परिवारिक कार्यक्रम (Family Event)</option>
                          <option value="शादी में सम्मिलित होना">शादी में सम्मिलित होना (Attending a Wedding)</option>
                          <option value="स्वास्थ्य परीक्षण">स्वास्थ्य परीक्षण (Medical Checkup)</option>
                          <option value="अत्यावश्यक कार्य">अत्यावश्यक कार्य (Emergency Work)</option>
                          <option value="बच्चों की देखभाल">बच्चों की देखभाल (Childcare)</option>
                          <option value="शोकसभा में सम्मिलित होना">शोकसभा में सम्मिलित होना (Attending a Funeral)</option>
                          <option value="यात्रा">यात्रा (Travel)</option>
                          <option value="सामाजिक कार्य">सामाजिक कार्य (Social Work)</option>
                          <option value="शैक्षणिक कारण">शैक्षणिक कारण (Educational Reason)</option>
                          <option value="प्राकृतिक आपदा">प्राकृतिक आपदा (Natural Calamity)</option>
                          <option value="अस्पताल में भर्ती">अस्पताल में भर्ती (Hospitalization)</option>
                          <option value="विश्राम की आवश्यकता">विश्राम की आवश्यकता (Need for Rest)</option>
                          <option value="माता-पिता की देखभाल">माता-पिता की देखभाल (Parental Care)</option>
                          <option value="निजी कार्य">निजी कार्य (Personal Work)</option>
                          <option value="कार्यालय के बाहर की बैठक">कार्यालय के बाहर की बैठक (Meeting Outside the Office)</option>
                          <option value="मानसिक स्वास्थ्य">मानसिक स्वास्थ्य (Mental Health)</option>
                          <option value="धार्मिक कारण">धार्मिक कारण (Religious Reason)</option>
                          <option value="बैंक या अन्य प्रशासनिक कार्य">बैंक या अन्य प्रशासनिक कार्य (Bank or Other Administrative Work)</option>
                          <option value="Other">अन्य (Other)</option>
                        </Input>
                        {showManualInput && (
                          <Input
                            type="text"
                            className='formInput mt-2'
                            placeholder="कारण दर्ज करें"
                            value={manualReason}
                            onChange={handleManualReasonChange}
                          />
                        )}
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Leave Type (अवकाश का प्रकार) <span style={mandatory}>*</span></Label>
                        <Input
                          type="select"
                          name="leaveType"
                          value={formData.leaveType}
                          onChange={handleLeaveTypeChange}
                        >
                          <option value={application.leaveType}>{application.leaveType}</option>
                          <option value="1">आकास्मिक अवकाश (Casual Leave)</option>
                          <option value="2">अर्जित अवकाश (Earned Leave)</option>
                          <option value="3">ऐच्छिक अवकाश (Optional Holiday)</option>
                          <option value="4">चिकित्सा अवकाश (Medical Leave)</option>
                          <option value="5">अर्धवैतनिक अवकाश (Half Pay Leave)</option>
                          <option value="6">प्रसूति अवकाश (Maternity Leave)</option>
                          <option value="7">दत्तक ग्रहण अवकाश (Adoption Leave)</option>
                          <option value="8">पितृत्व अवकाश (Paternity Leave)</option>
                          <option value="9">संतान पालन अवकाश (Child Care Leave)</option>
                          <option value="10">अदेय अवकाश (No Pay Leave)</option>
                          <option value="11">असाधारण अवकाश (Extraordinary Leave)</option>
                          {isTribal && isTribal === true && <>
                            <option value="12">
                              अनुसूचित आकस्मिक अवकाश (Tribal Casual Leave)
                            </option>
                            <option value="13">
                              अनुसूचित अर्जित अवकाश (Tribal Earned Leave)
                            </option> </>}
                        </Input>
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Maximum Leave (अधिकतम अवकाश)</Label>
                        <Input
                          type="text"
                          name="maxLeave"
                          value={maxLeave}
                          onChange={handleChange}
                          placeholder="अधिकतम अवकाश"
                        />
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>From Date (दिनांक से )<span style={mandatory}>*</span></Label>
                        <Input
                          type="date"
                          name="fromDate"
                          value={formData.fromDate}
                          onChange={handleDateChange} // Use the new handler
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Till Date (दिनांक तक ) <span style={mandatory}>*</span></Label>
                        <Input
                          type="date"
                          name="tillDate"
                          value={formData.tillDate}
                          onChange={handleDateChange} // Use the new handler
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>दिनों की संख्या (Day Count) <span style={mandatory}>*</span></Label>
                        <Input
                          type="text"
                          name="dayCount"
                          value={formData.dayCount}
                          // onChange={handleBalanceChange}
                          readOnly // Make this input read-only
                          placeholder="दिनों की संख्या"
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="3">
                      <FormGroup>
                        <Label className='Label'>Permission / मुख्यालय छोड़ने की अनुमति?</Label>
                        <div className="row" style={{ justifyContent: "space-evenly" }}>
                          <FormGroup check>
                            <Input type="radio" name="permission" value="1" checked={formData.permission === '1'} onChange={handleChange} />
                            <Label check>हाँ</Label>
                          </FormGroup>
                          <FormGroup check>
                            <Input type="radio" name="permission" value="0" checked={formData.permission === '0'} onChange={handleChange} />
                            <Label check>नहीं</Label>
                          </FormGroup>
                        </div>
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="6">
                      <FormGroup>
                        <Label className='Label'>Leave Address (अवकाश का पता )<span style={mandatory}>*</span></Label>
                        <Input
                          type="textarea"
                          name="stationAddress"
                          value={formData.stationAddress}
                          onChange={handleChange}
                          placeholder="अवकाश का पता"
                        />
                      </FormGroup>
                    </Col>
                    <Col lg="6">
                      <FormGroup>
                        <Label className='Label'>Remark (रिमार्क )</Label>
                        <Input
                          type="textarea"
                          name="remarkByUser"
                          value={formData.remarkByUser}
                          onChange={handleChange}
                          placeholder="Remark"
                        />
                      </FormGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg="6">
                      <FormGroup>
                        <Label className="Label" > <b style={{ color: message.color }}>{message.message}</b><br />
                          Upload Document (आवेदन की प्रति अपलोड करें){" "}
                          <span style={mandatory}>
                            (.Pdf फाइल 100 kb तक)
                          </span>
                        </Label>
                        <Input
                          type="file"
                          name="uploadFile"
                          onChange={handleFileChange}
                          placeholder="Upload leave copy"
                        />
                      </FormGroup>
                    </Col>
                  </Row>
                  <Button onClick={handleUpdate} color="primary">Submit</Button>
                </Form>
              </CardBody>

            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default EditAppliedLeave;
