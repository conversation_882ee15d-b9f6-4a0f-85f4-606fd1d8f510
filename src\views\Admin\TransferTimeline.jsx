// TransferDetails.js

import { useEffect, useState } from "react";
import formatDate from "../../utils/formateDate";
import axios from "axios";


const TransferTimeline = ({ data }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  console.log("Getting Data",data);
  

  const [timeline, setTimeline] = useState([]);

  useEffect(() => {
  if (data) {
    findEmployeeTransfer(data);
  }
}, [data]);


  const findEmployeeTransfer = async (data) => {
    fetchData(data);
    try {
      const response = await axios.get(
        `${endPoint}/api/all-transfer-employeewise/${data}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const newTimeLine = getCombinedSortedTimeline(response.data);
        setTimeline(newTimeLine);
        console.log(newTimeLine, "newTimeLine Gettng Data");
      } else {
        alert("Failed to fetch college data. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  function getCombinedSortedTimeline(history) {
    const timeline = [];
    history &&
      history.forEach((record) => {
        if (record.chargeReleaseOrderDate) {
          timeline.push({
            date: record.chargeReleaseOrderDate,
            action: "Relieved",
            collegeName: record.transferFromCollege?.name || "Unknown College",
          });
        }
        if (record.isJoiningDone && record.transferOrderDate) {
          timeline.push({
            date: record.chargeJoinDate,
            action: "Joined",
            collegeName: record.transferToCollege?.name || "Unknown College",
          });
        }
      });
    timeline.sort((a, b) => new Date(a.date) - new Date(b.date));
    return timeline;
  }
  const [appointmentDate, setAppointmentDate] = useState("");
  const [retirementDate, setRetirementDate] = useState("");

  const fetchData = async (id) => {
    console.log(id,"Getting ID");
    
    try {
      const response = await axios.get(
        `${endPoint}/api/get-emp-profile?empId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        console.log(data, "Getting PRofile Data");

        setAppointmentDate(
          data.presentInformation.appointmentDate
            ? data.presentInformation.appointmentDate
            : "N/A"
        );

        setRetirementDate(
          data.employeeInformation.retirementDate
            ? data.employeeInformation.retirementDate
            : "N/A"
        );
      } else {
        setAppointmentDate("N/A");
        setRetirementDate("N/A");
      }
    } catch (error) {
      console.error("Error fetching Employee data:", error);
      alert("Failed to load Employee data.");
    }
  };

  return (
    <div
      className="position-relative"
      style={{
        marginLeft: "25px",
        paddingLeft: "20px",
        borderLeft: "3px solid #007bff",
      }}
    >
      <div className="mb-4 position-relative" style={{ paddingLeft: "15px" }}>
        <div
          className="position-absolute text-white bg-primary rounded-circle d-flex align-items-center justify-content-center"
          style={{
            left: "-36px",
            top: "0",
            width: "28px",
            height: "28px",
            fontSize: "14px",
            fontWeight: "bold",
            boxShadow: "0 0 0 3px #fff",
          }}
        >
          0
        </div>
        <div
          className="rounded shadow-sm"
          style={{
            borderLeft: "10px solid  #4E6FB1",
            backgroundColor: "#c8d3e7",
            padding: "10px",
            color: "white",
            boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
          }}
        >
          <h5 className="mb-2 text-black">Higher Education</h5>
          <h5 className="text-black">
            <strong>Action:</strong> Appointment
            <br />
            <strong>Date:</strong>{" "}
            {appointmentDate != "N/A" ? formatDate(appointmentDate) : "N/A"}
          </h5>
        </div>
      </div>
      {timeline &&
        timeline?.map((step, index) => (
          <div
            key={index}
            className="mb-4 position-relative"
            style={{ paddingLeft: "15px" }}
          >
            <div
              className="position-absolute text-white bg-primary rounded-circle d-flex align-items-center justify-content-center"
              style={{
                left: "-36px",
                top: "0",
                width: "28px",
                height: "28px",
                fontSize: "14px",
                fontWeight: "bold",
                boxShadow: "0 0 0 3px #fff",
              }}
            >
              {index + 1}
            </div>
            <div
              className="rounded shadow-sm"
              style={{
                borderLeft: "10px solid  #4E6FB1",
                backgroundColor: "#c8d3e7",
                padding: "10px",
                color: "white",
                boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
              }}
            >
              <h5 className="mb-2 text-black">{step.collegeName}</h5>
              <h5 className="text-black">
                {step.action && (
                  <>
                    <strong>Action:</strong> {step.action}
                    <br />
                  </>
                )}
                {step.date && (
                  <>
                    <strong>Date:</strong> {formatDate(step.date)}
                  </>
                )}
              </h5>
            </div>
          </div>
        ))}
      <div className="mb-4 position-relative" style={{ paddingLeft: "15px" }}>
        <div
          className="position-absolute text-white bg-primary rounded-circle d-flex align-items-center justify-content-center"
          style={{
            left: "-36px",
            top: "0",
            width: "28px",
            height: "28px",
            fontSize: "14px",
            fontWeight: "bold",
            boxShadow: "0 0 0 3px #fff",
          }}
        >
          ...
        </div>
        <div
          className="rounded shadow-sm"
          style={{
            borderLeft: "10px solid  #4E6FB1",
            backgroundColor: "#c8d3e7",
            padding: "10px",
            color: "white",
            boxShadow: "5px 5px 10px 3px rgba(128, 128, 128, 0.5)",
          }}
        >
          <h5 className="mb-2 text-black">Service Retirement </h5>
          <h5 className="text-black">
            <strong>Action:</strong> Retirement
            <br />
            <strong>Date:</strong>{" "}
            {retirementDate != "N/A" ? formatDate(retirementDate) : "N/A"}
          </h5>
        </div>
      </div>
    </div>
  );
};

export default TransferTimeline;
