import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Spinner,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
const InstituteUpdate = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const { id } = useParams();
  const [formData, setFormData] = useState({
    name: "",
    collegeEmail: "",
    contactPerson: "",
    contactNumber: "",
    divison: "",
    district: "",
    aisheCode: "",
    isLead: 0,
  });
  const [errors, setErrors] = useState({});

  const [previewModal, setPreviewModal] = useState(false);

  const formatFieldValue = (key, value) => {
    if (key === "collegeType") {
      return value === "1" ? "Government" : "Private";
    }
    if (key === "isLead") {
      return value === 1 ? "Yes" : "No"; // Convert isLead value to Yes/No
    }
    if (key === "regDate") {
      return new Date(value).toLocaleDateString();
    }
    if (key === "isTribal") {
      return value ? "Yes" : "No";
    }
    return value || "Not provided";
  };
  const validateFields = () => {
    const newErrors = {};

    Object.keys(formData).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if there are no errors
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email format validation
    return regex.test(email);
  };
  const togglePreviewModal = () => setPreviewModal(!previewModal);
  useEffect(() => {
    const getuniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-college/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          let formattedDate;
          if (data.regDate) {
            formattedDate = new Date(data.regDate).toISOString().split("T")[0];
          } else {
            formattedDate = "";
          }
          setFormData({
            ...data,
            regDate: formattedDate,
            divison: data.divison || "",
            vidhansabha: data.vidhansabha || "",
            district: data.district || "",
            isLead: data.isLead,
          });
        }
      } catch (error) {
        console.error("Error fetching college data:", error);
        alert("Failed to load college data.");
      }
    };

    getuniversity();
  }, [id, endPoint, token]);

  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [id, endPoint, token]); // Include value and token as dependencies if they can change

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
    if (name === "collegeEmail") {
      if (!validateEmail(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          collegeEmail: "Please enter a valid email address.",
        }));
      }
    }
  };

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching division data:", error);
        alert("Failed to load division data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const handledivisonChange = async (e) => {
    const { value } = e.target;

    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setDistrict(response.data);
      } else {
        alert("Failed to fetch districts.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert("An error occurred. Please try again later.");
    }

    // Correct state update
    setFormData((prevData) => ({
      ...prevData,
      divison: value,
    }));
  };

  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({ ...formData, district: value });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
      } else {
        alert("Failed to fetch vidhansabha data.");
      }
    } catch (error) {
      console.error("Error fetching vidhansabha:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    validateFields();
    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.name,
      formData.collegeEmail,
      formData.contactPerson,
      formData.contactNumber,
      formData.aisheCode,
      formData.isLead,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      SwalMessageAlert(
        "Please fill out all fields before submitting.",
        "warning"
      );
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      SwalMessageAlert(
        "All fields are filled. You can proceed with submission.",
        "success"
      );
    }

    try {
      const body = {
        name: formData.name,
        collegeEmail: formData.collegeEmail,
        contactPerson: formData.contactPerson,
        contactNumber: String(formData.contactNumber),
        divison: String(formData.divison),
        district: String(formData.district),
        aisheCode: formData.aisheCode,
        isLead: String(formData.isLead),
      };
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/college/update/${id}`,
          { ...body },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          //  SwalMessageAlert(response.data.msg, "success");
          SwalMessageAlert("Institute Updated Successfully.", "success");
          setTimeout(() => {
            window.location.replace("admin/dashboard");
          }, 2000);
          setFormData({
            name: "",
            collegeEmail: "",
            contactPerson: "",
            contactNumber: "",
            divison: "",
            district: "",
            aisheCode: "",
            isLead: 0,
          });
          // navigate("admin/university");
        }
      } else {
        SwalMessageAlert("Adding for College failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert("An error occurred. Please try again later.", "error");

      // navigate("admin/university");
    }
  };
  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.divison}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.divison) fetchDistrict();
  }, [formData.divison, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.vidhansabha) fetchVidhansabha();
  }, [formData.vidhansabha, endPoint, token]);

  const updateCollegeStatus = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/college/update-status/${id}`,
        {}, // Pass empty object if no data needs to be sent in the body
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        // Check the status code to ensure success
        window.location.replace("admin/institute");
      } else {
        alert("Failed to fetch College data. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Update Institute</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <b>
                      Last Update Date : {formatDate(formData.updatedAt)} &nbsp;
                    </b>
                    {formData.status === false ? (
                      <button
                        className="btn btn-warning btn-sm"
                        onClick={() => updateCollegeStatus(id)}
                      >
                        <Spinner
                          size="sm"
                          color="white"
                          style={{ marginRight: "8px" }}
                        />
                        Verify
                      </button>
                    ) : (
                      <span className="btn btn-success btn-sm">Verified</span>
                    )}
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-aisheCode"
                          >
                            AISHE Code
                          </label>
                          <Input
                            name="aisheCode"
                            id="input-aisheCode"
                            autoComplete="pope"
                            placeholder="AISHE CODE"
                            type="text"
                            value={formData.aisheCode}
                            onChange={handleInputChange}
                          />
                          {errors.aisheCode && (
                            <small style={{ color: "red" }}>
                              {errors.aisheCode}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            Institute Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="Institute Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                          />
                          {errors.name && (
                            <p style={{ color: "red" }}>{errors.name}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-collegeEmail"
                          >
                            College Email
                          </label>
                          <Input
                            name="collegeEmail"
                            id="input-collegeEmail"
                            placeholder="College Email"
                            type="email"
                            autoComplete="pope"
                            value={formData.collegeEmail}
                            onChange={handleInputChange}
                          />
                          {errors.collegeEmail && (
                            <p style={{ color: "red" }}>
                              {errors.collegeEmail}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityType"
                          >
                            Is Lead College
                          </label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-yes"
                                value={1}
                                checked={formData.isLead === 1}
                                onChange={handleInputChange}
                              />
                              <Label check htmlFor="input-lead-yes">
                                Yes
                              </Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-no"
                                value={0}
                                checked={formData.isLead === 0}
                                onChange={handleInputChange}
                              />
                              <Label check htmlFor="input-lead-no">
                                No
                              </Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactPerson"
                          >
                            Contact Person
                          </label>
                          <Input
                            name="contactPerson"
                            id="input-contactPerson"
                            placeholder="Contact Person"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactPerson}
                            onChange={handleInputChange}
                          />
                          {errors.contactPerson && (
                            <p style={{ color: "red" }}>
                              {errors.contactPerson}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactNumber"
                          >
                            Contact Number
                          </label>
                          <Input
                            name="contactNumber"
                            id="input-contactNumber"
                            placeholder="Contact Number"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactNumber}
                            maxLength={10}
                            onChange={(e) => {
                              const value = e.target.value;

                              // Check if the input is empty
                              if (value === "") {
                                handleInputChange(e);
                                return;
                              }

                              // Regex to match numbers starting with 6, 7, 8, or 9
                              const validStartRegex = /^[6-9]/;

                              if (validStartRegex.test(value)) {
                                // If valid, pass 'true' as the second argument to handleInputChange
                                handleInputChange(e);
                              } else {
                                // Show alert if the input starts with invalid numbers
                                // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                SwalMessageAlert(
                                  " Mobile number must start with digits 6, 7, 8, or 9.",
                                  "warning"
                                );
                              }
                            }}
                          />
                          {errors.contactNumber && (
                            <p style={{ color: "red" }}>
                              {errors.contactNumber}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-division"
                          >
                            Division
                          </label>
                          <Input
                            name="divison"
                            id="input-division"
                            type="select"
                            value={formData.divison}
                            onChange={handledivisonChange}
                          >
                            <option value="" disabled>
                              Select Division
                            </option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.divisionCode}
                                  selected={
                                    Number(type.divisionCode) ===
                                    Number(formData.divison)
                                  }
                                >
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.divison && (
                            <small style={{ color: "red" }}>
                              {errors.divison}
                            </small>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            District
                          </label>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            onChange={handleDistrictChange}
                          >
                            <option value="" disabled>
                              Select Division
                            </option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.LGDCode}
                                  selected={
                                    Number(type.LGDCode) ===
                                    Number(formData.district)
                                  }
                                >
                                  {type.districtNameEng}
                                </option>
                              ))}
                            {/* Add more districts as needed */}
                          </Input>
                          {errors.district && (
                            <small style={{ color: "red" }}>
                              {errors.district}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button color="primary" onClick={togglePreviewModal}>
                      Preview & Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* ------- preview modal---------- */}

        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Preview of Institute Entry</h2>
            <h5 className="text-danger mt-2">
              Note: Please check all fields carefully before submitting
            </h5>
          </ModalHeader>
          <ModalBody style={{}}>
            <div>
              {/* Institute Name and University */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label
                      className="form-control-label"
                      htmlFor="input-address"
                    >
                      AISHE Code :
                    </label>
                    <input
                      type="text"
                      value={formData.aisheCode}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label
                      className="form-control-label"
                      htmlFor="input-address"
                    >
                      Institute Name :
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Institute Email:
                    </label>
                    <input
                      type="text"
                      value={formData.collegeEmail}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Contact Info and Establishment Year */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Is Lead Institute:
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue("isLead", formData.isLead)}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Person:
                    </label>
                    <input
                      type="text"
                      value={formData.contactPerson}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Number:
                    </label>
                    <input
                      type="text"
                      value={formData.contactNumber}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">Division:</label>

                    <input
                      type="text"
                      value={
                        division.find(
                          (type) =>
                            String(type.divisionCode) ===
                            String(formData.divison)
                        )?.name || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>

                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">District:</label>
                    <input
                      type="text"
                      value={
                        district.find(
                          (type) =>
                            String(type.LGDCode) === String(formData.district)
                        )?.districtNameEng || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
            </div>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button
              color="secondary"
              onClick={togglePreviewModal}
              style={{ borderRadius: "5px" }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{
                borderRadius: "5px",
                backgroundColor: "#007bff",
                border: "none",
              }}
            >
              Submit
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default InstituteUpdate;
