import React from "react";
import "../assets/css/RightSideBar.css"; // Import your CSS for styling
import InstitutePdf from "../assets/user_manual/User Manual for Institute.pdf";
import StateAdminPdf from "../assets/user_manual/UserManual_heonline.pdf";
import FaceAttendance from "../assets/user_manual/guide-face-attendance.pdf";
import IPRModule from "../assets/user_manual/IPR-Module.pdf";
import LMSModule from "../assets/user_manual/LMS-Module.pdf";
import EmpProfile from "../assets/user_manual/EmpProfile.pdf";
import ForgetPass from "../assets/user_manual/ForgetPassword.pdf";
const videoData = [
  {
    id: 1,
    title: "State Admin Login and Add University and Institute",
    url: "https://www.youtube.com/embed/ajI0q8kLIFc?si=dQZJ7VdIA2zLt2DU",
  },
  {
    id: 2,
    title: "How to Add Designation and Class",
    url: "https://www.youtube.com/embed/yUla7FC38zs?si=8yMsgLSeUbKz1Zu4",
  },
  {
    id: 3,
    title: "Subject Entry and Extend Subject Seat",
    url: "https://www.youtube.com/embed/6pubo_Dxvt4?si=yLRgham0hr9Mcglk",
    
  },
  {
    id: 4,
    title: "Log In to Institute and Complete Employee Registration",
    url: "https://www.youtube.com/embed/oGXi69J9KK8?si=Df2_TNqG-_VgA6_h",
  },
  {
    id: 5,
    title: "Employee Log In and Navigate Dashboard",
    url: "https://www.youtube.com/embed/gO6OtMyb03g?si=GjKE35laqMbYPbD4",
  },
  {
    id: 6,
    title: "How to Apply for Leave",
    url: "https://www.youtube.com/embed/FHaLMEXVtxk?si=pbOrBfS13XpJeh9N",
  },
  {
    id: 7,
    title: " Complete Guide for Employee Profile",
    url: "https://www.youtube.com/embed/6-4VGj_X6C8?si=_AHHgGBPm4Lpwu3-",
  },
  {
    id: 8,
    title: " Face Attendance System Complete Guide",
    url: "https://www.youtube.com/embed/2Fcn7HERyzA?si=bvFNTDUMP-DYP5QV",
  },
  
];
const userManual = [
  {
    id: 1,
    title: "State Admin",
    url: StateAdminPdf,
  },
  {
    id: 2,
    title: "Institute",
    url: InstitutePdf,
  },
  {
    id: 3,
    title: "IPR Module",
    url: IPRModule,
  },
  {
    id: 4,
    title: "Face Attendance App Manual",
    url: FaceAttendance,
  },
  {
    id: 5,
    title: "LMS Module",
    url: LMSModule,
  },
  {
    id: 6,
    title: "Employee Profile",
    url: EmpProfile,
  },
  {
    id: 7,
    title: "Forget Password",
    url: ForgetPass,
  },
];
function Help({ isOpen }) {
  return (
    <div
      style={{
        position: "fixed",
        right: 0,
        top: "110px",
        height: "calc(80vh - 110px)", 
        backgroundColor: "#ffffff",
        transform: isOpen ? "translateX(0)" : "translateX(100%)",
        transition: "transform 0.3s ease",
        padding: "20px",
        borderRadius: "8px", 
        boxShadow: "0 4px 12px rgba(0, 0, 0, 0.1)", 
        overflowY: "auto",
        zIndex: "1000",
      }}
    >
      <details open>
        <summary>User Manual</summary>
        <ul>
        {userManual.map((userManual) => (
            <li
              key={userManual.id}
              style={{
                marginBottom: "15px",
                fontSize: "15px",
                borderBottom: "1px solid #eee",
                paddingBottom: "10px",
              }}
            >
              <a
                href={userManual.url}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  textDecoration: "none",
                  color: "#007bff",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <i
                  className="fa fa-file text-danger"
                  style={{ marginRight: "10px", fontSize: "20px" }}
                />
                {userManual.title}
              </a>
            </li>
          ))}
        </ul>
      </details>
      <details>
        <summary>Video Tutorials</summary>
        <ul>
        {videoData.map((video) => (
            <li
              key={video.id}
              style={{
                marginBottom: "15px",
                fontSize: "15px",
                borderBottom: "1px solid #eee",
                paddingBottom: "10px",
              }}
            >
              <a
                href={video.url}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  textDecoration: "none",
                  color: "#007bff",
                  display: "flex",
                  alignItems: "center",
                }}
              >
                <i
                  className="fa fa-play text-danger"
                  style={{ marginRight: "10px", fontSize: "20px" }}
                />
                {video.title}
              </a>
            </li>
          ))}
        </ul>
      </details>
    </div>
  );
}

export default Help;
