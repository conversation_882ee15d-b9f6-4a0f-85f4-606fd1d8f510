import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    Label,
    Button,
    Input,
} from "reactstrap";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";
import axios from "axios";


const ACRForDirector = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);

    // // console.log(employee, "employeeGetting Employee");


    const [formData, setFormData] = useState({
        employeeId: employee._id,
        employeeName: employee.name,
        designation: employee.designationDetails.designation,
        padSthapnaJila: "",
        karyaKaSanchhiptVivran: "",
        uplabdhiKaUllekh: "",
        kathinayiBadhaKaUllekh: "",
        uplabdhiMeSahyog: "",

    })


    const [district, setDistrict] = useState([]);
    useEffect(() => {
        const fetchDistrict = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/district/get-all-district`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    //   // console.log(response.data);

                    setDistrict(response.data);
                } else {
                    alert("Failed to District  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDistrict();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token]); // Include value and token as dependencies if they can change


    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" });
    };

    const handleFinalSubmit = async (e) => {
        // Validation function to check for required fields
        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {
                padSthapnaJila: "",
                karyaKaSanchhiptVivran: "",
                uplabdhiKaUllekh: "",
                kathinayiBadhaKaUllekh: "",
                uplabdhiMeSahyog: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";
                    }
                }
            }

            return newErrors;
        };



        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {

            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert("स्व-प्रतिवेदन सफल हुआ", "success");
                    setTimeout(() => window.location.reload(), 5000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };



    return (
        <>
            <Container className="mt--7" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2">प्रपत्र - तीन </h2>
                                <h2> प्रथम श्रेणी, द्वितीय श्रेणी एवं कार्यपालिक तृतीय श्रेणी</h2>
                                <h2>
                                    अधिकारी का गोपनीय प्रतिवेदन
                                </h2>
                                <h3>31 मार्च {year} को समाप्त होने वाली अवधि</h3>
                                <br /><h2><u>भाग – एक</u></h2>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Row className="mb-3 ">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong>अधिकारी का नाम </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={formData.employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पदनाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="designation"
                                                        value={formData.designation} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                        readOnly
                                                    />
                                                    {errors.designation && (
                                                        <small className="text-danger">
                                                            {errors.designation}
                                                        </small>
                                                    )}

                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong> पदस्थापना जिला</Label>
                                                    <Input
                                                        type="select"
                                                        name="padSthapnaJila"
                                                        value={formData.padSthapnaJila} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    ><option

                                                        value={""}
                                                    >
                                                            No Option choosen
                                                        </option>
                                                        {district &&
                                                            district.length > 0 &&
                                                            district.map((type, index) => (
                                                                <option
                                                                    key={index}
                                                                    value={type.districtNameEng}
                                                                >
                                                                    {type.districtNameEng}{" "}({type.districtName})
                                                                </option>
                                                            ))}
                                                    </Input>
                                                    {errors.padSthapnaJila && (
                                                        <small className="text-danger">
                                                            {errors.padSthapnaJila}
                                                        </small>
                                                    )}
                                                </Col>

                                            </Row>

                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row>
                                        <Col>
                                            <Row className="mt-4" style={{ textAlign: "center", display: "block" }}>
                                                <Col>
                                                    <h2>
                                                        <u >भाग- दो</u>
                                                    </h2>
                                                </Col>
                                                <Col>
                                                    <h3>
                                                        (प्रतिवेदित अधिकारी द्वारा भरा जाए)

                                                    </h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>1.</strong> कार्य का संक्षिप्त विवरण :-</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKaSanchhiptVivran"
                                                        maxLength={300}
                                                        value={formData.karyaKaSanchhiptVivran} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaKaSanchhiptVivran && (
                                                        <small className="text-danger">
                                                            {errors.karyaKaSanchhiptVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt--4">
                                                    <Label><strong>2. </strong>कृपया आपके लिये निर्धारित गुणात्मक /
                                                        भौतिक / वित्तीय लक्ष्यों को प्राथमिकता क्रम से और
                                                        प्रत्येक लक्ष्य के विरूद्ध उपलब्धि का उल्लेख करें ।
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={300}
                                                        name="uplabdhiKaUllekh"
                                                        value={formData.uplabdhiKaUllekh} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.uplabdhiKaUllekh && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhiKaUllekh}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>3.</strong> कृपया, कॉलम 2 के सन्दर्भ में लक्ष्यों /
                                                        उद्देश्यों की पूर्ति में कमी का संक्षिप्त विवरण दें।
                                                        यदि लक्ष्यों की पूर्ति में कोई कठिनाई
                                                        (बाधा) आई हो तो उसको भी बतायें  :</Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={300}
                                                        name="kathinayiBadhaKaUllekh"
                                                        value={formData.kathinayiBadhaKaUllekh} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.kathinayiBadhaKaUllekh && (
                                                        <small className="text-danger">
                                                            {errors.kathinayiBadhaKaUllekh}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt-4">
                                                    <Label><strong>4. </strong>कृपया उन मदों को भी दर्शाएं जिनमें
                                                        अति महत्वपूर्ण उपलब्धियों में आपका सहयोग रहा हो -
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={300}
                                                        name="uplabdhiMeSahyog"
                                                        value={formData.uplabdhiMeSahyog} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.uplabdhiMeSahyog && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhiMeSahyog}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Button color="success" onClick={handleFinalSubmit}>
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRForDirector.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRForDirector;
