function isAlphaNumeric(e) {
    var keyCode = e.keyCode || e.which;
    var regex = /^[0-9a-zA-Z\s]+$/;
    var isValid = regex.test(String.fromCharCode(keyCode));
    return isValid;
}


    
        function numeric(evt) {
            var charCode = (evt.which) ? evt.which : event.keyCode
            if (charCode > 31 && ((charCode >= 48 && charCode <= 57) || charCode == 46))
                return true;
            else {
                alert('सिर्फ नंबर एंटर करे');
                return false;
            }
        }
    
function validateInput(event) {
    var regex = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/g;
    var key = String.fromCharCode(event.keyCode || event.which);
    if (regex.test(key)) {
        event.preventDefault();
        return false;
    }
}



  //  <!----- script for only character input---->
    
        function AllowAlphabet(e) {
            isIE = document.all ? 1 : 0
            keyEntry = !isIE ? e.which : event.keyCode;
            if (((keyEntry >= '65') && (keyEntry <= '90')) || ((keyEntry >= '97') && (keyEntry <= '122')) || (keyEntry == '46') || (keyEntry == '32') || keyEntry == '45')
                return true;
            else {
            alert('सिर्फ करैक्टर एंटर करे');
                return false;
            }
}