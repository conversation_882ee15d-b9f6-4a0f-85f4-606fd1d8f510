import React, { useState, useEffect } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  Card,
  CardHeader,
  CardBody,
  Input,
  Button,
  Container,
  Row,
  Col,
} from "reactstrap";
import {
  FormControl,
} from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const AddCourse = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [course, setCourse] = useState([]);
  const [newCourse, setnewCourse] = useState("");
  const [newCourseHin, setNewCourseHin] = useState("");
  const [editingCourse, setEditingCourse] = useState(null);
  const [updatedcourseName, setUpdatedcourseName] = useState("");
  const [updatedHinCourse, setUpdatedHinCourse] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  useEffect(() => {
    fetchCourse();
  }, []);

  const fetchCourse = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/get-course`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        const data = response.data;
        setCourse(data);
      } else {
        SwalMessageAlert("Failed Load Course!", "error");
      }
    } catch (error) {
      SwalMessageAlert(`Failed Load Course! ${error}`, "error");
    }
  };

  const handleAddCourse= async () => {
    try {
      // const newId = course.length + 1;
      const body = { courseName: newCourse , courseHin: newCourseHin};
      const response = await axios.post(`${endPoint}/api/course/add`, body, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
         SwalMessageAlert("Course Added Successfully", "success");
          setTimeout(() => {
          window.location.reload();
        }, 2000);
       
      } else {
        SwalMessageAlert(`${response.data.msg}`, "error");
      }
    } catch (error) {
      SwalMessageAlert(`CourseAdd Failed! ${error}`, "error");
    }
  };

  const handleEditCourse= (course) => {
    setEditingCourse(course._id);
    setUpdatedcourseName(course.courseName);
    setUpdatedHinCourse(course.courseHin);

  };

  const handleUpdateCourse= async (id) => {
    try {
      const body = { courseName: updatedcourseName, courseHin: updatedHinCourse }; // Prepare the updated Coursedata
      const response = await axios.put(
        `${endPoint}/api/course/update/${id}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Course Updated Successfully", "success");
        setEditingCourse(null); // Exit edit mode
        fetchCourse(); // Refresh the data
      } else {
        SwalMessageAlert(`${response.data.msg}`, "error");
      }
    } catch (error) {
      SwalMessageAlert(`CourseUpdate Failed! ${error}`, "error");
    }
  };

  const handleCancelEdit = () => {
    setEditingCourse(null);
    setUpdatedcourseName("");
    setUpdatedHinCourse("");
  };

  const columns = [
    {
      name: "Sr. No.",
      selector: "id",
      cell: (row, index) => (currentPage - 1) * rowsPerPage + index + 1,
    },
    {
      name: "CourseName",
      cell: (row) =>
        editingCourse=== row._id ? (
          <Input
            type="text"
            value={updatedcourseName}
            onChange={(e) => setUpdatedcourseName(e.target.value)} // Update the state
          />
        ) : (
          row.courseName
        ),
      sortable: true,
      width: "20%",
    },
       {
      name: "Course Hindi Name",
      cell: (row) =>
        editingCourse=== row._id ? (
          <Input
            type="text"
            value={updatedHinCourse}
            onChange={(e) => setUpdatedHinCourse(e.target.value)} // Update the state
          />
        ) : (
          row.courseHin
        ),
      sortable: true,
      width: "30%",
    },
    {
      name: "Operations",
      cell: (row) =>
        editingCourse=== row._id ? (
          <>
            <Button
              color="success"
              onClick={() => handleUpdateCourse(row._id)}
              size="sm"
              className="me-2"
            >
              Update
            </Button>
            <Button color="danger" onClick={handleCancelEdit} size="sm">
              Cancel
            </Button>
          </>
        ) : (
          <Button
            color="primary"
            onClick={() => handleEditCourse(row)}
            size="sm"
          >
            Edit
          </Button>
        ),
      width: "40%",
    },
  ];
  const [filterText, setFilterText] = useState("");
  const filteredData = course.filter((item) => {
    const filterTextLower = filterText.toLowerCase();
    return (
      item.courseName &&
      item.courseName.toLowerCase().includes(filterTextLower) ||
       item.courseHin &&
      item.courseHin.toLowerCase().includes(filterTextLower)
    );
  });
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col className="order-xl-1" xl="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                  <h3 className="mb-0">
                     Add / Edit Course 
                    </h3>
                <Row className="align-items-center">
                  
                  <Col xs="4">
                    
                    <div className="mt-4">
                      <Input
                        type="text"
                        value={newCourse}
                        onChange={(e) => setnewCourse(e.target.value)}
                        placeholder="Enter new Course"
                        className="mb-2"
                      />
                      
                    </div>
                  </Col>
                  <Col xs="4" >
                   <div className="mt-4">
                   <Input
                        type="text"
                        value={newCourseHin}
                        onChange={(e) => setNewCourseHin(e.target.value)}
                        placeholder="Enter new Course Hindi"
                        className="mb-2"
                      />
                      </div>
                  </Col>
                  
                  <FormControl
                    type="text"
                    placeholder="Search Course..."
                    className="ml-auto"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </Row>
                <Row>
                    <Col>
                      <div className="mt-4">
                 <Button
                        className="btn btn-sm"
                        color="primary"
                        onClick={handleAddCourse}
                        disabled={!newCourse.trim()}
                      >
                        Add New Course
                      </Button>
                      </div>
                    </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <DataTable
                  title="Course List"
                  columns={columns}
                  data={filteredData}
                  highlightOnHover
                  responsive
                  pagination
                  paginationPerPage={rowsPerPage}
                  onChangePage={(page) => setCurrentPage(page)}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default AddCourse;
