import { lazy, Suspense, useEffect } from "react";
import { SpeechProvider } from "./context/SpeechContext.jsx";
import PageReader from "./context/PageReader.jsx";
import SpeechToggle from "./context/SpeechToggle.jsx"; 

import {
  BrowserRouter as Router,
  Route,
  Routes,
  Navigate,
  useNavigate,
} from "react-router-dom";
import loadable from "@loadable/component";

import Index from "./views/Admin/Dashboard.jsx";
import AdminLayout from "./layouts/Admin.jsx";
const Login = loadable(() => import("./views/authorization/Login.jsx"));
const HomePage = loadable(() => import("./views/authorization/HomePage.jsx"));
const MainLogin = loadable(() => import("./views/authorization/MainLogin.jsx"));
const Register = loadable(() => import("./views/authorization/Register.jsx"));
const TermsConditions = loadable(()=> import("./views/Admin/TermsConditions"));

const OpenMarkAttendance = loadable(() =>
  import("./views/Attendance/open-mark-attendance.jsx")
);
import { SidebarProvider } from "./components/Sidebar/SidebarContext.jsx";
import OpenMarkNodeAttendance from "./views/Attendance/open-mark-node-attendance.jsx";

const token = sessionStorage.getItem("authToken");
const isAuthenticated = !!token; 

const PreventNavigation = () => {
  const navigate = useNavigate();
  useEffect(() => {
    window.history.pushState(null, "", window.location.href);
    const handlePopState = () => {
      window.history.pushState(null, "", window.location.href);
      alert("Navigation is disabled!");
    };
    window.addEventListener("popstate", handlePopState);
    return () => {
      window.removeEventListener("popstate", handlePopState);
    };
  }, [navigate]);
  return null;
};
const App = () => {
  
  return (
           <SpeechProvider>
    <SidebarProvider>
  <Router basename="/">
    <PreventNavigation />
       {/* Speech RX */}
      <SpeechToggle />
      <PageReader />
    <Routes>
      {/* 👇 Redirect to /admin if already logged in */}
      <Route path="/" element={ isAuthenticated ? <Navigate to="/admin" replace /> : <HomePage /> }/>
      {/* Public routes */}
      <Route path="/auth/login" element={ isAuthenticated ? <Navigate to="/admin" replace /> : <Login />}/>
      <Route path="/register" element={<Register />} />
      <Route path="/attendance" element={<OpenMarkAttendance />} />
      <Route path="/attendance-node" element={<OpenMarkNodeAttendance />} />
      <Route path="/privacy-policy" element={<TermsConditions />} />
      {/* 🔐 Protected Admin Routes */}
      {isAuthenticated ? (
        <Route path="/admin/*" element={<AdminLayout />}>
          <Route index element={<Index />} />
          <Route path="*" element={<Navigate to="/admin" replace />} />
        </Route>
      ) : null}
      {/* 🌐 Catch-all fallback */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  </Router>
</SidebarProvider>
  </SpeechProvider>

  );
};

export default App;
