import { useState, useEffect } from "react";
import axios from "axios";
import {
  Form,
  FormGroup,
  Label,
  Input,
  Button,
  CardFooter,
  Table,
  Container,
  Row,
  Col,
  CardBody,
  Card,
  CardHeader,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate  from "../../utils/formateDate.jsx";

const LeaveRuleRegistration = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  // State to manage form data
  const [formData, setFormData] = useState({
    empClass: "",
    type: "",
    maxCL: 0,
    maxEL: 0,
    maxRH: 0,
    maxMedicalLeave: 0,
    maxHPL: 0,
    maxMaternityLeave: 0,
    maxAdoptionLeave: 0,
    maxPaternityLeave: 0,
    maxCCL: 0,
    maxNPL: 0,
    maxEOL: 0,
    pAuthMaxCL: 0,
    pAuthMaxEL: 0,
    yealyCL: 0,
    yealyEL: 0,
    yealyRH: 0,
    yealyHPL: 0,
    pAuthMaxRH: 0,
    pAuthMaxMedicalLeave: 0,
    pAuthMaxHPL: 0,
    pAuthNPL: 0,
    pAuthCCL: 0,
    pAuthECL: 0,
    isVerified: 0,
  });
  const [classData, setClass] = useState([]);
  useEffect(() => {
    const fetchClass = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setClass(response.data);
        } else {
          alert("Class Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchClass();
  }, [token]);

  const [rules, setRules] = useState([]);
  useEffect(() => {
    const fetchLeaveRules = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/leave/rule/list`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setRules(response.data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchLeaveRules();
  }, [token]);



  const handleVerify = async (id) => {
    try {
      // console.log(id,"Getting rule Id for Verify");
      

      const response = await axios.put(`${endPoint}/api/leave/verify-rule/${id}`, {isVerfied:"1"}, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200) {
        SwalMessageAlert("The data has been Verified successfully.", "success");
        setTimeout(function() {
          window.location.reload();
      }, 3000);
      } else {
        SwalMessageAlert("Failed to update the data. Please try again.", "error");
      } 
    }
  catch (error) {
    console.error("An error occurred while updating the data:", error);
    SwalMessageAlert("Error", error.response?.data?.error || "An unexpected error occurred.", "error");
  }
};


const handleChange = (e) => {
  const { name, value } = e.target;
  setFormData({
    ...formData,
    [name]: value,
  });
};

const handleSubmit = async (e) => {
  e.target.disabled = true;

  setTimeout(() => {
    e.target.disabled = false;
  }, 5000);
  

  e.preventDefault();

  const body = {
    empClass: String(formData.empClass),
    type: String(formData.type),
    maxCL: String(formData.maxCL),
    maxEL: String(formData.maxEL),
    maxRH: String(formData.maxRH),
    maxMedicalLeave: String(formData.maxMedicalLeave),
    maxHPL: String(formData.maxHPL),
    maxMaternityLeave: String(formData.maxMaternityLeave),
    maxAdoptionLeave: String(formData.maxAdoptionLeave),
    maxPaternityLeave: String(formData.maxPaternityLeave),
    maxCCL: String(formData.maxCCL),
    maxNPL: String(formData.maxNPL),
    maxEOL: String(formData.maxEOL),
    pAuthMaxCL: String(formData.pAuthMaxCL),
    pAuthMaxEL: String(formData.pAuthMaxEL),
    pAuthMaxRH: String(formData.pAuthMaxRH),
    yealyCL: String(formData.yealyCL),
    yealyEL: String(formData.yealyEL),
    yealyRH: String(formData.yealyRH),
    yealyHPL: String(formData.yealyHPL),
    pAuthMaxMedicalLeave: String(formData.pAuthMaxMedicalLeave),
    pAuthMaxHPL: String(formData.pAuthMaxHPL),
    pAuthNPL: String(formData.pAuthNPL),
    pAuthCCL: String(formData.pAuthCCL),
    pAuthECL: String(formData.pAuthECL),
  };

  try {
    const response = await axios.post(
      `${endPoint}/api/leave/rule/add`,
      body,
      {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (response.status === 401) {
      SwalMessageAlert("This Class Rules Is Already Registerd.", "warning");
    } else if (response.status === 201) {
      SwalMessageAlert("Leave rule registered successfully.", "success");
      window.location.reload();
    }
  } catch (error) {
    if (error.response) {
      const statusCode = error.response.status;
      const backendMessage = error.response.data.error || "An error occurred";
      if (statusCode === 401) {
        SwalMessageAlert(backendMessage, "error"); // Alert: "This Class Rules Is Already Registered."
      } else if (statusCode === 400) {
        SwalMessageAlert(backendMessage, "error"); // Alert: "Employee class is required."
      } else {
        alert(`Error: ${backendMessage}`);
        SwalMessageAlert(`Error: ${backendMessage}`, "error"); // For other types of errors
      }
    } else {
      SwalMessageAlert(
        "An unexpected error occurred. Please try again.",
        "error"
      );
    }
    console.error("Error registering leave rule:", error);
  }
};

return (
  <>
    <Header />
    <Container className="mt--7" fluid>
      <Row>
        <Col>
          <Card className="bg-secondary shadow">
            <CardHeader className="bg-white border-0">
              <Row className="align-items-center">
                <Col xs="8">
                  <h3 className="mb-0">LEAVE RULE REGISTRATION</h3>
                </Col>
              </Row>
            </CardHeader>
            <CardBody>
              <Form >
                {/* Row 1 */}
                <hr />
                <h2 className="text-primary mt--4 mb--4">
                  Select Category <span className="text-danger">*</span>
                </h2>
                <hr />
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="empClass">Employee Class</Label>
                      <Input
                        type="select"
                        name="empClass"
                        id="empClass"
                        value={formData.empClass}
                        onChange={handleChange}
                        required
                      >
                        <option value="">Select Class</option>
                        {classData &&
                          classData.length > 0 &&
                          classData.map((type, index) => (
                            <option key={index} value={type._id}>
                              {type.className}
                            </option>
                          ))}   
                      </Input>
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="type">Work Type</Label>
                      <Input
                        type="select"
                        name="type"
                        id="type"
                        value={formData.type}
                        onChange={handleChange}
                        required
                      >
                        <option value="">Select Type</option>
                        <option value="TEACHING">Teaching</option>
                        <option value="NON TEACHING">Non-Teaching</option>
                      </Input>
                    </FormGroup>
                  </Col>
                </Row>
                <hr />
                <h2 className="text-primary mt--4 mb--4">Max In All Time</h2>
                <hr />
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxCL">Max Casual Leave (CL)</Label>
                      <Input
                        type="number"
                        name="maxCL"
                        value={formData.maxCL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxEL">Max Earned Leave (EL)</Label>
                      <Input
                        type="number"
                        name="maxEL"
                        value={formData.maxEL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxRH">Max Optional Leave (OL)</Label>
                      <Input
                        type="number"
                        name="maxRH"
                        value={formData.maxRH}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  {/* Row 2 */}
                </Row>
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxMedicalLeave">Max Medical Leave</Label>
                      <Input
                        type="number"
                        name="maxMedicalLeave"
                        value={formData.maxMedicalLeave}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxHPL">Max Half-Pay Leave (HPL)</Label>
                      <Input
                        type="number"
                        name="maxHPL"
                        value={formData.maxHPL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxMaternityLeave">
                        Max Maternity Leave
                      </Label>
                      <Input
                        type="number"
                        name="maxMaternityLeave"
                        value={formData.maxMaternityLeave}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxAdoptionLeave">Max Adoption Leave</Label>
                      <Input
                        type="number"
                        name="maxAdoptionLeave"
                        value={formData.maxAdoptionLeave}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                {/* Row 3 */}
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxPaternityLeave">
                        Max Paternity Leave
                      </Label>
                      <Input
                        type="number"
                        name="maxPaternityLeave"
                        value={formData.maxPaternityLeave}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxCCL">Max Child Care Leave (CCL)</Label>
                      <Input
                        type="number"
                        name="maxCCL"
                        value={formData.maxCCL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxNPL">Max No-Pay Leave (NPL)</Label>
                      <Input
                        type="number"
                        name="maxNPL"
                        value={formData.maxNPL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="maxEOL">
                        Max Extraordinary Leave (EOL)
                      </Label>
                      <Input
                        type="number"
                        name="maxEOL"
                        value={formData.maxEOL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                <hr />
                <h2 className="text-primary mt--4 mb--4">
                  Principal Authorized
                </h2>
                <hr />

                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthMaxCL">
                        Prinicpal Authorized Max CL
                      </Label>
                      <Input
                        type="number"
                        name="pAuthMaxCL"
                        value={formData.pAuthMaxCL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthMaxEL">
                        Prinicpal Authorized Max EL
                      </Label>
                      <Input
                        type="number"
                        name="pAuthMaxEL"
                        value={formData.pAuthMaxEL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthMaxRH">
                        Prinicpal Authorized Max OL
                      </Label>
                      <Input
                        type="number"
                        name="pAuthMaxRH"
                        value={formData.pAuthMaxRH}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthMaxMedicalLeave">
                        Prinicpal Authorized Max Medical Leave
                      </Label>
                      <Input
                        type="number"
                        name="pAuthMaxMedicalLeave"
                        value={formData.pAuthMaxMedicalLeave}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                {/* Row 5 */}
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthMaxHPL">
                        Prinicpal Authorized Max HPL
                      </Label>
                      <Input
                        type="number"
                        name="pAuthMaxHPL"
                        value={formData.pAuthMaxHPL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthNPL">Prinicpal Authorized NPL</Label>
                      <Input
                        type="number"
                        name="pAuthNPL"
                        value={formData.pAuthNPL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthCCL">Prinicpal Authorized CCL</Label>
                      <Input
                        type="number"
                        name="pAuthCCL"
                        value={formData.pAuthCCL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="pAuthECL">Prinicpal Authorized EOL</Label>
                      <Input
                        type="number"
                        name="pAuthECL"
                        value={formData.pAuthECL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                <hr />
                <h2 className="text-primary mt--4 mb--4">
                  Senctioned yearly
                </h2>
                <hr />
                <Row>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="yealyCL">Yearly Casual Leave</Label>
                      <Input
                        type="number"
                        name="yealyCL"
                        value={formData.yealyCL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="yealyEL">Yearly Earned Leave</Label>
                      <Input
                        type="number"
                        name="yealyEL"
                        value={formData.yealyEL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="yealyRH">Yearly Optional Leave</Label>
                      <Input
                        type="number"
                        name="yealyRH"
                        value={formData.yealyRH}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                  <Col md={3}>
                    <FormGroup>
                      <Label for="yealyHPL">Yearly Half Pay Leave</Label>
                      <Input
                        type="number"
                        name="yealyHPL"
                        value={formData.yealyHPL}
                        onChange={handleChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>

                {/* Row 6 */}

                <Button onClick={handleSubmit} color="primary">
                  Submit
                </Button>
              </Form>
            </CardBody>
          </Card>
        </Col>
      </Row>
      <Row className="mt-5">
        <Col>
          <Card className="bg-secondary shadow">
            <CardHeader className="bg-white border-0">
              <Row className="align-items-center">
                <Col xs="8">
                  <h3 className="mb-0">LEAVE RULES LIST</h3>
                </Col>
              </Row>
            </CardHeader>
            <CardBody>
              <Table striped bordered hover responsive>
                <thead>
                  <tr>
                    <th>SNo</th>
                    <th>Employee Class</th>
                    <th>Max Leave Limits</th>
                    <th>Max Principal Auth</th>
                    <th>Updated And Created</th>
                    <th>Verification</th>
                    <th>Edit</th>
                  </tr>
                </thead>
                <tbody>
                  {rules && rules.length > 0 ? (
                    rules.map((rule, index) => (
                      <tr key={index}>
                        <td>{index + 1}</td>
                        <td>
                          <span>
                            {
                              classData.find((a) => String(a._id) === String(rule.empClass))?.className
                            }
                          </span>
                          <br />
                          <span>{rule.type}</span>
                        </td>
                        <td>
                          {" "}
                          <span style={{ fontSize: "20", color: "red" }}>
                            Max Casual Leave - {rule.maxCL}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "blue" }}>
                            Max Earned Leave - {rule.maxEL}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "green" }}>
                            Max Optional Leave - {rule.maxRH}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "orange" }}>
                            Max Medical Leave - {rule.maxMedicalLeave}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "black" }}>
                            Max Half Pay Leave - {rule.maxHPL}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "purple" }}>
                            Max Maternity Leave - {rule.maxMaternityLeave}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "darkblue" }}>
                            Max Adoptional Leave - {rule.maxAdoptionLeave}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "brown" }}>
                            Max Paternity Leave - {rule.maxPaternityLeave}
                          </span>
                          <br />
                          <span
                            style={{ fontSize: "20", color: "darkgreen" }}
                          >
                            Max Child Care Leave - {rule.maxCCL}
                          </span>
                          <br />
                          <span
                            style={{ fontSize: "20", color: "darkred  " }}
                          >
                            Max Extraordinary Leave - {rule.maxEOL}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "magenta" }}>
                            Max No-Pay Leave- {rule.maxNPL}
                          </span>
                          <br />
                        </td>
                        <td>
                          {" "}
                          <span style={{ fontSize: "20", color: "red" }}>
                            {" "}
                            Casual Leave - {rule.pAuthMaxCL}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "blue" }}>
                            {" "}
                            Earned Leave - {rule.pAuthMaxEL}
                          </span>{" "}
                          <br />
                          <span style={{ fontSize: "20", color: "green" }}>
                            {" "}
                            Optional Leave - {rule.pAuthMaxRH}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "orange" }}>
                            {" "}
                            Medical Leave - {rule.pAuthMaxMedicalLeave}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "purple" }}>
                            {" "}
                            Half Pay Leave - {rule.pAuthMaxHPL}
                          </span>{" "}
                          <br />
                          <span
                            style={{ fontSize: "20", color: "darkgreen" }}
                          >
                            {" "}
                            Child Care Leave - {rule.pAuthCCL}
                          </span>
                          <br />
                          <span
                            style={{ fontSize: "20", color: "darkred  " }}
                          >
                            {" "}
                            Extraordinary Leave - {rule.pAuthECL}
                          </span>
                          <br />
                          <span style={{ fontSize: "20", color: "magenta" }}>
                            {" "}
                            No-Pay Leave- {rule.pAuthNPL}
                          </span>
                          <br />
                        </td>
                        <td>
                          <span style={{ color: "darkgreen" }}>
                            Last Updated -{" "}
                            {formatDate(rule.updatedAt)}
                          </span>
                          <br />
                          <span style={{ color: "darkblue" }}>
                            Created Date -{" "}
                            {formatDate(rule.createdAt)}
                          </span>
                          <br />
                        </td>

                        <td>
                          {rule.isVerfied === 1 ? (
                            <span
                              style={{
                                backgroundColor: "green",
                                color: "white",
                                padding: "5px 10px",
                                borderRadius: "8px",
                                fontWeight: "bold",
                              }}
                            >
                              Verified
                            </span>
                          ) : (
                            <button
                              style={{
                                backgroundColor: "darkorange",
                                color: "white",
                                padding: "5px 10px",
                                border: "none",
                                borderRadius: "8px",
                                cursor: "pointer",
                                fontWeight: "bold",
                              }}
                              onClick={()=>handleVerify(rule._id)} // Pass the function reference here
                            >
                              Verify
                            </button>
                          )}
                        </td>
                        <td>
                          <button
                            style={{
                              backgroundColor: "green",
                              color: "white",
                              padding: "5px 10px",
                              border: "none",
                              borderRadius: "8px",
                              cursor: "pointer",
                              fontWeight: "bold",
                            }}
                            onClick={() =>
                              (window.location.href = `/admin/update-Rules/${rule._id}`)
                            }
                          >
                            Edit
                          </button>
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="6" className="text-center">
                        No data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </CardBody>
            <CardFooter></CardFooter>
          </Card>
        </Col>
      </Row>
    </Container>
  </>
);
};

export default LeaveRuleRegistration;
