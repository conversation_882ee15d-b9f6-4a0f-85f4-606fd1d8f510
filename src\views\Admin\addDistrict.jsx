import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON><PERSON>,
  Card<PERSON>ooter,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Table,
  Pagination,
  PaginationItem,
  PaginationLink,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
const District = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredDistrict, setFiltereDistrict] = useState([]);

  const [formData, setFormData] = useState({
    sName: "",
    districtName: "",
    districtNameEng: "",
    LGDCode: "",
    division: "",
  });
  const [district, setDistrict] = useState([]);
  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          //   // console.log(response.data);

          setDistrict(response.data);
          setFiltereDistrict(response.data);
        } else {
          alert("Failed to District  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDistrict();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  useEffect(() => {
    const filteredItems = district.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFiltereDistrict(filteredItems);
    setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, district]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const fetchDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          //   // console.log(response.data);

          setDivision(response.data);
        } else {
          alert("Failed to Division  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDivision();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const handleSubmit = async (e) => {

    e.preventDefault();

    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.sName,
      formData.districtName,
      formData.districtNameEng,
      formData.LGDCode,
      formData.division,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    


    try {
      const body = {
        sName: String(formData.sName),
        districtName: String(formData.districtName),
        districtNameEng: String(formData.districtNameEng),
        LGDCode: String(formData.LGDCode),
        division: String(formData.division),
      };
      const response = await axios.post(
        `${endPoint}/api/district/add-district`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
          sName: "",
          districtName: "",
          districtNameEng: "",
          LGDCode: "",
          division: "",
        });
        window.location.reload();
      } else {
        alert("District Already Exists.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred while submitting the form:");
    }
  };

  // Handle Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page for pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredDistrict.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(district.length / itemsPerPage);
  const handleDistrictPageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Add District</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-division"
                          >
                            Select Division
                          </label>
                          <Input
                            name="division"
                            id="input-division"
                            type="select"
                            value={formData.division}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Division</option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option key={index} value={type.divisionCode}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-sName"
                          >
                            Short Name
                          </label>
                          <Input
                            name="sName"
                            id="input-sName"
                            placeholder="District Short Name"
                            type="text"
                            value={formData.sName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-districtName"
                          >
                            Name in Hindi
                          </label>
                          <Input
                            name="districtName"
                            id="input-districtName"
                            placeholder="District Name Hindi"
                            type="text"
                            value={formData.districtName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-districtNameEng"
                          >
                            Name in English
                          </label>
                          <Input
                            name="districtNameEng"
                            id="input-districtNameEng"
                            placeholder="District Name in English"
                            type="text"
                            value={formData.districtNameEng}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-LGDCode"
                          >
                            LGD Code
                          </label>
                          <Input
                            name="LGDCode"
                            id="input-LGDCode"
                            placeholder="LGD Code"
                            type="number"
                            autoComplete="pope"
                            value={formData.LGDCode}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex">
                <Col md={9}><h3 className="mb-0">District List</h3></Col>
                <Col md={3}>
                  <Input
                    type="text"
                    placeholder="Search District.."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ marginBottom: "10px" }}
                  />
                </Col>
              </CardHeader>
              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                  <th scope="col">Action</th>
                    <th scope="col">Short Name</th>
                    <th scope="col">Name</th>
                    <th scope="col">Name in Hindi</th>
                    <th scope="col">LGD Code</th>
                    <th scope="col">Division</th>
                   
                  </tr>
                </thead>
                <tbody>
                  {district &&
                    district.length > 0 &&
                    currentItems.map((district, index) => (
                      <tr key={index}>
                        <td>
                          <Link to={`/admin/update-district/${district._id}`}>
                            <button className="btn btn-warning btn-sm">
                              Edit
                            </button>
                          </Link>
                        </td>
                        <td>{district.sName}</td>
                        <td>{district.districtName}</td>
                        <td>{district.districtNameEng}</td>
                        <td>{district.LGDCode}</td>
                        <td>
                          {division &&
                            division.length > 0 &&
                            division.find(
                              (a) =>
                                String(a.divisionCode) ===
                                String(district.division)
                            )?.name}
                        </td>
                        
                      </tr>
                    ))}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <nav aria-label="...">
                  {/* Pagination Component */}
                  <Pagination className="pagination justify-content-end">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i} active={i + 1 === currentPage}>
                        <PaginationLink
                          onClick={() => handleDistrictPageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default District;
