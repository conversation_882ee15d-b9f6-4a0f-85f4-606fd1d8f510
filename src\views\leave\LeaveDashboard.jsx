import { useEffect, useState } from "react";
import { FaArrowRight } from "react-icons/fa";
import { CircularProgressbar } from "react-circular-progressbar";
import "react-circular-progressbar/dist/styles.css";
import Header from "../../components/Headers/Header.jsx";
import MyCalendar from "./Calander";
// import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx"

//#region reactstrap components
import {
  Button,
  Card, InputGroupText,
  CardHeader,
  CardBody,
  Progress,
  Table,
  Container,
  Row,
  Col,
  CardTitle,
  Label,
  InputGroup,
  InputGroupAddon,

  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  FormGroup,
  Input,
  Form,
} from "reactstrap";

import axios from "axios";
import NavImage from "../../assets/img/theme/user-icon.png";
import { Link } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";


const LeaveDashboard = () => {

  const userType = sessionStorage.getItem("type");
  const userType2 = sessionStorage.getItem("userType");

  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const [isTribal, setIsTribal] = useState(false);


  const id = sessionStorage.getItem("id");
  const [application, setApplication] = useState([]);
  const [user, setUser] = useState([]);
  const [clgInfo, setClgInfo] = useState([]);
  const [leaveRules, setLeaveRules] = useState([]);

  const [pendingJoining, setPendingJoining] = useState([]);

  const [pendingForYourAction, setPendingForYourAction] = useState([]);
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [designation, setDesignation] = useState([]);

  const [updateFieldEmployee, setUpdateFieldEmployee] = useState(false);
  const toggleUpdateFieldModalEmployee = () => setUpdateFieldEmployee(!updateFieldEmployee);

  const [employeeForm, setEmployeeForm] = useState({
    gender: "",
    title: "",
  });


  useEffect(() => {
    if (userType2 === "Employee" && userType === "4") {
      const getEmployeeData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/employee/get-employee/${id}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data;
            if (data.title === undefined) {
              // console.log(data, "Getting Employee");
              setUpdateFieldEmployee(true);
              // console.log(updateFieldEmployee, "Getting updateFieldEmployee ");

            }
          } else {
            SwalMessageAlert("Failed to load Employee data", "error");
          }
        } catch (error) {
          console.error("Error fetching Employee data:", error);
          SwalMessageAlert("Failed to load Employee data.", "error");
        }
      };
      getEmployeeData();
    }
  }, [id, endPoint, token]);

  const handleEmployeeFieldUpdateSubmit = async (e) => {
    e.preventDefault();
    try {
      const body = {
        gender: employeeForm.gender,
        title: employeeForm.title,
      };
      const response = await axios.put(
        `${endPoint}/api/employee/update-field/${id}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("Employee Updated Successfully.", "success");
        toggleUpdateFieldModalEmployee(false);
        setTimeout(() => {
          window.location.reload();
        }, 3000);
      } else {
        SwalMessageAlert("Updating for Employee failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };




  // // console.log(designationName,designation);


  useEffect(() => {
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/designation/getAll`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setDesignation(response.data);
        } else {
          alert("Designation Not Found.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchDesignation();
  }, [user]);

  // console.log(leaveRules, "Getting Leave Rules");


  useEffect(() => {
    const fetchCollegeInfo = async () => {
      // console.log(user.college, "Getting College in API");


      if (user && user.college) { // Check if user and college are defined
        // console.log(user.college, "Getting College in API");


        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-college/${user.college}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setClgInfo(response.data);
          } else {
            alert("College Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    };
    fetchCollegeInfo();
  }, [user]);

  useEffect(() => {
    const fetchRuleInfo = async () => {
      // console.log({ user }, 'USER')
      if (user && user.classData && user.workType) { // Check if user, college, and user ID are defined
        try {
          const response = await axios.get(
            `${endPoint}/api/leave/rule/listbyclass/${user.classData}/${user.workType}`, // Adjusted endpoint
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setLeaveRules(response.data);
          } else {
            alert("Rules Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    };
    fetchRuleInfo();
  }, [user]);



  useEffect(() => {
    const fetchApplications = async () => {
      try {
        if (user && user.college) {
          const response = await axios.get(
            `${endPoint}/api/leave/employees_leave/${user.college}`,
            {
              headers: {
                'Content-Type': 'application/json',
                'web-url': window.location.href,
                'Authorization': `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            const data = response.data.reverse(); // Reverse the fetched data
            const PendingForActionPrinciple = data.filter(
              (leaves) =>
                leaves.leaveStatus === 1 ||
                (leaves.leaveStatus === 6 &&
                  !(leaves.designation === 'UG Principal' || leaves.designation === 'PG Principal'))
            );
            setPendingForYourAction(PendingForActionPrinciple);
          } else {
            alert('Data Not Fetched.');
          }
        }
      } catch (error) {
        console.error('An error occurred while Getting Data:', error);
        alert('An error occurred. Please try again later.');
      }
    };

    fetchApplications(); // Call the async function
  }, [user, endPoint, token]); // Dependencies


  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/applied_Leaves/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setApplication(response.data);
          // window.location.replace("admin/Dashboard");
        } else {
          alert("Data Not Fetched.");

        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchApplications();
  }, []);

  const [pendingTotal, setPendingTotal] = useState([]);

  const [lastThreeLeave, setLastThree] = useState([]);

  useEffect(() => {
    const calculatePendingLeaves = () => {
      const pendingTotal = application.filter(
        (leave) => leave.leaveStatus === 1 || leave.leaveStatus === 2
      );

      const joiningPending = application.filter((leave) => leave.leaveStatus === 3 && leave.isJoined === 0);

      const totalApprovedPreviousLeave = application.filter((leave) => leave.leaveStatus === 3);

      setLastThree(totalApprovedPreviousLeave);

      setPendingJoining(joiningPending);


      setPendingTotal(pendingTotal);
    };

    calculatePendingLeaves();
  }, [application]);






  const imgDirectorate = "";
  const imgPrincipal = "";
  // const imgApplicant = "";


  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/leaveBalance/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setIsTribal(response.data[0]?.isTribal)
          setLeaveBalance(response.data);
        } else {
          alert("Data Not Fetched.");
          // navigate('/auth/Register');
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
        // navigate('/auth/Register');
      }
    };
    fetchApplications();
  }, []);

  const pendingTotalNumber = pendingTotal.length;

  const pendingMl =
    pendingTotal && pendingTotal.filter((item) => item.leaveType === "Medical Leave" || item.leaveType === "Half Pay Leave");
  const pendingCL =
    pendingTotal && pendingTotal.filter((items) => items.leaveType === "Casual Leave");
  const pendingRH =
    pendingTotal && pendingTotal.filter((items) => items.leaveType === "Restricted Holiday");
  const pendingEL =
    pendingTotal && pendingTotal.filter((items) => items.leaveType === "Earned Leave");
  const pendingForJoiningEL =
    pendingTotal && pendingJoining.filter((item) => item.leaveType === "Earned Leave");
  const pendingForJoiningHPL =
    pendingTotal && pendingJoining.filter((item) => (item.leaveType === "Medical Leave" || item.leaveType === "Half Pay Leave"));


  const currentYear = new Date().getFullYear();

  const yearlyUsedEL = application && application.filter((item) => {
    const appliedYear = new Date(item.appliedDate).getFullYear();
    return item.leaveType === "Earned Leave" &&
      item.leaveStatus === 3 &&
      appliedYear === currentYear;
  });


  // const yearlyUsedRH = application && application.filter((item) => {
  //   const appliedYear = new Date(item.appliedDate).getFullYear();
  //   return item.leaveType === "Restricted Holiday" &&
  //     item.leaveStatus === 3 &&
  //     appliedYear === currentYear;
  // });

  // const yearlyUsedCL = application && application.filter((item) => {
  //   const appliedYear = new Date(item.appliedDate).getFullYear();
  //   return item.leaveType === "Casual Leave" &&
  //     item.leaveStatus === 3 &&
  //     appliedYear === currentYear;
  // });


  // const TotalYearlyUsedCL = yearlyUsedCL ? yearlyUsedCL.reduce((total, item) => {
  //   return total + (item.dayCount || 0);
  // }, 0) : 0;


  // const TotalYearlyUsedRH = yearlyUsedRH ? yearlyUsedRH.reduce((total, item) => {
  //   return total + (item.dayCount || 0);
  // }, 0) : 0;

  let TotalYearlyUsedRH = 0;
  let TotalYearlyUsedCL = 0;

  if (leaveBalance[0]?.isUpdated === 1) {
    TotalYearlyUsedRH = Number(leaveRules[0]?.yealyRH) - Number(leaveBalance[0]?.restrictedHoliday)
    TotalYearlyUsedCL = Number(leaveRules[0]?.yealyCL) - Number(leaveBalance[0]?.casualLeave)
  }





  // // console.log(leaveRules[0]?.yealyRH,"leaveRules.yealyRH");

  // // console.log(leaveBalance[0]?.restrictedHoliday,"leaveBalance[0]?.restrictedHoliday");

  // // console.log(TotalYearlyUsedRH,"TotalYearlyUsedRH");




  const TotalYearlyUsedEL = yearlyUsedEL ? yearlyUsedEL.reduce((total, item) => {
    return total + (item.dayCount || 0);
  }, 0) : 0;


  const yearlyUsedHPL = application && application.filter((item) => {
    const appliedYear = new Date(item.appliedDate).getFullYear();
    return (item.leaveType === "Half Pay Leave") &&
      item.leaveStatus === 3 &&
      appliedYear === currentYear;
  });

  const TotalYearlyUsedHPL = yearlyUsedHPL ? yearlyUsedHPL.reduce((total, item) => {
    return total + (item.dayCount || 0);
  }, 0) : 0;

  const yearlyUsedML = application && application.filter((item) => {
    const appliedYear = new Date(item.appliedDate).getFullYear();
    return (item.leaveType === "Medical Leave") &&
      item.leaveStatus === 3 &&
      appliedYear === currentYear;
  });

  const TotalYearlyUsedML = yearlyUsedML ? yearlyUsedML.reduce((total, item) => {
    return total + (item.dayCount || 0);
  }, 0) : 0;





  const totalHPLandML = TotalYearlyUsedHPL + TotalYearlyUsedML * 2;

  // console.log(yearlyUsedHPL, yearlyUsedML, totalHPLandML, "Getting Leave");

  const [employeeData, setEmployeeData] = useState({
    isPassChangeFirst: "",
  });




  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUser(data);
          setEmployeeData({
            ...data,
            isPassChangeFirst: data.isPassChangeFirst,
          });
        } else {
          alert("Data Not Fetched.");
          // navigate('/auth/Register');
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
        // navigate('/auth/Register');
      }
    };
    fetchEmployeeData();
  }, [token]);

  // Modal state
  const [modalPasswordChange, setModalPasswordChange] = useState(false);
  useEffect(() => {
    if (employeeData && employeeData.isPassChangeFirst === false) {
      setModalPasswordChange(true);
    }
  }, [employeeData.isPassChangeFirst]);

  const toggleModalPasswordChange = () =>
    setModalPasswordChange(!modalPasswordChange);

  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });

  const handleInputFieldChange = (e) => {
    const { name, value } = e.target;
    setEmployeeForm({ ...employeeForm, [name]: value });
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const { newPassword, confirmPassword } = formData;
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

    if (!passwordRegex.test(newPassword)) {
      alert(
        "Password must be at least 8 characters long and include at least one uppercase letter, one lowercase letter, one number, and one special character."
      );
      return;
    }

    if (newPassword !== confirmPassword) {
      alert("New Password and Confirm Password do not match.");
      return;
    }
    alert("Password is valid!");
    try {
      const body = {
        password: formData.newPassword,
      };
      const response = await axios.put(
        `${endPoint}/api/update-password?id=${id}&key=Employee`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        window.location.reload();
      } else {
        alert("Password Update Failed");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("Password Update Failed.");
    }
  };







  return (
    <>
      <Modal isOpen={updateFieldEmployee} toggle={toggleUpdateFieldModalEmployee}>
        <ModalHeader toggle={toggleUpdateFieldModalEmployee}>Update Information</ModalHeader>
        <ModalBody>
          <Form onSubmit={handleEmployeeFieldUpdateSubmit}>
            <Row>
              <Col lg="6" md="12">
                <FormGroup>
                  <Label for="title" className="form-control-label">
                    Title
                  </Label>
                  <InputGroup className="input-group-alternative mb-3">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i className="fas fa-user"></i>
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      id="title"
                      name="title"
                      type="select" // Change type to "select"
                      value={employeeForm.title}
                      onChange={handleInputFieldChange}
                    >
                      <option value="">-- Select Title --</option>
                      <option value="Mr.">Mr.</option>
                      <option value="Mrs.">Mrs.</option>
                      <option value="Ms.">Ms.</option>
                      <option value="Miss">Miss</option>
                      <option value="Dr.">Dr.</option>
                      <option value="Prof.">Prof.</option>
                      <option value="Hon.">Hon.</option>
                    </Input>

                  </InputGroup>
                </FormGroup>
              </Col>

              <Col lg="6" md="12">
                <FormGroup>
                  <Label for="gender" className="form-control-label">
                    Gender
                  </Label>
                  <InputGroup className="input-group-alternative mb-3">
                    <InputGroupAddon addonType="prepend">
                      <InputGroupText>
                        <i className="fas fa-male" style={{ marginRight: '5px' }}></i>
                        <i className="fas fa-female"></i>
                      </InputGroupText>
                    </InputGroupAddon>
                    <Input
                      id="gender"
                      name="gender"
                      type="select" // Change type to "select"
                      value={employeeForm.gender}
                      onChange={handleInputFieldChange}
                    >
                      <option value="">-- Select Gender --</option>
                      <option value="Male">Male</option>
                      <option value="Female">Female</option>
                      <option value="Other">Other</option>
                    </Input>
                  </InputGroup>
                </FormGroup>
              </Col>
            </Row>
            <ModalFooter>
              <Button color="primary" type="submit">Submit</Button>
              <Button color="secondary" onClick={toggleUpdateFieldModalEmployee}>Cancel</Button>
            </ModalFooter>
          </Form>
        </ModalBody>
      </Modal>
      <Header />
      {/* Page content */}
      <Container className="mt--9" fluid style={{ backgroundColor: "#effbff" }}>
        <Modal
          style={{ maxWidth: "800px", minWidth: "400px" }}
          isOpen={modalPasswordChange}
          toggle={toggleModalPasswordChange}
          backdrop="static" // Makes the backdrop static
          keyboard={false} // Prevents closing with the 'Escape' key
        >
          <ModalHeader>
            Change Password
          </ModalHeader>
          <Form onSubmit={handleSubmit}>
            <ModalBody>
              <div className="pl-lg-4">
                <Row>
                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-newPassword"
                      >
                        New Password
                      </label>
                      <Input
                        name="newPassword"
                        id="input-newPassword"
                        placeholder="Enter New Password"
                        type="password"
                        value={formData.newPassword}
                        onChange={handleInputChange}
                        autoComplete="nope" // Disable autocomplete
                        required
                      />
                    </FormGroup>
                  </Col>

                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-confirmPassword"
                      >
                        Confirm Password
                      </label>
                      <Input
                        name="confirmPassword"
                        id="input-confirmPassword"
                        placeholder="Enter Confirm Password"
                        type="password"
                        autoComplete="pope"
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        required
                      />
                    </FormGroup>
                  </Col>
                </Row>
              </div>
            </ModalBody>
            <ModalFooter>
              <Button color="primary" type="submit">
                Save
              </Button>
            </ModalFooter>
          </Form>
        </Modal>


        <Col className="mt-5" >
          <Card
            className="card-stats mb-6 mb-xl-0"
            style={{
              height: "100%",
              backgroundColor: "#b4ebff",
              boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
            }}
          >
            <CardBody>
              <Row>
                <Col>
                  <p
                    style={{
                      color: "black",
                      fontSize: "22px",
                      marginBottom: "0px",
                      fontWeight: "bold",
                    }}
                  >
                    Available Leaves    {leaveBalance[0]?.isUpdated === 0 && <span style={{ fontSize: "14px", color: "red" }}>*(Your Leave Balance is Not updated by Higher Authority)</span>}
                  </p>
                </Col>
              </Row>
              <Row>
                <Col lg="6" xl="3">
                  <Card
                    className="card-stats mb-4 mb-xl-0"
                    style={{
                      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                      border: "1px solid black",
                    }}
                  >
                    <CardBody>
                      <Row>
                        <Col sm="9">
                          <CardTitle
                            tag="h5"
                            className="text-uppercase font-weight-bolder text-black mb-0"
                          >
                            Optional Holiday
                          </CardTitle>

                          <span className="h1 font-weight-bolder mb-0">
                            {leaveBalance[0]?.restrictedHoliday}
                          </span>
                        </Col>
                        <Col sm="3">
                          <div className="icon icon-shape bg-success text-white rounded-circle shadow">
                            <i className="ni ni-calendar-grid-58 text-black" />
                          </div>
                        </Col>
                      </Row>
                      <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                        <span
                          className="text-success mr-2 "
                          style={{ fontSize: "22px", fontWeight: "700" }}
                        >
                          {TotalYearlyUsedRH}
                        </span>{" "}
                        <span className="text-nowrap">Used in Year</span>
                      </p>
                    </CardBody>
                  </Card>
                </Col>
                <Col lg="6" xl="3">
                  <Card
                    className="card-stats mb-4 mb-xl-0"
                    style={{
                      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                      border: "1px solid black",
                    }}
                  >
                    <CardBody>
                      <Row>
                        <Col sm="9">
                          <CardTitle
                            tag="h5"
                            className="text-uppercase font-weight-bolder text-black mb-0"
                          >
                            Casual Leave
                          </CardTitle>
                          <span className="h1 font-weight-bolder mb-0">
                            {leaveBalance[0]?.casualLeave}
                          </span>
                        </Col>
                        <Col sm="3">
                          <div className="icon icon-shape bg-purple text-white rounded-circle shadow">
                            <i className="ni ni-calendar-grid-58 text-black" />
                          </div>
                        </Col>
                      </Row>
                      <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                        <span
                          className="text-purple mr-2 "
                          style={{ fontSize: "22px", fontWeight: "700" }}
                        >
                          {TotalYearlyUsedCL}
                        </span>{" "}
                        <span className="text-nowrap">Used in Year</span>
                      </p>
                    </CardBody>
                  </Card>
                </Col>
                <Col lg="6" xl="3">
                  <Card
                    className="card-stats mb-4 mb-xl-0"
                    style={{
                      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                      border: "1px solid black",
                    }}
                  >
                    <CardBody>
                      <Row>
                        <Col sm="9">
                          <CardTitle
                            tag="h5"
                            className="text-uppercase font-weight-bolder text-black mb-0"
                          >
                            Earned Leave
                          </CardTitle>
                          <span className="h1 font-weight-bolder mb-0">
                            {leaveBalance[0]?.earnedLeave}
                          </span>
                        </Col>
                        <Col sm="3">
                          <div
                            style={{ background: "#ff9100" }}
                            className="icon icon-shape  text-white rounded-circle shadow"
                          >
                            <i className="ni ni-calendar-grid-58 text-black" />
                          </div>
                        </Col>
                      </Row>
                      <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                        <span
                          className="text-orange mr-2 "
                          style={{ fontSize: "22px", fontWeight: "700" }}
                        >
                          {TotalYearlyUsedEL}
                        </span>{" "}
                        <span className="text-nowrap">Used In Year</span>
                      </p>
                    </CardBody>
                  </Card>
                </Col>
                <Col lg="6" xl="3">
                  <Card
                    className="card-stats mb-4 mb-xl-0"
                    style={{
                      boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                      border: "1px solid black",
                    }}
                  >
                    <CardBody>
                      <Row>
                        <Col sm="9">
                          <CardTitle
                            tag="h5"
                            className="text-uppercase font-weight-bolder text-black mb-0"
                          >
                            Half Pay Leave
                          </CardTitle>
                          <span className="h1 font-weight-bolder mb-0">
                            {leaveBalance[0]?.halfPayLeave}
                          </span>
                        </Col>
                        <Col sm="3">
                          <div className="icon icon-shape bg-info text-white rounded-circle shadow">
                            <i className="ni ni-calendar-grid-58 text-black" />
                          </div>
                        </Col>
                      </Row>
                      <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                        <span
                          className="text-info mr-2 "
                          style={{ fontSize: "22px", fontWeight: "700" }}
                        >
                          {totalHPLandML}
                        </span>{" "}
                        <span className="text-nowrap">Used In Year</span>
                      </p>
                    </CardBody>
                  </Card>
                </Col>
              </Row>

              {isTribal && isTribal === true && <>
                <Row className="mt-3">
                  <Col lg="6" >
                    <Card
                      className="card-stats mb-4 mb-xl-0"
                      style={{
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                        border: "1px solid black",
                      }}
                    >
                      <CardBody>
                        <Row>
                          <Col sm="9">
                            <CardTitle
                              tag="h5"
                              className="text-uppercase font-weight-bolder text-black mb-0"
                            >
                              Tribal Casual Leave
                            </CardTitle>
                            <span className="h1 font-weight-bolder mb-0">
                              {leaveBalance[0]?.tribalCasualLeave}
                            </span>
                          </Col>
                          <Col sm="3">
                            <div className="icon icon-shape bg-success text-white rounded-circle shadow">
                            <i className="fas fa-tree"></i>
                            </div>
                          </Col>
                        </Row>
                        <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                          <span
                            className="text-info mr-2 "
                            style={{ fontSize: "22px", fontWeight: "700" }}
                          >
                            {/* {totalHPLandML} */}
                          </span>{" "}
                          <span className="text-nowrap">Used In Year</span>
                        </p>
                      </CardBody>
                    </Card>
                  </Col>
                  <Col lg="6" >
                    <Card
                      className="card-stats mb-4 mb-xl-0"
                      style={{
                        boxShadow: "0 4px 8px rgba(0, 0, 0, 0.6)",
                        border: "1px solid black",
                      }}
                    >
                      <CardBody>
                        <Row>
                          <Col sm="9">
                            <CardTitle
                              tag="h5"
                              className="text-uppercase font-weight-bolder text-black mb-0"
                            >
                              Tribal Earned Leave
                            </CardTitle>
                            <span className="h1 font-weight-bolder mb-0">
                              {leaveBalance[0]?.tribalEarnedLeave}
                            </span>
                          </Col>
                          <Col sm="3">
                            <div className="icon icon-shape bg-success text-white rounded-circle shadow">
                            <i className="fas fa-tree"></i>
                            </div>
                          </Col>
                        </Row>
                        <p className="mt-3 mb-0 text-black font-weight-bold text-bg">
                          <span
                            className="text-info mr-2 "
                            style={{ fontSize: "22px", fontWeight: "700" }}
                          >
                            {/* {totalHPLandML} */}
                          </span>{" "}
                          <span className="text-nowrap">Used In Year</span>
                        </p>
                      </CardBody>
                    </Card>
                  </Col>
                </Row> </>}
            </CardBody>
          </Card>
        </Col>

        <br />
        <br />
        <br />
        <Row
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "stretch",
          }}
        >
          <Col className="mb-5 mb-xl-0" xl="6">
            <Card
              style={{
                backgroundColor: "#f8fdff",
                boxShadow: "0 4px 12px rgba(0, 0, 0, 0.8)",
              }}
              className="bg-white"
            >
              <CardHeader className="bg-transparent">
                <Row className="align-items-center">
                  <div className="col">
                    <h2 className="text-black font-weight-bolder  mb-0">
                      Leave Used in Year {new Date().getFullYear()}
                    </h2>
                  </div>
                </Row>
              </CardHeader>
              <CardBody
                style={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  flexDirection: "column",
                }}
              >
                {/* Circular Progress Bars */}
                <Row className="pt-2 justify-content-center">
                  <Col
                    md="6"
                    xs="12"
                    className="d-flex justify-content-center"
                    style={{ padding: "20px" }}
                  >
                    <div
                      style={{ width: "100%", maxWidth: 170, height: "auto" }}
                    >
                      <CircularProgressbar
                        value={((TotalYearlyUsedRH) / (leaveRules[0]?.yealyRH)) * 100}
                        text={`OL - ${TotalYearlyUsedRH}/${leaveRules[0]?.yealyRH}`}
                        styles={{
                          path: {
                            stroke: "#0ab35c", // Blue color for 50%
                            strokeWidth: 8, // Set the path width
                          },
                          text: {
                            fill: "#0ab35c", // Text color
                            fontSize: "12px", // Text size
                            fontWeight: "bold",
                            fontFamily: "Times New Roman, serif",
                          },
                          trail: {
                            stroke: "#d6d6d6", // Trail color
                            strokeWidth: 8, // Adjust trail size if needed
                          },
                        }}
                      />
                    </div>
                  </Col>
                  <Col
                    md="6"
                    xs="12"
                    className="d-flex justify-content-center"
                    style={{ padding: "20px" }}
                  >
                    <div
                      style={{ width: "100%", maxWidth: 170, height: "auto" }}
                    >
                      <CircularProgressbar
                        value={((TotalYearlyUsedCL / leaveRules[0]?.yealyCL)) * 100}
                        text={`CL - ${TotalYearlyUsedCL}/${leaveRules[0]?.yealyCL}`}
                        styles={{
                          path: {
                            stroke: "#7d09cc", // Blue color for 50%
                            strokeWidth: 8, // Set the path width
                          },
                          text: {
                            fill: "#7d09cc", // Text color
                            fontSize: "12px", // Text size
                            fontWeight: "bold",
                            fontFamily: "Times New Roman, serif",
                          },
                          trail: {
                            stroke: "#d6d6d6", // Trail color
                            strokeWidth: 8, // Adjust trail size if needed
                          },
                        }}
                      />
                    </div>
                  </Col>
                </Row>
                <Row style={{ justifyContent: "space-evenly" }}>
                  <Col
                    md="6"
                    xs="12"
                    className="d-flex justify-content-center"
                    style={{ padding: "20px" }}
                  >
                    <div
                      style={{ width: "100%", maxWidth: 170, height: "auto" }}
                    >
                      <CircularProgressbar
                        value={(TotalYearlyUsedEL / leaveRules[0]?.yealyEL) * 100}
                        text={`EL - ${TotalYearlyUsedEL}`}
                        styles={{
                          path: {
                            stroke: "#fda102", // Blue color for 50%
                            strokeWidth: 8, // Set the path width
                          },
                          text: {
                            fill: "#fda102", // Text color
                            fontSize: "10px", // Text size
                            fontWeight: "bold",
                            fontFamily: "Times New Roman, serif",
                          },
                          trail: {
                            stroke: "#d6d6d6", // Trail color
                            strokeWidth: 8, // Adjust trail size if needed
                          },
                        }}
                      />
                    </div>
                  </Col>

                  <Col
                    md="6"
                    xs="12"
                    className="d-flex justify-content-center"
                    style={{ padding: "20px" }}
                  >
                    <div
                      style={{ width: "100%", maxWidth: 170, height: "auto" }}
                    >
                      <CircularProgressbar
                        value={(((totalHPLandML) / leaveRules[0]?.yealyHPL)) * 100}
                        text={`HPL - ${totalHPLandML}`}
                        styles={{
                          path: {
                            stroke: "#2196F3", // Blue color for 50%
                            strokeWidth: 8, // Set the path width
                          },
                          text: {
                            fill: "#2196F3", // Text color
                            fontSize: "10px", // Text size
                            fontWeight: "bold",
                            fontFamily: "Times New Roman, serif",
                            whiteSpace: "pre-line",
                          },
                          trail: {
                            stroke: "#d6d6d6", // Trail color
                            strokeWidth: 8, // Adjust trail size if needed
                          },
                        }}
                      />
                    </div>
                  </Col>
                </Row>
              </CardBody>
            </Card>
          </Col>
          <Col xs="6">
            <Card
              className="mb-4 pb-4"
              style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
            >
              <CardHeader className="bg-transparent">
                <Row className="align-items-center">
                  <div className="col">
                    <h2 className="text-black font-weight-bolder  mb-3">
                      My Pending Applications
                    </h2>
                  </div>
                </Row>
              </CardHeader>
              <CardBody className="pt-2">
                <Row className="pt-3">
                  <Col style={{ borderRight: "1px solid gray" }} sm={2}>
                    <p
                      style={{
                        textAlign: "center",
                        color: "#0009ff",
                        fontFamily: "Times-new-Roman",
                        fontWeight: "bolder",
                        fontSize: "50px",
                      }}
                    >
                      {pendingTotalNumber}
                    </p>
                  </Col>
                  <Col sm={10} className="text-center">
                    <Row>
                      <Col>
                        <Progress
                          value={100}
                          color="success"
                          style={{ height: "26px" }}
                        >
                          {" "}
                          <span
                            style={{
                              fontSize: "16px",
                              fontWeight: "bolder",
                              fontFamily: "Times-new-Roman",
                            }}
                          >
                            {" "}
                            {pendingRH.length} OL
                          </span>
                        </Progress>
                      </Col>
                      <Col>
                        <Progress
                          value={100}
                          color="purple"
                          style={{ height: "26px" }}
                        >
                          {" "}
                          <span
                            style={{
                              fontSize: "16px",
                              fontWeight: "bolder",
                              fontFamily: "Times-new-Roman",
                            }}
                          >
                            {" "}
                            {pendingCL.length}CL
                          </span>
                        </Progress>
                      </Col>
                    </Row>
                    <Row className="mt-3">
                      <Col>
                        <Progress
                          value={100}
                          color="warning"
                          style={{ height: "26px" }}
                        >
                          {" "}
                          <span
                            style={{
                              fontSize: "16px",
                              fontWeight: "bolder",
                              fontFamily: "Times-new-Roman",
                            }}
                          >
                            {" "}
                            {pendingEL.length} EL
                          </span>
                        </Progress>
                      </Col>
                      <Col>
                        <Progress
                          value={100}
                          color="info"
                          style={{ height: "26px" }}
                        >
                          {" "}
                          <span
                            style={{
                              fontSize: "16px",
                              fontWeight: "bolder",
                              fontFamily: "Times-new-Roman",
                            }}
                          >
                            {" "}
                            {pendingMl.length} HPL
                          </span>
                        </Progress>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </CardBody>
            </Card>
            {userType === "4" && (
              <Card
                className="pb-4"
                style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
              >
                <CardHeader className="bg-transparent">
                  <Row className="align-items-center">
                    <div className="col">
                      <h2 className="text-black font-weight-bolder  mb-0">
                        Leave Joining Pending
                      </h2>
                    </div>
                  </Row>
                </CardHeader>
                <CardBody>
                  <Row className="pt-3">
                    <Col style={{ borderRight: "1px solid gray", }} sm={2}>
                      <p
                        style={{
                          textAlign: "center",
                          color: "#0009ff",
                          fontFamily: "Times-new-Roman",
                          fontWeight: "bolder",
                          fontSize: "50px",
                        }}
                      >
                        {pendingForJoiningHPL.length + pendingForJoiningEL.length}
                      </p>
                    </Col>
                    <Col sm={10} className="text-center" >
                      <Row>
                        <Col>
                          <Progress
                            value={100}
                            color="success"
                            style={{ height: "26px" }}
                          >
                            {" "}
                            <span
                              style={{
                                fontSize: "16px",
                                fontWeight: "bolder",
                                fontFamily: "Times-new-Roman",
                              }}
                            >
                              {pendingForJoiningHPL.length} Half Pay Leave
                            </span>
                          </Progress>
                        </Col>
                      </Row>
                      <Row className="mt-3">
                        <Col>
                          <Progress
                            value={100}
                            color="warning"
                            style={{ height: "26px" }}
                          >
                            {" "}
                            <span
                              style={{
                                fontSize: "16px",
                                fontWeight: "bolder",
                                fontFamily: "Times-new-Roman",
                              }}
                            >
                              {" "}
                              {pendingForJoiningEL.length} Earned Leave
                            </span>
                          </Progress>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </CardBody>
              </Card>
            )}

          </Col>
        </Row>
        <Row className="mt-4" >
          <Col xs={6}>
            {(userType === "4") && (
              <Row className="align-items-center">
                <Col>
                  <Card
                    style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)", height: "100%", }}
                    className="mt-1"
                  >
                    <CardHeader className="border-1 ">
                      <Row className="align-items-center">
                        <Col xs={12}>
                          <h2 className="text-black font-weight-bolder  mb-0">
                            Pending For Your Actions <span className="text-danger">({(designation.find((type) => type._id === user.designation)?.designation === 'UG Principal' || designation.find((type) => type._id === user.designation)?.designation === 'PG Principal') ? pendingForYourAction.length : "0"})</span><br /><span style={{ fontSize: "14px", color: "red" }}>{(designation.find((type) => type._id === user.designation)?.designation === 'UG Principal' || designation.find((type) => type._id === user.designation)?.designation === 'PG Principal') && "Note - action can be taken by Institute panel"}</span>
                          </h2>
                        </Col>
                        <div className="col text-right">
                          <Link to={`/admin/applicationForDesignation`}>
                            <Button
                              color="primary"
                              size="sm"
                            >
                              View All
                            </Button>
                          </Link>
                        </div>
                      </Row>
                    </CardHeader>

                    <Table className="align-items-center table-flush" responsive>
                      <thead className="thead-light">
                        <tr>
                          <th scope="col">S.N.</th>
                          <th scope="col">Applicant</th>
                          <th scope="col">Application ID</th>
                          <th scope="col">Applied <br /> Date</th>
                          <th scope="col">Details</th>
                        </tr>
                      </thead>
                      <tbody>
                        {pendingForYourAction && pendingForYourAction.length > 0 && (designation.find((type) => type._id === user.designation)?.designation === 'UG Principal' || designation.find((type) => type._id === user.designation)?.designation === 'PG Principal') && pendingForYourAction.slice(0, 3).map((item, index) => (
                          <tr key={index}>
                            <td>{index + 1}</td>
                            <td>{item.applicantName}</td>
                            <td>{item.applicationId}</td>
                            <td>{formatDate(item.appliedDate)}</td>
                            <td>
                              <Link to={`/admin/applicationForDesignation`}>
                                <Button
                                  color="primary"
                                  size="sm"
                                >
                                  Details
                                </Button>
                              </Link>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Card>
                </Col>
              </Row>
            )}

            {(userType === "4") && (
              <Row>
                <Col>
                  <Card
                    style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)", height: "100%" }}
                    className="mt-1"
                  >
                    <CardHeader className="border-1 ">
                      <Row className="align-items-center">
                        <div className="col">
                          <h2 className="text-black font-weight-bolder  mb-0">
                            My Recent Leave History
                          </h2>
                        </div>
                        <div className="col text-right">
                          <Link to={`/admin/leave-reports`}>
                            <Button
                              color="primary"
                              size="sm"
                            >
                              View All
                            </Button>
                          </Link>
                        </div>
                      </Row>
                    </CardHeader>

                    <Table className="align-items-center table-flush" responsive>
                      <thead className="thead-light">
                        <tr>
                          <th scope="col">Leave <br /> Type</th>
                          <th scope="col">Application <br /> ID</th>
                          <th scope="col">Applied <br /> Date</th>
                          <th scope="col">between <br /> Date</th>
                        </tr>
                      </thead>
                      <tbody>
                        {lastThreeLeave && lastThreeLeave.slice(0, 3).map((item, index) => (
                          <tr key={index}>
                            <td>{item.leaveType}</td>
                            <td>{item.applicationId}</td>
                            <td>{formatDate(item.appliedDate)}</td>
                            <td>({formatDate(item.fromDate)})<br />to ({formatDate(item.tillDate)})</td>

                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Card>
                </Col>
              </Row>
            )}
          </Col>
          <Col xs={6}>
            <Card
              style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
              className="mt-1"
            >
              <CardHeader className="bg-transparent">
                <Row className="align-items-center">
                  <div className="col">
                    <h2 className="text-black font-weight-bolder  mb-0">
                      Government Calendar
                    </h2>
                  </div>
                </Row>
              </CardHeader>
              <CardBody>
                <Row className="pt-3">
                  <Col style={{ borderRight: "1px solid gray" }}>
                    <MyCalendar style={{ backgroundColor: "white" }} />
                  </Col>
                </Row>
              </CardBody>
            </Card>
          </Col>
        </Row>
        <Row>

        </Row>
        <Row className="mt-5">
          {userType !== "1" && (
            <Col className="mb-5 mb-xl-0" xl="12">
              <Card
                style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
                className="mt-1"
              >
                <CardHeader className="border-1 ">
                  <Row className="align-items-center">
                    <div className="col">
                      <h2 className="text-black font-weight-bolder  mb-0">
                        Leave Work Flow
                      </h2>
                    </div>
                  </Row>
                </CardHeader>
                {userType === "4" && (
                  <CardBody>
                    <Row className="text-center">
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={NavImage}
                            alt="imgApplicant"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>{user.name}</h5>
                        </div>
                        <div className="mt-2">
                          <h3>{designation.find(
                            (type) => type._id === user.designation
                          )?.designation || "N/A"}</h3>
                        </div>
                      </Col>

                      {!(designation.find((type) => type._id === user.designation)?.designation === 'UG Principal' || designation.find((type) => type._id === user.designation)?.designation === 'PG Principal') && <><Col
                        sm={1}
                        className="justify-content-center"
                        style={{ paddingTop: "37px" }}
                      >
                        <FaArrowRight size={20} />
                      </Col>
                        <Col>
                          <div
                            className="rounded-circle shadow mx-auto"
                            style={{
                              width: "100px",
                              height: "100px",
                              overflow: "hidden",
                            }}
                          >
                            <img
                              src={NavImage}
                              alt="imgPrincipal"
                              style={{ width: "100%", height: "100%" }}
                            />
                          </div>
                          <div className="mt-2">
                            <h5>{clgInfo.contactPerson}</h5>
                          </div>
                          <div className="mt-2">
                            <h3>Principal</h3>
                          </div>
                        </Col></>}
                      <Col
                        sm={1}
                        className="justify-content-center"
                        style={{ paddingTop: "37px" }}
                      >
                        <FaArrowRight size={20} />
                      </Col>
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={NavImage}
                            alt="imgDirectorate"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>State Admin</h5>
                        </div>
                        <div className="mt-2">
                          <h3>Directorate</h3>
                        </div>
                      </Col>
                      <Col
                        sm={1}
                        className="justify-content-center"
                        style={{ paddingTop: "37px" }}
                      >
                        <FaArrowRight size={20} />
                      </Col>
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={NavImage}
                            alt="imgDirectorate"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>DDO</h5>
                        </div>
                        <div className="mt-2">
                          <h3>DDO</h3>
                        </div>
                      </Col>
                      <Col
                        sm={1}
                        className="justify-content-center"
                        style={{ paddingTop: "37px" }}
                      >
                        <FaArrowRight size={20} />
                      </Col>
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={NavImage}
                            alt="imgDirectorate"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>Dealing Clerk</h5>
                        </div>
                        <div className="mt-2">
                          <h3>Dealing Clerk</h3>
                        </div>
                      </Col>
                    </Row>
                  </CardBody>
                )}
                {userType === "3" && (
                  <CardBody>
                    <Row className="text-center">
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={imgPrincipal}
                            alt="imgPrincipal"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>{clgInfo.contactPerson}</h5>
                        </div>
                        <div className="mt-2">
                          <h3>Principal</h3>
                        </div>
                      </Col>
                      <Col
                        sm={1}
                        className="justify-content-center"
                        style={{ paddingTop: "37px" }}
                      >
                        <FaArrowRight size={20} />{" "}
                        {/* Right directional icon */}
                      </Col>
                      <Col>
                        <div
                          className="rounded-circle shadow mx-auto"
                          style={{
                            width: "100px",
                            height: "100px",
                            overflow: "hidden",
                          }}
                        >
                          <img
                            src={imgDirectorate}
                            alt="imgDirectorate"
                            style={{ width: "100%", height: "100%" }}
                          />
                        </div>
                        <div className="mt-2">
                          <h5>Dealing Clerk</h5>
                        </div>
                        <div className="mt-2">
                          <h3>Dealing Clerk</h3>
                        </div>
                      </Col>
                    </Row>
                  </CardBody>
                )}
              </Card>
            </Col>
          )}
          {/* {userType === "3" && (
            <Col xs={6}>
              <Card
                className="pb-4"
                style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
              >
                <CardHeader className="bg-transparent">
                  <Row className="align-items-center">
                    <div className="col">
                      <h2 className="text-black font-weight-bolder  mb-0">
                        Leave Joining Pending
                      </h2>
                    </div>
                  </Row>
                </CardHeader>
                <CardBody>
                  <Row className="pt-3">
                    <Col style={{ borderRight: "1px solid gray" }} sm={2}>
                      <p
                        style={{
                          textAlign: "center",
                          color: "#0009ff",
                          fontFamily: "Times-new-Roman",
                          fontWeight: "bolder",
                          fontSize: "50px",
                        }}
                      >
                        0
                      </p>
                    </Col>
                    <Col sm={10} className="text-center">
                      <Row>
                        <Col>
                          <Progress
                            value={100}
                            color="success"
                            style={{ height: "26px" }}
                          >
                            {" "}
                            <span
                              style={{
                                fontSize: "16px",
                                fontWeight: "bolder",
                                fontFamily: "Times-new-Roman",
                              }}
                            >
                              0 Half Pay Leave
                            </span>
                          </Progress>
                        </Col>
                      </Row>
                      <Row className="mt-3">
                        <Col>
                          <Progress
                            value={100}
                            color="warning"
                            style={{ height: "26px" }}
                          >
                            {" "}
                            <span
                              style={{
                                fontSize: "16px",
                                fontWeight: "bolder",
                                fontFamily: "Times-new-Roman",
                              }}
                            >
                              {" "}
                              0 Earned Leave
                            </span>
                          </Progress>
                        </Col>
                      </Row>
                    </Col>
                  </Row>
                </CardBody>
              </Card>
            </Col>
          )} */}
        </Row>
      </Container>
    </>
  );
};

export default LeaveDashboard;
