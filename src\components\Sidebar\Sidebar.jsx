import { useState, useEffect } from "react";
import { NavLink as NavLinkRRD, Link, useLocation } from "react-router-dom";
import { Collapse, Navbar, NavItem, NavLink, Nav, Navbar<PERSON><PERSON>, Container, <PERSON><PERSON> } from "reactstrap";
import loadable from "@loadable/component";
import logo from "../../assets/img/brand/CGGov.png";
import { useSidebar } from "../Sidebar/SidebarContext.jsx";
// Lazy load routes array
const LoadableRoutes = loadable(() => import("../../routers/routes.jsx"));

const Sidebar = (props) => {

  const { isSidebarVisible } = useSidebar();



  const [collapseOpen, setCollapseOpen] = useState(false);
  const [activeAccordion, setActiveAccordion] = useState(null);
  const [filteredRoutes, setFilteredRoutes] = useState([]);
  const location = useLocation();
  const userType = sessionStorage.getItem("userType"); // Example condition
  const type = sessionStorage.getItem("type"); // Example condition
  const college = sessionStorage.getItem("college");
  const employeeName = sessionStorage.getItem("employeeName");
  const director = sessionStorage.getItem("director");
const name = sessionStorage.getItem("name");

  console.log(director);
  useEffect(() => {
    // Filter routes when routes are loaded
    LoadableRoutes.load().then((module) => {
      const routes = module.default;
      const filtered = filterRoutes(routes);
      setFilteredRoutes(filtered);
    });
  }, [type]);

  const activeRoute = (routeName) => location.pathname.includes(routeName) ? "active" : "";

  const toggleCollapse = () => setCollapseOpen((prevState) => !prevState);

  const closeCollapse = () => setCollapseOpen(false);

  const toggleAccordion = (index) => setActiveAccordion(activeAccordion === index ? null : index);

  // Filter routes based on userType or other conditions
  const filterRoutes = (routes) =>
    routes.filter((route) => {
      if (String(type) === "1" && route.accessibleTo.includes("admin")) return true;
      if (String(type) === "3" && route.accessibleTo.includes("college")) return true;
      if (String(type) === "4" && route.accessibleTo.includes("employee")) return true;
      if (String(type) === "5" && route.accessibleTo.includes("director")) return true;
      if (String(type) === "6" && route.accessibleTo.includes("commissioner")) return true;
      if (String(type) === "7" && route.accessibleTo.includes("division")) return true;
      if (String(type) === "8" && route.accessibleTo.includes("secretary")) return true;
      return false;
    });
const getuserType = (type) => {
  if (type === "1") return "State Admin";
  if (type === "3") return college;
  if (type === "4") return employeeName;
  if (type === "5") return director;
  if (type === "6") return name;
  if (type === "7") return "Division Office";
  if (type === "8") return "Secretary";
  if (type === "2") return "Lead College";

  return ""; // or null if nothing should be returned
};

// Usage in JSX


  const createLinks = (filteredRoutes) =>
    filteredRoutes.map((prop, key) => {
      if (prop.children) {
        return (
          <NavItem key={key}>
            <NavLink style={{ color: "#444343" }} onClick={() => toggleAccordion(key)} className="dropdown-item dropdown-toggle">
              <i className={prop.icon} />
              {prop.name}
            </NavLink>
            <Collapse isOpen={activeAccordion === key}>
              <Nav navbar className="ml-3">
                {prop.children.map((child, childKey) => (
                  <NavItem key={`${key}-${childKey}`}>
                    <NavLink style={{ color: "#444343" }}
                      to={child.layout + child.path}
                      tag={NavLinkRRD}
                      onClick={closeCollapse}
                      className={activeRoute(child.layout + child.path)}
                    >
                      <i className={child.icon} />
                      {child.name}
                    </NavLink>
                  </NavItem>
                ))}
              </Nav>
            </Collapse>
          </NavItem>
        );
      }

      return (
        <NavItem key={key}>
          <NavLink
            style={{ color: "#444343" }}
            to={prop.layout + prop.path}
            tag={NavLinkRRD}
            onClick={closeCollapse}
            className={activeRoute(prop.layout + prop.path)}
          >
            <i className={prop.icon} />
            {prop.name}
          </NavLink>
        </NavItem>
      );
    });

  return (
    // Example: Only show sidebar if userType matches a specific value
    (isSidebarVisible === true && userType !== "guest") ? (
      <Navbar className="navbar-vertical fixed-left navbar-dark bg-white"  expand="md" id="sidenav-main">
        <Container fluid>
          <Button className="navbar-toggler" type="button" onClick={toggleCollapse}>
            <span className="navbar-toggler-icon" />
          </Button>
          {logo && (
            <NavbarBrand className="pt-0">
              <img alt="logo" className="navbar-brand-img" src={logo} />
            </NavbarBrand>
          )}
          <h3 style={{ textAlign: "center", textTransform: "uppercase" }}>
            {userType === "1" ? "State Admin" : getuserType(type)} <br />

           
          </h3>
          <Collapse navbar isOpen={collapseOpen}>
            <Nav navbar>
              {/* Use loadable to dynamically load and filter routes */}
              {createLinks(filteredRoutes)}
            </Nav>
          </Collapse>
        </Container>
      </Navbar>
    ) : null  // Do not render the sidebar if the user is a guest
  );
};

export default Sidebar;
