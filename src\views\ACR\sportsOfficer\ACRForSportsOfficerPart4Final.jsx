import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    Label,
    Input,
} from "reactstrap";


const ACRForSportsOfficerPart4Final = (employee) => {
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);


    const [errors, setErrors] = useState({});


    return (
        <>
            <Container fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2">कार्यालय, आयुक्त, उच्च शिक्षा, छत्तीसगढ़ रायपुर</h2>
                                <h2> गोपनीय प्रतिवेदन प्रपत्र </h2>
                                <h3>31 मार्च {year} को समाप्त होने वाले वर्ष के लिये</h3>
                                <br /><h2>क्रीड़ा अधिकारी संवर्ग</h2>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong> नाम
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={employee.employee?.basicDetails[0].employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>

                                                {employee.employee?.basicDetails[0].gender === "Female" &&
                                                    <Col md="3" className="mt--4">
                                                        <Label><strong>1.(अ)</strong>(महिला अधिकारी विवाह के पूर्व का नाम भी लिखें)</Label>
                                                        <Input
                                                            type="text"
                                                            name="secondaryName"
                                                            value={employee.employee?.basicDetails[0].secondaryName} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.secondaryName && (
                                                            <small className="text-danger">
                                                                {errors.secondaryName}
                                                            </small>
                                                        )}
                                                    </Col>}
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पिता/पति का नाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="pitaPatiKaNaam"
                                                        readOnly
                                                        value={employee.employee?.basicDetails[0].pitaPatiKaNaam} // Use employee data here
                                                        className="form-control"

                                                    />
                                                    {errors.pitaPatiKaNaam && (
                                                        <small className="text-danger">
                                                            {errors.pitaPatiKaNaam}
                                                        </small>
                                                    )}

                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong> जन्म तिथि</Label>
                                                    <Input
                                                        type="date"
                                                        name="dateOfBirth"
                                                        value={employee.employee?.basicDetails[0].dateOfBirth} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.dateOfBirth && (
                                                        <small className="text-danger">
                                                            {errors.dateOfBirth}
                                                        </small>
                                                    )}

                                                </Col>

                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="3">
                                                    <Label>
                                                        <strong>4.</strong> शैक्षणिक योग्यता:
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="saikshanikYogyta"
                                                        value={employee.employee?.basicDetails[0].saikshanikYogyta} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.saikshanikYogyta && (
                                                        <small className="text-danger">
                                                            {errors.saikshanikYogyta}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(अ )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति का दिनांक
                                                    </Label>
                                                    <Input
                                                        type="date"
                                                        name="niyuktiDinank"
                                                        value={employee.employee?.basicDetails[0].niyuktiDinank} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.niyuktiDinank && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiDinank}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(ब )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति प्रकार
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="niyuktiPrakar"
                                                        value={employee.employee?.basicDetails[0].niyuktiPrakar} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.niyuktiPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiPrakar}
                                                        </small>
                                                    )}
                                                </Col>

                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col>
                                            <Row className="d-block mt-3 mb-3">
                                                <Col>
                                                    <Row>
                                                        <Col>
                                                            <Label><strong>6.</strong> वेतनमान
                                                            </Label>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(अ) वर्तमान वेतनमान
                                                            </Label>
                                                            <Input
                                                                type="text"
                                                                name="vartmanVetan"
                                                                readOnly
                                                                value={employee.employee?.basicDetails[0].vartmanVetan} // Use employee data here
                                                                className="form-control"

                                                            />
                                                            {errors.vartmanVetan && (
                                                                <small className="text-danger">
                                                                    {errors.vartmanVetan}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(ब) वरिष्ठ श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="varisthShreniParitDinak"
                                                                readOnly
                                                                value={employee.employee?.basicDetails[0].varisthShreniParitDinak} // Use employee data here
                                                                className="form-control"

                                                            />
                                                            {errors.varisthShreniParitDinak && (
                                                                <small className="text-danger">
                                                                    {errors.varisthShreniParitDinak}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(स) प्रवर श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="prawarShreniParitDinank"
                                                                readOnly
                                                                value={employee.employee?.basicDetails[0].prawarShreniParitDinank} // Use employee data here
                                                                className="form-control"

                                                            />
                                                            {errors.prawarShreniParitDinank && (
                                                                <small className="text-danger">
                                                                    {errors.prawarShreniParitDinank}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>7.</strong> वर्ष में किस महाविद्यालय में पदस्थ रहे.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="sewaKalMahavidyalayaPadankit"
                                                        value={employee.employee?.basicDetails[0].sewaKalMahavidyalayaPadankit} // Use employee data here
                                                        className="form-control"
                                                        readOnly


                                                    />
                                                    {errors.sewaKalMahavidyalayaPadankit && (
                                                        <small className="text-danger">
                                                            {errors.sewaKalMahavidyalayaPadankit}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt--4">
                                                    <Label><strong>8. </strong>विचाराधीन वर्ष में खेलकूद आदि की व्यवस्था बढ़ाने में प्रमुख महयोग / प्रयास
                                                        ( क्रीडा अधिकारी उन्हें सौपे गये कार्यों के निष्पादन प्रतिवेदन संलग्न करें)
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="vicharDhinYogdan"
                                                        value={employee.employee?.basicDetails[0].vicharDhinYogdan} // Use employee data here
                                                        className="form-control"
                                                        readOnly


                                                    />
                                                    {errors.vicharDhinYogdan && (
                                                        <small className="text-danger">
                                                            {errors.vicharDhinYogdan}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            {employee.employee.levelIndex >= 0 && employee.employee.preDefinedLevels[0].status === true && <>
                                                <Row className="text-center mt-5">
                                                    <Col>
                                                        <h2><u>प्रतिवेदक अधिकारी की अभ्युक्ति</u></h2>
                                                    </Col>
                                                </Row>
                                                <Row className="d-block mt-3 mb-3">
                                                    <Col>
                                                        <Row className="mb-4">
                                                            <Col md="6" >
                                                                <Label> <strong>1. </strong>विचाराधीन वर्ष में खेलकूद आदि की
                                                                    व्यवस्था बढ़ाने में प्रमुख सहयोग / प्रयास
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="viharaDhinSafalta"
                                                                    value={employee.employee?.levelDetails[0].data.viharaDhinSafalta} // Use employee data here
                                                                    readOnly
                                                                    className="form-control"

                                                                />
                                                                {errors.viharaDhinSafalta && (
                                                                    <small className="text-danger">
                                                                        {errors.viharaDhinSafalta}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" >
                                                                <Label> <strong>2. </strong>उच्चाधिकारियों, सहकर्मियों तथा अधिनस्थों से संबंध :
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="sahkarmiyonSeSambandh"
                                                                    value={employee.employee?.levelDetails[0].data.sahkarmiyonSeSambandh} // Use employee data here

                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.sahkarmiyonSeSambandh && (
                                                                    <small className="text-danger">
                                                                        {errors.sahkarmiyonSeSambandh}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-4">
                                                            <Col md="6">
                                                                <Label> <strong>3. </strong>समग्र व्यक्तित्व :</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="samgraVyaktitva"
                                                                    value={employee.employee?.levelDetails[0].data.samgraVyaktitva} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.samgraVyaktitva && (
                                                                    <small className="text-danger">
                                                                        {errors.samgraVyaktitva}
                                                                    </small>
                                                                )}
                                                            </Col>

                                                            <Col md="6">
                                                                <Label> <strong>4. </strong>कोई प्रतिकूल टीका / दंड दिया गया हो तो उल्लेख करें :</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="pratikulDandUlekh"
                                                                    value={employee.employee?.levelDetails[0].data.pratikulDandUlekh} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.pratikulDandUlekh && (
                                                                    <small className="text-danger">
                                                                        {errors.pratikulDandUlekh}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-4">
                                                            <Col md="6">
                                                                <Label> <strong>5. </strong>निष्ठा :</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="nishtha"
                                                                    value={employee.employee?.levelDetails[0].data.nishtha} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.nishtha && (
                                                                    <small className="text-danger">
                                                                        {errors.nishtha}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6">
                                                                <Label> <strong>6. </strong>(अ)समग्र मूल्याकंन
                                                                    :</Label>
                                                                <Input
                                                                    type="select"
                                                                    name="samagraMulyankan"
                                                                    value={employee.employee?.levelDetails[0].data.samagraMulyankan} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                >
                                                                    <option value="">विकल्प चुने </option>
                                                                    <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                    <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                    <option value="अच्छा">अच्छा</option>
                                                                    <option value="साधारण">साधारण</option>
                                                                    <option value="घटिया">घटिया</option>

                                                                </Input>
                                                                {errors.samagraMulyankan && (
                                                                    <small className="text-danger">
                                                                        {errors.samagraMulyankan}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mt-4">
                                                            <Col md="12">
                                                                <Label> <strong>6. </strong>(ब) (उत्कृष्ठ या घटिया वर्गीकरण करने पर इसका
                                                                    औचित्य भी स्पष्टतः अकिंत करें ) :</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="grading"
                                                                    style={{ height: "90px" }}
                                                                    value={employee.employee?.levelDetails[0].data.grading} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.grading && (
                                                                    <small className="text-danger">
                                                                        {errors.grading}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                    </Col>
                                                </Row>
                                            </>}
                                            <Row className="d-block mt-3 mb-3">
                                                <Col>
                                                    <>
                                                        {employee.employee.levelIndex >= 1 && employee.employee.preDefinedLevels[1].status === true && <>
                                                            <hr />
                                                            <Row className="mb-3">
                                                                <Col>
                                                                    <h2 style={{ textAlign: "center" }}><u> समीक्षक अधिकारी की टिप्पणी </u>
                                                                    </h2>
                                                                </Col>
                                                            </Row>
                                                            <Row className="mb-4">
                                                                <Col md="3">
                                                                    <Label>श्रेणीकरण</Label>
                                                                    <Input
                                                                        name="shreniKaran1"
                                                                        type="select"
                                                                        id="recordsPerPage"
                                                                        readOnly
                                                                        value={employee.employee?.levelDetails[1].data.shreniKaran1}


                                                                    >
                                                                        <option value="">चुनें</option>
                                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                        <option value="अच्छा">अच्छा</option>
                                                                        <option value="साधारण">साधारण</option>
                                                                        <option value="घटिया">घटिया</option>
                                                                    </Input>
                                                                    {errors.shreniKaran1 && (
                                                                        <small className="text-danger">
                                                                            {errors.shreniKaran1}
                                                                        </small>
                                                                    )}
                                                                </Col>
                                                            </Row>
                                                            <Row className="mb-4">
                                                                <Col md="12">
                                                                    <Label>रिमार्क</Label>
                                                                    <Input
                                                                        name="remark1"
                                                                        type="textarea"
                                                                        style={{ height: "90px" }}
                                                                        readOnly
                                                                        value={employee.employee?.levelDetails[1].data.remark1}


                                                                    />
                                                                    {errors.remark1 && (
                                                                        <small className="text-danger">
                                                                            {errors.remark1}
                                                                        </small>)}
                                                                </Col>
                                                            </Row>
                                                        </>}
                                                        {employee.employee.levelIndex >= 2 && employee.employee.preDefinedLevels[2].status === true && <>
                                                            <hr />
                                                            <Row className="mb-3">
                                                                <Col>
                                                                    <h2 style={{ textAlign: "center" }}>
                                                                        <u>स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                                    </h2>
                                                                </Col>
                                                            </Row>
                                                            <Row className="mb-4">
                                                                <Col md="3">
                                                                    <Label>श्रेणीकरण</Label>
                                                                    <Input
                                                                        name="shreniKaran2"
                                                                        type="select"
                                                                        id="recordsPerPage"
                                                                        readOnly
                                                                        value={employee.employee?.levelDetails[2].data.shreniKaran2}

                                                                    >
                                                                        <option value="">चुनें</option>
                                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                        <option value="अच्छा">अच्छा</option>
                                                                        <option value="साधारण">साधारण</option>
                                                                        <option value="घटिया">घटिया</option>
                                                                    </Input>
                                                                    {errors.shreniKaran2 && (
                                                                        <small className="text-danger">
                                                                            {errors.shreniKaran2}
                                                                        </small>
                                                                    )}
                                                                </Col>
                                                            </Row>
                                                            <Row className="mb-4">
                                                                <Col md="12">
                                                                    <Label>रिमार्क</Label>
                                                                    <Input
                                                                        name="remark2"
                                                                        type="textarea"
                                                                        style={{ height: "90px" }}
                                                                        readOnly
                                                                        value={employee.employee?.levelDetails[2].data.remark2}

                                                                    />
                                                                    {errors.remark2 && (
                                                                        <small className="text-danger">{errors.remark2}</small>
                                                                    )}
                                                                </Col>
                                                            </Row>
                                                        </>}
                                                    </>
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRForSportsOfficerPart4Final.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRForSportsOfficerPart4Final;
