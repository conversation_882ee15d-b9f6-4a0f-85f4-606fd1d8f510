import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Label,
  Input,
  Table,
} from "reactstrap";

const ACRFormClass3or4FinalForm = (employee) => {
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);
  const [errors, setErrors] = useState({});








  return (
    <>
      <Row>
        <Col>
          <Card className="shadow pb-4">
            <CardHeader
              className="bg-white border-0"
              style={{ textAlign: "center" }}
            >
              <h2 className="mb-0">प्रपत्र --- दो</h2>
              {employee.employeeDetails?.classDetails.className ===
                "Class 3" ? (
                <>
                  <h3 className="mb-0">
                    तृतीय श्रेणी एवं अन्य लिपिक वर्गीय कर्मचारी के गोपनीय
                    प्रतिवेदन लिखे जाने का प्रपत्र 31 मार्च, {lastYearDate} को
                    समाप्त होने वाले वर्ष के लिए |
                  </h3>
                </>
              ) : (
                <>
                  <h3 className="mb-0">
                    चतुर्थ श्रेणी के शासकीय कर्मचारियों के संबंध में प्रतिवर्ष
                    माह अप्रैल के प्रथम सप्ताह में लिखी जाने वाली चरित्र पंजी का
                    फार्म 31 मार्च {lastYearDate}
                    को समाप्त होने वाले वर्ष के लिये
                  </h3>
                </>
              )}
            </CardHeader>
            <CardBody>
              <div className="mb-4">
                <Row className="mb-3">
                  <Table>
                    <tbody>
                      <tr>
                        <h2 style={{ textAlign: "center" }}>प्रतिवेदक</h2>
                      </tr>
                      {employee.employee.employeeDetails?.classDetails.className ===
                        "Class 3" ? (
                        <>
                          <tr>
                            <td>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>पूरा नाम श्री/श्रीमती / कुमारी </Label>
                                  <Input
                                    type="text"
                                    name="employeeName"
                                    value={employee?.employee?.basicDetails[0].employeeName}
                                    readOnly
                                    className="form-control"
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पिता अथवा पति का नाम </Label>
                                  <Input
                                    type="text"
                                    name="fatherName"
                                    value={
                                      employee.employee.basicDetails[0]?.fatherName
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.fatherName && (
                                    <small className="text-danger">
                                      {errors.fatherName}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>जन्म तिथि</Label>
                                  <Input
                                    type="date"
                                    name="dateofBirth"
                                    value={
                                      employee.employee.basicDetails[0]?.dateofBirth
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.dateofBirth && (
                                    <small className="text-danger">
                                      {errors.dateofBirth}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>
                                    धारित पदनाम (मूल / स्थानापन्न / अस्थायी)
                                    वर्तमान
                                  </Label>
                                  <Input
                                    type="text"
                                    name="designation"
                                    value={
                                      employee.employee.basicDetails[0]?.designation
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                </Col>
                              </Row>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>महाविद्यालय का नाम</Label>
                                  <Input
                                    type="text"
                                    name="collegeName"
                                    value={
                                      employee.employee.basicDetails[0]?.collegeName
                                    } // Use employee data here
                                    readOnly
                                    className="form-control"
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>वरिष्ठता सूची क्रमांक </Label>
                                  <Input
                                    type="number"
                                    name="varishtSuchiNumber"
                                    value={employee.employee.basicDetails[0]
                                      .varishtSuchiNumber
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.varishtSuchiNumber && (
                                    <small className="text-danger">
                                      {errors.varishtSuchiNumber}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>प्रथम नियमित नियुक्ति दिनांक</Label>
                                  <Input
                                    type="date"
                                    name="firstNiyamitNiyuktiDate"
                                    value={
                                      employee.employee.basicDetails[0]
                                        .firstNiyamitNiyuktiDate
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.firstNiyamitNiyuktiDate && (
                                    <small className="text-danger">
                                      {errors.firstNiyamitNiyuktiDate}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>
                                    वर्तमान पद पर पदोन्नति दिनांक (यदि हो तो)
                                  </Label>
                                  <Input
                                    type="date"
                                    name="currentPadonatiDate"
                                    value={
                                      employee.employee.basicDetails[0]
                                        .currentPadonatiDate
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.currentPadonatiDate && (
                                    <small className="text-danger">
                                      {errors.currentPadonatiDate}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-4">
                                <Col md="3">
                                  <Label>वेतन</Label>
                                  <Input
                                    type="number"
                                    name="salary"
                                    value={employee.employee.basicDetails[0]?.currentSalary} // Use employee data here
                                    className="form-control"
                                    readOnly
                                  />
                                </Col>
                                <Col md="9">
                                  <Label>कर्तव्यों का संक्षिप्त विवरण </Label>
                                  <Input
                                    type="textarea"
                                    name="kartavyaKaVivran"
                                    value={
                                      employee.employee.basicDetails[0]
                                        .kartavyaKaVivran
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.kartavyaKaVivran && (
                                    <small className="text-danger">
                                      {errors.kartavyaKaVivran}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                            </td>
                          </tr>
                        </>
                      ) : (
                        <>
                          <tr>
                            <td>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>कर्मचारी का नाम </Label>
                                  <Input
                                    type="text"
                                    name="employeeName"
                                    value={
                                      employee.employee.basicDetails[0]?.employeeName
                                    }
                                    readOnly
                                    className="form-control"
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पिता अथवा पति का नाम </Label>
                                  <Input
                                    type="text"
                                    name="fatherName"
                                    value={
                                      employee.employee.basicDetails[0]?.fatherName
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.fatherName && (
                                    <small className="text-danger">
                                      {errors.fatherName}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>जन्म तिथि</Label>
                                  <Input
                                    type="date"
                                    name="dateofBirth"
                                    value={
                                      employee.employee.basicDetails[0]?.dateofBirth
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.dateofBirth && (
                                    <small className="text-danger">
                                      {errors.dateofBirth}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>शैक्षणिक योग्यता, यदि कोई हो</Label>
                                  <Input
                                    type="text"
                                    name="shenikYogyata"
                                    value={
                                      employee.employee.basicDetails[0]?.shenikYogyata
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.shenikYogyata && (
                                    <small className="text-danger">
                                      {errors.shenikYogyata}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-3">
                                <Col md="3">
                                  <Label>वरिष्ठता सूची क्रमांक </Label>
                                  <Input
                                    type="number"
                                    name="varishtSuchiNumber"
                                    value={
                                      employee.employee.basicDetails[0]?.varishtSuchiNumber
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.varishtSuchiNumber && (
                                    <small className="text-danger">
                                      {errors.varishtSuchiNumber}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>वेतन</Label>
                                  <Input
                                    type="number"
                                    name="salary"
                                    value={employee.employee.basicDetails[0]?.currentSalary} // Use employee data here
                                    className="form-control"
                                    readOnly
                                  />
                                </Col>
                                <Col md="3">
                                  <Label>पदनाम / स्थायी / अस्थायी </Label>
                                  <Input
                                    type="text"
                                    name="designation"
                                    value={
                                      employee.employee.basicDetails[0]?.designation
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.kartavyaKaVivran && (
                                    <small className="text-danger">
                                      {errors.kartavyaKaVivran}
                                    </small>
                                  )}
                                </Col>
                                <Col md="3">
                                  <Label>नियमित नियुक्ति की तिथि </Label>
                                  <Input
                                    type="date"
                                    name="niyamitNiyukti"
                                    value={
                                      employee.employee.basicDetails[0]?.niyamitNiyukti
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.niyamitNiyukti && (
                                    <small className="text-danger">
                                      {errors.niyamitNiyukti}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                              <Row className="mb-4">
                                <Col md="6">
                                  <Label>महाविद्यालय का नाम</Label>
                                  <Input
                                    type="text"
                                    name="collegeName"
                                    value={
                                      employee.employee.basicDetails[0]?.collegeName
                                    } // Use employee data here
                                    className="form-control"
                                    readOnly
                                  />
                                </Col>
                                <Col md="6">
                                  <Label>
                                    अवधि जिसके लिए मत अंकित किया जा रहा है{" "}
                                  </Label>
                                  <Input
                                    type="textarea"
                                    name="avadhiAnkit"
                                    value={
                                      employee.employee.basicDetails[0]?.avadhiAnkit
                                    }
                                    className="form-control"
                                    readOnly
                                  />
                                  {errors.avadhiAnkit && (
                                    <small className="text-danger">
                                      {errors.avadhiAnkit}
                                    </small>
                                  )}
                                </Col>
                              </Row>
                            </td>
                          </tr>
                        </>
                      )}
                    </tbody>
                  </Table>
                </Row>

                {employee.employee.levelIndex >= 0 && employee.employee.preDefinedLevels[0].status === true && <>
                  <Row className="mb-3">
                    <Col>
                      <h2 style={{ textAlign: "center" }}>प्रथम मतांकन</h2>
                    </Col>
                  </Row>
                  {employee.employee.employeeDetails?.classDetails.className ===
                    "Class 3" ? (
                    <>
                      <Row className="mb-3">
                        <Col md="6">
                          <Label>व्यक्तित्व एवं व्यवहार</Label>
                          <Input
                            type="textarea"
                            name="vektiVehwar"
                            value={employee.employee?.levelDetails[0].data.vektiVehwar}
                            className="form-control"
                            readOnly
                          />
                          {errors.vektiVehwar && (
                            <small className="text-danger">
                              {errors.vektiVehwar}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>आचरण / चरित्र </Label>
                          <Input
                            type="textarea"
                            name="acharanCharitra"
                            value={
                              employee.employee.levelDetails[0].data.acharanCharitra
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.acharanCharitra && (
                            <small className="text-danger">
                              {errors.acharanCharitra}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-3">
                        <Col md="6">
                          <Label>प्रारूप और टीप लिखने की योग्यता</Label>
                          <Input
                            type="textarea"
                            name="prarupTip"
                            value={employee.employee.levelDetails[0].data.prarupTip}
                            className="form-control"
                            readOnly
                          />
                          {errors.prarupTip && (
                            <small className="text-danger">
                              {errors.prarupTip}
                            </small>
                          )}
                        </Col>

                        <Col md="6">
                          <Label>
                            कार्यालय प्रक्रिया और नियमों का ज्ञान तथा प्रयास करने
                            की योग्यता
                          </Label>
                          <Input
                            type="textarea"
                            name="karyalayaPrakriya"
                            value={
                              employee.employee.levelDetails[0].data.karyalayaPrakriya
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.karyalayaPrakriya && (
                            <small className="text-danger">
                              {errors.karyalayaPrakriya}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-3">
                        <Col md="6">
                          <Label>प्रकरण के परीक्षण की क्षमता</Label>
                          <Input
                            type="textarea"
                            name="prakrankiParikhsha"
                            value={
                              employee.employee.levelDetails[0].data.prakrankiParikhsha
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.prakrankiParikhsha && (
                            <small className="text-danger">
                              {errors.prakrankiParikhsha}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>कार्य के निपटारे की तत्परता</Label>
                          <Input
                            type="textarea"
                            name="karyakeNiptare"
                            value={
                              employee.employee.levelDetails[0].data.karyakeNiptare
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.karyakeNiptare && (
                            <small className="text-danger">
                              {errors.karyakeNiptare}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-4">
                        <Col md="6">
                          <Label>उपस्थिति की नियमितता और समय की पाबंदी</Label>
                          <Input
                            type="textarea"
                            name="samaykiPabandi"
                            value={
                              employee.employee.levelDetails[0].data.samaykiPabandi
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.samaykiPabandi && (
                            <small className="text-danger">
                              {errors.samaykiPabandi}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>उच्च अधिकारियों एवं सहभागियों से संबंध</Label>
                          <Input
                            type="textarea"
                            name="sahbhagiyonSeSambhand"
                            value={
                              employee.employee.levelDetails[0].data
                                .sahbhagiyonSeSambhand
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.sahbhagiyonSeSambhand && (
                            <small className="text-danger">
                              {errors.sahbhagiyonSeSambhand}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-4">
                        <Col md="6">
                          <Label>
                            नित्य कार्य जैसे—असिस्टेंट की डायरी का रख-रखाव, गार्ड
                            फाइलें, रिकार्डिंग आदि का ध्यान रखा जाना
                          </Label>
                          <Input
                            type="textarea"
                            name="nityaKaryaJaise"
                            value={
                              employee.employee.levelDetails[0].data.nityaKaryaJaise
                            } // Use employee data here
                            className="form-control"
                            readOnly
                          />
                          {errors.nityaKaryaJaise && (
                            <small className="text-danger">
                              {errors.nityaKaryaJaise}
                            </small>
                          )}
                        </Col>

                        <Col md="6">
                          <Label>सनिष्ठा</Label>
                          <Input
                            type="textarea"
                            name="sanishth"
                            value={employee.employee.levelDetails[0].data.sanishth}
                            className="form-control"
                            readOnly
                          />
                          {errors.sanishth && (
                            <small className="text-danger">
                              {errors.sanishth}
                            </small>
                          )}
                        </Col>

                      </Row>
                      <Row className="mb-4">
                        <Col md="6">
                          <Label>
                            कर्मचारी द्वारा यदि कोई असाधारण या उल्लेखनीय कार्य
                            किया गया हो तो वह संक्षेप में बतावें ।
                          </Label>
                          <Input
                            type="textarea"
                            name="asadharanOrUlekhNiya"
                            value={
                              employee.employee.levelDetails[0].data
                                .asadharanOrUlekhNiya
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.asadharanOrUlekhNiya && (
                            <small className="text-danger">
                              {errors.asadharanOrUlekhNiya}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>पदोन्नति की उपयुक्तता</Label>
                          <Input
                            type="textarea"
                            name="padonatiKiUpyukta"
                            value={
                              employee.employee.levelDetails[0].data.padonatiKiUpyukta
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.padonatiKiUpyukta && (
                            <small className="text-danger">
                              {errors.padonatiKiUpyukta}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-4">
                        <Col md="3">
                          <Label>श्रेणीकरण</Label>
                          <Input
                            name="shreniKaran"
                            type="select"
                            id="recordsPerPage"
                            value={employee.employee.levelDetails[0].data.shreniKaran}
                            readOnly
                          >
                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                            <option value="अच्छा">अच्छा</option>
                            <option value="साधारण">साधारण</option>
                            <option value="घटिया">घटिया</option>
                          </Input>
                          {errors.shreniKaran && (
                            <small className="text-danger">
                              {errors.shreniKaran}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-4">
                        <Col md="12">
                          <Label>रिमार्क</Label>
                          <Input
                            name="remark"
                            type="textarea"
                            style={{ height: "100px" }}
                            value={employee.employee.levelDetails[0].data.remark}
                            readOnly
                          />
                          {errors.remark && (
                            <small className="text-danger">{errors.remark}</small>
                          )}
                        </Col>
                      </Row>
                    </>
                  ) : (
                    <>
                      <Row className="mb-3">
                        <Col md="6">
                          <Label>आचरण / व्यवहार तथा आज्ञाकारिता</Label>
                          <Input
                            type="textarea"
                            name="acharanVevhar"
                            value={employee.employee?.levelDetails[0]?.data?.acharanVevhar}
                            className="form-control"
                            readOnly
                          />
                          {errors.acharanVevhar && (
                            <small className="text-danger">
                              {errors.acharanVevhar}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>समय की पाबंदी </Label>
                          <Input
                            type="textarea"
                            name="samayKiPabandi"
                            value={employee.employee?.levelDetails[0].data.samayKiPabandi
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.samayKiPabandi && (
                            <small className="text-danger">
                              {errors.samayKiPabandi}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-4">
                        <Col md="6">
                          <Label>शारीरिक क्षमता</Label>
                          <Input
                            type="textarea"
                            name="sharirikShamta"
                            value={employee.employee?.levelDetails[0].data.sharirikShamta
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.sharirikShamta && (
                            <small className="text-danger">
                              {errors.sharirikShamta}
                            </small>
                          )}
                        </Col>

                        <Col md="6">
                          <Label>सौपे गये कार्य को करने की समझ और योग्यता</Label>
                          <Input
                            type="textarea"
                            name="karyaKiYogyata"
                            value={employee.employee?.levelDetails[0].data.karyaKiYogyata
                            }
                            className="form-control"
                            readOnly
                          />
                          {errors.karyaKiYogyata && (
                            <small className="text-danger">
                              {errors.karyaKiYogyata}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-3">
                        <Col md="6">
                          <Label>
                            स्थानांतरण, दण्ड आदि के संबंध में सामान्य मत
                          </Label>
                          <Input
                            type="textarea"
                            name="stanantaran"
                            value={employee.employee?.levelDetails[0].data.stanantaran}
                            className="form-control"
                            readOnly
                          />
                          {errors.stanantaran && (
                            <small className="text-danger">
                              {errors.stanantaran}
                            </small>
                          )}
                        </Col>
                        <Col md="6">
                          <Label>श्रेणीकरण</Label>
                          <Input
                            name="shreniKaran"
                            type="select"
                            id="recordsPerPage"
                            value={employee.employee?.levelDetails[0].data.shreniKaran}
                            readOnly
                          >
                            <option value="">चुने</option>
                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                            <option value="अच्छा">अच्छा</option>
                            <option value="साधारण">साधारण</option>
                            <option value="घटिया">घटिया</option>
                          </Input>
                          {errors.shreniKaran && (
                            <small className="text-danger">
                              {errors.shreniKaran}
                            </small>
                          )}
                        </Col>
                      </Row>
                      <Row className="mb-3">
                        <Col md="12">
                          <Label>रिमार्क</Label>
                          <Input
                            name="remark"
                            style={{ height: "100px" }} // Set the height here
                            type="textarea"
                            maxLength={500}
                            value={employee.employee?.levelDetails[0].data.remark}
                            readOnly
                          />
                          {errors.remark && (
                            <small className="text-danger">{errors.remark}</small>
                          )}
                        </Col>
                      </Row>
                    </>
                  )}
                </>}

                {
                  employee.employee.levelIndex >= 1 && employee.employee.preDefinedLevels[1].status === true && <>
                    <Row className="mb-3">
                      <Col>
                        <h2 style={{ textAlign: "center" }}>
                          समीक्षक अधिकारी की टिप्पणी
                        </h2>
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="3">
                        <Label>श्रेणीकरण</Label>
                        <Input
                          name="shreniKaran1"
                          type="select"
                          id="recordsPerPage"
                          value={employee.employee?.levelDetails[1].data?.shreniKaran1}
                          readOnly
                        >
                          <option value="">चुनें</option>
                          <option value="उत्कृष्ट">उत्कृष्ट</option>
                          <option value="बहुत अच्छा">बहुत अच्छा</option>
                          <option value="अच्छा">अच्छा</option>
                          <option value="साधारण">साधारण</option>
                          <option value="घटिया">घटिया</option>
                        </Input>
                      </Col>
                    </Row>
                    <Row className="mb-4">
                      <Col md="12">
                        <Label>रिमार्क</Label>
                        <Input
                          name="remark1"
                          style={{ height: "100px" }} // Set the height here

                          type="textarea"
                          value={employee.employee?.levelDetails[1].data.remark1}
                          readOnly
                        />
                        {errors.remark1 && (
                          <small className="text-danger">{errors.remark1}</small>
                        )}
                      </Col>
                    </Row>
                  </>
                }

                {employee.employee.levelIndex >= 2 && employee.employee.preDefinedLevels[2].status === true && <>
                  < Row className="mb-3">
                    <Col>
                      <h2 style={{ textAlign: "center" }}>
                        स्वीकृतकर्ता अधिकारी की टिप्पणी
                      </h2>
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="3">
                      <Label>श्रेणीकरण</Label>
                      <Input
                        name="shreniKaran2"
                        type="select"
                        id="recordsPerPage"
                        value={employee.employee?.levelDetails[2].data.shreniKaran2}
                        readOnly
                      >
                        <option value="">चुनें</option>
                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                        <option value="अच्छा">अच्छा</option>
                        <option value="साधारण">साधारण</option>
                        <option value="घटिया">घटिया</option>
                      </Input>
                      {errors.shreniKaran2 && (
                        <small className="text-danger">
                          {errors.shreniKaran2}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="12">
                      <Label>रिमार्क</Label>
                      <Input
                        name="remark2"
                        type="textarea"
                        style={{ height: "100px" }} // Set the height here

                        value={employee.employee?.levelDetails[2].data.remark2}
                        readOnly
                      />
                      {errors.remark2 && (
                        <small className="text-danger">{errors.remark2}</small>
                      )}
                    </Col>
                  </Row>
                </>
                }
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row >
    </>
  );
};

ACRFormClass3or4FinalForm.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormClass3or4FinalForm;
