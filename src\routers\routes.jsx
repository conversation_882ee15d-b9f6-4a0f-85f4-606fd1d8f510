import loadable from "@loadable/component";
import FileMovementReport from "../views/leave/FileMovementReport.jsx";
const AddSection = loadable(() => import("../views/Admin/addSection.jsx"));
const SectionList = loadable(() => import("../views/Admin/sectionList.jsx"));

import {
  Dashboard,
  HolidayDetails,
  InstituteEntry,
  SubjectEntry,
  SubjectEntryForSeat,
  University,
  Director,
  LeaveDashboard,
  ApplyLeave,
  LeaveList,
  JoiningList,
  LeaveActionByPrincipal,
  ActionsReportAtPrincipal,
  UniversityList,
  InstituteList,
  EmployeeAdd,
  EmployeeList,
  LeaveActionAtDirector,
  ActionsReportAtDirector,
  LeaveBalanceList,
  LeaveBalanceListPrincipal,
  AddDesignation,
  DesignationList,
  Division,
  District,
  AddVidhansabha,
  LeaveRuleRegistration,
  EmployeeCode,
  DeletedEmployeeList,
  IPRForm,
  IPRList,
  IPRDateForm,
  AttendanceList,
  AttendanceTodayList,
  GenerateAttendanceReport,
  GenerateEmmployeeReport,
  ErrorLogs,
  DirectorEmpList,
  ACRMapping,
  ACRFormList,
  ACRDateForm,
  IprListAtSection,
  IPRACR,
  DirectorateACRMapping,
  ACRFormDirectorList,
  SeatExtensionList,
  PrincipalList,
  JanBhagiDariCourse,
  GenerateEmployeeReportCount,
  NewCourseCollegeReport,
  NewCourseAllReport,
  NewCourseAllReportGovt,
  GuestFacultyForm,
  GuestFacultyList,
  AllGuestFacultyList,
  EmployeeTransfer,
  TransferedEmpList,
  IncomingTransfer,
  OutgoingTransfer,
  TransferedEmpListByCollege,
  PrincipalChargeList,
  CollegeChargeList,
  AllGuestFacultyReportCount,
  AddCourse,
  TrashGuestFaculty,
  NewPhdRequestApplication,
  PhdRequestListReport,
  PHD_Form_set,
} from "./DynamicComponents";

const userType = Number(sessionStorage.getItem("type"));
const acrInRange = Boolean(sessionStorage.getItem("dateRangeACR"));
const sectionNameString = sessionStorage.getItem("sectionName");

const YojnaSection = sectionNameString
  ? sectionNameString.split(",").find((a) => a === "Yojna")
  : [];
const GopniyaSection = sectionNameString
  ? sectionNameString.split(",").find((a) => a === "Gopniya Prakoshtha")
  : [];

const pracharyaSection = sectionNameString
  ? sectionNameString.split(",").find((a) => a === "Prachary")
  : [];

const rajpatritSection = sectionNameString
  ? sectionNameString.split(",").find((a) => a === "Rajpatrit")
  : [];

const arajpatritSection = sectionNameString
  ? sectionNameString.split(",").find((a) => a === "Arajpatrit (Directorate)")
  : [];

const routes = [
  ...(userType === 5
    ? [
        {
          path: "/ipr-section-list",
          name: "IPR List",
          icon: "ni ni-bullet-list-67 text-primary",
          component: <IprListAtSection />,
          layout: "/admin",
          accessibleTo: ["director"],
        },
      ]
    : ""),
  ...(userType === 4
    ? [
        {
          path: "/dashboard",
          name: "Home / होम",
          icon: "ni ni-shop text-primary",
          component: <Dashboard />,
          layout: "/admin",
          accessibleTo: ["employee"],
        },
        {
          name: "IPR  / अचल संपत्ति विवरण ",
          icon: "ni ni-collection text-yellow",
          layout: "/admin",
          accessibleTo: ["employee"],
          children: [
            {
              path: "/ipr-file",
              name: "Apply IPR / अचल संपत्ति विवरण  ",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <IPRForm />,
              layout: "/admin",
              accessibleTo: ["employee"],
            },
            {
              path: "/iprlist",
              name: "IPR Report / अचल संपत्ति विवरण रिपोर्ट",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <IPRList />,
              layout: "/admin",
              accessibleTo: ["employee"],
            },
          ],
        },
        {
          path: "/new-phd-request-application",
          name: "PHD / पीएचडी हेतु आवेदन",
          icon: "ni ni-single-copy-04 text-primary",
          component: <NewPhdRequestApplication />,
          layout: "/admin",
          accessibleTo: ["employee"],
        },
      ]
    : [
        {
          path: "/dashboard",
          name: "Dashboard",
          icon: "ni ni-tv-2 text-primary",
          component: <Dashboard />,
          layout: "/admin",
          accessibleTo: ["admin", "college", "employee"], // Accessible to all users
        },
        {
          path: "/iprlist",
          name: "IPR Report / अचल संपत्ति विवरण रिपोर्ट",
          icon: "ni ni-bullet-list-67 text-primary",
          component: <IPRList />,
          layout: "/admin",
          accessibleTo: ["college"],
        },
      ]),
  ...(userType === 1
    ? [
        {
          name: "University",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/university",
              name: "Add University",
              icon: "ni ni-single-02 text-yellow",
              component: <University />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/university-list",
              name: "University List",
              icon: "ni ni-single-02 text-yellow",
              component: <UniversityList />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          name: "Institute",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/institute",
              name: "Add Institute",
              icon: "ni ni-single-02 text-yellow",
              component: <InstituteEntry />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/institute-list",
              name: "Institute List",
              icon: "ni ni-single-02 text-yellow",
              component: <InstituteList />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          name: "Designation",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/add-designation",
              name: "Add Designation",
              icon: "ni ni-single-02 text-yellow",
              component: <AddDesignation />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/designation-list",
              name: "Designation List",
              icon: "ni ni-single-02 text-yellow",
              component: <DesignationList />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          name: "Section",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/add-section",
              name: "Add section",
              icon: "ni ni-single-02 text-yellow",
              component: <AddSection />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/list-section",
              name: "Section List & Mapping",
              icon: "ni ni-single-02 text-yellow",
              component: <SectionList />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          name: "Subjects & Seat",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/subject-entry",
              name: "Subjects",
              icon: "ni ni-single-02 text-yellow",
              component: <SubjectEntry />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/add-Course",
              name: "Add Course",
              icon: "ni ni-single-02 text-yellow",
              component: <AddCourse />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          path: "/HolidayDetails",
          name: "Holiday Details",
          icon: "ni ni-pin-3 text-orange",
          component: <HolidayDetails />,
          layout: "/admin",
          accessibleTo: ["admin"],
        },
        {
          path: "/ipr-date-form",
          name: "IPR Date Range",
          icon: "ni ni-pin-3 text-orange",
          component: <IPRDateForm />,
          layout: "/admin",
          accessibleTo: ["admin"],
        },
        {
          name: "Director /  Commissioner",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/director",
              name: "Add Employee",
              icon: "ni ni-single-02 text-yellow",
              component: <Director />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/directorate-emp-list",
              name: "Employee List",
              icon: "fa fa-list text-warning",
              layout: "/admin",
              accessibleTo: ["admin"],
              component: <DirectorEmpList />,
            },
          ],
        },
        {
          name: "Div. / Dist. / Vidhansabha",
          icon: "ni ni-bullet-list-67 text-blue",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/division",
              name: "Division",
              icon: "ni ni-collection text-red",
              component: <Division />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/district",
              name: "District",
              icon: "ni ni-collection text-red",
              component: <District />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/vidhansabha",
              name: "Vidhansabha",
              icon: "ni ni-collection text-red",
              component: <AddVidhansabha />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },

        {
          name: "Ph.D /पी.एच.डी",
          icon: "ni ni-bullet-list-67 text-primary",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/form-phd-opening",
              name: "Ph.D Application Control/पी.एच.डी आवेदन नियंत्रण",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <PHD_Form_set />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/phd-request-list-report",
              name: "Ph.D Request List Report/पी.एच.डी आवेदन रिपोर्ट",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <PhdRequestListReport />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
      ]
    : []),
  ...(userType === 3
    ? [
        {
          name: "Employee",
          icon: "ni ni-single-02 text-yellow",
          layout: "/admin",
          accessibleTo: ["college"],
          children: [
            {
              path: "/employee-add",
              name: "Add Employee",
              icon: "ni ni-single-02 text-yellow",
              component: <EmployeeAdd />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/employee-list",
              name: "Employee List",
              icon: "ni ni-single-02 text-yellow",
              component: <EmployeeList />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/employee-code",
              name: "Employee Code",
              icon: "ni ni-single-02 text-yellow",
              component: <EmployeeCode />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/deleted-employee",
              name: "Trash Employee List ",
              icon: "ni ni-single-02 text-yellow",
              component: <DeletedEmployeeList />,
              layout: "/admin",
            },
          ],
        },
      ]
    : []),
  ...(userType === 1
    ? [
        {
          name: "Attendance",
          icon: "fa fa-file text-warning",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/attendance-list",
              name: "Attendance Report",
              icon: "fa fa-list text-warning",
              layout: "/admin",
              accessibleTo: ["admin"],
              component: <AttendanceList />,
            },
          ],
        },
      ]
    : []),
  ...(userType === 2 || userType === 3
    ? [
        {
          name: "Attendance",
          icon: "fa fa-file text-warning",
          layout: "/admin",
          accessibleTo: ["college"],
          children: [
            {
              path: "/today-attendance-list",
              name: "Today Attendance",
              icon: "fa fa-list text-warning",
              component: <AttendanceTodayList />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/generate-attendance-report",
              name: "Generate Attendance",
              icon: "fa fa-list text-warning",
              component: <GenerateAttendanceReport />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
          ],
        },
        {
          path: "/subject-entry-for-seat",
          name: "Seat Extension",
          icon: "ni ni-single-02 text-yellow",
          component: <SubjectEntryForSeat />,
          layout: "/admin",
          accessibleTo: ["college"],
        },
        {
          name: "Guest Faculty",
          icon: "fa fa-file text-warning",
          layout: "/admin",
          accessibleTo: ["college"],
          children: [
            {
              path: "/guest-lecture",
              name: "Add Guest Faculty",
              icon: "ni ni-single-02 text-yellow",
              component: <GuestFacultyForm />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/guest-faculty-list",
              name: "Guest Faculty List",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <GuestFacultyList />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/deleted-guest-faculty",
              name: "Trash Guest faculty List ",
              icon: "ni ni-single-02 text-yellow",
              component: <TrashGuestFaculty />,
              layout: "/admin",
            },
          ],
        },
      ]
    : []),
  ...(userType === 3 ||
  userType === 4 ||
  userType === 1 ||
  userType === 5 ||
  userType === 6
    ? [
        {
          name: "Leave Management",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: [
            "admin",
            "college",
            "employee",
            "director",
            "commissioner",
          ],
          children: [
            ...(userType === 4
              ? [
                  {
                    path: "/leave-dashboard",
                    name: "Leave Dashboard ",
                    icon: "ni ni-shop text-primary",
                    component: <LeaveDashboard />,
                    layout: "/admin",
                    accessibleTo: ["employee"],
                  },
                  {
                    path: "/apply-leave",
                    name: "Apply / आवेदन",
                    icon: "ni ni-check-bold text-primary",
                    component: <ApplyLeave />,
                    layout: "/admin",
                    accessibleTo: ["employee"],
                  },
                  {
                    path: "/leave-reports",
                    name: "My Reports / रिपोर्ट",
                    icon: "ni ni-single-copy-04 text-primary",
                    component: <LeaveList />,
                    layout: "/admin",
                    accessibleTo: ["employee"],
                  },
                  {
                    path: "/joining",
                    name: "Joining / ज्वाइनिंग",
                    icon: "ni ni-single-copy-04 text-primary",
                    component: <JoiningList />,
                    layout: "/admin",
                    accessibleTo: ["employee"],
                  },
                ]
              : []),
            ...(userType === 3
              ? [
                  {
                    path: "/LeaveActionAtPrincipal",
                    name: "Action/ कार्यवाही करें",
                    icon: "ni ni-send text-primary",
                    component: <LeaveActionByPrincipal />,
                    layout: "/admin",
                    accessibleTo: ["college"],
                  },
                  {
                    path: "/ActionsReportAtPrincipal",
                    name: "Action List/कार्यावाही सुची",
                    icon: "ni ni-chart-pie-35 text-primary",
                    component: <ActionsReportAtPrincipal />,
                    layout: "/admin",
                    accessibleTo: ["college"],
                  },
                  {
                    path: "/empLeaveBalanceAtPrincipal",
                    name: "Leave balance/ अवकाश खाता",
                    icon: "ni ni-chart-pie-35 text-primary",
                    component: <LeaveBalanceList />,
                    layout: "/admin",
                    accessibleTo: ["college"],
                  },
                ]
              : []),
            ...(userType === 1 || userType === 5 || userType === 6
              ? [
                  {
                    path: "/LeaveActionAtDirector",
                    name: "Action/ कार्यवाही करें",
                    icon: "ni ni-send text-primary",
                    component: <LeaveActionAtDirector />,
                    layout: "/admin",
                    accessibleTo: ["admin", "director"],
                  },
                  {
                    path: "/file-movement",
                    name: "Forwarded File / अग्रेसित फाईल",
                    icon: "ni ni-send text-primary",
                    component: <FileMovementReport />,
                    layout: "/admin",
                    accessibleTo: ["admin", "director"],
                  },
                ]
              : []),
            ...(userType === 1
              ? [
                  {
                    path: "/ActionsReport",
                    name: "Actions List/कार्यावाही सुची",
                    icon: "ni ni-chart-pie-35 text-primary",
                    component: <ActionsReportAtDirector />,
                    layout: "/admin",
                    accessibleTo: ["admin"],
                  },
                  {
                    path: "/empLeaveBalanceAtDirector",
                    name: "Leave balance/ अवकाश खाता",
                    icon: "ni ni-chart-pie-35 text-primary",
                    component: <LeaveBalanceListPrincipal />,
                    layout: "/admin",
                    accessibleTo: ["admin"],
                  },
                  {
                    path: "/leaveRules",
                    name: "Leave Rules/ अवकाश नियम ",
                    icon: "ni ni-chart-pie-35 text-primary",
                    component: <LeaveRuleRegistration />,
                    layout: "/admin",
                    accessibleTo: ["admin"],
                  },
                ]
              : []),
          ],
        },
      ]
    : []),
  ...(userType === 1
    ? [
        {
          name: "Reports",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: ["admin", "college", "director"],
          children: [
            {
              path: "/employee-report",
              name: "Employee Report",
              icon: "ni ni-check-bold text-primary",
              component: <GenerateEmmployeeReport />,
              layout: "/admin",
              accessibleTo: ["admin,college"],
            },
            {
              path: "/employee-report-count",
              name: "Employee Report Count",
              icon: "ni ni-check-bold text-primary",
              component: <GenerateEmployeeReportCount />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/guest-faculty-list",
              name: "Guest Faculty List",
              icon: "ni ni-check-bold text-primary",
              component: <AllGuestFacultyList />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/guest-faculty-report-count",
              name: "Guest Faculty Report Count",
              icon: "ni ni-check-bold text-primary",
              component: <AllGuestFacultyReportCount />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
        {
          name: "ACR Section",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: ["admin"],
          children: [
            {
              path: "/acr-date-form",
              name: "ACR Date Range",
              icon: "ni ni-pin-3 text-orange",
              component: <ACRDateForm />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/acr-mapping",
              name: "ACR Mapping",
              icon: "ni ni-check-bold text-primary",
              component: <ACRMapping />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
            {
              path: "/acr-director-mapping",
              name: "ACR Mapping (Directorate Employee)",
              icon: "ni ni-check-bold text-primary",
              component: <DirectorateACRMapping />,
              layout: "/admin",
              accessibleTo: ["admin"],
            },
          ],
        },
      ]
    : []),
  ...(pracharyaSection === "Prachary" ||
  rajpatritSection === "Rajpatrit" ||
  arajpatritSection === "Arajpatrit (Directorate)"
    ? [
        {
          name: "Employee Transfer",
          icon: "ni ni-check-bold text-primary",
          layout: "/admin",
          accessibleTo: ["director"],
          children: [
            {
              path: "/employee-transfer",
              name: "Add & Transfer",
              icon: "ni ni-check-bold text-primary",
              component: <EmployeeTransfer />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
            {
              path: "/transfer-list",
              name: "Transfered Employee List",
              icon: "ni ni-check-bold text-primary",
              component: <TransferedEmpList />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
          ],
        },
      ]
    : []),
  ...(acrInRange === true
    ? [
        {
          name: "ACR Form",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: [
            "admin",
            "college",
            "director",
            "employee",
            "division",
            "commissioner",
            "secretary",
          ],
          children: [
            ...(userType === 4 || userType === 5
              ? [
                  {
                    path: "/acr-list",
                    name: "ACR List ",
                    icon: "fa fa-file text-yellow",
                    component: <ACRFormList />,
                    layout: "/admin",
                  },
                  ...(userType === 1 ||
                  userType === 5 ||
                  userType === 6 ||
                  userType === 8
                    ? [
                        ...(userType === 5
                          ? [
                              {
                                path: "/acr-directorate-list",
                                name: "ACR Directorate List",
                                icon: "fa fa-file text-yellow",
                                component: <ACRFormDirectorList />,
                                layout: "/admin",
                              },
                            ]
                          : []),
                      ]
                    : []),
                ]
              : []),
          ],
        },
      ]
    : []),
  ...(GopniyaSection === "Gopniya Prakoshtha"
    ? [
        {
          path: "/ipr-acr",
          name: "IPR / ACR",
          icon: "ni ni-bullet-list-67 text-primary",
          component: <IPRACR />,
          layout: "/admin",
          accessibleTo: ["director"],
        },
      ]
    : ""),
  ...(YojnaSection === "Yojna"
    ? [
        {
          path: "/seat-extension-list",
          name: "Seat Extension List",
          icon: "ni ni-bullet-list-67 text-primary",
          component: <SeatExtensionList />,
          layout: "/admin",
          accessibleTo: ["director"],
        },
      ]
    : ""),
  ...(pracharyaSection === "Prachary"
    ? [
        {
          path: "/principal-list",
          name: "Principal List",
          icon: "ni ni-bullet-list-67 text-primary",
          component: <PrincipalList />,
          layout: "/admin",
          accessibleTo: ["director"],
        },
        {
          name: "Charge Control",
          icon: "ni ni-bullet-list-67 text-primary",
          layout: "/admin",
          accessibleTo: ["director"],
          children: [
            {
              path: "/charge-list",
              name: "Principal Charge",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <PrincipalChargeList />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
            {
              path: "/college-charge-list",
              name: "College Wise Charge",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <CollegeChargeList />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
          ],
        },
      ]
    : ""),
  ...(YojnaSection === "Yojna"
    ? [
        {
          name: "NEW Course Application",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: ["director"],
          children: [
            {
              path: "/new-course-application-list",
              name: "Janbhagidari/Self-Finance",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <NewCourseAllReport />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
            {
              path: "/govt-course-application",
              name: "By Government",
              icon: "ni ni-bullet-list-67 text-primary",
              component: <NewCourseAllReportGovt />,
              layout: "/admin",
              accessibleTo: ["director"],
            },
          ],
        },
      ]
    : ""),
  ...(userType === 3
    ? [
        {
          name: "Employee Transfer",
          icon: "ni ni-collection text-primary",
          layout: "/admin",
          accessibleTo: ["college"],
          children: [
            {
              path: "/incoming-transfer",
              name: "Incoming Transfer",
              icon: "ni ni-check-bold text-primary",
              component: <IncomingTransfer />,
              layout: "/admin",
              accessibleTo: ["college"],
            },

            {
              path: "/outgoing-transfer",
              name: "Outgoing Transfer",
              icon: "ni ni-check-bold text-primary",
              component: <OutgoingTransfer />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
            {
              path: "/all-transfer",
              name: "Final Transfered List",
              icon: "ni ni-check-bold text-primary",
              component: <TransferedEmpListByCollege />,
              layout: "/admin",
              accessibleTo: ["college"],
            },
          ],
        },
      ]
    : ""),

  {
    path: "/error-logs",
    name: "Error Logs",
    icon: "ni ni-check-bold text-primary",
    component: <ErrorLogs />,
    layout: "/admin",
    accessibleTo: ["admin", "college", "director", "employee", "commissioner"],
  },

  {
    name: "NEW Course",
    icon: "ni ni-collection text-primary",
    layout: "/admin",
    accessibleTo: ["college"],
    children: [
      {
        path: "/new-course",
        name: "New Course",
        icon: "ni ni-check-bold text-primary",
        component: <JanBhagiDariCourse />,
        layout: "/admin",
        accessibleTo: ["college"],
      },
      {
        path: "/new-course-list",
        name: "Previous List",
        icon: "ni ni-check-bold text-primary",
        component: <NewCourseCollegeReport />,
        layout: "/admin",
        accessibleTo: ["college"],
      },
    ],
  },
];
export default routes;
