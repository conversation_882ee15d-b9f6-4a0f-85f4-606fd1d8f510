import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  Container,
  Row,
  Col,
  Form,
  FormGroup,
  Input,
  Label,
  Modal,
  Collapse,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Badge,
  Nav,
  NavItem,
  NavLink,
  TabContent,
  TabPane,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import formatDate from "../../utils/formateDate.jsx";
import { Link, useNavigate, useParams } from "react-router-dom";
import { FaDownload } from "react-icons/fa";
import classnames from "classnames"; // For toggling active class
import Swal from "sweetalert2";

const EmployeeRegistrationClassWise = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  let collegeId;

  const [isOpen, setIsOpen] = useState(false);

  const toggleAccordian = () => setIsOpen(!isOpen);

  const [base64Image, setBase64Image] = useState("");
  const [showPreview, setShowPreview] = useState(false);

  const { id } = useParams();
  const [user, setUser] = useState([]);
  const [activeTab, setActiveTab] = useState("1");
  const toggleTab = (tab) => {
    // console.log(`Active tab before change: ${activeTab}`);
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  };

  const handlePreview = () => {
    const submitType = "finalSubmit";

    // e.preventDefault();
    const validateForm = (submitType) => {
      const newErrors = {};
      const errorFields = []; // Array to hold names of fields with errors
      const errorSections = {}; // Object to hold sections and their corresponding fields with errors

      // Define required fields for each submit type
      const requiredFieldsByType = {
        personalInformation: {
          employeeDOB: "",
          employeeCategory: "",
          homeDistrict: "",
        },
        academicInformation: {
          educationQualification: "",
          appointmentCategory: "",
          workplaceDivision: "",
          workplaceDistrict: "",
          workplaceVidhansabha: "",
        },
        employeeInformation: {
          employeeClass: "",
          payScaleType: "",
          firstRegularAppointmentDesignation: "",
          appointmentType: "",
        },
        presentPosition: {
          presentPost: "",
          dateOfJoiningPresentPost: "",
          appointmentDate: "",
          presentBasicpay: "",
          presentPayLevel: "",
          dateOfPresentClgPosting: "",
          probationCompleteStatus: "",
        },
        otherInformation: {
          //latestSeniorityList: "",
          courtCaseStatus: "",
        },
      };

      const addConditionalFields = (formData) => {
        if (formData.disability === "true") {
          requiredFieldsByType.personalInformation.disabiltyTypes = "";
        }
        if (formData.educationQualification === "Other") {
          requiredFieldsByType.academicInformation.qualificationDetails = "";
        }

        if (formData.homeDistrict === "other") {
          requiredFieldsByType.personalInformation.state = "";
          requiredFieldsByType.personalInformation.otherDistrict = "";
        }

        if (formData.homeDistrict === "CG") {
          requiredFieldsByType.personalInformation.cgDistrict = "";
        }

        if (formData.payScaleType === "UGC") {
          requiredFieldsByType.employeeInformation.CurrentAGP = "";
        }

        if (formData.CurrentAGP === "7000") {
          requiredFieldsByType.employeeInformation.dateOfAGPSevenThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleSevenAGP = "";
        }
        if (formData.CurrentAGP === "8000") {
          requiredFieldsByType.employeeInformation.dateOfAGPEightThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleEightAGP = "";
        }
        if (formData.CurrentAGP === "9000") {
          requiredFieldsByType.employeeInformation.dateOfAGPNineThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleNineAGP = "";
        }

        if (formData.appointmentType === "Samviliyan") {
          requiredFieldsByType.employeeInformation.dateOfSam = "";
        }
        if (formData.appointmentType === "Anukampa") {
          requiredFieldsByType.employeeInformation.dateOfAnu = "";
          requiredFieldsByType.employeeInformation.TypingPass = "";
        }
        if (formData.appointmentType === "Ad hoc") {
          requiredFieldsByType.employeeInformation.dateOfAdhoc = "";
          requiredFieldsByType.employeeInformation.dateofRegularApponitment =
            "";
        }
        if (formData.TypingPass === "true") {
          requiredFieldsByType.employeeInformation.uploadTypingPassCertificate =
            "";
        }
        if (
          getClassName(formData.employeeClass) === "Class 3" ||
          getClassName(formData.employeeClass) === "Class 4"
        ) {
          requiredFieldsByType.employeeInformation.uploadsMarksheet = "";
        }
        if (getClassName(formData.employeeClass) === "Class 3") {
          requiredFieldsByType.employeeInformation.IsAccountsTrained = "";
        }

        if (formData.IsAccountsTrained === "true") {
          requiredFieldsByType.employeeInformation.IsAccountsTrainingPass = "";
        }

        if (formData.IsAccountsTrainingPass === "true") {
          requiredFieldsByType.employeeInformation.uploadAccTCertificate = "";
        }

        if (formData.probationCompleteStatus === "onProbation") {
          requiredFieldsByType.presentPosition.policeVerification = "";
        }

        if (formData.probationCompleteStatus === "completion") {
          requiredFieldsByType.presentPosition.dateofCompletion = "";
        }

        // if (formData.policeVerification === "Completed") {
        //   requiredFieldsByType.presentPosition.uploadPoliceverification = "";
        // }

        if (formData.firstTimeScale === "true") {
          requiredFieldsByType.otherInformation.firstScaleDate = "";
        }

        if (formData.secondTimeScale === "true") {
          requiredFieldsByType.otherInformation.secondScaleDate = "";
        }

        if (formData.thirdTimeScale === "true") {
          requiredFieldsByType.otherInformation.thirdScaleDate = "";
        }

        if (
          formData.employeeType === "TEACHING" &&
          formData.presentPost != "Lab Technician"
        ) {
          requiredFieldsByType.otherInformation.subject = "";
        }

        if (formData.Mphil === "true") {
          requiredFieldsByType.otherInformation.subjectOfMphil = "";
          requiredFieldsByType.otherInformation.MphilAwardDate = "";
        }

        if (formData.phd === "true") {
          requiredFieldsByType.otherInformation.subjectOfPhd = "";
          requiredFieldsByType.otherInformation.phdNotifyDate = "";
        }

        if (formData.DscOrDlit === "true") {
          requiredFieldsByType.otherInformation.subjectOfDscOrDlit = "";
          requiredFieldsByType.otherInformation.DscOrDlitNotifyDate = "";
        }

        if (formData.NetSet === "true") {
          requiredFieldsByType.otherInformation.NetSetCertificateDate = "";
        }
      };

      const fieldDisplayNames = {
        employeeDOB: "Date of Birth",
        employeeCategory: "Employee Category",
        homeDistrict: "Home State",
        cgDistrict: "CG District",
        educationQualification: "Qualification",
        appointmentCategory: "Category ,In Which service Joined",
        workplaceDivision: "Workplace Division",
        workplaceDistrict: "Workplace District",
        workplaceVidhansabha: "Workplace Vidhansabha",
        employeeClass: "Class",
        payScaleType: "Pay Scale Type",
        firstRegularAppointmentDesignation:
          "Designation of First Regular Appointment",
        appointmentType: "Appointment Type",
        presentPost: "Present Post",
        dateOfJoiningPresentPost: "Date of Joining Present Post",
        appointmentDate: "Date of First Appointment",
        presentBasicpay: "Present Basic Pay",
        presentPayLevel: "Present Pay Level",
        dateOfPresentClgPosting: "Date of Posting in Present College",
        probationCompleteStatus: "Probation Completion Status",
        //latestSeniorityList: "Latest Seniority List",
        courtCaseStatus: "Court Case Status",
        disabiltyTypes: "Disablity Type",
        qualificationDetails: "Specify Other Qualification",
        state: "Other State",
        otherDistrict: "Other District",
        CurrentAGP: "Current AGP",
        dateOfAGPSevenThous: "Date of getting AGP 7000",
        basicPayscaleSevenAGP:
          "Basic pay in 7th Pay Scale after getting AGP 7000",
        dateOfAGPEightThous: "Date of getting AGP 8000",
        basicPayscaleEightAGP:
          "Basic pay in 7th Pay Scale after getting AGP 8000",
        dateOfAGPNineThous: "Date of getting AGP 9000",
        basicPayscaleNineAGP:
          "Basic pay in 7th Pay Scale after getting AGP 9000",
        dateOfSam: "Date of Samviliyan",
        dateOfAnu: "Date of Anukampa Niyukti",
        TypingPass: "Typing Pass",
        dateOfAdhoc: "Date of Adhoc Appointment",
        dateofRegularApponitment: "Date of regular appointment",
        uploadTypingPassCertificate: "Upload Typing Pass Certificate",
        uploadsMarksheet: "Upload 12th Marksheet",
        IsAccountsTrained: "Accounts Trained (if class 3)",
        IsAccountsTrainingPass: "Accounts Training Pass",
        uploadAccTCertificate: "Upload Accounts Training Certificate",
        policeVerification: "Police Verification (if on Probation)",
        dateofCompletion: "Date of Completion",
        //uploadPoliceverification:"Upload Police Verification (if completed)",
        firstScaleDate: "Availed 1st TimeScale Pay Date",
        secondScaleDate: "Availed 2nd TimeScale Pay Date",
        thirdScaleDate: "Availed 3rd Time Scale Pay Date",
        subject: "Subject",
        subjectOfMphil: "Subject of M. Phil",
        MphilAwardDate: "M.phil awared date/year (if Mphil)",
        subjectOfPhd: "Subject of PhD",
        phdNotifyDate: "PhD Notification Date",
        subjectOfDscOrDlit: "Subject of Dsc / DLit",
        DscOrDlitNotifyDate: "Dsc / DLit Notification Date",
        NetSetCertificateDate: "NET / SET Certificate Date",
      };

      addConditionalFields(formData);

      // Define all required fields for final submit
      const allRequiredFields = {
        ...requiredFieldsByType.personalInformation,
        ...requiredFieldsByType.academicInformation,
        ...requiredFieldsByType.employeeInformation,
        ...requiredFieldsByType.presentPosition,
        ...requiredFieldsByType.otherInformation,
      };

      // console.log("Getting Submit Type");

      const fieldsToValidate =
        submitType === "finalSubmit"
          ? allRequiredFields
          : requiredFieldsByType[submitType];

      if (fieldsToValidate) {
        for (const field in fieldsToValidate) {
          if (fieldsToValidate.hasOwnProperty(field)) {
            if (
              !formData[field] ||
              (typeof formData[field] === "string" && !formData[field].trim())
            ) {
              newErrors[field] = "This Field is Required.";
              errorFields.push(field);

              for (const section in requiredFieldsByType) {
                if (requiredFieldsByType[section].hasOwnProperty(field)) {
                  if (!errorSections[section]) {
                    errorSections[section] = [];
                  }
                  errorSections[section].push(field);
                }
              }
            }
          }
        }
      } else {
        console.error(
          `No required fields defined for submit type: ${submitType}`
        );
      }

      // Construct alert message with custom field names
      if (Object.keys(errorSections).length > 0) {
        let alertMessage =
          "<strong>Please fill in the following fields:</strong><br><br>";

        for (const section in errorSections) {
          alertMessage += `<strong>${section}:</strong><br> - `;
          alertMessage += errorSections[section]
            .map((field) => fieldDisplayNames[field] || field) // Use custom display names or fallback to the key
            .join(", ");
          alertMessage += "<br><br>";
        }

        Swal.fire({
          icon: "warning",
          title: "Missing Fields",
          html: alertMessage,
          confirmButtonText: "OK",
        });
      }

      return newErrors;
    };

    const validationErrors = validateForm(submitType);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setShowPreview(!showPreview);
  };

  const [formData, setFormData] = useState({
    name: "",
    empCode: "",
    employeeGender: "",
    MobileNo: "",
    permanentHomeAddress: "",
    email: "",
    employeeClass: "",
    employeeType: "",
    employeeDOB: "",
    employeeCategory: "",
    disability: "false",
    disabiltyTypes: "",
    homeDistrict: "",
    cgDistrict: "",
    otherDistrict: "",
    state: "",
    educationQualification: "",
    qualificationDetails: "",
    appointmentCategory: "",
    workplaceDivision: "",
    courtCaseStatus: "",
    workplaceDistrict: "",
    workplaceVidhansabha: "",
    retirementDate: "",
    payScaleType: "",
    firstRegularAppointmentDesignation: "",
    appointmentType: "",
    dateOfSam: "",
    dateOfAnu: "",
    dateOfAdhoc: "",
    dateofRegularApponitment: "",
    CurrentAGP: "",
    dateOfAGPSevenThous: null,
    dateOfAGPEightThous: null,
    dateOfAGPNineThous: null,
    basicPayscaleSevenAGP: "",
    basicPayscaleEightAGP: "",
    basicPayscaleNineAGP: "",
    presentPost: "",
    dateOfJoiningPresentPost: "",
    dateOfPresentClgPosting: "",
    appointmentDate: "",
    probationCompleteStatus: "",
    dateofCompletion: null,
    presentBasicpay: "",
    collegeName: "",
    presentPayLevel: "",
    uploadsMarksheet: null,
    IsAccountsTrained: "false",
    IsAccountsTrainingPass: "false",
    uploadAccTCertificate: null,
    policeVerification: "",
    //uploadPoliceverification: null,
    firstTimeScale: "false",
    secondTimeScale: "false",
    thirdTimeScale: "false",
    firstScaleDate: "",
    secondScaleDate: "",
    thirdScaleDate: "",
    TypingPass: "false",
    uploadTypingPassCertificate: null,
    subject: "",
    Mphil: "false",
    subjectOfMphil: "",
    MphilAwardDate: null,
    phd: "false",
    subjectOfPhd: "",
    phdNotifyDate: null,
    DscOrDlit: "false",
    subjectOfDscOrDlit: "",
    DscOrDlitNotifyDate: null,
    NetSet: "false",
    NetSetCertificateDate: null,
    PendingDeptEnquireCase: "false",
    latestSeniorityList: "",
    presentAddress: "",
  });

  // console.log(formData.firstTimeScale, "getting firstTimeScale at First");

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);
  const [districtAll, setDistrictAll] = useState([]);
  const [stateWiseDistrict, setstateWiseDistrict] = useState([]);
  const [isDivisionChanged, setIsDivisionChanged] = useState(false);
  const [division, setDivision] = useState([]);
  const [showDateOfCompletion, setShowDateOfCompletion] = useState(false);
  const [showOtherQualification, setShowOtherQualification] = useState(false);
  const [errors, setErrors] = useState({});
  const [classData, setClassData] = useState([]);
  const [updatedFields, setUpdatedFields] = useState({});
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [submittedSections, setSubmittedSections] = useState({
    personalInformation: false,
    academicInformation: false,
    employeeInformation: false,
    presentPosition: false,
    otherInformation: false,
  });
  const handleInputChange = (e) => {
    // console.log(e);
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
    setUpdatedFields((prevUpdatedFields) => ({
      ...prevUpdatedFields,
      [name]: value, // Store modified field
    }));

    // console.log(formData.firstTimeScale, "getting firstTimeScale at 3rd");

    // Update formData based on the field name
    setFormData((prevFormData) => {
      let updatedFormData = { ...prevFormData, [name]: value };

      // Handle homeDistrict field (conditional rendering)
      if (name === "homeDistrict") {
        setIsOtherSelected(value === "other");
      }

      // Handle educationQualification field (conditional logic for "Other")

      // Handle probationCompleteStatus field (conditional logic)
      if (name === "probationCompleteStatus") {
        if (value === "completion") {
          setShowDateOfCompletion("true");
          updatedFormData.dateofCompletion = ""; // Ensure date is cleared
        } else if (value === "onProbation") {
          setShowDateOfCompletion("false");
          updatedFormData.dateofCompletion = null; // Remove date
        }
      }

      if (name === "CurrentAGP") {
        updatedFormData.dateOfAGPSevenThous =
          value === "7000" ? prevFormData.dateOfAGPSevenThous : "";
        updatedFormData.dateOfAGPEightThous =
          value === "8000" ? prevFormData.dateOfAGPEightThous : "";
        updatedFormData.dateOfAGPNineThous =
          value === "9000" ? prevFormData.dateOfAGPNineThous : "";
      }

      if (
        name === "firstRegularAppointmentDesignation" ||
        name === "presentPost"
      ) {
        if (
          updatedFormData.firstRegularAppointmentDesignation ===
          updatedFormData.presentPost
        ) {
          updatedFormData.dateOfJoiningPresentPost =
            updatedFormData.appointmentDate;
        } else {
          updatedFormData.dateOfJoiningPresentPost = "";
        }
      }

      if (name === "payScaleType") {
        updatedFormData.presentBasicpay = "";
        updatedFormData.presentPayLevel = "";
      }

      return updatedFormData;
    });
  };

  useEffect(() => {
    const getEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee-byId/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          setUser(data);
          // console.log(data, "Getting Employee Data");

          setFormData({
            ...formData,
            name: data.name,
            empCode: data.empCode,
            employeeGender: data.gender,
            MobileNo: data.contact,
            permanentHomeAddress: data.address,
            email: data.email,
            employeeClass: data.classData,
            employeeType: data.workType,
            presentPost: data.designationDetails?.designation,
          });

          collegeId = data.college;
        }
      } catch (error) {
        console.error("Error fetching Employee data:", error);
        alert("Failed to load Employee data.");
      }
    };
    getEmployee();
  }, [endPoint]);

  const [userProfile, setUserProfile] = useState([]);

  const [currentClass, setCurrentClass] = useState("");

  const getEmployee = async () => {
    const formatDateForInput = (dateString) => {
      if (!dateString) return ""; // Return empty if no date
      const date = new Date(dateString);
      if (isNaN(date)) return ""; // Check if the date is valid
      return date.toISOString().split("T")[0];
    };

    try {
      // console.log(id, "Getting ID");

      const response = await axios.get(
        `${endPoint}/api/get-emp-profile?empId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setUserProfile(data);
        setFormData((prevFormData) => ({
          ...prevFormData,

          // Personal Information
          employeeDOB: data?.personalInformation?.employeeDOB
            ? formatDateForInput(data.personalInformation.employeeDOB)
            : prevFormData.employeeDOB,
          employeeCategory:
            data?.personalInformation?.employeeCategory ??
            prevFormData.employeeCategory,
          disability: data?.personalInformation?.disability
            ? String(data.personalInformation.disability)
            : prevFormData.disability,
          disabiltyTypes: data?.personalInformation?.disabiltyTypes
            ? String(data.personalInformation.disabiltyTypes)
            : prevFormData.disabiltyTypes,
          homeDistrict:
            data?.personalInformation?.homeDistrict ??
            prevFormData.homeDistrict,
          cgDistrict:
            data?.personalInformation?.cgDistrict ?? prevFormData.cgDistrict,
          otherDistrict:
            data?.personalInformation?.otherDistrict ??
            prevFormData.otherDistrict,
          state: data?.personalInformation?.state ?? prevFormData.state,

          // Academic Information
          educationQualification:
            data?.academicInformation?.educationQualification ??
            prevFormData.educationQualification,
          qualificationDetails:
            data?.academicInformation?.qualificationDetails ??
            prevFormData.qualificationDetails,
          appointmentCategory:
            data?.academicInformation?.appointmentCategory ??
            prevFormData.appointmentCategory,
          workplaceDivision:
            data?.academicInformation?.workplaceDivision ??
            prevFormData.workplaceDivision,
          workplaceDistrict:
            data?.academicInformation?.workplaceDistrict ??
            prevFormData.workplaceDistrict,
          workplaceVidhansabha:
            data?.academicInformation?.workplaceVidhansabha ??
            prevFormData.workplaceVidhansabha,

          // Employee Information
          retirementDate: data?.employeeInformation?.retirementDate
            ? formatDateForInput(data.employeeInformation.retirementDate)
            : prevFormData.retirementDate,
          payScaleType:
            data?.employeeInformation?.payScaleType ??
            prevFormData.payScaleType,
          firstRegularAppointmentDesignation:
            data?.employeeInformation?.firstRegularAppointmentDesignation ??
            prevFormData.firstRegularAppointmentDesignation,
          appointmentType:
            data?.employeeInformation?.appointmentType ??
            prevFormData.appointmentType,
          dateOfAdhoc: data?.employeeInformation?.dateOfAdhoc
            ? formatDateForInput(data.employeeInformation.dateOfAdhoc)
            : prevFormData.dateOfAdhoc,
          dateOfSam: data?.employeeInformation?.dateOfSam
            ? formatDateForInput(data.employeeInformation.dateOfSam)
            : prevFormData.dateOfSam,
          dateOfAnu: data?.employeeInformation?.dateOfAnu
            ? formatDateForInput(data.employeeInformation.dateOfAnu)
            : prevFormData.dateOfAnu,
          dateofRegularApponitment: data?.employeeInformation
            ?.dateofRegularApponitment
            ? formatDateForInput(
                data.employeeInformation.dateofRegularApponitment
              )
            : prevFormData.dateofRegularApponitment,
          CurrentAGP:
            data?.employeeInformation?.CurrentAGP ?? prevFormData.CurrentAGP,
          dateOfAGPSevenThous: data?.employeeInformation?.dateOfAGPSevenThous
            ? formatDateForInput(data.employeeInformation.dateOfAGPSevenThous)
            : prevFormData.dateOfAGPSevenThous,
          dateOfAGPEightThous: data?.employeeInformation?.dateOfAGPEightThous
            ? formatDateForInput(data.employeeInformation.dateOfAGPEightThous)
            : prevFormData.dateOfAGPEightThous,
          dateOfAGPNineThous: data?.employeeInformation?.dateOfAGPNineThous
            ? formatDateForInput(data.employeeInformation.dateOfAGPNineThous)
            : prevFormData.dateOfAGPNineThous,
          basicPayscaleSevenAGP:
            data?.employeeInformation?.basicPayscaleSevenAGP ??
            prevFormData.basicPayscaleSevenAGP,
          basicPayscaleEightAGP:
            data?.employeeInformation?.basicPayscaleEightAGP ??
            prevFormData.basicPayscaleEightAGP,
          basicPayscaleNineAGP:
            data?.employeeInformation?.basicPayscaleNineAGP ??
            prevFormData.basicPayscaleNineAGP,
          IsAccountsTrained:
            data?.employeeInformation?.IsAccountsTrained ??
            prevFormData.IsAccountsTrained,
          IsAccountsTrainingPass:
            data?.employeeInformation?.IsAccountsTrainingPass ??
            prevFormData.IsAccountsTrainingPass,
          uploadAccTCertificate:
            data?.employeeInformation?.uploadAccTCertificate ??
            prevFormData.uploadAccTCertificate,
          TypingPass:
            data?.employeeInformation?.TypingPass ?? prevFormData.TypingPass,
          uploadTypingPassCertificate:
            data?.employeeInformation?.uploadTypingPassCertificate ??
            prevFormData.uploadTypingPassCertificate,
          uploadsMarksheet:
            data?.employeeInformation?.uploadsMarksheet ??
            prevFormData.uploadsMarksheet,
          employeeClass:
            data?.employeeInformation?.employeeClass ??
            prevFormData.employeeClass,

          // Current Position Information
          dateOfJoiningPresentPost: data?.presentInformation
            ?.dateOfJoiningPresentPost
            ? formatDateForInput(
                data.presentInformation.dateOfJoiningPresentPost
              )
            : prevFormData.dateOfJoiningPresentPost,
          dateOfPresentClgPosting: data?.presentInformation
            ?.dateOfPresentClgPosting
            ? formatDateForInput(
                data.presentInformation.dateOfPresentClgPosting
              )
            : prevFormData.dateOfPresentClgPosting,
          appointmentDate: data?.presentInformation?.appointmentDate
            ? formatDateForInput(data.presentInformation.appointmentDate)
            : prevFormData.appointmentDate,
          probationCompleteStatus:
            data?.presentInformation?.probationCompleteStatus ??
            prevFormData.probationCompleteStatus,
          dateofCompletion: data?.presentInformation?.dateofCompletion
            ? formatDateForInput(data.presentInformation.dateofCompletion)
            : prevFormData.dateofCompletion,
          presentBasicpay:
            data?.presentInformation?.presentBasicpay ??
            prevFormData.presentBasicpay,
          collegeName:
            data?.presentInformation?.collegeName ?? prevFormData.collegeName,
          presentPayLevel:
            data?.presentInformation?.presentPayLevel ??
            prevFormData.presentPayLevel,
          policeVerification:
            data?.presentInformation?.policeVerification ??
            prevFormData.policeVerification,
          //uploadPoliceverification: data?.presentInformation?.uploadPoliceverification ?? prevFormData.uploadPoliceverification,

          // Other Information
          firstTimeScale: data?.otherInformation?.firstTimeScale
            ? String(data.otherInformation.firstTimeScale)
            : prevFormData.firstTimeScale,
          secondTimeScale: data?.otherInformation?.secondTimeScale
            ? String(data.otherInformation.secondTimeScale)
            : prevFormData.secondTimeScale,
          thirdTimeScale: data?.otherInformation?.thirdTimeScale
            ? String(data.otherInformation.thirdTimeScale)
            : prevFormData.thirdTimeScale,
          firstScaleDate: data?.otherInformation?.firstScaleDate
            ? formatDateForInput(data.otherInformation.firstScaleDate)
            : prevFormData.firstScaleDate,
          secondScaleDate: data?.otherInformation?.secondScaleDate
            ? formatDateForInput(data.otherInformation.secondScaleDate)
            : prevFormData.secondScaleDate,
          thirdScaleDate: data?.otherInformation?.thirdScaleDate
            ? formatDateForInput(data.otherInformation.thirdScaleDate)
            : prevFormData.thirdScaleDate,
          subject: data?.otherInformation?.subject ?? prevFormData.subject,
          Mphil: data?.otherInformation?.Mphil
            ? String(data.otherInformation.Mphil)
            : prevFormData.Mphil,
          subjectOfMphil:
            data?.otherInformation?.subjectOfMphil ??
            prevFormData.subjectOfMphil,
          MphilAwardDate: data?.otherInformation?.MphilAwardDate
            ? formatDateForInput(data.otherInformation.MphilAwardDate)
            : prevFormData.MphilAwardDate,
          phd: data?.otherInformation?.phd
            ? String(data.otherInformation.phd)
            : prevFormData.phd,
          subjectOfPhd:
            data?.otherInformation?.subjectOfPhd ?? prevFormData.subjectOfPhd,
          phdNotifyDate: data?.otherInformation?.phdNotifyDate
            ? formatDateForInput(data.otherInformation.phdNotifyDate)
            : prevFormData.phdNotifyDate,
          NetSet: data?.otherInformation?.NetSet
            ? String(data.otherInformation.NetSet)
            : prevFormData.NetSet,
          NetSetCertificateDate: data?.otherInformation?.NetSetCertificateDate
            ? formatDateForInput(data.otherInformation.NetSetCertificateDate)
            : prevFormData.NetSetCertificateDate,
          courtCaseStatus:
            data?.otherInformation?.courtCaseStatus !== undefined &&
            data?.otherInformation?.courtCaseStatus !== ""
              ? data.otherInformation.courtCaseStatus
              : prevFormData.courtCaseStatus,
          PendingDeptEnquireCase:
            data?.otherInformation?.PendingDeptEnquireCase !== undefined &&
            data?.otherInformation?.PendingDeptEnquireCase !== ""
              ? String(data.otherInformation.PendingDeptEnquireCase)
              : prevFormData.PendingDeptEnquireCase,
          latestSeniorityList:
            data?.otherInformation?.latestSeniorityList !== undefined &&
            data?.otherInformation?.latestSeniorityList !== ""
              ? data.otherInformation.latestSeniorityList
              : prevFormData.latestSeniorityList,
          DscOrDlit:
            data?.otherInformation?.DscOrDlit !== undefined &&
            data?.otherInformation?.DscOrDlit !== ""
              ? data.otherInformation.DscOrDlit
              : prevFormData.DscOrDlit,
          subjectOfDscOrDlit:
            data?.otherInformation?.subjectOfDscOrDlit !== undefined &&
            data?.otherInformation?.subjectOfDscOrDlit !== ""
              ? String(data.otherInformation.subjectOfDscOrDlit)
              : prevFormData.subjectOfDscOrDlit,
          DscOrDlitNotifyDate:
            data?.otherInformation?.DscOrDlitNotifyDate !== undefined &&
            data?.otherInformation?.DscOrDlitNotifyDate !== ""
              ? formatDateForInput(data.otherInformation.DscOrDlitNotifyDate)
              : prevFormData.DscOrDlitNotifyDate,
        }));
      }
    } catch (error) {
      console.error("Error fetching Employee data:", error);
      alert("Failed to load Employee data.");
    }
  };

  useEffect(() => {
    getEmployee();
  }, [user]);

  useEffect(() => {
    const fetchCollegeInfo = async () => {
      if (!collegeId) {
        console.warn("No collegeId provided");
        return;
      }

      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-college/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200 && response.data) {
          setFormData((prevData) => ({
            ...prevData,
            collegeName: response.data.name, // Assuming the API response contains `collegeName`
          }));
        } else {
          alert("College Not Found.");
        }
      } catch (error) {
        console.error("An error occurred while fetching college data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollegeInfo();
  }, [collegeId, token]);

  useEffect(() => {
    const getDistrictAll = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrictAll(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 1.");
      }
    };

    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 2.");
      }
    };

    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 
             "Content-Type": "application/json",
             'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          const data = response.data;
          setClassData(response.data);

          // console.log(user?.classData, "Getting At first hhhh");

          const specificClass = data.find(
            (type) => type._id === user?.classData
          );
          setCurrentClass(specificClass.className);
          // console.log(specificClass.className, "Getting Class");
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    if (user?.classData) {
      fetchClassData();
    }

    getDistrict();
    getDistrictAll();
    getDistrictAllStateWise();
  }, [endPoint, token, user]); // Dependencies

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    getDivision();
  }, [endPoint, token]); // Dependencies

  const handleDivisionChange = async (e) => {
    const { value } = e.target;
    setIsDivisionChanged(true); // Set to true when a new division is selected
    if (!value) {
      setDistrict([]); // District list ko empty kar do
      return;
    }
    setFormData((prevData) => ({
      ...prevData,
      workplaceDivision: value,
      workplaceDistrict: "",
      workplaceVidhansabha: "",
    }));
    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // Check if the response is successful and contains valid data
      if (response.status === 200 && Array.isArray(response.data)) {
        setDistrict(response.data); // Set the fetched district data
      } else {
        alert("Failed to fetch districts. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert(
        "An error occurred while fetching districts. Please try again later."
      );
    }
  };

  const getDistrictAllStateWise = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-alldistrict-state`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setstateWiseDistrict(data);
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
      alert("Failed to load district data 3.");
    }
  };
  const handleStateChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/get-district-state-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setstateWiseDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      state: value,
    });
  };
  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    // Check if "Other" is selected for Home District

    setFormData({
      ...formData,
      workplaceDistrict: value, // Correctly update the workplaceDistrict in formData
    });

    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      // Check if "Other" is selected for Home District
      if (name === "homeDistrict") {
        setIsOtherSelected(value === "other");
      }
      // Check if the response is successful and contains valid data
      if (response.status === 200 && Array.isArray(response.data)) {
        setVidhansabha(response.data); // Set the fetched Vidhansabha data
      } else {
        alert("Failed to fetch Vidhansabha data. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching Vidhansabha data:", error);
      alert(
        "An error occurred while fetching Vidhansabha data. Please try again later."
      );
    }
  };

  const handleVidhansabhaChange = (e) => {
    setFormData({
      ...formData,
      workplaceVidhansabha: e.target.value,
    });
  };
  const handleStateDistChange = (e) => {
    const { value } = e.target; // Destructure value from the target
    setFormData((prevFormData) => ({
      ...prevFormData, // Keep the other fields intact
      otherDistrict: value, // Update the specific field
    }));
  };
  // useEffect(() => {
  //   const fetchCollegeInfo = async () => {
  //     if (user && user.college) {
  //       // Check if user and college are defined

  //       try {
  //         const response = await axios.get(
  //           `${endPoint}/api/college/get-college/${user.college}`,
  //           {
  //             headers: {
  //               "Content-Type": "application/json",

  //               Authorization: `Bearer ${token}`,
  //             },
  //           }
  //         );
  //         if (response.status === 200) {
  //           setClgInfo(response.data);
  //         } else {
  //           alert("College Not Found.");
  //         }
  //       } catch (error) {
  //         console.error("An error occurred while Getting Data:", error);
  //         alert("An error occurred. Please try again later.");
  //       }
  //     }
  //   };
  //   fetchCollegeInfo();
  // }, [user, endPoint, token]);

  const [appointment, setAppointment] = useState([]);
  useEffect(() => {
    const getAppointment = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-appointment-type`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setAppointment(data);
        }
      } catch (error) {
        console.error("Error fetching appointment data:", error);
        alert("Failed to load appointment data.");
      }
    };

    getAppointment(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const [category, setCategory] = useState([]);
  useEffect(() => {
    const getCategory = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-category-types`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setCategory(data);
        }
      } catch (error) {
        console.error("Error fetching Category data:", error);
        alert("Failed to load Category data.");
      }
    };

    getCategory(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const [firstAppointment, setFirstAppointment] = useState([]);
  useEffect(() => {
    const getFirstAppointment = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-first-post-appointment`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setFirstAppointment(data);
        }
      } catch (error) {
        console.error("Error fetching Post of First Appointment data:", error);
        alert("Failed to load Post of First Appointment data.");
      }
    };

    getFirstAppointment(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.workplaceDivision}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.workplaceDivision) fetchDistrict();
  }, [formData.workplaceDivision, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.workplaceDistrict}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,

            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.workplaceVidhansabha) fetchVidhansabha();
  }, [formData.workplaceVidhansabha, endPoint, token]);

  const getClassName = (value) => {
    // console.log(value, classData, "adfjkahsd");
    const classObj = classData.find((classItem) => classItem._id === value);
    return classObj ? classObj.className : "Unknown Class Name";
  };

  // Handle Class Type Change (Triggers fetching designations)
  const handleClassInputChange = async (e) => {
    const { name, value } = e.target;
    setErrors({ ...errors, [name]: "" });
    try {
      // Fetch designation data based on class
      const response = await axios.get(
        `${endPoint}/api/degisnation-class-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        // Set designation data if found
        setDesignationData(response.data);
      } else {
        SwalMessageAlert("Designation Not Found", "error");
      }
    } catch (error) {
      console.error("Error fetching designation data:", error);
    }
    setFormData({
      ...formData,
      employeeClass: value,
      firstRegularAppointmentDesignation: formData.designation, // Reset designation when classType changes
      uploadsMarksheet:
        value === "Class 3" || value === "Class 4"
          ? formData.uploadsMarksheet
          : "",
      IsAccountsTrained: value === "Class 3" ? formData.IsAccountsTrained : "",
      className: getClassName(value), // Set the className based on classId
    });
  };

  const [designationData, setDesignationData] = useState([]);
  const [disabiltyType, setDisabiltyType] = useState([]);
  const [subjectData, setSubjectData] = useState([]);
  useEffect(() => {
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: { 
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDesignation();
  }, [endPoint, token]); // Dependencies
  // Fetch disability types
  useEffect(() => {
    const fetchDisabilityList = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-disability-types`,
          {
            headers: { 
              
              'web-url': window.location.href,
              Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setDisabiltyType(response.data);
        } else {
          SwalMessageAlert("No Disability Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDisabilityList();
  }, [endPoint, token]); // Dependencies

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: { 

            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchSubjectList();
  }, [endPoint, token]);

  const [stateName, setStateName] = useState([]); // For table data
  useEffect(() => {
    const fetchStateList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-all-state`, {
          headers: { 
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setStateName(response.data);
        } else {
          SwalMessageAlert("No State Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchStateList();
  }, [endPoint, token]); // Dependencies

  const [payLevel, setPayLevel] = useState([]); // For table data
  const [basicPay, setBasicPay] = useState([]);
  const [basicPayTeaching, setBasicPayTeaching] = useState([]);

  useEffect(() => {
    const fetchPayLevel = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-pay-level`, {
          headers: { 
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setPayLevel(response.data); // ✅ Correctly updating PayLevel state
        } else {
          SwalMessageAlert("No Pay Level Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching Pay Level data:", error);
      }
    };

    fetchPayLevel();
  }, [endPoint, token]); // Dependencies

  const [payLevelTeaching, setPayLevelTeaching] = useState([]);

  useEffect(() => {
    const fetchPayLevelTeaching = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-pay-level-teaching`,
          {
            headers: { 
              'web-url': window.location.href,
              Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setPayLevelTeaching(response.data);
          // console.log(response.data,"Getting Pay Level Teaching ");
        } else {
          SwalMessageAlert("No Pay Level Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching Pay Level data:", error);
      }
    };

    fetchPayLevelTeaching();
  }, [endPoint, token]); // Dependencies

  useEffect(() => {
    const getBasicPay = async () => {
      try {
        let response;
        if (formData.employeeType === "TEACHING") {
          response = await axios.get(`${endPoint}/api/get-all-basic-pay`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
        } else {
          response = await axios.get(
            `${endPoint}/api/get-all-basic-pay-teaching`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
        }
        if (response.status === 200) {
          setBasicPay(response.data);
        }
      } catch (error) {
        console.error("Error fetching Basic Pay data:", error);
        alert("Failed to load Basic Pay data.");
      }
    };

    getBasicPay();
  }, [endPoint, token]);

  const handlePayLevelChange = async (e) => {
    const { name, value } = e.target;
    try {
      let response;

      if (formData.payScaleType === "UGC") {
        response = await axios.get(
          `${endPoint}/api/get-basic-pay-level-wise-teaching/${value}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
      } else {
        response = await axios.get(
          `${endPoint}/api/get-basic-pay-level-wise/${value}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
      }

      if (response.status === 200) {
        setBasicPay(response.data);
        // console.log(response.data,"Getting Pay basic Pay teaching");

        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    if (name === "presentPayLevel") {
      setFormData({
        ...formData,
        presentPayLevel: value,
        presentBasicPay: formData.presentBasicPay || "", // Agar blank ho toh reset ho jaye
      });

      // Error remove karna
      setErrors({ ...errors, presentPayLevel: "" });

      setErrors((prevErrors) => ({
        ...prevErrors,
        presentPayLevel: "",
      }));
    }
    // setFormData({
    //     ...formData,
    //     presentBasicpay: Number(value),
    // });
  };

  useEffect(() => {
    if (formData.disability === "false") {
      setFormData((prevState) => ({
        ...prevState,
        disabiltyTypes: "",
      }));
    }
  }, [formData.disability, setFormData]);

  // Effect for education qualification
  useEffect(() => {
    if (formData.educationQualification !== "Others") {
      setFormData((prevState) => ({
        ...prevState,
        qualificationDetails: "",
      }));
    }
  }, [formData.educationQualification, setFormData]);

  // Effect for home district
  useEffect(() => {
    if (formData.homeDistrict === "CG") {
      setFormData((prevState) => ({
        ...prevState,
        state: "",
        otherDistrict: "",
      }));
    }
  }, [formData.homeDistrict, setFormData]);

  // Effect for Current AGP
  useEffect(() => {
    if (formData.CurrentAGP === "7000") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfAGPEightThous: "",
        basicPayscaleEightAGP: "",
        dateOfAGPNineThous: "",
        basicPayscaleNineAGP: "",
      }));
    } else if (formData.CurrentAGP === "8000") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfAGPSevenThous: "",
        basicPayscaleSevenAGP: "",
        dateOfAGPNineThous: "",
        basicPayscaleNineAGP: "",
      }));
    } else if (formData.CurrentAGP === "9000") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfAGPSevenThous: "",
        basicPayscaleSevenAGP: "",
        dateOfAGPEightThous: "",
        basicPayscaleEightAGP: "",
      }));
    }
  }, [formData.CurrentAGP, setFormData]);

  // Effect for pay scale type
  useEffect(() => {
    if (formData.payScaleType !== "UGC") {
      setFormData((prevState) => ({
        ...prevState,
        CurrentAGP: "",
        dateOfAGPSevenThous: "",
        basicPayscaleSevenAGP: "",
        dateOfAGPEightThous: "",
        basicPayscaleEightAGP: "",
        dateOfAGPNineThous: "",
        basicPayscaleNineAGP: "",
      }));
    }
  }, [formData.payScaleType, setFormData]);

  // Effect for appointment type
  useEffect(() => {
    if (formData.appointmentType !== "Samviliyan") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfSam: "",
      }));
    }
    if (formData.appointmentType !== "Ad hoc") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfAdhoc: "",
        dateofRegularApponitment: "",
      }));
    }
    if (formData.appointmentType !== "Anukampa") {
      setFormData((prevState) => ({
        ...prevState,
        dateOfAnu: "",
        TypingPass: "false",
      }));
    }
  }, [formData.appointmentType, setFormData]);

  // Effect for TypingPass
  useEffect(() => {
    if (formData.TypingPass === "false") {
      setFormData((prevState) => ({
        ...prevState,
        uploadTypingPassCertificate: "",
      }));
    }
  }, [formData.TypingPass, setFormData]);

  // Effect for date validations

  // Effect for time scales
  useEffect(() => {
    if (formData.firstTimeScale === "false") {
      setFormData((prevState) => ({
        ...prevState,
        secondTimeScale: "false",
        thirdTimeScale: "false",
        firstScaleDate: "",
        secondScaleDate: "",
        thirdScaleDate: "",
      }));
    }
    if (formData.secondTimeScale === "false") {
      setFormData((prevState) => ({
        ...prevState,
        thirdTimeScale: "false",
        secondScaleDate: "",
        thirdScaleDate: "",
      }));
    }
    if (formData.thirdTimeScale === "false") {
      setFormData((prevState) => ({
        ...prevState,
        thirdScaleDate: "",
      }));
    }
  }, [
    formData.firstTimeScale,
    formData.secondTimeScale,
    formData.thirdTimeScale,
    setFormData,
  ]);

  // Effect for employee type
  useEffect(() => {
    if (formData.employeeType !== "TEACHING") {
      setFormData((prevState) => ({
        ...prevState,
        subject: "",
        Mphil: "",
      }));
    }
  }, [formData.employeeType, setFormData]);

  // Effect for Mphil
  useEffect(() => {
    if (formData.Mphil === "false") {
      setFormData((prevState) => ({
        ...prevState,
        subjectOfMphil: "",
        MphilAwardDate: "",
      }));
    }
  }, [formData.Mphil, setFormData]);

  // Effect for PhD
  useEffect(() => {
    if (formData.phd === "false") {
      setFormData((prevState) => ({
        ...prevState,
        subjectOfPhd: "",
        phdNotifyDate: "",
      }));
    }
  }, [formData.phd, setFormData]);

  // Effect for DscOrDlit
  useEffect(() => {
    if (formData.DscOrDlit === "true") {
      setFormData((prevState) => ({
        ...prevState,
        subjectOfDscOrDlit: "",
        DscOrDlitNotifyDate: "",
      }));
    }
  }, [formData.DscOrDlit, setFormData]);

  // Effect for NetSet
  useEffect(() => {
    if (formData.NetSet === "true") {
      setFormData((prevState) => ({
        ...prevState,
        NetSetCertificateDate: "",
      }));
    }
  }, [formData.NetSet, setFormData]);

  const handleSubmit = async (e, sectionName) => {
    e.preventDefault();
    try {
      const section = sectionName;

      const sections = {
        personalInformation: [
          "empCode",
          "name",
          "email",
          "MobileNo",
          "employeeDOB",
          "employeeGender",
          "employeeCategory",
          "homeDistrict",
          "cgDistrict",
          "state",
          "otherDistrict",
          "permanentHomeAddress",
          "disability",
          "disabiltyTypes",
        ],

        academicInformation: [
          "appointmentCategory",
          "educationQualification",
          "qualificationDetails",
          "workplaceDivision",
          "workplaceDistrict",
          "workplaceVidhansabha",
        ],

        employeeInformation: [
          "employeeType",
          "payScaleType",
          "CurrentAGP",
          "dateOfAGPSevenThous",
          "basicPayscaleSevenAGP",
          "dateOfAGPEightThous",
          "basicPayscaleEightAGP",
          "dateOfAGPNineThous",
          "basicPayscaleNineAGP",
          "retirementDate",
          "employeeClass",
          "firstRegularAppointmentDesignation",
          "appointmentType",
          "dateOfSam",
          "dateOfAnu",
          "dateOfAdhoc",
          "dateofRegularApponitment",
          "TypingPass",
          "IsAccountsTrained",
          "IsAccountsTrainingPass",
        ],

        presentPosition: [
          "presentPost",
          "dateOfJoiningPresentPost",
          "appointmentDate",
          "dateOfPresentClgPosting",
          "collegeName",
          "probationCompleteStatus",
          "policeVerification",
          "dateofCompletion",
          "presentPayLevel",
          "presentBasicpay",
        ],

        otherInformation: [
          "firstTimeScale",
          "firstScaleDate",
          "secondTimeScale",
          "secondScaleDate",
          "thirdTimeScale",
          "thirdScaleDate",
          "latestSeniorityList",
          "subject",
          "Mphil",
          "subjectOfMphil",
          "MphilAwardDate",
          "phd",
          "subjectOfPhd",
          "phdNotifyDate",
          "DscOrDlit",
          "subjectOfDscOrDlit",
          "DscOrDlitNotifyDate",
          "NetSet",
          "NetSetCertificateDate",
          "courtCaseStatus",
          "PendingDeptEnquireCase",
        ],
      };

      const fieldsToUpdate = sections[section];

      const formDataSubmit = new FormData();
      Object.keys(formData).forEach((key) => {
        if (
          fieldsToUpdate.includes(key) &&
          formData[key] !== null &&
          formData[key] !== undefined
        ) {
          formDataSubmit.append(key, formData[key]);
        }
      });

      formDataSubmit.append("infoType", section);
      formDataSubmit.append("employeeId", id);

      // console.log("getting Section Name", section);

      // if (section === "personalInformation") {
      //   formDataSubmit.append("infoType", section);
      // }
      // else if (section === "academicInformation") {
      //   formDataSubmit.append("infoType", section);
      // }
      if (section === "employeeInformation") {
        formDataSubmit.append(
          "uploadAccTCertificate",
          formData.uploadAccTCertificate
        );
        formDataSubmit.append(
          "uploadTypingPassCertificate",
          formData.uploadTypingPassCertificate
        );
        formDataSubmit.append("uploadsMarksheet", formData.uploadsMarksheet);
      }
      // else if (section === "presentPosition") {
      //   //formDataSubmit.append("uploadPoliceverification", formData.uploadPoliceverification);
      // }
      // else if (section === "otherInformation") {
      //   formDataSubmit.append("uploadPoliceverification", formData.uploadPoliceverification);
      // }

      // console.log("getting Section Name hitting in bottom", section);

      const response = await axios.post(
        `${endPoint}/api/employee-register/add`,
        formDataSubmit,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            'web-url': window.location.href,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Handle Response
      if (response.status === 200) {
        SwalMessageAlert("Data Saving", "success");
        // console.log("getting Section Name hitting in bottom with 200", section);
      } else {
        SwalMessageAlert(
          "Unexpected response from server. Please try again.",
          "error"
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      SwalMessageAlert(
        "Failed to submit. Please check the console for details.",
        "error"
      );
    }
  };

  const getErrorField = (fieldIndex) => {
    switch (fieldIndex) {
      case 1:
        return "uploadPoliceverification";
      case 2:
        return "uploadsMarksheet";
      case 3:
        return "uploadAccTCertificate";
      case 4:
        return "uploadTypingPassCertificate";
      default:
        return "";
    }
  };

  const [uploadPoliceverification, setUploadPoliceverification] = useState([]); // For table data
  const [uploadsMarksheet, setUploadsMarksheet] = useState([]); // For table data
  const [uploadAccTCertificate, setUploadAccTCertificate] = useState([]); // For table data
  const [uploadTypingPassCertificate, setUploadTypingPassCertificate] =
    useState([]); // For table data

  // console.log(currentClass,"Getting Fsdkfjsldkfjalksjdelfj");

  const handleImageChange = (e, fieldIndex) => {
    // console.log("Hitting here!");

    if (!e.target.files || e.target.files.length === 0) {
      // console.log("No file selected");
      return;
    }

    const file = e.target.files[0];

    const maxSize = 250 * 1024;
    if (file) {
      //  Check file type (Only PDF allowed)
      if (file.type !== "application/pdf") {
        e.target.value = ""; // Clear input field
        setErrors({
          ...errors,
          [getErrorField(fieldIndex)]: "Only PDF files are allowed!",
        });
        return;
      }

      //  Check file size (Max 250KB)
      if (file.size > maxSize) {
        e.target.value = ""; // Clear input field
        setErrors({
          ...errors,
          [getErrorField(fieldIndex)]: "File size should not exceed 250KB!",
        });
        return;
      }

      //  If valid, clear error
      setErrors({
        ...errors,
        [getErrorField(fieldIndex)]: "",
      });
    }
    const reader = new FileReader();
    reader.onload = () => {
      const imageDataURL = reader.result;
      switch (fieldIndex) {
        case 1:
          // setFormData((prevState) => ({
          //   ...prevState,
          //   uploadPoliceverification: file,
          // }));
          // setUploadPoliceverification(imageDataURL)
          break;
        case 2:
          setFormData((prevState) => ({
            ...prevState,
            uploadsMarksheet: file,
          }));
          setUploadsMarksheet(imageDataURL);

          break;
        case 3:
          setFormData((prevState) => ({
            ...prevState,
            uploadAccTCertificate: file,
          }));
          setUploadAccTCertificate(imageDataURL);

          break;
        case 4:
          setFormData((prevState) => ({
            ...prevState,
            uploadTypingPassCertificate: file,
          }));
          setUploadTypingPassCertificate(imageDataURL);
          break;
        default:
          break;
      }
    };
    reader.readAsDataURL(file);
    handleFileNameStore(file, fieldIndex);
  };

  const handleFileNameStore = (e, fieldIndex) => {
    // Check if a file is selected
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Update the formData based on fieldIndex
      setFormData((prevState) => {
        switch (fieldIndex) {
          case 1:
          //return { ...prevState, uploadPoliceverification: file.name };
          case 2:
            return { ...prevState, uploadsMarksheet: file.name };
          case 3:
            return { ...prevState, uploadAccTCertificate: file.name };
          case 4:
            return { ...prevState, uploadTypingPassCertificate: file.name };
          default:
            return prevState;
        }
      });
    }
  };

  const [proilePercent, setProfilePercent] = useState(0);
  useEffect(() => {
    const calculateProfilePercent = () => {
      if (userProfile && userProfile.SubmitStatus) {
        // Count the number of true values
        const trueCount = Object.values(userProfile.SubmitStatus).filter(
          (status) => status === true
        ).length;

        // Calculate the percentage
        const percentage = (trueCount / 5) * 100; // Assuming there are 5 statuses
        setProfilePercent(percentage);
      }
    };

    calculateProfilePercent();
  }, [userProfile]);

  const handleChangeEditable = async (e, editType) => {
    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह EDIT करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, EDIT",
        cancelButtonText: "No, cancel",
      });
      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/update-edit?empId=${id}`,
          { editType: editType },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert(response.data.msg, "success");
          getEmployee();
        }
      } else {
        SwalMessageAlert("Cancelled", "error");
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const handleFinalSubmit = async (e, submitType) => {
    // console.log(submitType, "Getting Submit Type");

    e.preventDefault();
    const validateForm = (submitType) => {
      const newErrors = {};
      const errorFields = []; // Array to hold names of fields with errors
      const errorSections = {}; // Object to hold sections and their corresponding fields with errors

      // Define required fields for each submit type
      const requiredFieldsByType = {
        personalInformation: {
          employeeDOB: "",
          employeeCategory: "",
          homeDistrict: "",
          // cgDistrict: "",
        },
        academicInformation: {
          educationQualification: "",
          appointmentCategory: "",
          workplaceDivision: "",
          workplaceDistrict: "",
          workplaceVidhansabha: "",
        },
        employeeInformation: {
          employeeClass: "",
          payScaleType: "",
          firstRegularAppointmentDesignation: "",
          appointmentType: "",
        },
        presentPosition: {
          presentPost: "",
          dateOfJoiningPresentPost: "",
          appointmentDate: "",
          presentBasicpay: "",
          presentPayLevel: "",
          dateOfPresentClgPosting: "",
          probationCompleteStatus: "",
        },
        otherInformation: {
          //latestSeniorityList: "",
          courtCaseStatus: "",
        },
      };

      const addConditionalFields = (formData) => {
        if (formData.disability === "true") {
          requiredFieldsByType.personalInformation.disabiltyTypes = "";
        }
        if (formData.educationQualification === "Other") {
          requiredFieldsByType.academicInformation.qualificationDetails = "";
        }
        if (formData.homeDistrict === "other") {
          requiredFieldsByType.personalInformation.state = "";
          requiredFieldsByType.personalInformation.otherDistrict = "";
        }

        if (formData.homeDistrict === "CG") {
          requiredFieldsByType.personalInformation.cgDistrict = "";
        }

        if (formData.payScaleType === "UGC") {
          requiredFieldsByType.employeeInformation.CurrentAGP = "";
        }

        if (formData.CurrentAGP === "7000") {
          requiredFieldsByType.employeeInformation.dateOfAGPSevenThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleSevenAGP = "";
        }
        if (formData.CurrentAGP === "8000") {
          requiredFieldsByType.employeeInformation.dateOfAGPEightThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleEightAGP = "";
        }
        if (formData.CurrentAGP === "9000") {
          requiredFieldsByType.employeeInformation.dateOfAGPNineThous = "";
          requiredFieldsByType.employeeInformation.basicPayscaleNineAGP = "";
        }

        if (formData.appointmentType === "Samviliyan") {
          requiredFieldsByType.employeeInformation.dateOfSam = "";
        }
        if (formData.appointmentType === "Anukampa") {
          requiredFieldsByType.employeeInformation.dateOfAnu = "";
          requiredFieldsByType.employeeInformation.TypingPass = "";
        }
        if (formData.appointmentType === "Ad hoc") {
          requiredFieldsByType.employeeInformation.dateOfAdhoc = "";
          requiredFieldsByType.employeeInformation.dateofRegularApponitment =
            "";
        }
        if (formData.TypingPass === "true") {
          requiredFieldsByType.employeeInformation.uploadTypingPassCertificate =
            "";
        }
        if (
          getClassName(formData.employeeClass) === "Class 3" ||
          getClassName(formData.employeeClass) === "Class 4"
        ) {
          requiredFieldsByType.employeeInformation.uploadsMarksheet = "";
        }
        if (getClassName(formData.employeeClass) === "Class 3") {
          requiredFieldsByType.employeeInformation.IsAccountsTrained = "";
        }

        if (formData.IsAccountsTrained === "true") {
          requiredFieldsByType.employeeInformation.IsAccountsTrainingPass = "";
        }

        if (formData.IsAccountsTrainingPass === "true") {
          requiredFieldsByType.employeeInformation.uploadAccTCertificate = "";
        }

        if (formData.probationCompleteStatus === "onProbation") {
          requiredFieldsByType.presentPosition.policeVerification = "";
        }

        if (formData.probationCompleteStatus === "completion") {
          requiredFieldsByType.presentPosition.dateofCompletion = "";
        }

        // if (formData.policeVerification === "Completed") {
        //   requiredFieldsByType.presentPosition.uploadPoliceverification = "";
        // }

        if (formData.firstTimeScale === "true") {
          requiredFieldsByType.otherInformation.firstScaleDate = "";
        }

        if (formData.secondTimeScale === "true") {
          requiredFieldsByType.otherInformation.secondScaleDate = "";
        }

        if (formData.thirdTimeScale === "true") {
          requiredFieldsByType.otherInformation.thirdScaleDate = "";
        }

        if (
          formData.employeeType === "TEACHING" &&
          formData.presentPost != "Lab Technician"
        ) {
          requiredFieldsByType.otherInformation.subject = "";
        }

        if (formData.Mphil === "true") {
          requiredFieldsByType.otherInformation.subjectOfMphil = "";
          requiredFieldsByType.otherInformation.MphilAwardDate = "";
        }

        if (formData.phd === "true") {
          requiredFieldsByType.otherInformation.subjectOfPhd = "";
          requiredFieldsByType.otherInformation.phdNotifyDate = "";
        }

        if (formData.DscOrDlit === "true") {
          requiredFieldsByType.otherInformation.subjectOfDscOrDlit = "";
          requiredFieldsByType.otherInformation.DscOrDlitNotifyDate = "";
        }

        if (formData.NetSet === "true") {
          requiredFieldsByType.otherInformation.NetSetCertificateDate = "";
        }
      };

      const fieldDisplayNames = {
        employeeDOB: "Date of Birth",
        employeeCategory: "Employee Category",
        homeDistrict: "Home State",
        cgDistrict: "CG District",
        educationQualification: "Qualification",
        appointmentCategory: "Category ,In Which service Joined",
        workplaceDivision: "Workplace Division",
        workplaceDistrict: "Workplace District",
        workplaceVidhansabha: "Workplace Vidhansabha",
        employeeClass: "Class",
        payScaleType: "Pay Scale Type",
        firstRegularAppointmentDesignation:
          "Designation of First Regular Appointment",
        appointmentType: "Appointment Type",
        presentPost: "Present Post",
        dateOfJoiningPresentPost: "Date of Joining Present Post",
        appointmentDate: "Date of First Appointment",
        presentBasicpay: "Present Basic Pay",
        presentPayLevel: "Present Pay Level",
        dateOfPresentClgPosting: "Date of Posting in Present College",
        probationCompleteStatus: "Probation Completion Status",
        //latestSeniorityList: "Latest Seniority List",
        courtCaseStatus: "Court Case Status",
        disabiltyTypes: "Disablity Type",
        qualificationDetails: "Specify Other Qualification",
        state: "Other State",
        otherDistrict: "Other District",
        CurrentAGP: "Current AGP",
        dateOfAGPSevenThous: "Date of getting AGP 7000",
        basicPayscaleSevenAGP:
          "Basic pay in 7th Pay Scale after getting AGP 7000",
        dateOfAGPEightThous: "Date of getting AGP 8000",
        basicPayscaleEightAGP:
          "Basic pay in 7th Pay Scale after getting AGP 8000",
        dateOfAGPNineThous: "Date of getting AGP 9000",
        basicPayscaleNineAGP:
          "Basic pay in 7th Pay Scale after getting AGP 9000",
        dateOfSam: "Date of Samviliyan",
        dateOfAnu: "Date of Anukampa Niyukti",
        TypingPass: "Typing Pass",
        dateOfAdhoc: "Date of Adhoc Appointment",
        dateofRegularApponitment: "Date of regular appointment",
        uploadTypingPassCertificate: "Upload Typing Pass Certificate",
        uploadsMarksheet: "Upload 12th Marksheet",
        IsAccountsTrained: "Accounts Trained (if class 3)",
        IsAccountsTrainingPass: "Accounts Training Pass",
        uploadAccTCertificate: "Upload Accounts Training Certificate",
        policeVerification: "Police Verification (if on Probation)",
        dateofCompletion: "Date of Completion",
        //uploadPoliceverification:"Upload Police Verification (if completed)",
        firstScaleDate: "Availed 1st TimeScale Pay Date",
        secondScaleDate: "Availed 2nd TimeScale Pay Date",
        thirdScaleDate: "Availed 3rd Time Scale Pay Date",
        subject: "Subject",
        subjectOfMphil: "Subject of M. Phil",
        MphilAwardDate: "M.phil awared date/year (if Mphil)",
        subjectOfPhd: "Subject of PhD",
        phdNotifyDate: "PhD Notification Date",
        subjectOfDscOrDlit: "Subject of Dsc / DLit",
        DscOrDlitNotifyDate: "Dsc / DLit Notification Date",
        NetSetCertificateDate: "NET / SET Certificate Date",
      };

      addConditionalFields(formData);

      // Define all required fields for final submit
      const allRequiredFields = {
        ...requiredFieldsByType.personalInformation,
        ...requiredFieldsByType.academicInformation,
        ...requiredFieldsByType.employeeInformation,
        ...requiredFieldsByType.presentPosition,
        ...requiredFieldsByType.otherInformation,
      };

      // console.log("Getting Submit Type");

      const fieldsToValidate =
        submitType === "finalSubmit"
          ? allRequiredFields
          : requiredFieldsByType[submitType];

      if (fieldsToValidate) {
        for (const field in fieldsToValidate) {
          if (fieldsToValidate.hasOwnProperty(field)) {
            if (
              !formData[field] ||
              (typeof formData[field] === "string" && !formData[field].trim())
            ) {
              newErrors[field] = "This Field is Required.";
              errorFields.push(field);

              for (const section in requiredFieldsByType) {
                if (requiredFieldsByType[section].hasOwnProperty(field)) {
                  if (!errorSections[section]) {
                    errorSections[section] = [];
                  }
                  errorSections[section].push(field);
                }
              }
            }
          }
        }
      } else {
        console.error(
          `No required fields defined for submit type: ${submitType}`
        );
      }

      // Construct alert message with custom field names
      if (Object.keys(errorSections).length > 0) {
        let alertMessage =
          "<strong>Please fill in the following fields:</strong><br><br>";

        for (const section in errorSections) {
          alertMessage += `<strong>${section}:</strong><br> - `;
          alertMessage += errorSections[section]
            .map((field) => fieldDisplayNames[field] || field) // Use custom display names or fallback to the key
            .join(", ");
          alertMessage += "<br><br>";
        }

        Swal.fire({
          icon: "warning",
          title: "Missing Fields",
          html: alertMessage,
          confirmButtonText: "OK",
        });
      }

      return newErrors;
    };
    const validationErrors = validateForm(submitType);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // console.log(userProfile, "Getting user Profile");

    // if (submitType === "finalSubmit" && userProfile && userProfile.SubmitStatus.firstInfo === true) {

    // }

    if (
      submitType === "finalSubmit" &&
      userProfile &&
      userProfile.SubmitStatus &&
      (userProfile.SubmitStatus.firstInfo === false ||
        userProfile.SubmitStatus.secondInfo === false ||
        userProfile.SubmitStatus.thirdInfo === false ||
        userProfile.SubmitStatus.fourthInfo === false ||
        userProfile.SubmitStatus.fifthInfo === false)
    ) {
      SwalMessageAlert("All Section is Not Completed", "error");
      return;
    }

    if (
      submitType === "verify" &&
      userProfile &&
      userProfile.isFinalSubmit !== true
    ) {
      SwalMessageAlert("Final Submit is Not Completed", "error");
      return;
    }

    let result;

    try {
      if (submitType === "verify") {
        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह VERIFY करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, Verify",
          cancelButtonText: "No, cancel",
        });
      } else if (submitType === "edit") {
        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह EDIT करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, EDIT",
          cancelButtonText: "No, cancel",
        });
      } else if (submitType === "finalSubmit") {
        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप Final Submit करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, submit",
          cancelButtonText: "No, cancel",
        });
      } else {
        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, submit",
          cancelButtonText: "No, cancel",
        });
      }

      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/update-emp-profile?empId=${id}`,
          { submitType: submitType },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          if (submitType === "verify") {
            SwalMessageAlert("Profile Verfication Completed", "success");
            setTimeout(() => window.location.reload(), 3000);
          } else if (submitType === "edit") {
            SwalMessageAlert("Open For Edit and Update", "success");
            setTimeout(() => window.location.reload(), 1000);
          } else if (submitType === "finalSubmit") {
            SwalMessageAlert("Final Submitteed Successfuly", "success");
            setTimeout(() => window.location.reload(), 1000);
          } else {
            SwalMessageAlert(response.data.msg, "success");
            getEmployee();
          }
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const currentDate = new Date();

  const minDate = new Date();
  minDate.setFullYear(currentDate.getFullYear() - 18);

  // Calculate the maximum date (65 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(currentDate.getFullYear() - 65);

  // Format dates to YYYY-MM-DD for the input
  const formattedMinDate = minDate.toISOString().split("T")[0];
  const formattedMaxDate = maxDate.toISOString().split("T")[0];

  const formatTime = (dateString) => {
    if (!dateString) return "No update";

    const date = new Date(dateString);

    // Format the time as hh:mm:ss AM/PM
    const optionsTime = {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true, // Use false for 24-hour format
    };

    return date.toLocaleTimeString("en-US", optionsTime);
  };

  // const sectionNameString = sessionStorage.getItem("sectionName");

  // const pracharyaSection = sectionNameString
  //   ? sectionNameString.split(",").find((a) => a === "Prachary")
  //   : [];

  // const navigate = useNavigate();
  // if (user.designationDetails?.designation === 'UG Principal' || user.designationDetails?.designation === 'PG Principal' && pracharyaSection !== 'Prachary') {
  //   SwalMessageAlert("Employee Is Principal which is updated by Prachary Section", "error");
  //   setTimeout(() => {
  //     navigate("/admin/employee-list");
  //   }, 5000);
  // }

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row className="justify-content-center md-7">
          <Col className="order-xl-1 " xl="3">
            <Card style={{ boxShadow: "1px 5px 10px 3px black" }}>
              <Row className="justify-content-center md-7">
                <Col className="order-lg-2" lg="3">
                  <div className="card-profile-image">
                    <a href="#pablo" onClick={(e) => e.preventDefault()}>
                      {user &&
                      user.encodedImage &&
                      user.faceVerified !== false &&
                      user.encodedImage !== "" ? (
                        <img
                          src={`data:image/png;base64,${user.encodedImage}`}
                          onClick={() =>
                            handleConvertAndDisplay(user.encodedImage)
                          }
                          style={{
                            width: "220px",
                            height: "180px",
                            borderRadius: "100%",
                            objectFit: "cover",
                          }}
                          alt="User Profile"
                        />
                      ) : (
                        <img
                          src={NavImage}
                          style={{
                            width: "220px",
                            height: "180px",
                            borderRadius: "100%",
                            objectFit: "cover",
                          }}
                          alt="Default Profile"
                        />
                      )}
                    </a>
                  </div>
                </Col>
              </Row>
              <CardHeader className="text-center border-0 pt-8 pt-md-4 pb-0 pb-md-4 mt-5"></CardHeader>
              <br />
              <hr className="my-4 primary" />

              <CardBody className="pt-0 pt-md-0">
                <div className="text-center mt--3">
                  <h3 className="font-weight-bolder mb-1">
                    {user.title}
                    {/* Reduced margin-bottom */}
                    {user.name} <br />
                  </h3>
                </div>
                <div className="h5 mt-1 text-center">
                  {" "}
                  {/* Reduced margin-top */}
                  {user.designationDetails?.designation}
                </div>
                <div className="h5 text-center mb--4 mt--2">
                  <h3 className="mb-1">
                    {" "}
                    {/* Reduced margin-bottom */}
                    <span style={{ color: "blue", fontSize: "14px" }}>
                      {" "}
                      Employee Code: - {user.empCode}
                    </span>{" "}
                    <br />
                    <span style={{ color: "orange", fontSize: "14px" }}>
                      {" "}
                      {user.collegeDetails?.name}{" "}
                    </span>
                    <br></br>
                    <span className="font-weight-bold">
                      {user.activeStatus === true ? (
                        <span style={{ fontSize: "14px", color: "green" }}>
                          Active
                        </span>
                      ) : (
                        <span style={{ fontSize: "18px", color: "red" }}>
                          Inactive
                        </span>
                      )}
                    </span>
                  </h3>
                </div>

                <hr />

                <div className="text-center mb--4 mt--4">
                  <div className="h5 font-weight-600 mb-1">
                    {" "}
                    {/* Reduced margin-bottom */}
                    Email - <a href={`mailto:${user.email}`}>{user.email}</a>
                  </div>
                  <div className="h5 font-weight-600 mb-1">
                    {" "}
                    {/* Reduced margin-bottom */}
                    Contact - <a>{user.contact}</a>
                  </div>
                </div>
                <hr />

                <div className="text-center mb-4 mt--4">
                  <div className="h5 font-weight-600 mb-1">
                    {" "}
                    {/* Reduced margin-bottom */}
                    <i className="ni location_pin mr-2" />
                    Address - {user.address}
                  </div>
                  <hr className="my-4" />

                  <div className="d-block justify-content-center mt--2">
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Registered:{" "}
                        <span className="text-primary">
                          {formatDate(user.createdAt)}
                        </span>
                      </span>
                    </div>
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Last Updated :{" "}
                        <span className="text-primary">
                          {formatDate(user.updatedAt)}
                        </span>
                      </span>
                    </div>
                  </div>
                  <hr />
                  <div>
                    <span style={{ fontWeight: "bold" }}>
                      Profile Completed -
                    </span>
                    <span>
                      {" "}
                      {proilePercent >= 80 ? (
                        <span className="text-success">
                          {" "}
                          {proilePercent} %{" "}
                        </span>
                      ) : (
                        <span className="text-danger">{proilePercent} % </span>
                      )}
                    </span>
                  </div>
                  <div>
                    <span style={{ fontWeight: "bold" }}>Profile Status -</span>
                    <span>
                      {" "}
                      {userProfile && userProfile.isVerified === true ? (
                        <Badge className="bg-success text-white">
                          VERIFIED
                        </Badge>
                      ) : (
                        <Badge className="bg-danger text-white">
                          NOT VERIFIED
                        </Badge>
                      )}
                    </span>
                  </div>
                  <div>
                    <span style={{ fontWeight: "bold" }}>Last Verified -</span>
                    <span style={{ fontWeight: "bold", color: "green" }}>
                      {" "}
                      {userProfile &&
                      userProfile.verificationDate !== undefined ? (
                        userProfile && formatDate(userProfile.verificationDate)
                      ) : (
                        <span style={{ color: "red" }}>Not Verified</span>
                      )}
                    </span>
                  </div>
                </div>
              </CardBody>
            </Card>
          </Col>

          <Col className="order-xl-2" xl="9">
            <Card style={{ boxShadow: "1px 5px 10px 3px black" }}>
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="6">
                    <h2 className="mb-0 text-primary">
                      <strong>
                        {" "}
                        <i className="ni ni-single-02 text-primary"></i>{" "}
                        Employee Profile
                      </strong>
                    </h2>
                  </Col>
                  <Col
                    xs="6"
                    style={{ display: "flex", justifyContent: "right" }}
                  >
                    <h2 className="mb-0 text-primary text-right">
                      <Link to={`/admin/profile-print/${id}`}>
                        <Button
                          className=" btn btn-sm mr-2"
                          style={{ backgroundColor: "orange" }}
                        >
                          <i className="fas fa-print"></i>
                        </Button>
                      </Link>
                    </h2>

                    {/* {userProfile && userProfile.isFinalSubmit === true && userProfile.isVerified === true &&
                      <h2 className="mb-0 text-primary text-right">
                        <Button
                          color="warning"
                          className="mr-2"
                          size="sm"
                          onClick={(e) =>
                            handleFinalSubmit(e, "edit")
                          }>
                          Edit Profile
                        </Button>
                      </h2>
                    } */}
                    {userProfile && userProfile.isFinalSubmit === true && (
                      <h2 className="mb-0 text-primary text-right">
                        {userProfile && userProfile.isVerified === false && (
                          <Button
                            color="warning"
                            className="mr-2"
                            onClick={(e) => handleFinalSubmit(e, "verify")}
                            size="sm"
                          >
                            Verify Profile
                          </Button>
                        )}
                        {userProfile && userProfile.isVerified === true && (
                          <Button color="success" className="mr-2" size="sm">
                            Verified
                          </Button>
                        )}
                      </h2>
                    )}

                    <h2 className="mb-0 text-primary text-right">
                      {userProfile && userProfile.isFinalSubmit === false && (
                        <Button
                          color="warning"
                          onClick={handlePreview}
                          size="sm"
                        >
                          Final Submit
                        </Button>
                      )}
                      {userProfile && userProfile.isFinalSubmit === true && (
                        <Button color="success" size="sm">
                          Final Submitted
                        </Button>
                      )}
                    </h2>
                  </Col>
                  <Col className="text-right" xs="4"></Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Nav tabs>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "1" })}
                      onClick={() => toggleTab("1")}
                      style={{
                        backgroundColor: userProfile?.SubmitStatus?.firstInfo
                          ? "green"
                          : "red",
                        borderBottom:
                          activeTab === "1" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Personal Information
                      <br />
                      <h5 style={{ color: "yellow" }}>
                        {userProfile && userProfile.isVerified !== true
                          ? "Draft Saved"
                          : "Updated on"}{" "}
                        :{" "}
                        {userProfile &&
                        userProfile.personalInformation &&
                        userProfile.personalInformation.lastUpdate
                          ? formatDate(
                              userProfile.personalInformation.lastUpdate
                            )
                          : "No update"}{" "}
                        <br />
                        {formatTime(
                          userProfile?.personalInformation?.lastUpdate
                        )}
                      </h5>
                    </NavLink>
                  </NavItem>

                  <NavItem>
                    <NavLink
                      className={classnames("rounded-0", {
                        active: activeTab === "2",
                      })}
                      onClick={() => toggleTab("2")}
                      style={{
                        backgroundColor: userProfile?.SubmitStatus?.secondInfo
                          ? "green"
                          : "red",
                        borderBottom:
                          activeTab === "2" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Academic Information
                      <br />
                      <h5 style={{ color: "yellow" }}>
                        {userProfile && userProfile.isVerified !== true
                          ? "Draft Saved"
                          : "Updated on"}
                        :{" "}
                        {userProfile &&
                        userProfile.academicInformation &&
                        userProfile.academicInformation.lastUpdate
                          ? formatDate(
                              userProfile.academicInformation.lastUpdate
                            )
                          : "No update"}{" "}
                        <br />
                        {formatTime(
                          userProfile?.academicInformation?.lastUpdate
                        )}
                      </h5>
                    </NavLink>
                  </NavItem>

                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "3" })}
                      onClick={() => toggleTab("3")}
                      style={{
                        backgroundColor: userProfile?.SubmitStatus?.thirdInfo
                          ? "green"
                          : "red",
                        borderBottom:
                          activeTab === "3" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Employee Information <br />
                      <h5 style={{ color: "yellow" }}>
                        {userProfile && userProfile.isVerified !== true
                          ? "Draft Saved"
                          : "Updated on"}
                        :{" "}
                        {userProfile &&
                        userProfile.employeeInformation &&
                        userProfile.employeeInformation.lastUpdate
                          ? formatDate(
                              userProfile.employeeInformation.lastUpdate
                            )
                          : "No update"}{" "}
                        <br />
                        {formatTime(
                          userProfile?.employeeInformation?.lastUpdate
                        )}
                      </h5>
                    </NavLink>
                  </NavItem>

                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "4" })}
                      onClick={() => toggleTab("4")}
                      style={{
                        backgroundColor: userProfile?.SubmitStatus?.fourthInfo
                          ? "green"
                          : "red",
                        borderBottom:
                          activeTab === "4" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Present Position <br />
                      <h5 style={{ color: "yellow" }}>
                        {userProfile && userProfile.isVerified !== true
                          ? "Draft Saved"
                          : "Updated on"}
                        :{" "}
                        {userProfile &&
                        userProfile.presentInformation &&
                        userProfile.presentInformation.lastUpdate
                          ? formatDate(
                              userProfile.presentInformation.lastUpdate
                            )
                          : "No update"}{" "}
                        <br />
                        {formatTime(
                          userProfile?.presentInformation?.lastUpdate
                        )}
                      </h5>
                    </NavLink>
                  </NavItem>

                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "5" })}
                      onClick={() => toggleTab("5")}
                      style={{
                        backgroundColor: userProfile?.SubmitStatus?.fifthInfo
                          ? "green"
                          : "red",
                        borderBottom:
                          activeTab === "5" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Other Information
                      <br />
                      <h5 style={{ color: "yellow" }}>
                        {userProfile && userProfile.isVerified !== true
                          ? "Draft Saved"
                          : "Updated on"}
                        :{" "}
                        {userProfile &&
                        userProfile.otherInformation &&
                        userProfile.otherInformation.lastUpdate
                          ? formatDate(userProfile.otherInformation.lastUpdate)
                          : "No update"}{" "}
                        <br />
                        {formatTime(userProfile?.otherInformation?.lastUpdate)}
                      </h5>
                    </NavLink>
                  </NavItem>
                </Nav>

                <TabContent activeTab={activeTab}>
                  <TabPane tabId="1">
                    <Form className="mt-3">
                      <Card className="bg-secondary shadow-lg mb-4">
                        <CardBody>
                          <h4
                            className="mb-4"
                            style={{ justifyContent: "space-between" }}
                          >
                            <i className="ni ni-single-02 text-primary"></i>{" "}
                            Personal Information
                            {userProfile && userProfile.isVerified === true && (
                              <Button
                                color="warning"
                                className="mr-2 ml-2"
                                size="sm"
                                onClick={() =>
                                  setUserProfile({
                                    ...userProfile,
                                    editSection: {
                                      secondInfo: false,
                                      firstInfo: true,
                                      thirdInfo: false,
                                      fourthInfo: false,
                                      fifthInfo: false,
                                    },
                                  })
                                }
                                // onClick={(e) =>
                                //   handleChangeEditable(e, "firstInfo")
                                // }
                              >
                                Edit
                              </Button>
                            )}
                          </h4>

                          <hr />
                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-dob"
                                >
                                  Employee Code
                                </label>
                                <Input
                                  id="input-dob"
                                  name="empCode"
                                  value={formData.empCode}
                                  onChange={handleInputChange}
                                  type="text"
                                  readOnly
                                />
                                {errors.empCode && (
                                  <small className="text-danger">
                                    {errors.empCode}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-gender"
                                >
                                  Employee Name ({user.title})
                                </label>
                                <Input
                                  id="input-gender"
                                  type="text"
                                  name="name" // Add name attribute for handleInputChange
                                  value={formData.name} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  readOnly
                                ></Input>
                                {errors.name && (
                                  <small className="text-danger">
                                    {errors.name}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category"
                                >
                                  Email Id
                                </label>
                                <Input
                                  id="input-category"
                                  type="email"
                                  name="email"
                                  value={formData.email} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  disabled
                                ></Input>
                                {errors.email && (
                                  <small className="text-danger">
                                    {errors.email}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>
                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-MobileNo"
                                >
                                  Working Mobile No.
                                </label>
                                <Input
                                  id="input-MobileNo"
                                  name="MobileNo"
                                  value={formData.MobileNo}
                                  onChange={handleInputChange}
                                  type="text"
                                  disabled
                                />
                                {errors.MobileNo && (
                                  <small className="text-danger">
                                    {errors.MobileNo}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-dob"
                                >
                                  Date of Birth
                                </label>
                                <Input
                                  id="input-dob"
                                  name="employeeDOB"
                                  value={formData.employeeDOB}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection &&
                                    userProfile.editSection.firstInfo === false
                                  }
                                  type="date"
                                  min={formattedMaxDate} // Set the minimum date to 65 years ago
                                  max={formattedMinDate}
                                />
                                {errors.employeeDOB && (
                                  <small className="text-danger">
                                    {errors.employeeDOB}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-gender"
                                >
                                  Gender
                                </label>
                                <Input
                                  id="input-gender"
                                  type="select"
                                  name="employeeGender" // Add name attribute for handleInputChange
                                  value={formData.employeeGender} // Bind state
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.firstInfo === false
                                  }
                                  onChange={handleInputChange} // Update state on change
                                  readOnly
                                >
                                  <option value="">Select Gender</option>
                                  <option value="Male">Male</option>
                                  <option value="Female">Female</option>
                                  <option value="Transgender">
                                    Transgender
                                  </option>
                                </Input>
                                {errors.gender && (
                                  <small className="text-danger">
                                    {errors.gender}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>

                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category"
                                >
                                  Category
                                </label>
                                <Input
                                  id="input-category"
                                  type="select"
                                  name="employeeCategory"
                                  value={formData.employeeCategory} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.firstInfo === false
                                  }
                                >
                                  <option value="">Select Category</option>
                                  {category.length > 0 ? (
                                    category.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.category}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                                {errors.employeeCategory && (
                                  <small className="text-danger">
                                    {errors.employeeCategory}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category"
                                >
                                  Home State
                                </label>
                                <Input
                                  name="homeDistrict"
                                  id="input-district"
                                  type="select"
                                  value={formData.homeDistrict}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.firstInfo === false
                                  }
                                >
                                  <option value="">Select District</option>
                                  <option value="CG">Chhattisgarh</option>
                                  <option value="other">Other</option>
                                </Input>
                                {errors.homeDistrict && (
                                  <small className="text-danger">
                                    {errors.homeDistrict}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.homeDistrict === "CG" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-category"
                                  >
                                    District
                                  </label>
                                  <Input
                                    name="cgDistrict"
                                    id="input-district"
                                    type="select"
                                    value={formData.cgDistrict}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.firstInfo ===
                                        false
                                    }
                                  >
                                    <option value="">Select District</option>
                                    {districtAll &&
                                    Array.isArray(districtAll) &&
                                    districtAll.length > 0 ? (
                                      districtAll.map((type, index) => (
                                        <option key={index} value={type._id}>
                                          {type.districtNameEng}
                                        </option>
                                      ))
                                    ) : (
                                      <option value="">
                                        No districts available
                                      </option>
                                    )}
                                  </Input>
                                  {errors.cgDistrict && (
                                    <small className="text-danger">
                                      {errors.cgDistrict}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {formData.homeDistrict === "other" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="state"
                                    >
                                      State
                                    </label>
                                    <Input
                                      id="state"
                                      type="select"
                                      name="state"
                                      value={formData.state}
                                      onChange={handleStateChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.firstInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select State</option>
                                      {stateName &&
                                        stateName.length > 0 &&
                                        stateName.map((type, index) => (
                                          <option
                                            key={index}
                                            value={type.STATEID}
                                            selected={
                                              Number(type.STATEID) ===
                                              Number(formData.state)
                                            }
                                          >
                                            {type.STATTENAME}
                                          </option>
                                        ))}
                                    </Input>
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="input-category"
                                    >
                                      District
                                    </label>
                                    <Input
                                      name="otherDistrict"
                                      id="input-district"
                                      type="select"
                                      value={formData.otherDistrict}
                                      onChange={handleStateDistChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.firstInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select District</option>
                                      {stateWiseDistrict.length > 0 ? (
                                        stateWiseDistrict.map((type, index) => (
                                          <option key={index} value={type._id}>
                                            {type.allDistrictName}
                                          </option>
                                        ))
                                      ) : (
                                        <option disabled>
                                          No options available
                                        </option>
                                      )}
                                    </Input>
                                  </FormGroup>
                                </Col>
                              </>
                            )}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category"
                                >
                                  Permanent Home Address
                                </label>
                                <Input
                                  name="permanentHomeAddress"
                                  id="input-district"
                                  type="text"
                                  value={formData.permanentHomeAddress}
                                  onChange={handleInputChange}
                                  disabled
                                />
                                {errors.permanentHomeAddress && (
                                  <small className="text-danger">
                                    {errors.permanentHomeAddress}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category-of-appointment"
                                >
                                  Disability
                                </label>
                                <div>
                                  <FormGroup check inline>
                                    <Input
                                      type="radio"
                                      name="disability"
                                      value={"true"} // "Yes" corresponds to true
                                      checked={formData.disability === "true"} // If disability is true, the radio will be checked
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.firstInfo ===
                                          false
                                      }
                                    />

                                    <label>Yes</label>
                                  </FormGroup>

                                  <FormGroup check inline>
                                    <Input
                                      type="radio"
                                      name="disability"
                                      value={"false"} // "No" corresponds to false
                                      checked={formData.disability === "false"} // If disability is false, the radio will be checked
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.firstInfo ===
                                          false
                                      }
                                    />

                                    <label>No</label>
                                  </FormGroup>
                                </div>
                              </FormGroup>
                            </Col>

                            {/* Conditionally render the "Type of Disability" dropdown */}
                            {formData.disability === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <Label
                                    className="form-control-label"
                                    htmlFor="disability-type"
                                  >
                                    Type of Disability
                                  </Label>
                                  <Input
                                    type="select"
                                    name="disabiltyTypes"
                                    value={formData.disabiltyTypes}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.firstInfo ===
                                        false
                                    }
                                  >
                                    <option value="">
                                      Select Disability Types
                                    </option>
                                    {disabiltyType.length > 0 ? (
                                      disabiltyType.map((type, index) => (
                                        <option key={index} value={type.id}>
                                          {type.disablilityTypes}
                                        </option>
                                      ))
                                    ) : (
                                      <option disabled>
                                        No options available
                                      </option>
                                    )}
                                  </Input>
                                </FormGroup>
                              </Col>
                            )}
                          </Row>
                          <Row></Row>
                        </CardBody>
                      </Card>
                      {(userProfile?.editSection?.firstInfo === true ||
                        userProfile?.editSection?.firstInfo === undefined) && (
                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) =>
                              handleSubmit(e, "personalInformation")
                            }
                          >
                            Save as Draft
                          </Button>
                          {userProfile && userProfile.isDrafted === true && (
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "personalInformation");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "personalInformation");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>
                          )}
                        </div>
                      )}
                    </Form>
                  </TabPane>
                  <TabPane tabId="2">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            Academic Information
                            {userProfile && userProfile.isVerified === true && (
                              <Button
                                color="warning"
                                className="mr-2 ml-2"
                                size="sm"
                                onClick={() =>
                                  setUserProfile({
                                    ...userProfile,
                                    editSection: {
                                      secondInfo: true,
                                      firstInfo: false,
                                      thirdInfo: false,
                                      fourthInfo: false,
                                      fifthInfo: false,
                                    },
                                  })
                                }
                                // onClick={(e) =>handleChangeEditable(e, "secondInfo")}
                              >
                                Edit
                              </Button>
                            )}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category-of-appointment"
                                >
                                  Category ,In Which service Joined
                                </label>
                                <Input
                                  id="input-category-of-appointment"
                                  type="select"
                                  name="appointmentCategory"
                                  value={formData.appointmentCategory} // Bind state
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.secondInfo === false
                                  }
                                >
                                  <option value="">
                                    Select Category of Appointment
                                  </option>
                                  {category.length > 0 ? (
                                    category
                                      .filter((type) =>
                                        formData.employeeCategory === "General"
                                          ? type.category === "General"
                                          : true
                                      ) // Show only "General" if employeeCategory is "General"
                                      .map((type, index) => (
                                        <option key={index} value={type.id}>
                                          {type.category}
                                        </option>
                                      ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                                {errors.appointmentCategory && (
                                  <small className="text-danger">
                                    {errors.appointmentCategory}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-educational-qualification"
                                >
                                  Qualification
                                </label>
                                <Input
                                  id="input-educational-qualification"
                                  placeholder="Enter Qualification"
                                  type="select"
                                  name="educationQualification"
                                  value={formData.educationQualification}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.secondInfo === false
                                  }
                                >
                                  <option value="">
                                    Select Qualification Type
                                  </option>
                                  <option value="12 th">12 th</option>
                                  <option value="UG">UG</option>
                                  <option value="PG">PG</option>
                                  <option value="PG">Below 12th </option>
                                  <option value="Other">Other</option>
                                </Input>
                                {errors.educationQualification && (
                                  <small className="text-danger">
                                    {errors.educationQualification}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {formData.educationQualification === "Other" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-other-qualification"
                                  >
                                    Specify Other Qualification
                                  </label>
                                  <Input
                                    id="input-other-qualification"
                                    placeholder="Enter Other Qualification"
                                    type="text"
                                    name="qualificationDetails"
                                    value={formData.qualificationDetails}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.secondInfo ===
                                        false
                                    }
                                  />
                                  {errors.qualificationDetails && (
                                    <small className="text-danger">
                                      {errors.qualificationDetails}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-workplace-division"
                                >
                                  Workplace Division
                                </label>
                                <Input
                                  id="input-workplace-division"
                                  type="select"
                                  name="workplaceDivision"
                                  value={formData.workplaceDivision}
                                  onChange={handleDivisionChange} // Update division
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.secondInfo === false
                                  }
                                >
                                  <option value="">Select Division</option>
                                  {division &&
                                    division.length > 0 &&
                                    division.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.divisionCode}
                                        selected={
                                          Number(type.divisionCode) ===
                                          Number(formData.workplaceDivision)
                                        }
                                      >
                                        {type.name}
                                      </option>
                                    ))}
                                </Input>
                                {errors.workplaceDivision && (
                                  <small className="text-danger">
                                    {errors.workplaceDivision}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {/* Workplace District */}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-workplace-district"
                                >
                                  Workplace District
                                </label>
                                <Input
                                  id="input-workplace-district"
                                  type="select"
                                  name="workplaceDistrict"
                                  value={formData.workplaceDistrict}
                                  onChange={handleDistrictChange} // Fetch district dynamically
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.secondInfo === false
                                  }
                                >
                                  <option value="">Select District</option>
                                  {district &&
                                    district.length > 0 &&
                                    district.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.LGDCode}
                                        selected={
                                          Number(type.LGDCode) ===
                                          Number(formData.workplaceDistrict)
                                        }
                                      >
                                        {type.districtNameEng}
                                      </option>
                                    ))}
                                </Input>

                                {errors.workplaceDistrict && (
                                  <small className="text-danger">
                                    {errors.workplaceDistrict}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {/* Workplace Vidhansabha */}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-workplace-vidhansabha"
                                >
                                  Workplace Vidhansabha
                                </label>
                                <Input
                                  id="input-workplace-vidhansabha"
                                  type="select"
                                  name="workplaceVidhansabha"
                                  value={formData.workplaceVidhansabha}
                                  onChange={handleVidhansabhaChange} // Fetch Vidhan Sabha dynamically
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.secondInfo === false
                                  }
                                >
                                  <option value="">Select Vidhan Sabha</option>
                                  {vidhansabha &&
                                    vidhansabha.length > 0 &&
                                    vidhansabha.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.ConstituencyNumber}
                                      >
                                        {type.ConstituencyName}
                                      </option>
                                    ))}
                                </Input>

                                {errors.workplaceVidhansabha && (
                                  <small className="text-danger">
                                    {errors.workplaceVidhansabha}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>
                        </CardBody>
                      </Card>
                      {(userProfile?.editSection?.secondInfo === true ||
                        userProfile?.editSection?.secondInfo === undefined) && (
                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) =>
                              handleSubmit(e, "academicInformation")
                            }
                          >
                            Save as Draft
                          </Button>
                          {userProfile && userProfile.isDrafted === true && (
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "academicInformation");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "academicInformation");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>
                          )}
                        </div>
                      )}
                    </Form>
                  </TabPane>

                  <TabPane tabId="3">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="mt-2">
                          <h4 className="mb-4">
                            <i className="ni ni-single-02 text-primary"></i>{" "}
                            Employee Information
                            {userProfile && userProfile.isVerified === true && (
                              <Button
                                color="warning"
                                className="mr-2 ml-2"
                                size="sm"
                                onClick={() =>
                                  setUserProfile({
                                    ...userProfile,
                                    editSection: {
                                      secondInfo: false,
                                      firstInfo: false,
                                      thirdInfo: true,
                                      fourthInfo: false,
                                      fifthInfo: false,
                                    },
                                  })
                                }
                                // onClick={(e) =>handleChangeEditable(e, "thirdInfo")}
                              >
                                Edit
                              </Button>
                            )}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="employeeType"
                                >
                                  Employee / Work Type
                                </label>
                                <Input
                                  type="select"
                                  name="employeeType"
                                  value={formData.employeeType}
                                  readOnly
                                  onChange={handleInputChange}
                                >
                                  <option value="">Select Employee Type</option>
                                  <option value="TEACHING">Teaching</option>
                                  <option value="NON TEACHING">
                                    Non Teaching
                                  </option>
                                </Input>
                              </FormGroup>
                            </Col>

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="employeeType"
                                >
                                  Pay Scale Type
                                </label>
                                <Input
                                  type="select"
                                  name="payScaleType"
                                  value={formData.payScaleType}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.thirdInfo === false
                                  }
                                >
                                  <option value="">
                                    Select Pay Scale Type
                                  </option>
                                  <option value="UGC">UGC</option>
                                  <option value="State">State</option>
                                </Input>
                                {errors.payScaleType && (
                                  <small className="text-danger">
                                    {errors.payScaleType}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.payScaleType === "UGC" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="CurrentAGP"
                                  >
                                    Current AGP (According to 6th Pay) (if UGC)
                                  </label>
                                  <Input
                                    id="CurrentAGP"
                                    type="select"
                                    name="CurrentAGP"
                                    value={formData.CurrentAGP}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  >
                                    <option value="">Select Current AGP</option>
                                    <option value="6000">6000</option>
                                    <option value="7000">7000</option>
                                    <option value="8000">8000</option>
                                    <option value="9000">9000</option>
                                    <option value="10000">10000</option>
                                  </Input>
                                  {errors.CurrentAGP && (
                                    <small className="text-danger">
                                      {errors.CurrentAGP}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}

                            {formData.CurrentAGP === "7000" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="dateOfAGPSevenThous"
                                    >
                                      Date of getting AGP 7000
                                    </label>
                                    <Input
                                      id="dateOfAGPSevenThous"
                                      type="date"
                                      name="dateOfAGPSevenThous"
                                      value={formData.dateOfAGPSevenThous || ""}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.dateOfAGPSevenThous && (
                                      <small className="text-danger">
                                        {errors.dateOfAGPSevenThous}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="basicPayscaleSevenAGP"
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 7000
                                    </label>
                                    <Input
                                      id="basicPayscaleSevenAGP"
                                      type="text"
                                      name="basicPayscaleSevenAGP"
                                      value={formData.basicPayscaleSevenAGP}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.basicPayscaleSevenAGP && (
                                      <small className="text-danger">
                                        {errors.basicPayscaleSevenAGP}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}

                            {formData.CurrentAGP === "8000" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="dateOfAGPEightThous"
                                    >
                                      Date of getting AGP 8000
                                    </label>
                                    <Input
                                      id="dateOfAGPEightThous"
                                      type="date"
                                      name="dateOfAGPEightThous"
                                      value={formData.dateOfAGPEightThous || ""}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.employeeInformation && (
                                      <small className="text-danger">
                                        {errors.employeeInformation}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="basicPayscaleEightAGP"
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 8000
                                    </label>
                                    <Input
                                      id="basicPayscaleEightAGP"
                                      type="text"
                                      name="basicPayscaleEightAGP"
                                      value={formData.basicPayscaleEightAGP}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.basicPayscaleEightAGP && (
                                      <small className="text-danger">
                                        {errors.basicPayscaleEightAGP}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}

                            {formData.CurrentAGP === "9000" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="dateOfAGPNineThous"
                                    >
                                      Date of getting AGP 9000
                                    </label>
                                    <Input
                                      id="dateOfAGPNineThous"
                                      type="date"
                                      name="dateOfAGPNineThous"
                                      value={formData.dateOfAGPNineThous || ""}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.dateOfAGPNineThous && (
                                      <small className="text-danger">
                                        {errors.dateOfAGPNineThous}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="basicPayscaleNineAGP"
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 9000
                                    </label>
                                    <Input
                                      id="basicPayscaleNineAGP"
                                      type="text"
                                      name="basicPayscaleNineAGP"
                                      value={formData.basicPayscaleNineAGP}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.basicPayscaleNineAGP && (
                                      <small className="text-danger">
                                        {errors.basicPayscaleNineAGP}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-retirementDate"
                                >
                                  Retirement Date
                                </label>
                                <Input
                                  id="input-retirementDate"
                                  type="date"
                                  name="retirementDate"
                                  value={formData.retirementDate}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.thirdInfo === false
                                  }
                                />
                                {errors.retirementDate && (
                                  <small className="text-danger">
                                    {errors.retirementDate}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <Label
                                  className="form-control-label"
                                  htmlFor="input-category"
                                >
                                  Class of First Regular Appointment
                                </Label>
                                <Input
                                  name="employeeClass"
                                  id="input-district"
                                  type="select"
                                  value={formData.employeeClass}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.thirdInfo === false
                                  }
                                  onChange={handleClassInputChange}
                                >
                                  <option value="">Select Class</option>
                                  {classData &&
                                    classData.length > 0 &&
                                    classData
                                      .filter((type) => {
                                        // Extract the numeric part from the employeeClass
                                        const employeeClassNumber =
                                          currentClass &&
                                          currentClass.split(" ")[1];
                                        const currentClassNumber =
                                          type.className.split(" ")[1];

                                        // Show classes that are equal to or greater than the employee class
                                        return (
                                          currentClassNumber >=
                                          employeeClassNumber
                                        );
                                      })
                                      .map((type, index) => (
                                        <option
                                          key={index}
                                          value={type._id}
                                          selected={
                                            type._id === formData.employeeClass
                                          }
                                        >
                                          {type.className}
                                        </option>
                                      ))}
                                </Input>
                                {errors.employeeClass && (
                                  <small className="text-danger">
                                    {errors.employeeClass}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {/* Render designation fields */}
                            {designationData && designationData.length > 0 && (
                              <Col lg="4">
                                <FormGroup>
                                  <Label
                                    className="form-control-label"
                                    htmlFor="firstRegularAppointmentDesignation"
                                  >
                                    {" "}
                                    Designation of First Regular Appointment
                                  </Label>
                                  <Input
                                    type="select"
                                    name="firstRegularAppointmentDesignation"
                                    value={
                                      formData.firstRegularAppointmentDesignation
                                    }
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  >
                                    <option value="">Select Designation</option>
                                    {designationData &&
                                      designationData.length > 0 &&
                                      designationData.map((type, index) => (
                                        <option
                                          key={index}
                                          value={type._id}
                                          selected={
                                            String(type._id) ===
                                            String(
                                              formData.firstRegularAppointmentDesignation
                                            )
                                              ? true
                                              : false
                                          }
                                        >
                                          {type.designation}
                                        </option>
                                      ))}
                                  </Input>
                                  {errors.firstRegularAppointmentDesignation && (
                                    <small className="text-danger">
                                      {
                                        errors.firstRegularAppointmentDesignation
                                      }
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-appointment-type"
                                >
                                  Appointment Type
                                </label>
                                <Input
                                  id="input-appointment-type"
                                  type="select"
                                  name="appointmentType"
                                  value={formData.appointmentType}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.thirdInfo === false
                                  }
                                >
                                  <option value="">
                                    Select Appointment Type
                                  </option>
                                  {appointment.length > 0 ? (
                                    appointment
                                      .filter(
                                        (type) =>
                                          !(
                                            (getClassName(
                                              formData.employeeClass
                                            ) === "Class 1" ||
                                              getClassName(
                                                formData.employeeClass
                                              ) === "Class 2") &&
                                            type.appointment === "Anukampa"
                                          )
                                      ) // Adjust the condition as needed
                                      .map((type, index) => (
                                        <option key={index} value={type.id}>
                                          {type.appointment}
                                        </option>
                                      ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                                {errors.appointmentType && (
                                  <small className="text-danger">
                                    {errors.appointmentType}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.appointmentType === "Samviliyan" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-dateOfSam"
                                  >
                                    Date of Samviliyan
                                  </label>
                                  <Input
                                    id="input-dateOfSam"
                                    type="date"
                                    name="dateOfSam"
                                    value={formData.dateOfSam}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  />
                                  {errors.dateOfSam && (
                                    <small className="text-danger">
                                      {errors.dateOfSam}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}

                            {formData.appointmentType === "Anukampa" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-dateOfAnu"
                                  >
                                    Date of Anukampa Niyukti
                                  </label>
                                  <Input
                                    id="input-dateOfAnu"
                                    type="date"
                                    name="dateOfAnu"
                                    value={formData.dateOfAnu}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  />
                                  {errors.dateOfAnu && (
                                    <small className="text-danger">
                                      {errors.dateOfAnu}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}

                            {formData.appointmentType === "Ad hoc" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="input-dateOfAdhoc"
                                    >
                                      Date of Adhoc Appointment
                                    </label>
                                    <Input
                                      id="input-dateOfAdhoc"
                                      type="date"
                                      name="dateOfAdhoc"
                                      value={formData.dateOfAdhoc}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.dateOfAdhoc && (
                                      <small className="text-danger">
                                        {errors.dateOfAdhoc}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="input-dateofRegularApponitment"
                                    >
                                      Date of regular appointment
                                    </label>
                                    <Input
                                      id="input-dateofRegularApponitment"
                                      type="date"
                                      name="dateofRegularApponitment"
                                      value={formData.dateofRegularApponitment}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.thirdInfo ===
                                          false
                                      }
                                    />
                                    {errors.dateofRegularApponitment && (
                                      <small className="text-danger">
                                        {errors.dateofRegularApponitment}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}
                            {formData.appointmentType === "Anukampa" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="num-TypingPass"
                                  >
                                    Typing Pass
                                  </label>
                                  <Input
                                    id="num-TypingPass"
                                    type="select"
                                    name="TypingPass"
                                    value={formData.TypingPass}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  >
                                    <option value="">Select Typing Pass</option>
                                    <option value={"true"}>Yes</option>
                                    <option value={"false"}>No</option>
                                  </Input>
                                  {errors.TypingPass && (
                                    <small className="text-danger">
                                      {errors.TypingPass}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {formData.TypingPass === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="num-uploadTypingPassCertificate"
                                  >
                                    Upload Typing Pass Certificate
                                  </label>
                                  <Input
                                    id="num-uploadTypingPassCertificate"
                                    type="file"
                                    name="uploadTypingPassCertificate"
                                    accept=".pdf" // Restrict allowed file types
                                    // onChange={handleFileChange} // Handle file selection
                                    onChange={(e) => {
                                      // console.log("File input changed");
                                      handleImageChange(e, 4);
                                    }}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  />
                                  <a
                                    href={`https://heonline.cg.nic.in/${formData.uploadTypingPassCertificate}`}
                                    download
                                  >
                                    <Button className="btn-sm btn-primary">
                                      {formData.uploadTypingPassCertificate ? (
                                        <FaDownload size={18} />
                                      ) : (
                                        "No File"
                                      )}
                                    </Button>
                                  </a>
                                  {errors.uploadTypingPassCertificate && (
                                    <small className="text-danger">
                                      {errors.uploadTypingPassCertificate}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {getClassName(formData.employeeClass) ===
                              "Class 3" ||
                            getClassName(formData.employeeClass) ===
                              "Class 4" ? (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="uploadsMarksheet"
                                  >
                                    Upload 12th Marksheet (if class 3 or 4)
                                  </label>
                                  <Input
                                    id="uploadsMarksheet"
                                    type="file"
                                    name="uploadsMarksheet"
                                    accept=".pdf" // Restrict allowed file types
                                    // onChange={handleFileChange} // Handle file selection
                                    onChange={(e) => {
                                      // console.log("File input changed");
                                      handleImageChange(e, 2);
                                    }}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  />
                                  <a
                                    href={`https://heonline.cg.nic.in/${formData.uploadsMarksheet}`}
                                    download
                                  >
                                    <Button className="btn-sm btn-primary">
                                      {formData.uploadsMarksheet ? (
                                        <FaDownload size={18} />
                                      ) : (
                                        "No File"
                                      )}
                                    </Button>
                                  </a>
                                  {errors.uploadsMarksheet && (
                                    <small className="text-danger">
                                      {errors.uploadsMarksheet}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            ) : null}

                            {getClassName(formData.employeeClass) ===
                            "Class 3" ? (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="IsAccountsTrained"
                                  >
                                    Accounts Trained (if class 3)
                                  </label>
                                  <Input
                                    id="IsAccountsTrained"
                                    type="select"
                                    name="IsAccountsTrained"
                                    value={formData.IsAccountsTrained || ""}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  >
                                    <option value="">
                                      Select Accounts Trained
                                    </option>
                                    <option value={"true"}>Yes</option>
                                    <option value={"false"}>No</option>
                                  </Input>
                                  {errors.IsAccountsTrained && (
                                    <small className="text-danger">
                                      {errors.IsAccountsTrained}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            ) : null}
                            {formData.IsAccountsTrained === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="IsAccountsTrainingPass"
                                  >
                                    Accounts Training Pass
                                  </label>
                                  <Input
                                    id="IsAccountsTrainingPass"
                                    type="select"
                                    name="IsAccountsTrainingPass"
                                    value={
                                      formData.IsAccountsTrainingPass || ""
                                    }
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  >
                                    <option value="">
                                      Select Accounts Training Pass
                                    </option>
                                    <option value={"true"}>Yes</option>
                                    <option value={"false"}>No</option>
                                  </Input>
                                </FormGroup>
                              </Col>
                            )}
                            {/* Conditionally Render Date of Completion */}
                            {formData.IsAccountsTrainingPass === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="uploadAccTCertificate"
                                  >
                                    Upload Accounts Training Certificate
                                  </label>
                                  <Input
                                    id="uploadAccTCertificate"
                                    type="file"
                                    name="uploadAccTCertificate"
                                    accept=".pdf" // Restrict allowed file types
                                    onChange={(e) => handleImageChange(e, 3)}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.thirdInfo ===
                                        false
                                    }
                                  ></Input>
                                  <a
                                    href={`https://heonline.cg.nic.in/${formData.uploadAccTCertificate}`}
                                    download
                                  >
                                    <Button className="btn-sm btn-primary">
                                      {formData.uploadAccTCertificate ? (
                                        <FaDownload size={18} />
                                      ) : (
                                        "No File"
                                      )}
                                    </Button>
                                  </a>
                                  {errors.uploadAccTCertificate && (
                                    <small className="text-danger">
                                      {errors.uploadAccTCertificate}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                          </Row>
                          <Row></Row>
                          <Row>{/* Conditionally Render Date Inputs */}</Row>
                        </CardBody>
                      </Card>
                      {(userProfile?.editSection?.thirdInfo === true ||
                        userProfile?.editSection?.thirdInfo === undefined) && (
                        <div className="text-center mt-3">
                          {/* <Button color="primary"

                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) =>
                              handleSubmit(e, "employeeInformation")
                            }
                          >
                            Save as Draft
                          </Button>
                          {userProfile &&
                            userProfile.isDrafted === true &&
                            userProfile.isVerified !== true && (
                              <Button
                                color="success"
                                onClick={(e) => {
                                  handleSubmit(e, "employeeInformation");
                                  setTimeout(() => {
                                    handleFinalSubmit(e, "employeeInformation");
                                  }, 1000); // 1000ms = 1 second
                                }}
                                size="sm"
                              >
                                Submit
                              </Button>
                            )}
                        </div>
                      )}
                    </Form>
                  </TabPane>

                  <TabPane tabId="4">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            Present Position
                            {userProfile && userProfile.isVerified === true && (
                              <Button
                                color="warning"
                                className="mr-2 ml-2"
                                size="sm"
                                onClick={() =>
                                  setUserProfile({
                                    ...userProfile,
                                    editSection: {
                                      secondInfo: false,
                                      firstInfo: false,
                                      thirdInfo: false,
                                      fourthInfo: true,
                                      fifthInfo: false,
                                    },
                                  })
                                }
                                // onClick={(e) =>handleChangeEditable(e, "fourthInfo")}
                              >
                                Edit
                              </Button>
                            )}
                          </h4>
                          <hr />

                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-present-post"
                                >
                                  Present Post (Designation)
                                </label>
                                <Input
                                  id="input-present-post"
                                  placeholder="Enter Present Post"
                                  type="text"
                                  name="presentPost"
                                  value={formData.presentPost}
                                  onChange={handleInputChange}
                                  disabled
                                >
                                  <option value="">Select Post</option>
                                  {designationData &&
                                    designationData.length > 0 &&
                                    designationData.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type._id}
                                        selected={
                                          String(type._id) ===
                                          String(formData.presentPost)
                                            ? true
                                            : false
                                        }
                                      >
                                        {type.designation}
                                      </option>
                                    ))}
                                </Input>
                                {errors.presentPost && (
                                  <small className="text-danger">
                                    {errors.presentPost}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="appointment-date"
                                >
                                  Date of First Appointment
                                </label>
                                <Input
                                  id="appointment-date"
                                  type="date"
                                  name="appointmentDate"
                                  value={formData.appointmentDate}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fourthInfo === false
                                  }
                                />
                                {errors.appointmentDate && (
                                  <small className="text-danger">
                                    {errors.appointmentDate}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.firstRegularAppointmentDesignation !==
                              formData.presentPost && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-dateOfJoiningPresentPost"
                                  >
                                    Date of Joining Present Post
                                  </label>
                                  <Input
                                    id="input-dateOfJoiningPresentPost"
                                    type="date"
                                    name="dateOfJoiningPresentPost"
                                    value={
                                      formData.dateOfJoiningPresentPost || ""
                                    }
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.fourthInfo ===
                                        false
                                    }
                                  />
                                  {errors.dateOfJoiningPresentPost && (
                                    <small className="text-danger">
                                      {errors.dateOfJoiningPresentPost}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                          </Row>

                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="joining-date"
                                >
                                  Date of Posting in Present College
                                </label>
                                <Input
                                  id="joining-date"
                                  type="date"
                                  name="dateOfPresentClgPosting"
                                  value={formData.dateOfPresentClgPosting}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fourthInfo === false
                                  }
                                />
                                {errors.dateOfPresentClgPosting && (
                                  <small className="text-danger">
                                    {errors.dateOfPresentClgPosting}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="collegeName"
                                >
                                  Name of Present College
                                </label>
                                <Input
                                  id="collegeName"
                                  type="text"
                                  name="collegeName"
                                  value={user?.collegeDetails?.name}
                                  onChange={handleInputChange}
                                  readOnly
                                />
                                {errors.collegeName && (
                                  <small className="text-danger">
                                    {errors.collegeName}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="joining-date"
                                >
                                  Probation Completion Status
                                </label>
                                <Input
                                  id="joining-date"
                                  type="select"
                                  name="probationCompleteStatus"
                                  value={formData.probationCompleteStatus}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fourthInfo === false
                                  }
                                >
                                  <option value="">Select Status</option>
                                  <option value="completion">Completion</option>
                                  <option value="onProbation">
                                    On Probation
                                  </option>
                                </Input>
                                {errors.probationCompleteStatus && (
                                  <small className="text-danger">
                                    {errors.probationCompleteStatus}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.probationCompleteStatus ===
                              "onProbation" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="policeVerification"
                                  >
                                    Police Verification (if on Probation)
                                  </label>
                                  <Input
                                    id="policeVerification"
                                    type="select"
                                    name="policeVerification"
                                    value={formData.policeVerification}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.fourthInfo ===
                                        false
                                    }
                                  >
                                    <option value="">Select Status</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Not Completed">
                                      Not Completed
                                    </option>
                                  </Input>
                                  {errors.policeVerification && (
                                    <small className="text-danger">
                                      {errors.policeVerification}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {/* Conditionally Render Date of Completion */}
                            {formData.probationCompleteStatus ===
                              "completion" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="date-of-completion"
                                  >
                                    Date of Completion
                                  </label>
                                  <Input
                                    id="date-of-completion"
                                    type="date"
                                    name="dateofCompletion"
                                    value={formData.dateofCompletion}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.fourthInfo ===
                                        false
                                    }
                                  />
                                  {errors.dateofCompletion && (
                                    <small className="text-danger">
                                      {errors.dateofCompletion}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {/* {formData.policeVerification === "Completed" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="uploadPoliceverification"
                                  >
                                    Upload Police Verification (if completed)
                                  </label>
                                  <Input
                                    id="uploadPoliceverification"
                                    type="file"
                                    name="uploadPoliceverification"
                                    accept=".pdf" // Restrict allowed file types
                                    onChange={(e) => handleImageChange(e, 1)}
                                    disabled={userProfile && userProfile.editSection && userProfile.editSection.fourthInfo === false}

                                  />  <a href={`https://heonline.cg.nic.in/${formData.uploadPoliceverification}`} download>
                                    <Button className="btn-sm btn-primary" >
                                      {formData.uploadPoliceverification ? <FaDownload size={18} /> : "No File"}
                                    </Button>
                                  </a>
                                  {errors.uploadPoliceverification && (
                                    <small className="text-danger">
                                      {errors.uploadPoliceverification}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )} */}

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="presentPayLevel"
                                >
                                  Present Pay Level (7th Pay)
                                </label>
                                <Input
                                  id="presentPayLevel"
                                  type="select"
                                  name="presentPayLevel"
                                  value={formData.presentPayLevel}
                                  onChange={handlePayLevelChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fourthInfo === false
                                  }
                                >
                                  <option value="">Select Level</option>
                                  {formData.payScaleType === "UGC" ? (
                                    <>
                                      {payLevelTeaching.length > 0 &&
                                        payLevelTeaching.map((type, index) => (
                                          <option
                                            key={index}
                                            value={type.payID} //  Use type.payID (Number)
                                            selected={
                                              Number(type.payID) ===
                                              Number(formData.presentPayLevel)
                                            }
                                          >
                                            {type.payLevel}
                                          </option>
                                        ))}
                                    </>
                                  ) : (
                                    <>
                                      {payLevel.length > 0 &&
                                        payLevel.map((type, index) => (
                                          <option
                                            key={index}
                                            value={type.payID} //  Use type.payID (Number)
                                            selected={
                                              Number(type.payID) ===
                                              Number(formData.presentPayLevel)
                                            }
                                          >
                                            {type.payLevel}
                                          </option>
                                        ))}
                                    </>
                                  )}
                                </Input>
                                {errors.presentPayLevel && (
                                  <small className="text-danger">
                                    {errors.presentPayLevel}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="regular-appointment-date"
                                >
                                  Present Basic Pay (7th Pay)
                                </label>
                                <Input
                                  id="regular-appointment-date"
                                  type="select"
                                  name="presentBasicpay"
                                  value={formData.presentBasicpay}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fourthInfo === false
                                  }
                                >
                                  <option value="">
                                    Select Present Basic Pay
                                  </option>
                                  {basicPay.length > 0 ? (
                                    basicPay.map((type, index) => (
                                      <option key={index} 
                                      selected={String(type._id) === String(formData.presentBasicpay)}
                                      value={type._id}
                                      >
                                        {type.basicPayScale}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                                {errors.presentBasicpay && (
                                  <small className="text-danger">
                                    {errors.presentBasicpay}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>
                        </CardBody>
                      </Card>
                      {(userProfile?.editSection?.fourthInfo === true ||
                        userProfile?.editSection?.fourthInfo === undefined) && (
                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            size="sm"
                            color="info"
                            onClick={(e) => handleSubmit(e, "presentPosition")}
                          >
                            Save as Draft
                          </Button>
                          {userProfile && userProfile.isDrafted === true && (
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "presentPosition");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "presentPosition");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>
                          )}
                        </div>
                      )}
                    </Form>
                  </TabPane>

                  <TabPane tabId="5">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            Other Informations
                            {userProfile && userProfile.isVerified === true && (
                              <Button
                                color="warning"
                                className="mr-2 ml-2"
                                size="sm"
                                onClick={() =>
                                  setUserProfile({
                                    ...userProfile,
                                    editSection: {
                                      secondInfo: false,
                                      firstInfo: false,
                                      thirdInfo: false,
                                      fourthInfo: false,
                                      fifthInfo: true,
                                    },
                                  })
                                }
                                // onClick={(e) =>handleChangeEditable(e, "fifthInfo")}
                              >
                                Edit
                              </Button>
                            )}
                          </h4>
                          <hr />
                          <Row></Row>

                          <Row>
                            {/* First Time Scale Pay */}
                            <Col lg="4">
                              <FormGroup>
                                <label className="form-control-label">
                                  Availed 1st TimeScale Pay
                                </label>
                                <div>
                                  <FormGroup check inline>
                                    <Input
                                      type="radio"
                                      id="first-scale-yes"
                                      name="firstTimeScale"
                                      value={"true"}
                                      checked={
                                        formData.firstTimeScale === "true"
                                      }
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    />

                                    <label
                                      htmlFor="first-scale-yes"
                                      className="ml-2"
                                    >
                                      Yes
                                    </label>
                                  </FormGroup>
                                  <FormGroup check inline>
                                    <Input
                                      type="radio"
                                      id="first-scale-no"
                                      name="firstTimeScale"
                                      value={"false"}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                      checked={
                                        formData.firstTimeScale === "false"
                                      }
                                      onChange={handleInputChange}
                                      className="ml-4"
                                    />

                                    <label
                                      htmlFor="first-scale-no"
                                      className="ml-2"
                                    >
                                      No
                                    </label>
                                  </FormGroup>
                                </div>

                                {/* Conditional Rendering for First TimeScale Date */}
                                {formData.firstTimeScale === "true" && (
                                  <div>
                                    <Input
                                      id="first-scale-date"
                                      type="date"
                                      name="firstScaleDate"
                                      value={formData.firstScaleDate}
                                      onChange={handleInputChange}
                                      placeholder="Enter Date"
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    />
                                    {errors.firstScaleDate && (
                                      <small className="text-danger">
                                        {errors.firstScaleDate}
                                      </small>
                                    )}
                                  </div>
                                )}
                              </FormGroup>
                            </Col>

                            {/* Second Time Scale Pay */}
                            {formData.firstTimeScale === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label className="form-control-label">
                                    Availed 2nd Time Scale Pay
                                  </label>
                                  <div>
                                    <FormGroup check inline>
                                      <Input
                                        type="radio"
                                        id="second-scale-yes"
                                        name="secondTimeScale"
                                        value={"true"}
                                        checked={
                                          formData.secondTimeScale === "true"
                                        }
                                        onChange={handleInputChange}
                                        disabled={
                                          userProfile &&
                                          userProfile.editSection &&
                                          userProfile.editSection.fifthInfo ===
                                            false
                                        }
                                      />

                                      <label
                                        htmlFor="second-scale-yes"
                                        className="ml-2"
                                      >
                                        Yes
                                      </label>
                                    </FormGroup>
                                    <FormGroup check inline>
                                      <Input
                                        type="radio"
                                        id="second-scale-no"
                                        name="secondTimeScale"
                                        value="false"
                                        checked={
                                          formData.secondTimeScale === "false"
                                        }
                                        onChange={handleInputChange}
                                        disabled={
                                          userProfile &&
                                          userProfile.editSection &&
                                          userProfile.editSection.fifthInfo ===
                                            false
                                        }
                                        className="ml-4"
                                      />

                                      <label
                                        htmlFor="second-scale-no"
                                        className="ml-2"
                                      >
                                        No
                                      </label>
                                    </FormGroup>
                                  </div>

                                  {/* Conditional Rendering for Second TimeScale Date */}
                                  {formData.secondTimeScale === "true" && (
                                    <div>
                                      <Input
                                        id="second-scale-date"
                                        type="date"
                                        name="secondScaleDate"
                                        value={formData.secondScaleDate}
                                        onChange={handleInputChange}
                                        disabled={
                                          userProfile &&
                                          userProfile.editSection &&
                                          userProfile.editSection.fifthInfo ===
                                            false
                                        }
                                        placeholder="Enter Date"
                                      />
                                      {errors.secondScaleDate && (
                                        <small className="text-danger">
                                          {errors.secondScaleDate}
                                        </small>
                                      )}
                                    </div>
                                  )}
                                </FormGroup>
                              </Col>
                            )}
                            {formData.firstTimeScale === "true" &&
                              formData.secondTimeScale === "true" && (
                                <Col lg="4">
                                  <FormGroup>
                                    <label className="form-control-label">
                                      Availed 3rd Time Scale Pay
                                    </label>
                                    <div>
                                      <FormGroup check inline>
                                        <Input
                                          type="radio"
                                          id="third-scale-yes"
                                          name="thirdTimeScale"
                                          value={"true"}
                                          checked={
                                            formData.thirdTimeScale === "true"
                                          }
                                          onChange={handleInputChange}
                                          disabled={
                                            userProfile &&
                                            userProfile.editSection &&
                                            userProfile.editSection
                                              .fifthInfo === false
                                          }
                                        />

                                        <label
                                          htmlFor="third-scale-yes"
                                          className="ml-2"
                                        >
                                          Yes
                                        </label>
                                      </FormGroup>
                                      <FormGroup check inline>
                                        <Input
                                          type="radio"
                                          id="third-scale-no"
                                          name="thirdTimeScale"
                                          value={"false"}
                                          checked={
                                            formData.thirdTimeScale === "false"
                                          }
                                          onChange={handleInputChange}
                                          disabled={
                                            userProfile &&
                                            userProfile.editSection &&
                                            userProfile.editSection
                                              .fifthInfo === false
                                          }
                                          className="ml-4"
                                        />

                                        <label
                                          htmlFor="third-scale-no"
                                          className="ml-2"
                                        >
                                          No
                                        </label>
                                      </FormGroup>
                                    </div>

                                    {/* Conditional Rendering for Third TimeScale Date */}
                                    {formData.thirdTimeScale === "true" && (
                                      <div>
                                        <Input
                                          id="third-scale-date"
                                          type="date"
                                          name="thirdScaleDate"
                                          value={formData.thirdScaleDate}
                                          disabled={
                                            userProfile &&
                                            userProfile.editSection &&
                                            userProfile.editSection
                                              .fifthInfo === false
                                          }
                                          onChange={handleInputChange}
                                          placeholder="Enter Date"
                                        />
                                        {errors.thirdScaleDate && (
                                          <small className="text-danger">
                                            {errors.thirdScaleDate}
                                          </small>
                                        )}
                                      </div>
                                    )}
                                  </FormGroup>
                                </Col>
                              )}
                          </Row>

                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="present-status"
                                >
                                  Latest Seniority List (Anitm) Sr. No.
                                </label>
                                <Input
                                  id="present-status"
                                  type="text"
                                  name="latestSeniorityList"
                                  value={formData.latestSeniorityList}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                ></Input>
                                {/* {errors.latestSeniorityList && (
                                  <small className="text-danger">
                                    {errors.latestSeniorityList}
                                  </small>
                                )} */}
                              </FormGroup>
                            </Col>
                            {/* Conditionally render Subject and M.Phil fields when Employee Type is Teaching */}
                            {(formData.employeeType === "TEACHING") &
                              (formData.presentPost != "Lab Technician") && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="subject"
                                    >
                                      Subject
                                    </label>
                                    <Input
                                      id="subject"
                                      type="select"
                                      name="subject"
                                      value={formData.subject}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select Subject</option>
                                      {subjectData.length > 0 ? (
                                        subjectData.map((type, index) => (
                                          <option key={index} value={type.id}>
                                            {type.subjectName}
                                          </option>
                                        ))
                                      ) : (
                                        <option disabled>
                                          No options available
                                        </option>
                                      )}
                                    </Input>
                                    {errors.subject && (
                                      <small className="text-danger">
                                        {errors.subject}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="Mphil"
                                    >
                                      M. Phil
                                    </label>
                                    <Input
                                      id="Mphil"
                                      type="select"
                                      name="Mphil"
                                      value={formData.Mphil}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select</option>
                                      <option value={"true"}>Yes</option>
                                      <option value={"false"}>No</option>
                                    </Input>
                                    {errors.Mphil && (
                                      <small className="text-danger">
                                        {errors.Mphil}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.Mphil === "true" && (
                                  <>
                                    <Col lg="4">
                                      <FormGroup>
                                        <label
                                          className="form-control-label"
                                          htmlFor="subjectOfMphil"
                                        >
                                          Subject of M. Phil
                                        </label>
                                        <Input
                                          id="subjectOfMphil"
                                          type="select"
                                          name="subjectOfMphil"
                                          value={formData.subjectOfMphil}
                                          onChange={handleInputChange}
                                          disabled={
                                            userProfile &&
                                            userProfile.editSection &&
                                            userProfile.editSection
                                              .fifthInfo === false
                                          }
                                        >
                                          <option value="">
                                            Select M. Phil Subject
                                          </option>
                                          {subjectData.length > 0 ? (
                                            subjectData.map((type, index) => (
                                              <option
                                                key={index}
                                                value={type.id}
                                              >
                                                {type.subjectName}
                                              </option>
                                            ))
                                          ) : (
                                            <option disabled>
                                              No options available
                                            </option>
                                          )}
                                        </Input>
                                        {errors.subjectOfMphil && (
                                          <small className="text-danger">
                                            {errors.subjectOfMphil}
                                          </small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    <Col lg="4">
                                      <FormGroup>
                                        <label
                                          className="form-control-label"
                                          htmlFor="present-status"
                                        >
                                          M.phil awared date/year (if Mphil)
                                        </label>
                                        <Input
                                          id="present-status"
                                          type="date"
                                          name="MphilAwardDate"
                                          value={formData.MphilAwardDate}
                                          onChange={handleInputChange}
                                          disabled={
                                            userProfile &&
                                            userProfile.editSection &&
                                            userProfile.editSection
                                              .fifthInfo === false
                                          }
                                        ></Input>
                                        {errors.MphilAwardDate && (
                                          <small className="text-danger">
                                            {errors.MphilAwardDate}
                                          </small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                  </>
                                )}
                              </>
                            )}

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="status"
                                >
                                  PhD
                                </label>
                                <Input
                                  id="status"
                                  type="select"
                                  name="phd"
                                  value={formData.phd}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                >
                                  <option value="">Select</option>
                                  <option value={"true"}>Yes</option>
                                  <option value={"false"}>No</option>
                                </Input>
                                {errors.phd && (
                                  <small className="text-danger">
                                    {errors.phd}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.phd === "true" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="status"
                                    >
                                      Subject of PhD
                                    </label>
                                    <Input
                                      id="status"
                                      type="select"
                                      name="subjectOfPhd"
                                      value={formData.subjectOfPhd}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select Subject</option>
                                      {subjectData.length > 0 ? (
                                        subjectData.map((type, index) => (
                                          <option key={index} value={type.id}>
                                            {type.subjectName}
                                          </option>
                                        ))
                                      ) : (
                                        <option disabled>
                                          No options available
                                        </option>
                                      )}
                                    </Input>
                                    {errors.subjectOfPhd && (
                                      <small className="text-danger">
                                        {errors.subjectOfPhd}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="phdNotifyDate"
                                    >
                                      PhD Notification Date
                                    </label>
                                    <Input
                                      id="phdNotifyDate"
                                      type="date"
                                      name="phdNotifyDate"
                                      value={formData.phdNotifyDate}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    ></Input>
                                    {errors.phdNotifyDate && (
                                      <small className="text-danger">
                                        {errors.phdNotifyDate}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="DscOrDlit"
                                >
                                  Dsc / DLit
                                </label>
                                <Input
                                  id="DscOrDlit"
                                  type="select"
                                  name="DscOrDlit"
                                  value={formData.DscOrDlit}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                >
                                  <option value="">Select</option>
                                  <option value={"true"}>Yes</option>
                                  <option value={"false"}>No</option>
                                </Input>
                                {errors.DscOrDlit && (
                                  <small className="text-danger">
                                    {errors.DscOrDlit}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {formData.DscOrDlit === "true" && (
                              <>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="subjectOfDscOrDlit"
                                    >
                                      Subject of Dsc / DLit
                                    </label>
                                    <Input
                                      id="subjectOfDscOrDlit"
                                      type="select"
                                      name="subjectOfDscOrDlit"
                                      value={formData.subjectOfDscOrDlit}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    >
                                      <option value="">Select Subject</option>
                                      {subjectData.length > 0 ? (
                                        subjectData.map((type, index) => (
                                          <option key={index} value={type.id}>
                                            {type.subjectName}
                                          </option>
                                        ))
                                      ) : (
                                        <option disabled>
                                          No options available
                                        </option>
                                      )}
                                    </Input>
                                    {errors.subjectOfDscOrDlit && (
                                      <small className="text-danger">
                                        {errors.subjectOfDscOrDlit}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="4">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="phdNotifyDate"
                                    >
                                      Dsc / DLit Notification Date
                                    </label>
                                    <Input
                                      id="phdNotifyDate"
                                      type="date"
                                      name="DscOrDlitNotifyDate"
                                      value={formData.DscOrDlitNotifyDate}
                                      onChange={handleInputChange}
                                      disabled={
                                        userProfile &&
                                        userProfile.editSection &&
                                        userProfile.editSection.fifthInfo ===
                                          false
                                      }
                                    ></Input>
                                    {errors.DscOrDlitNotifyDate && (
                                      <small className="text-danger">
                                        {errors.DscOrDlitNotifyDate}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}
                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="NetSet"
                                >
                                  NET / SET
                                </label>
                                <Input
                                  id="NetSet"
                                  type="select"
                                  name="NetSet"
                                  value={formData.NetSet}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                >
                                  <option value="">Select</option>
                                  <option value={"true"}>Yes</option>
                                  <option value={"false"}>No</option>
                                </Input>
                                {errors.NetSet && (
                                  <small className="text-danger">
                                    {errors.NetSet}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {formData.NetSet === "true" && (
                              <Col lg="4">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="NetSetCertificateDate"
                                  >
                                    NET / SET Certificate Date
                                  </label>
                                  <Input
                                    id="NetSetCertificateDate"
                                    type="date"
                                    name="NetSetCertificateDate"
                                    value={formData.NetSetCertificateDate}
                                    onChange={handleInputChange}
                                    disabled={
                                      userProfile &&
                                      userProfile.editSection &&
                                      userProfile.editSection.fifthInfo ===
                                        false
                                    }
                                  ></Input>
                                  {errors.NetSetCertificateDate && (
                                    <small className="text-danger">
                                      {errors.NetSetCertificateDate}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}

                            <Col lg="4">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="courtCaseStatus"
                                >
                                  Court Case Status
                                </label>
                                <Input
                                  id="courtCaseStatus"
                                  type="select"
                                  name="courtCaseStatus"
                                  value={formData.courtCaseStatus}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                  onChange={handleInputChange}
                                >
                                  <option value="">Select Case Status</option>
                                  <option value="courtCase">Court Case</option>
                                  <option value="noCourtCase">
                                    No Court Case
                                  </option>
                                </Input>
                                {errors.courtCaseStatus && (
                                  <small className="text-danger">
                                    {errors.courtCaseStatus}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="8">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="PendingDeptEnquireCase"
                                >
                                  Pending Deaprtmental Enquire / Prosecution
                                  Case
                                </label>
                                <Input
                                  id="PendingDeptEnquireCase"
                                  type="select"
                                  name="PendingDeptEnquireCase"
                                  value={formData.PendingDeptEnquireCase}
                                  onChange={handleInputChange}
                                  disabled={
                                    userProfile &&
                                    userProfile.editSection &&
                                    userProfile.editSection.fifthInfo === false
                                  }
                                >
                                  <option value="">Select</option>
                                  <option value={"true"}>Yes</option>
                                  <option value={"false"}>No </option>
                                </Input>
                                {errors.PendingDeptEnquireCase && (
                                  <small className="text-danger">
                                    {errors.PendingDeptEnquireCase}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>
                        </CardBody>
                      </Card>
                      {(userProfile?.editSection?.fifthInfo === true ||
                        userProfile?.editSection?.fifthInfo === undefined) && (
                        <div className="text-center  mt-4">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) => handleSubmit(e, "otherInformation")}
                          >
                            Save as Draft
                          </Button>
                          {userProfile && userProfile.isDrafted === true && (
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "otherInformation");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "otherInformation");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>
                          )}

                          {/* <Button
                          color="success"
                          onClick={(e) =>
                            handleSubmit(e, "otherInformation", false)
                          }
                          disabled={
                            submittedSections.otherInformation || isSubmitted
                          } // Disable only if the section is submitted
                        >
                          {submittedSections.otherInformation
                            ? "Form Submitted"
                            : "Final Submit"}
                        </Button> */}
                        </div>
                      )}
                    </Form>
                  </TabPane>
                </TabContent>
                <Modal
                  isOpen={showPreview}
                  toggle={() => setShowPreview(false)}
                  style={{ maxWidth: "70%" }}
                >
                  <ModalHeader toggle={handlePreview}>
                    Print Preview
                  </ModalHeader>
                  <ModalBody>
                    <div id="print-container">
                      <Row>
                        <div className="profile-table">
                          <table
                            style={{ width: "99%", borderCollapse: "collapse" }}
                          >
                            <tbody>
                              <tr
                                style={{
                                  border: "3px solid black",
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}
                              >
                                <td colspan="100%" style={{ padding: "1px" }}>
                                  <strong
                                    style={{
                                      fontSize: "18px",
                                      textAlign: "center",
                                      textJustify: "center",
                                    }}
                                  >
                                    Personal Information
                                  </strong>
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  textAlign: "left",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Employee Code:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.empCode}
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Employee Name ({user.title}):
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.name}
                                </td>
                              </tr>

                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Email Id:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.email}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Working Mobile No:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.MobileNo}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Date of Birth:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.employeeDOB}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Gender:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.employeeGender}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Category:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.employeeCategory}
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Home State:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.homeDistrict}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Permanent Home Address:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.permanentHomeAddress}
                                </td>
                                {formData.homeDistrict === "other" && (
                                  <>
                                    <Col
                                      md="4"
                                      className=" p-2"
                                      style={{ fontSize: "13px" }}
                                    >
                                      <strong> State : </strong>{" "}
                                      <strong className=" text-primary ml-2">
                                        {stateName.find(
                                          (type) =>
                                            String(type.STATEID) ===
                                            String(formData.state)
                                        )?.STATTENAME || "N/A"}
                                      </strong>
                                    </Col>
                                    <Col
                                      md="4"
                                      className=" p-2"
                                      style={{ fontSize: "13px" }}
                                    >
                                      <strong>Other District:</strong>{" "}
                                      <strong className=" text-primary ml-2">
                                        {stateWiseDistrict.find(
                                          (type) =>
                                            String(type._id) ===
                                            String(formData.otherDistrict)
                                        )?.allDistrictName || "N/A"}
                                      </strong>
                                    </Col>
                                  </>
                                )}
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Disability:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.disability === "true"
                                    ? "Yes"
                                    : "No"}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "1px solid black",
                                  fontSize: "14px",
                                  padding: "10px",
                                }}
                              >
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Type of Disability:
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontSize: "16px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.disabiltyTypes}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "3px solid black",
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}
                              >
                                <td colspan="100%" style={{ padding: "1px" }}>
                                  <strong
                                    style={{
                                      fontSize: "18px",
                                      textAlign: "center",
                                      textJustify: "center",
                                    }}
                                  >
                                    Academic Information
                                  </strong>
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Category, In Which Service Joined
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.appointmentCategory || "N/A"}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Qualification
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.educationQualification || "N/A"}
                                </td>
                              </tr>
                              <tr>
                                {formData.educationQualification ===
                                  "Other" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Specify Other Qualification
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.qualificationDetails || "N/A"}
                                    </td>
                                  </>
                                )}
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Workplace Division
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {
                                    division.find(
                                      (type) =>
                                        String(type.divisionCode) ===
                                        String(formData.workplaceDivision)
                                    )?.name
                                  }
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Workplace District
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {
                                    district.find(
                                      (type) =>
                                        String(type.LGDCode) ===
                                        String(formData.workplaceDistrict)
                                    )?.districtNameEng
                                  }
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Workplace Vidhansabha
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {
                                    vidhansabha.find(
                                      (type) =>
                                        String(type.ConstituencyNumber) ===
                                        String(formData.workplaceVidhansabha)
                                    )?.ConstituencyName
                                  }
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "3px solid black",
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}
                              >
                                <td colspan="100%" style={{ padding: "1px" }}>
                                  <strong
                                    style={{
                                      fontSize: "18px",
                                      textAlign: "center",
                                      textJustify: "center",
                                    }}
                                  >
                                    Employee Information
                                  </strong>
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Employee / Work Type
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.employeeType || "N/A"}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Pay Scale Type
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.payScaleType || "N/A"}
                                </td>
                              </tr>

                              {formData.payScaleType === "UGC" && (
                                <tr>
                                  <td
                                    style={{
                                      border: "1px solid black",
                                      padding: "10px",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    Current AGP (According to 6th Pay) (if UGC)
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid black",
                                      padding: "10px",
                                      color: "darkblue",
                                    }}
                                  >
                                    {formData.CurrentAGP || "N/A"}
                                  </td>
                                </tr>
                              )}
                              {formData.CurrentAGP === "7000" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting AGP 7000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateOfAGPSevenThous || "N/A"}
                                    </td>
                                  </tr>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 7000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.basicPayscaleSevenAGP || "N/A"}
                                    </td>
                                  </tr>
                                </>
                              )}
                              {formData.CurrentAGP === "8000" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting AGP 8000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(
                                        formData.dateOfAGPEightThous
                                      ) || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 8000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.basicPayscaleEightAGP || "N/A"}
                                    </td>
                                  </tr>
                                </>
                              )}
                              {formData.CurrentAGP === "9000" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting AGP 9000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateOfAGPNineThous || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Basic pay in 7th Pay Scale after getting
                                      AGP 9000
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.basicPayscaleNineAGP || "N/A"}
                                    </td>
                                  </tr>
                                </>
                              )}
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Retirement Date
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formatDate(formData.retirementDate)}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Class of First Regular Appointment
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {getClassName(formData.employeeClass)}
                                </td>
                              </tr>

                              <tr>
                                {" "}
                                {designationData &&
                                  designationData.length > 0 && (
                                    <>
                                      <td
                                        style={{
                                          border: "1px solid black",
                                          padding: "10px",
                                          fontWeight: "bold",
                                        }}
                                      >
                                        Designation of First Regular Appointment
                                      </td>
                                      <td
                                        style={{
                                          border: "1px solid black",
                                          padding: "10px",
                                          color: "darkblue",
                                        }}
                                      >
                                        {designationData.find(
                                          (type) =>
                                            String(type._id) ===
                                            String(
                                              formData.firstRegularAppointmentDesignation
                                            )
                                        )?.designation || "N/A"}
                                      </td>
                                    </>
                                  )}
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Appointment Type
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.appointmentType || "N/A"}
                                </td>
                              </tr>
                              <tr>
                                {" "}
                                {formData.appointmentType === "Samviliyan" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of Samviliyan
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateOfSam || "N/A"}
                                    </td>
                                  </>
                                )}
                                {formData.appointmentType === "Anukampa" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Typing Pass
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.TypingPass === "true"
                                        ? "Yes"
                                        : "No"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of Anukampa Niyukti
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(formData.dateOfAnu) || "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                {" "}
                                {formData.appointmentType === "Ad hoc" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of Adhoc Appointment
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateOfAdhoc || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of regular appointment
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateofRegularApponitment ||
                                        "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                {formData.TypingPass === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Upload Typing Pass Certificate
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.uploadTypingPassCertificate
                                        ? "Uploaded"
                                        : "Not Uploaded"}
                                    </td>
                                  </>
                                )}
                                {getClassName(formData.employeeClass) ===
                                  "Class 3" ||
                                getClassName(formData.employeeClass) ===
                                  "Class 4" ? (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Upload 12th Marksheet (if class 3 or 4)
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.uploadsMarksheet
                                        ? "Uploaded"
                                        : "Not Uploaded"}
                                    </td>
                                  </>
                                ) : null}
                              </tr>
                              <tr>
                                {getClassName(formData.employeeClass) ===
                                  "Class 3" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Accounts Trained (if class 3)
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.IsAccountsTrained === "true"
                                        ? "Yes"
                                        : "No"}
                                    </td>
                                  </>
                                )}
                                {formData.IsAccountsTrained === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Accounts Training Pass
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.IsAccountsTrainingPass ===
                                      "true"
                                        ? "Yes"
                                        : "No"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              {formData.IsAccountsTrainingPass === "true" && (
                                <tr>
                                  <td
                                    style={{
                                      border: "1px solid black",
                                      padding: "10px",
                                      fontWeight: "bold",
                                    }}
                                  >
                                    Upload Accounts Training Certificate
                                  </td>
                                  <td
                                    style={{
                                      border: "1px solid black",
                                      padding: "10px",
                                      color: "darkblue",
                                    }}
                                  >
                                    {formData.uploadAccTCertificate
                                      ? "Uploaded"
                                      : "Not Uploaded"}
                                  </td>
                                </tr>
                              )}
                              <tr
                                style={{
                                  border: "3px solid black",
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}
                              >
                                <td colspan="100%" style={{ padding: "1px" }}>
                                  <strong
                                    style={{
                                      fontSize: "18px",
                                      textAlign: "center",
                                      textJustify: "center",
                                    }}
                                  >
                                    Present Information
                                  </strong>
                                </td>
                              </tr>

                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Present Post (Designation)
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData?.presentPost || "N/A"}
                                </td>
                                {formData.firstRegularAppointmentDesignation !==
                                  formData.presentPost && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of Joining Present Post
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(
                                        formData.dateOfJoiningPresentPost
                                      ) || "N/A"}
                                    </td>
                                  </>
                                )}{" "}
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Date of First Appointment
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formatDate(formData.appointmentDate) ||
                                    "N/A"}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Date of Posting in Present College
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formatDate(
                                    formData.dateOfPresentClgPosting
                                  ) || "N/A"}
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Name of Present College
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {user?.collegeDetails?.name || "N/A"}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Probation Completion Status
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.probationCompleteStatus || "N/A"}
                                </td>
                              </tr>
                              <tr>
                                {formData.probationCompleteStatus ===
                                  "onProbation" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Police Verification (if on Probation)
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.policeVerification || "N/A"}
                                    </td>
                                  </>
                                )}
                                {formData.probationCompleteStatus ===
                                  "completion" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of Completion
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.dateofCompletion || "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                {/* {formData.policeVerification === "Completed" && (
                                  <>
                                    <td style={{ border: '1px solid black', padding: "10px", fontWeight: "bold" }}>
                                      Upload Police Verification (if completed)
                                    </td>
                                    <td style={{ border: '1px solid black', padding: "10px", color: "darkblue" }}>
                                      {formData.uploadPoliceverification ? "Uploaded" : "Not Uploaded"}
                                    </td>
                                  </>
                                )} */}

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Present Pay Level (7th Pay)
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.presentPayLevel || "N/A"}
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Present Basic Pay (7th Pay)
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {basicPay.find(
                                    (type) =>
                                      String(type._id) ===
                                      String(formData.presentBasicpay)
                                  )?.basicPayScale || "N/A"}
                                </td>
                              </tr>
                              <tr
                                style={{
                                  border: "3px solid black",
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}
                              >
                                <td colspan="100%" style={{ padding: "1px" }}>
                                  <strong
                                    style={{
                                      fontSize: "18px",
                                      textAlign: "center",
                                      textJustify: "center",
                                    }}
                                  >
                                    Other Informations
                                  </strong>
                                </td>
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Availed 1st TimeScale Pay
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.firstTimeScale === "true"
                                    ? "Yes"
                                    : "No"}
                                </td>

                                {formData.firstTimeScale === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting 1st TimeScale Pay
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(formData.firstScaleDate) ||
                                        "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Availed 2nd Time Scale Pay
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.secondTimeScale === "true"
                                    ? "Yes"
                                    : "No"}
                                </td>

                                {formData.secondTimeScale === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting 2nd TimeScale Pay
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(formData.secondScaleDate) ||
                                        "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Availed 3rd Time Scale Pay
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.thirdTimeScale === "true"
                                    ? "Yes"
                                    : "No"}
                                </td>

                                {formData.thirdTimeScale === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Date of getting 3rd TimeScale Pay
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(formData.thirdScaleDate) ||
                                        "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Latest Seniority List (Anitm) Sr. No.{" "}
                                  {formData.employeeType}
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.latestSeniorityList || "N/A"}
                                </td>
                              </tr>
                              {formData.employeeType === "TEACHING" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Subject
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.subject || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      M. Phil
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.Mphil === "true" ? "Yes" : "No"}
                                    </td>
                                  </tr>
                                  {formData.Mphil === "true" && (
                                    <>
                                      <tr>
                                        <td
                                          style={{
                                            border: "1px solid black",
                                            padding: "10px",
                                            fontWeight: "bold",
                                          }}
                                        >
                                          Subject of M. Phil
                                        </td>
                                        <td
                                          style={{
                                            border: "1px solid black",
                                            padding: "10px",
                                            color: "darkblue",
                                          }}
                                        >
                                          {formData.subjectOfMphil || "N/A"}
                                        </td>

                                        <td
                                          style={{
                                            border: "1px solid black",
                                            padding: "10px",
                                            fontWeight: "bold",
                                          }}
                                        >
                                          M. Phil Award Date
                                        </td>
                                        <td
                                          style={{
                                            border: "1px solid black",
                                            padding: "10px",
                                            color: "darkblue",
                                          }}
                                        >
                                          {formatDate(
                                            formData.MphilAwardDate
                                          ) || "N/A"}
                                        </td>
                                      </tr>
                                    </>
                                  )}
                                </>
                              )}
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  PhD
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.phd === "true" ? "Yes" : "No"}
                                </td>
                              </tr>
                              {formData.phd === "true" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Subject of PhD
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.subjectOfPhd || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      PhD Notification Date
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(formData.phdNotifyDate) ||
                                        "N/A"}
                                    </td>
                                  </tr>
                                </>
                              )}
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Dsc / DLit
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.DscOrDlit === "true" ? "Yes" : "No"}
                                </td>
                              </tr>
                              {formData.DscOrDlit === "true" && (
                                <>
                                  <tr>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Subject of Dsc / DLit
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formData.subjectOfDscOrDlit || "N/A"}
                                    </td>

                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      Dsc / DLit Notification Date
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(
                                        formData.DscOrDlitNotifyDate
                                      ) || "N/A"}
                                    </td>
                                  </tr>
                                </>
                              )}
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  NET / SET
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.NetSet === "true" ? "Yes" : "No"}
                                </td>

                                {formData.NetSet === "true" && (
                                  <>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        fontWeight: "bold",
                                      }}
                                    >
                                      NET / SET Certificate Date
                                    </td>
                                    <td
                                      style={{
                                        border: "1px solid black",
                                        padding: "10px",
                                        color: "darkblue",
                                      }}
                                    >
                                      {formatDate(
                                        formData.NetSetCertificateDate
                                      ) || "N/A"}
                                    </td>
                                  </>
                                )}
                              </tr>
                              <tr>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Court Case Status
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.courtCaseStatus || "N/A"}
                                </td>

                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  Pending Departmental Enquiry / Prosecution
                                  Case
                                </td>
                                <td
                                  style={{
                                    border: "1px solid black",
                                    padding: "10px",
                                    color: "darkblue",
                                  }}
                                >
                                  {formData.PendingDeptEnquireCase === "true"
                                    ? "Yes"
                                    : "No"}
                                </td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </Row>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <Button color="secondary" onClick={() => handlePreview()}>
                      Close
                    </Button>
                    <Button
                      color="primary"
                      onClick={(e) => handleFinalSubmit(e, "finalSubmit")}
                    >
                      Final Submit
                    </Button>
                  </ModalFooter>
                </Modal>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default EmployeeRegistrationClassWise;
