import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Container,
  Input,
  Row,
  Col,
  Label,
  Table,
  Modal,
  ModalHeader,
  Spinner,
  <PERSON>dalB<PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>ge,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate";
import Swal from "sweetalert2";
import { BsDownload, BsEye } from "react-icons/bs";


const OutGoingTranferList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");

  const [formData, setFormData] = useState({
    chargeReleaseOrderNo: "",
    chargeReleaseOrderDate: "",
  });

  const [copyOfChargeReleaseLetter, setCopyOfChargeReleaseLetter] =
    useState(null);

  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => {
    setPreviewModal(!previewModal);
  };

  const [errors, setErrors] = useState({});

  const [empData, setEmpData] = useState([]);

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/reliving-data-byClgId/${id}`,
          {
            headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setEmpData(response.data);
          console.log("");
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token]);

  const [selectedTransfer, setSelectedTransfer] = useState({});

  const updateReliveingDetails = async (emp, e) => {
    console.log(emp, "Getting Employee Details");
    const empId = emp;

    try {
      const response = await axios.get(
        `${endPoint}/api/transfered-emp-byId/${empId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const selectedEmployee = response.data;
        togglePreviewModal();
        setSelectedTransfer(selectedEmployee);
      }
    } catch (error) {
      console.error("Error fetching Employee data:", error);
      alert("Failed to load Employee data.");
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (
      formData.chargeReleaseOrderDate === "" ||
      formData.chargeReleaseOrderDate === null ||
      formData.chargeReleaseOrderNo === "" ||
      formData.chargeReleaseOrderNo === null
    ) {
      SwalMessageAlert("Please Fill All Details", "error");
      return;
    }

    if (
      copyOfChargeReleaseLetter === "" ||
      copyOfChargeReleaseLetter === null
    ) {
      SwalMessageAlert("Please Upload File", "error");
      return;
    }

    Swal.fire({
      title: "Are you sure?",
      html: `
    क्या आप सुनिश्चित करते हैं कि आप <strong>${selectedTransfer.name}</strong> को भार मुक्त करना चाहते हैं?<br><br>
    <strong>Emp Code:</strong> ${selectedTransfer.empCode}<br>
    <strong>From :</strong> ${selectedTransfer.transferFromCollege.name}<br>
    <strong>To College:</strong> ${selectedTransfer.transferToCollege.name}
  `,
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "हाँ , भार मुक्त  करें!",
      cancelButtonText: "नहीं , रद्द करें !",
      reverseButtons: true,
    }).then(async (result) => {
      if (result.isConfirmed) {
        e.target.disabled = true;
        setTimeout(() => {
          e.target.disabled = false;
        }, 5000);

        const newformData = new FormData();
        for (const key in formData) {
          if (formData.hasOwnProperty(key)) {
            newformData.append(key, formData[key]);
          }
        }
        newformData.append(
          "copyOfChargeReleaseLetter",
          copyOfChargeReleaseLetter
        );

        try {
          const response = await axios.post(
            `${endPoint}/api/reliving-details-byId/${selectedTransfer._id}`,
            newformData,
            {
              headers: {
                "Content-Type": "multipart/form-data",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            SwalMessageAlert(
              "Employee Transfered Updated successfully!",
              "success"
            );
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          } else {
            SwalMessageAlert(response.data.msg, "error");
          }
        } catch (error) {
          console.error("An error occurred while submitting the form:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    });
  };

  const columns = [
    {
      name: "S.no",
      selector: (row, index) => index + 1,
      width: "100px",
      sortable: true,
    },
    {
      name: "Transfer ID / Date",
      selector: (row) => (
        <>
          <div className="font-weight-700">{row.transferID}</div>
          <div className="font-weight-700">({formatDate(row.createdAt)})</div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Transfer Order No / Date",
      selector: (row) => (
        <>
          <div>{row.transferOrderNo}</div>
          <div style={{ fontSize: "0.85em", color: "#555" }}>
            {formatDate(row.transferOrderDate)}
          </div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Update Reliveing Details",
      selector: (row) => (
        <>
          {row.isChargeReleasingDone === false ? (
            <div>
              <Button
                className="btn-sm bg-warning text-white"
                onClick={(e) => updateReliveingDetails(row._id, e)}
              >
                <Spinner
                  size="sm"
                  color="white"
                  style={{ marginRight: "8px" }}
                />
                Update
              </Button>
            </div>
          ) : (
            <Badge
              style={{ fontSize: "12px" }}
              className="bg-success text-white"
            >
              Updated
            </Badge>
          )}
        </>
      ),
      sortable: true,
    },

    {
      name: "Employee Details",
      selector: (row) => (
        <div>
          {row.name || "N/A"}
          <br />
          <strong>Emp Code: </strong>
          {row.empCode || "N/A"}
          <br />
          <strong>S/o: </strong>
          {row.fatherName || "N/A"}
          <br />
          <strong>Designation: </strong>
          {row.designation || "N/A"}
          <br />
          <strong>Contact No: </strong>
          {row.mobileNo || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Institute Name",
      selector: (row) => (
        <div>
          <strong>From College: </strong>
          {row.transferFromCollege?.name || "N/A"}
          <br />
          <strong>Target College: </strong>
          {row.transferToCollege?.name || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Charge Relieving Details",
      selector: (row) => (
        <div>
          <strong>Relieving Status : </strong>
          {row.isChargeReleasingDone === true ? (
            <Badge className="bg-success text-white">Completed </Badge>
          ) : (
            <Badge className="bg-warning text-white">Pending </Badge>
          )}
          <br />
          {row.isChargeReleasingDone === true && (
            <>
              <strong>Order No :</strong>
              {row.chargeReleaseOrderNo || "N/A"}
              <br />
              <strong>Order Date :</strong>
              {formatDate(row.chargeReleaseOrderDate) || "N/A"}
              <br />
              <strong>Order Letter : </strong>
              <a
                href={`https://heonline.cg.nic.in/${row.copyOfChargeReleaseLetter}`}
                download
              >
                <Button className="btn-sm btn-primary">
                  {row.copyOfChargeReleaseLetter ? <BsDownload color="blue"  size={18} /> : "No File"}
                </Button>
              </a>
            </>
          )}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
  ];

  const [pdfFile, setPdfFile] = useState(null);
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file.type !== "application/pdf") {
      setErrors((prevErrors) => ({
        ...prevErrors,
        copyOfChargeReleaseLetter: "Please upload a PDF file.",
      }));
      setCopyOfChargeReleaseLetter(null);
      event.target.value = "";
      return;
    }

    if (file.size > 200 * 1024) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        copyOfChargeReleaseLetter: "File size must be less than 200 KB.",
      }));
      setCopyOfChargeReleaseLetter(null);
      event.target.value = "";

      return;
    }
    setErrors((prevErrors) => ({
      ...prevErrors,
      copyOfChargeReleaseLetter: null,
    }));
    setCopyOfChargeReleaseLetter(file);
    setPdfFile(URL.createObjectURL(file));
  };

  const handleChange = (e) => {
    const { name } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: e.target.value,
    }));
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="border-0 d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">Update Relieving Details</h3>
            </Col>
          </CardHeader>
          <CardBody>
            <DataTable
              columns={columns}
              data={empData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              defaultSortField="name"
              defaultSortAsc={true}
              customStyles={{
                header: {
                  style: {
                    backgroundColor: "#f8f9fa",
                    fontWeight: "bold",
                  },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff",
                    borderBottom: "1px solid #ddd",
                  },
                  // Apply hover effect through the :hover pseudo-class directly in custom CSS
                  onHoverStyle: {
                    backgroundColor: "#ffff99", // Hover color
                  },
                },
              }}
            />
          </CardBody>
        </Card>
        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Update Relieing Details</h2>
            <h5 className="text-danger mt-2">
              Note: Please check all fields carefully before submitting
            </h5>
          </ModalHeader>
          <ModalBody style={{ backgroundColor: "#f8f9fa", padding: "20px" }}>
            <Row>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-transferID">Transfer Id.</Label>
                  <Input
                    type="text"
                    name="transferID"
                    disabled
                    value={selectedTransfer.transferID}
                  />
                </FormGroup>
              </Col>

              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-transferOrderNo">
                    Transfer Order No
                  </Label>
                  <Input
                    name="transferOrderNo"
                    type="text"
                    value={selectedTransfer.transferOrderNo}
                    disabled
                  />
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-transferOrderDate">
                    Transfer Order Date
                  </Label>
                  <Input
                    name="transferOrderDate"
                    type="text"
                    value={formatDate(selectedTransfer.transferOrderDate)}
                    disabled
                  />
                </FormGroup>
              </Col>
            </Row>
            <Row>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-emp Code">Employee Name</Label>
                  <Input disabled name="name" value={selectedTransfer.name} />
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-fatherName">Father`s Name</Label>
                  <Input
                    placeholder="Enter Father Name"
                    disabled
                    name="fatherName"
                    value={selectedTransfer.fatherName}
                  />
                  {errors.fatherName && (
                    <span className="text-danger">{errors.fatherName}</span>
                  )}
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-empCode">Employee Code</Label>
                  <Input
                    type="text"
                    name="empCode"
                    disabled
                    value={selectedTransfer.empCode}
                  />
                </FormGroup>
              </Col>
            </Row>
            <Row>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-mobileNo">Mobile No.</Label>
                  <Input
                    type="text"
                    name="mobileNo"
                    disabled
                    value={selectedTransfer.mobileNo}
                  />
                </FormGroup>
              </Col>

              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-designation">Designation</Label>
                  <Input
                    name="designation"
                    type="text"
                    value={selectedTransfer.designation}
                    disabled
                  />
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-fromCollege">Target College</Label>
                  <Input
                    name="designation"
                    type="text"
                    value={selectedTransfer?.transferToCollege?.name}
                    disabled
                  />
                </FormGroup>
              </Col>
            </Row>
            <Row>
              <Col>
                {pdfFile && (
                  <iframe
                    src={pdfFile}
                    title="PDF Preview"
                    width="100%"
                    height="500px"
                    style={{
                      marginTop: "20px",
                      border: "1px solid #ccc",
                    }}
                  />
                )}
              </Col>
            </Row>
            <Row>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-chargeReleaseOrderNo">
                    Charge Release Order No
                  </Label>
                  <Input
                    type="text"
                    name="chargeReleaseOrderNo"
                    value={formData.chargeReleaseOrderNo}
                    autoComplete="off"
                    onChange={handleChange}
                    disabled={false} // Set to true if you want to disable the input
                  />
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-chargeReleaseOrderDate">
                    Charge Release Order Date
                  </Label>
                  <Input
                    type="date"
                    name="chargeReleaseOrderDate"
                    value={formData.chargeReleaseOrderDate}
                    max={new Date().toISOString().split("T")[0]}
                    min={
                      selectedTransfer.transferOrderDate
                        ? new Date(selectedTransfer.transferOrderDate)
                            .toISOString()
                            .split("T")[0]
                        : ""
                    }
                    onChange={handleChange}
                    disabled={false} // Set to true if you want to disable the input
                  />
                </FormGroup>
              </Col>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-copyOfChargeReleaseLetter">
                    Copy of Charge Release Letter
                  </Label>
                  <input
                    type="file"
                    style={{ fontWeight: "bolder" }}
                    onChange={handleFileChange}
                    name="copyOfChargeReleaseLetter"
                    className="form-control"
                    id="inputGroupFile01"
                    accept=".pdf"
                  ></input>
                  {/* Optionally display selected file name */}
                </FormGroup>
                {errors.copyOfChargeReleaseLetter && (
                  <span className="text-danger">
                    {errors.copyOfChargeReleaseLetter}
                  </span>
                )}
              </Col>
            </Row>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button
              color="danger"
              onClick={togglePreviewModal}
              style={{ borderRadius: "5px" }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{
                borderRadius: "5px",
                backgroundColor: "#007bff",
                border: "none",
              }}
            >
              Submit
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default OutGoingTranferList;
