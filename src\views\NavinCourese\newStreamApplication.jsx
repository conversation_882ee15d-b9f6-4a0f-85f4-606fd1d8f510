import { useRef, useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  CardBody,
  CardHeader,
  <PERSON>dal,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  ModalBody,
  <PERSON><PERSON><PERSON>ooter,
  CardFooter,
} from "reactstrap";
import { useParams, useNavigate } from "react-router-dom";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import Header from "../../components/Headers/Header.jsx";

import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";


const NewStreamApplication = () => {
  const trgetRef = useRef(null);
  const { id } = useParams();
  const navigate = useNavigate();

  // const id = "67e3929308f4c424c150cfce";
  const endPoint = import.meta.env.VITE_API_URL;
  const [showPreview, setShowPreview] = useState(true);



  const token = sessionStorage.getItem("authToken");
  const date = new Date();

  const [data, setData] = useState({})

  const [designations, setDesignations] = useState([]);
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const verifiedDesignations = response.data.filter(
            (designation) => designation.isVerified === 1
          );

          setDesignations(verifiedDesignations);
        } else {
          alert("Failed to fetch designations. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchDesignations();
  }, [endPoint, token]);


  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]);

  const [subjectData, setSubjectData] = useState([]);

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {

          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchSubjectList();
  }, [endPoint, token]);



  useEffect(() => {
    const courseData = async () => {
      // console.log("Entering here");

      try {
        const response = await axios.get(
          `${endPoint}/api/newCourse/getById/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setData(response.data);
          // console.log(response.data, "Getting Response Data");
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    courseData(); // Call the async function
  }, [id, token, endPoint]);


  const generatePDF = () => {
    const data = trgetRef.current;

    html2canvas(data).then((canvas) => {
      const imgdata = canvas.toDataURL("img/png");
      const pdf = new jsPDF("p", "mm", "a4", true);
      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();
      const imgWidth = pdfWidth - 20;
      const imgHeight = (canvas.height * imgWidth) / canvas.width + 50;
      let yPosition = -10;
      while (yPosition < imgHeight) {
        pdf.addImage(imgdata, 10, -yPosition, imgWidth, imgHeight);
        pdf.setFillColor(255, 255, 255);
        pdf.rect(0, 0, pdfWidth, 10, "F");
        pdf.rect(0, pdfHeight - 10, pdfWidth, 10, "F");
        yPosition += 297 - 20;
        if (yPosition < imgHeight) {
          pdf.addPage();
        }
      }
      pdf.save("Application.pdf");
    });
  };


  // console.log(data, "Data here"); // Check the structure of data
  // console.log(data.navinVishay, "Navin Vishay");

  const year = date.getFullYear();

  return (
    <>
      <Header />
      <Modal isOpen={showPreview} toggle={() => setShowPreview(false)} style={{ maxWidth: "72%" }}>
        <ModalHeader >
          Print Preview
        </ModalHeader>
        <ModalBody>
          <Container>
            <Card style={{ padding: "4px", margin: "10px", fontSize: "12px" }}>
              <div ref={trgetRef}>
                <CardHeader>
                  <Row>
                    <Col>
                      {data &&
                        data.navinVishay &&
                        data.navinVishay.length > 0 &&
                        (data.navinVishay[0].type === "janBhagidari" || data.navinVishay[0].type === "selfFinance") &&
                        <>
                          <h5>
                            आयुक्त <br />
                            उच्च शिक्षा संचालनालय <br />
                            इंद्रावती भवन, नवा रायपुर <br />
                            अटल नगर जिला रायपुर (छ0ग0) <br />
                            <br />
                            विषयः- शैक्षणिक सत्र {year}-{(year + 1).toString().slice(2)} से स्ववित्तीय योजना/जनभागीदारी मद से नवीन विषय/संकाय/कक्षा प्रारंभ करने की अनुमति हेतु ऑनलाईन आवेदन बाबत्। <br /><br />
                            उपरोक्त विषयान्तर्गत लेख है कि महाविद्यालय में स्ववित्तीय योजना/जनभागीदारी मद से नवीन विषय/संकाय/कक्षा प्रारंभ हेतु वांछित जानकारी निर्धारित प्रारूप में नीचे दर्शित तालिका अनुसार ऑनलाईन प्रेषित है। कृपया अनुमति प्रदान करना चाहेगे।
                         
                          </h5>

                        </>}

                      {data &&
                        data.navinVishay &&
                        data.navinVishay.length > 0 &&
                        (data.navinVishay[0].type === "government") &&
                        <>
                          <h5>
                            आयुक्त <br />
                            उच्च शिक्षा संचालनालय <br />
                            इंद्रावती भवन, नवा रायपुर <br />
                            अटल नगर जिला रायपुर (छ0ग0) <br />
                            <br />
                            विषयः- शैक्षणिक सत्र {year}-{(year + 1).toString().slice(2)} से शासन से नवीन विषय/संकाय/कक्षा प्रारंभ करने की अनुमति हेतु ऑनलाईन आवेदन बाबत्। <br /><br />
                            उपरोक्त विषयान्तर्गत लेख है कि महाविद्यालय में शासन से नवीन विषय/संकाय/कक्षा प्रारंभ हेतु वांछित जानकारी निर्धारित प्रारूप में नीचे दर्शित तालिका अनुसार ऑनलाईन प्रेषित है। कृपया अनुमति प्रदान करना चाहेगे।
                          
                          </h5>
                        </>}
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody style={{ margin: "15px" , marginTop:"-50px"}}>
                  <table border={1}>
                    <tbody style={{ textAlign: "center" }}>
                      <tr>
                        <td
                          style={{ width: "250px" }}
                          rowSpan={data && data.navinVishay && data.navinVishay.length + 1}
                        >
                          प्रस्तावित
                          नवीन संकाय कक्षा का नाम एवं वांछित सीट संख्या{ }
                          <strong>
                            (स्नातक या स्नातकोत्तर स्तर का उल्लेख करें)
                          </strong>
                        </td>
                        <th colSpan={2}>विषय संकाय कक्षा का नाम</th>
                        <th colSpan={2}>वांछित सीट संख्या</th>
                        <th colSpan={2}>संचालन का प्रकार</th>

                      </tr>
                      {data && data.navinVishay && data?.navinVishay.map((data) => {
                        return (
                          <> <tr>
                            <td colSpan={2}>
                              {data.subject}/ {data.course}/ {data.className}
                            </td>
                            <td colSpan={2}>
                              {data.requiredSeat}
                            </td>
                            <td colSpan={2}>
                              {data.type === "janBhagidari" ? "जनभागीदारी" : data.type === "government" ? "शासकीय" : data.type === "selfFinance" ? "स्व-वित्तीय" : ""}
                            </td>
                          </tr>
                          </>)
                      })}

                      {data?.courseType === "UG" && <>
                        <tr>
                          <td
                            style={{ width: "250px" }}
                            rowSpan={data && data.poshakShalaStudent && data.poshakShalaStudent.length + 1}
                          >
                            <b>स्नातक स्तर पर</b> प्रस्तावित नवीन नवीन संकाय/कक्षा
                            प्रारंभ करने हेतु पोषक शालाओं में संबंधित संकाय/विषय में
                            छात्र संख्या एवं गतवर्ष उत्तीर्ण छात्र संख्या ।
                          </td>
                          <th colSpan={2}>पोषक शाला का नाम</th>
                          <th colSpan={1}> कला</th>
                          <th colSpan={1}>विज्ञान</th>
                          <th colSpan={1}>वाणिज्य</th>
                          {/* </tr> */}
                        </tr>
                        {data && data.poshakShalaStudent && data.poshakShalaStudent.map((data) => {
                          return (
                            <>
                              <tr>
                                <td colSpan={2}>{data.school}</td>
                                <td colSpan={1}> {data.arts}</td>
                                <td colSpan={1}>{data.science}</td>
                                <td colSpan={1}>{data.commerce}</td>
                              </tr>
                            </>)
                        })}
                      </>}

                      {data?.courseType === "PG" && <>
                        <tr>
                          <td
                            style={{ width: "250px" }}
                            rowSpan={data && data.snatakStudent && data?.snatakStudent.length + 1}
                          >
                            स्नातकोत्तर स्तर पर प्रस्तावित नवीन नवीन संकाय/कक्षा
                            प्रारंभ करने हेतु घोषक कक्षा
                            <strong>
                              (संबंधित विषय के स्नातक अंतिम वर्ष में अध्ययनरत छात्र
                              संख्या एवं गत वर्षों में उत्तीर्ण छात्र संख्या।
                            </strong>
                          </td>
                          <th colSpan={2} width={400}>
                            गत वर्ष स्नातक अंतिम वर्ष में उत्तीर्ण छात्र संख्या
                          </th>

                          <th colSpan={3}>
                            चालू सत्र में संबंधित विषय के स्नातक अंतिम वर्ष में
                            अध्ययनरत छात्र संख्या
                          </th>
                        </tr>
                        {data && data.snatakStudent && data.snatakStudent.map((data) => {
                          return (
                            <>
                              <tr>
                                <td colSpan={2}>
                                  {data.subject}({data.passedStudent})
                                </td>
                                <td colSpan={3}>
                                  {data.subject}({data.lastYearStudent})
                                </td>
                              </tr>
                            </>
                          );
                        })}
                      </>}

                      <tr>
                        <td
                          style={{ width: "250px" }}
                          rowSpan={data && data.sambandhitVishwaVidyalaya && data.sambandhitVishwaVidyalaya.length + 1}
                        >
                          उक्त प्रस्तावित नवीन संकाय/कक्षा की संबंद्धता किस
                          विश्वविद्यालय से प्राप्त की जावेगी।
                          <strong>
                            (संबंधित विषय के स्नातक अंतिम वर्ष में अध्ययनरत छात्र
                            संख्या एवं गत वर्षों में उत्तीर्ण छात्र संख्या।
                          </strong>
                        </td>
                      </tr>
                      {data && data.sambandhitVishwaVidyalaya && data.sambandhitVishwaVidyalaya.map((data) => {
                        return (
                          <>
                            <tr>
                              <td colSpan={5}>
                                {university &&
                                  university.length > 0 &&
                                  university
                                    .filter((type) => type._id === data.university) // Find the matching university
                                    .map((type, index) => <span key={index}>({type.name})</span>)}
                                {data.certificate}
                              </td>
                            </tr>
                          </>
                        );
                      })}

                      {data &&
                        data.navinVishay &&
                        data.navinVishay.length > 0 &&
                        (data.navinVishay[0].type === "janBhagidari" || data.navinVishay[0].type === "selfFinance") &&
                        <>
                          <tr>
                            <td style={{ width: "250px" }} rowSpan={data && data.selfTeacherEstimate && data.selfTeacherEstimate.length + 1}>
                              क्या उक्त प्रस्तावित नवीन संकाय कक्षा में प्रवेश प्राप्त
                              छात्रों के अध्यापन हेतु पृथक से स्ववित्तीय जनभागीदारी मद
                              से किसी शैक्षणिक स्टॉफ नियुक्त किया जावेगा?{" "}
                              <strong>(यदि हॉ)</strong> तो शैक्षणिक स्टॉफ के अनुमानित
                              मासिक वेतन की जानकारी (उदाहरण अनुसार)
                            </td>
                            <th colSpan={1}>विषय संकाय/कक्षा का नाम</th>
                            <th colSpan={1}>शैक्षणिक स्टॉफ</th>
                            <th colSpan={1} width={150}>
                              पद संख्या
                            </th>
                            <th colSpan={1}>अनुमानित मासिक वेतन</th>
                            <th colSpan={1}>
                              वार्षिक वित्तीय भार (मासिक वेतन पदों की संख्या -12)
                            </th>
                          </tr>
                          {data && data.selfTeacherEstimate && data.selfTeacherEstimate.map((sub) => {
                            return (
                              <>
                                <tr>
                                  <td colSpan={1}>
                                    {sub.subject}/{sub.course}/{sub.className}
                                  </td>
                                  <td colSpan={1}>
                                    {designations &&
                                      designations.length > 0 &&
                                      designations.find((type) => type._id === sub.designation)?.designation || "N/A"}
                                  </td>
                                  <td colSpan={1}>{sub.seats}</td>
                                  <td colSpan={1}>{sub.estimatedSalaryMonthly}</td>
                                  <td colSpan={1}>{sub.estimatedSalaryYearly}</td>
                                </tr>
                              </>
                            );
                          })}
                          <tr>
                            <td style={{ width: "250px" }} rowSpan={data && data.incomeFromStudent && data.incomeFromStudent.length + 1}>
                              उक्त प्रस्तावित नवीन संकाय कक्षा में प्रवेश हेतु प्रति
                              छात्र कितना अनुमानित वार्षिक शुल्क निर्धारित होगा तथा सीट
                              संख्या अनुसार प्रति छात्र हेतु निर्धारित वार्षिक शुल्क से
                              कितना वार्षिक आय प्राप्त होगा, की जानकारी देवें।
                            </td>
                            <th colSpan={2}>विषय संकाय/कक्षा का नाम</th>

                            <th colSpan={1}>
                              प्रति छात्र हेतु अनुमानित निर्धारित वार्षिक शुल्क
                            </th>

                            <th colSpan={2}>
                              वार्षिक आय (सीट संख्या x प्रति छात्र निर्धारित वार्षिक
                              शुल्क)
                            </th>
                          </tr>
                          {data && data.incomeFromStudent && data.incomeFromStudent.map((data) => {
                            return (
                              <>
                                <tr>
                                  <td colSpan={2}>
                                    {data.subject}/ {data.course}/{data.className}
                                  </td>
                                  <td colSpan={1}>{data.yearlyFees}</td>
                                  <td colSpan={2}>{data.totalIncome}</td>
                                </tr>
                              </>
                            );
                          })}
                          <tr>
                            <td
                              style={{ width: "250px" }}
                              rowSpan={5}
                            >
                              पिछले 02 वर्षों में स्ववित्तीय/जनभागीदारी मद अंतर्गत
                              प्राप्त आय एवं व्यय का विवरण
                            </td>

                            <th colSpan={1}>मद नाम</th>
                            <th colSpan={1}>वर्ष</th>
                            <th colSpan={1}>प्राप्त आय</th>
                            <th colSpan={1}>व्यय राशि</th>
                            <th colSpan={1}>शेष राशि</th>
                          </tr>
                          {/* <tr>  */}

                          {data && data.lastTwoYearRecord && data.lastTwoYearRecord.map((data, index) => {
                            return (
                              <>
                                <tr>
                                  {(index + 1) % 2 === 1 &&
                                    <td rowSpan={2}>
                                      {index === 0 || index === 1 ? "स्ववित्तीय मद " : "जनभागीदारी मद"}
                                    </td>}

                                  <td rowSpan={1} colSpan={1}>
                                    {data.year}
                                  </td>
                                  <td rowSpan={1} colSpan={1}>
                                    {data.income}
                                  </td>
                                  <td rowSpan={1} colSpan={1}>
                                    {data.expense}
                                  </td>
                                  <td rowSpan={1} colSpan={1}>
                                    {data.balance}
                                  </td>
                                </tr>
                              </>
                            );
                          })}

                        </>}
                      <tr>
                        <td
                          style={{ width: "250px" }}
                          rowSpan={data && data.mahavidyalayaJankari && data.mahavidyalayaJankari.length + 1}
                        >
                          महाविद्यालय में उपलब्ध भवन/ अध्ययन कक्ष/फर्नीचर
                          संख्या/प्रयोगशाला कक्ष संख्या प्रस्तावित नवीन विषय/संकाय
                          की पुस्तक संख्या की जानकारी
                        </td>
                        <th colSpan={1}>स्वयं का भवन (हॉ/नही)</th>

                        <th colSpan={1}>अध्ययन कक्ष संख्या</th>
                        <th colSpan={1}>फर्नीचर संख्या</th>

                        <th colSpan={1}>प्रयोगशाला कक्ष संख्या</th>
                        <th colSpan={1}>
                          प्रस्तावित नवीन विषय संकाय की पुस्तक संख्या
                        </th>
                      </tr>
                      {data && data.mahavidyalayaJankari && data.mahavidyalayaJankari.map((data) => {
                        return (
                          <>
                            <tr>
                              <td colSpan={1}>{data.selfBuilding}</td>
                              <td colSpan={1}>{data.classroomNo}</td>
                              <td colSpan={1}>{data.furnitureNo}</td>
                              <td colSpan={1}>{data.labsNo}</td>
                              <td colSpan={1}>{data.booksNo}</td>
                            </tr>
                          </>
                        );
                      })}
                      <tr>
                        <td style={{ width: "250px" }} rowSpan={data && data.previousCourseSubjects && data.previousCourseSubjects.length + 1}>
                          महाविद्यालय में पूर्व से संचालित सभी संकाय/कक्षा का नाम
                          एवं निर्धारित सीट संख्या की जानकारी। टीपः केवल प्रथम वर्ष
                          या प्रथम सेमेस्टर हेतु निर्धारित सीट संख्या की जानकारी
                          देवें।
                        </td>
                        <th colSpan={2}>विषय/संकाय/कक्षा का नाम</th>

                        <th colSpan={1}>
                          संचालन का प्रकार (शासन स्तर/स्ववित्तीय योजना/जनभागीदारी
                          मद)
                        </th>

                        <th colSpan={2}>निर्धारित सीट संख्या</th>
                      </tr>
                      {data && data.previousCourseSubjects && data.previousCourseSubjects.map((data) => {
                        return (
                          <>
                            <tr>
                              <td colSpan={2}>
                                {data.subject}/{data.course}/{data.className}
                              </td>
                              <td colSpan={1}>
                                {data.sanchalanType === "janBhagidari" ? "जनभागीदारी" : data.sanchalanType === "government" ? "शासकीय" : data.sanchalanType === "selfFinance" ? "स्व-वित्तीय" : ""}

                              </td>
                              <td colSpan={2}>
                                {data.seats}
                              </td>
                            </tr>
                          </>
                        );
                      })}
                      <tr>
                        <td
                          style={{ width: "250px" }}
                          rowSpan={data && data.currentStaff && data.currentStaff.length + 1}
                        >
                          वर्तमान में महाविद्यालय हेतु शासन से स्वीकृत समस्त
                          शैक्षणिक एवं अशैक्षणिक पदों की जानकारी (उदाहरण:-
                          प्राध्यापक इतिहास या सहायक प्राध्यापक हिन्दी)
                        </td>
                        <th colSpan={2}>पदनाम</th>

                        <th colSpan={1}>स्वीकृत</th>
                        <th colSpan={1}>कार्यरत</th>

                        <th colSpan={1}>रिक्त</th>
                      </tr>
                      {data && data.currentStaff && data.currentStaff.map((data) => {
                        return (
                          <>
                            <tr>
                              <td colSpan={2}>
                                {designations &&
                                  designations.length > 0 &&
                                  designations.find((type) => type._id === data.designation)?.designation || "N/A"}
                              </td>
                              <td colSpan={1}>{data.sanctioned}</td>
                              <td colSpan={1}>{data.working}</td>
                              <td colSpan={1}>{data.vacant}</td>
                            </tr>
                          </>
                        );
                      })}
                      {data &&
                        data.navinVishay &&
                        data.navinVishay.length > 0 &&
                        (data.navinVishay[0].type === "government") &&
                        <>
                          <tr>
                            <td
                              style={{ width: "250px" }}
                              rowSpan={data && data.requiredStaff && data.requiredStaff.length + 1}
                            >
                              प्रस्तावित नवीन विषय/संकाय / कक्षा प्रारंभकरने हेतु वांछित
                              शैक्षणिक / अशैक्षणिक पदों की संख्या एवं पदों का वित्तीय
                              भार{" "}
                              <b>
                                (उदाहरणः सहा.प्राच्या हिन्दी 01 पद या प्राध्यापक
                                इतिहास-01 पद){" "}
                              </b>{" "}
                            </td>
                            <th colSpan={2} style={{ textDecoration: "underline" }}>
                              वांछित पदों का नाम
                            </th>
                            <th colSpan={2} style={{ textDecoration: "underline" }}>
                              वांछित पदों की संख्या
                            </th>
                            <th colSpan={1} style={{ textDecoration: "underline" }}>
                              वित्तीय भार
                            </th>
                          </tr>
                          {data && data.requiredStaff && data.requiredStaff.map((data) => {
                            return (
                              <>
                                <tr>
                                  <td colSpan={2}>  {designations &&
                                    designations.length > 0 &&
                                    designations.find((type) => type._id === data.designation)?.designation || "N/A"}</td>
                                  <td colSpan={2}>{data.seats}</td>
                                  <td colSpan={1}>{data.financialLoad}</td>
                                </tr>
                              </>
                            );
                          })}
                        </>}
                      <tr>
                        <td style={{ width: "250px" }}>
                          स्तावित नवीन संकाय/ कक्षा प्रारंभ करने का औचित्य (अनिवार्य
                          रूप से उल्लेख करें)
                        </td>
                        <td colSpan={5}>{data && data.remarkByPrincipal}</td>
                      </tr>
                    </tbody>
                  </table>
                  <br />
                  <br />
                  {data &&
                    data.navinVishay &&
                    data.navinVishay.length > 0 &&
                    (data.navinVishay[0].type === "janBhagidari" || data.navinVishay[0].type === "selfFinance") &&
                    <>
                      <h4 style={{ textAlign: "left", textDecoration: "underline" }}>
                        मैं स्ववित्तीय/जनभागीदारी मद अंतर्गत उक्त नवीन विषय/संकाय/कक्षा को निम्नांकित शर्तो के अधीन प्रारंभ करने हेतु सहमत हूँ।
                      </h4>
                      <ol style={{ fontSize: "14px" }}>
                        <li>
                          स्ववित्तीय योजना/जनभागीदारी मद के अंतर्गत उक्त नवीन
                          विषय/संकाय/कक्षा का संचालन शैक्षणिक सत्र {year}-{year + 1} से प्राचार्य
                          द्वारा संबंधित विश्वविद्यालय से नियमानुसार संबंद्धता प्राप्त
                          करने के उपरांत किया जावेगा। संबंद्धता प्राप्त करने की संपूर्ण
                          जिम्मेदारी संबंधित प्राचार्य की होगी।
                        </li>
                        <li>
                          स्ववित्तीय योजना/जनभागीदारी मद के अंतर्गत उक्त नवीन
                          विषय/संकाय/कक्षा के अध्यापन हेतु शैक्षणिक स्टॉफ, लाइब्रेरी,
                          फर्नीचर उपकरण तथा कम्प्यूटर आदि की व्यवस्था संस्था को अपने
                          स्वयं के स्त्रोतों से वहन करना होगा। इसके लिए किसी प्रकार का
                          वित्तीय भार शासन द्वारा वहन नहीं किया जावेगा अर्थात् राज्य
                          शासन द्वारा भविष्य में किसी प्रकार का आवर्ती तथा अनावर्ती व्यय
                          हेतु कोई भी अनुदान नहीं दिया जावेगा।
                        </li>
                        <li>
                          शिक्षकों की नियुक्ति विश्वविद्यालय अनुदान आयोग द्वारा
                          निर्धारित शैक्षणिक योग्यता के आधार पर शासन के निर्धारित मापदंड
                          पर की जावेगी।
                        </li>
                        <li>
                          उक्त नवीन विषय/संकाय/कक्षा प्रारंभ करने पर आय-व्यय के लेखों का
                          सत्यापन विभागीय अंकेक्षण दल/सी.ए. द्वारा कराया जाकर प्रतिवर्ष
                          उच्च शिक्षा संचालनालय को अपना प्रतिवेदन सौंपना होगा।
                        </li>
                      </ol>

                    </>}
                  {data &&
                    data.navinVishay &&
                    data.navinVishay.length > 0 &&
                    (data.navinVishay[0].type === "government") &&
                    <>
                      <h5 style={{ textAlign: "left", textDecoration: "underline" }}>
                        मैं
                        निम्नलिखित शर्तों के अधीन उपरोक्त नवीन विषय/संकाय / कक्षा
                        प्रारंभ करने हेतु सहमत हूँ-
                      </h5>
                      <ol style={{ fontSize: "14px" }}>
                        <li>
                          उक्त प्रस्तावित नवीन विषय / संकाय / कक्षा प्रारंभ का संचालन
                          शैक्षणिक सत्र 2025-26 से किया जावेगा।
                        </li>
                        <li>
                          महाविद्यालय द्वारा उक्त प्रस्तावित नवीन विषय / संकाय / कक्षा
                          प्रारंभ का संचालन संबंधित विश्वविद्यालय ते संबंद्धता प्राप्त
                          करने के उपरांत ही किया जावेगा। संबंद्धता प्राप्त करने की
                          जिम्मेदारी संबंधित प्राचार्य की होगी।
                        </li>
                        <li>
                          शासन द्वारा जारी सभी आदेशों / शर्तों का पूर्णतः पालन किया
                          जावेगा।
                        </li>
                      </ol>
                    </>}

                  <p style={{ textAlign: "left", fontWeight: "630" }}>Date - {formatDate(data.createdAt)}</p>
                  <p style={{ textAlign: "right", paddingRight: "50px" }}>

                    <strong style={{ fontWeight: "630" }}>{data?.collegeDetails?.contactPerson}<br />{data?.collegeDetails?.name}</strong>

                  </p>
                </CardBody>
              </div>
            </Card>
          </Container>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={() => { setShowPreview(false); navigate("/admin/dashboard"); }}>
            Close
          </Button>
          <Button color="primary" onClick={() => { generatePDF(); navigate("/admin/dashboard"); }}>
            Print
          </Button>
        </ModalFooter>
      </Modal >
    </>
  );
};

export default NewStreamApplication;
