import Swal from "sweetalert2";
export default function SwalMessageAlert(message, icon, html) {
  if (icon === "success") {
    Swal.fire({
      title: "",
      text: message,
      icon: icon,
      timer: 5000,
    });
  }
  if (icon === "error") {
    Swal.fire({
      title: "",
      text: message,
      icon: icon,
      timer: 5000,
    });
  }
  if (icon === "warning") {
    Swal.fire({
      title: "",
      text: message,
      icon: icon,
      timer: 5000,
    });
  }
  if (icon === "info") {
    Swal.fire({
      title: "",
      text: message,
      icon: icon,
      timer: 5000,
    });
  }
  if (icon === "question") {
    Swal.fire({
      title: "",
      text: message,
      icon: icon,
      timer: 5000,
    });
  }
  if (icon === "warning1") {
    Swal.fire({
      title: "",
      html: html,
      text: message,
      icon: "warning",
    });
  }
}
