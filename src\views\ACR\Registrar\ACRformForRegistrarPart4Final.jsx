import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    FormGroup,
    Col,
    Label,
    Button,
    Input,
} from "reactstrap";


const ACRformForRegistrarPart4Final = (employee) => {

    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);
    const [errors, setErrors] = useState({});





    return (
        <>
            <Container className="mt--6" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2"> <u>प्रपत्र - तीन</u></h2>
                                <h2>प्रथम श्रेणी, द्वितीय श्रेणी, कार्यपालिक श्रेणी के अधिकारी का</h2>
                                <br /><h2> <u>गोपनीय प्रतिवेदन</u></h2>
                                <h3>|| 31 मार्च {year} को समाप्त होने होने वाली अवधि ||</h3>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - एक</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong>अधिकारी का नाम

                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={employee.employee?.basicDetails[0].employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पदनाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="designation"
                                                        value={employee.employee?.basicDetails[0].designation} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.designation && (
                                                        <small className="text-danger">
                                                            {errors.designation}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong>नियोजन का प्रकार</Label>
                                                    <Input
                                                        type="text"
                                                        name="niyojanKaPrakar"
                                                        value={employee.employee?.basicDetails[0].niyojanKaPrakar} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.niyojanKaPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyojanKaPrakar}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>4.</strong>पदस्थापना का जिला</Label>
                                                    <Input
                                                        type="text"
                                                        name="padSthapnaKaJila"
                                                        value={employee.employee?.basicDetails[0].padSthapnaKaJila} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.padSthapnaKaJila && (
                                                        <small className="text-danger">
                                                            {errors.padSthapnaKaJila}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - दो ( प्रतिवेदित अधिकारी द्वारा जाये ).</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="12">
                                                    <Label> <strong>1.</strong>कार्य का संक्षिप्त विवरण</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKaVivran"
                                                        value={employee.employee?.basicDetails[0].karyaKaVivran} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.karyaKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.karyaKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <Label> <strong>2.</strong>कृपया आपके लिये निर्धारित गुणात्मक / भौतिक/वित्तीय लक्ष्यों को प्राथमिकता क्रम में और प्रत्येक लक्ष्य के विरूद्ध उपलब्धि का उल्लेख करें ।
                                                    </Label>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>लक्ष्य : </strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        style={{ height: "150px" }}

                                                        name="lakshya"
                                                        value={employee.employee?.basicDetails[0].lakshya} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.lakshya && (
                                                        <small className="text-danger">
                                                            {errors.lakshya}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label><strong>उपलब्धियाँ :</strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        style={{ height: "150px" }}
                                                        name="uplabdhi"
                                                        value={employee.employee?.basicDetails[0].uplabdhi} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.uplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>3. </strong>
                                                        कृपया कालम 2 के संदर्भ में लक्ष्यों /
                                                        उद्देश्यों की पूर्ति में कमी का संक्षिप्त विवरण दें ।
                                                        यदि लक्ष्यों की पूर्ति में कोई कठिनाई
                                                        (बाधा) आई हो तो उसको भी बतायें ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        style={{ height: "150px" }}
                                                        name="purtiKeKamiKaVivran"
                                                        value={employee.employee?.basicDetails[0].purtiKeKamiKaVivran} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.purtiKeKamiKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.purtiKeKamiKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label><strong>4. </strong>
                                                        कृपया उन मदों को भी दर्शाये
                                                        जिनमें अति महत्वपूर्ण उपलब्धियां
                                                        और आपका सहयोग <br />रहा हो ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        style={{ height: "150px" }}
                                                        name="atiMahatvaPurnUplabdhi"
                                                        value={employee.employee?.basicDetails[0].atiMahatvaPurnUplabdhi} // Use employee data here
                                                        className="form-control"
                                                        readOnly

                                                    />
                                                    {errors.atiMahatvaPurnUplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.atiMahatvaPurnUplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>

                                    {employee.employee.levelIndex >= 0 && employee.employee.preDefinedLevels[0].status === true && <>

                                        <hr />


                                        <Row className="pl-4 pr-4">
                                            <Col>
                                                <h3><i> <u>भाग - तीन (प्रतिवेदन अधिकारी द्वारा भरा जावे).</u></i></h3>
                                            </Col>
                                        </Row>
                                        <Row className="pl-4 pr-4">
                                            <Col>
                                                <Row className="mb-3 mt-4">
                                                    <Col>
                                                        <h3><strong>(अ) कार्य का स्परूप एवं प्रकार-</strong></h3>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-4">
                                                    <Col md="6">
                                                        <Label> <strong>1.</strong>कृपया अधिकारी द्वारा भरे गये भाग-दो पर विशेष रूप से लक्ष्यों और उद्देश्यों, उपलब्धियों, कर्मियों से संबंधित उत्तरों से सहमति संबंधी टीप दें, यदि किसी उद्देश्यों की पूर्ति में कोई बाधा न हो तो इनका भी उल्लेख करें ।
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="sahmatiSambandhiTeep"
                                                            value={employee.employee?.levelDetails[0].data.sahmatiSambandhiTeep} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.sahmatiSambandhiTeep && (
                                                            <small className="text-danger">
                                                                {errors.sahmatiSambandhiTeep}
                                                            </small>
                                                        )}
                                                    </Col>
                                                    <Col md="6">
                                                        <Label> <strong>2 . किये गये कार्य की गुणवत्ता -
                                                        </strong> <br /> कृपया अधिकारी द्वारा किये गये
                                                            कार्य की गुणवत्ता, स्तर और
                                                            कार्यक्रम का उद्देश्य और बाधाएं
                                                            यदि कोई हो, के संबंध में टीप दें !</Label>
                                                        <Input
                                                            type="textarea"
                                                            name="karyaKiGunwatta"
                                                            value={employee.employee?.levelDetails[0].data.karyaKiGunwatta} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.karyaKiGunwatta && (
                                                            <small className="text-danger">
                                                                {errors.karyaKiGunwatta}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-3">
                                                    <Col md="6">
                                                        <Label> <strong>3 .कार्य क्षेत्र का नाम- </strong> <br />कृपया विशिष्ट रूप से इनमें
                                                            से प्रत्येक पर टीप दें, कार्यों के
                                                            ज्ञान कांस्तर संबंधित अनुदेश और
                                                            उनका लागू किया जाना ।</Label>
                                                        <Input
                                                            type="textarea"
                                                            name="karyaChhetraKaNaam"
                                                            value={employee.employee?.levelDetails[0].data.karyaChhetraKaNaam} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.karyaChhetraKaNaam && (
                                                            <small className="text-danger">
                                                                {errors.karyaChhetraKaNaam}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-4">
                                                    <Col>
                                                        <h3><strong>(ब) विशेष -</strong></h3>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-4">
                                                    <Col md="6">
                                                        <Label> <strong>1.  कार्य के प्रति दृष्टिकोण
                                                        </strong> <br /> अधिकारी द्वारा किस हद तक
                                                            कार्य समर्पण, प्रेरणा, उसकी
                                                            इच्छा और पहल कर व्यवस्थित
                                                            रूप से किया गया पर टीप दें,
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="karyaKePratiDrishtikon"
                                                            value={employee.employee?.levelDetails[0].data.karyaKePratiDrishtikon} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.karyaKePratiDrishtikon && (
                                                            <small className="text-danger">
                                                                {errors.karyaKePratiDrishtikon}
                                                            </small>
                                                        )}
                                                    </Col>
                                                    <Col md="6">
                                                        <Label> <strong> 2.निर्णय लेने की योग्यता  <br />
                                                        </strong>निर्णय लेने के गुण,
                                                            पक्ष-विपक्ष को देखते हुए <br />
                                                            वैकल्पिक योग्यता पर टीप दें-
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="nirnayLeneKiYogyta"
                                                            value={employee.employee?.levelDetails[0].data.nirnayLeneKiYogyta} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.nirnayLeneKiYogyta && (
                                                            <small className="text-danger">
                                                                {errors.nirnayLeneKiYogyta}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-3">
                                                    <Col md="6">
                                                        <Label> <strong>3  पहल <br />
                                                        </strong>अधिकारी की अप्रत्यक्ष परिस्थितयों
                                                            से निपटने की क्षमता और
                                                            उपाय और कार्य के नवीन
                                                            क्षेत्रों में स्वेच्छा से अतिरिक्त
                                                            उत्तरदायित्व लेने के संबंध में टीप.
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="pahal"
                                                            value={employee.employee?.levelDetails[0].data.pahal} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.pahal && (
                                                            <small className="text-danger">
                                                                {errors.pahal}
                                                            </small>
                                                        )}
                                                    </Col>
                                                    <Col md="6">
                                                        <Label> <strong>4. प्रोत्साहन और प्रेरणा की योग्यता <br />
                                                        </strong>कृपया अधिकारी की प्रेरणा देने,
                                                            स्वयं के आचरण और विश्वास से
                                                            सहयोग प्राप्त करने की क्षमता पर टीप दें.

                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="prenaKiYogyta"
                                                            value={employee.employee?.levelDetails[0].data.prenaKiYogyta} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.prenaKiYogyta && (
                                                            <small className="text-danger">
                                                                {errors.prenaKiYogyta}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-3">
                                                    <Col md="6">
                                                        <Label> <strong>5. संसूचना कौशल (लिखित और मौखिक)
                                                        </strong><br /> अधिकारी संसुचना एवं तर्क प्रस्तुत .
                                                            करने की योग्यता के संबंध में टीप.
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="sansuchnaKaushal"
                                                            value={employee.employee?.levelDetails[0].data.sansuchnaKaushal} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.sansuchnaKaushal && (
                                                            <small className="text-danger">
                                                                {errors.sansuchnaKaushal}
                                                            </small>
                                                        )}
                                                    </Col>

                                                    <Col md="6">
                                                        <Label> <strong>6. व्यक्तिगत संबंध एवं समूह कार्य (टीम वर्क)
                                                        </strong> <br /> उच्च अधिकारियों, सहयोगियों एवं
                                                            अघनस्थों से संबंध दूसरों के विचारों
                                                            का सराहना एवं सदभावना से ली
                                                            गयी सलाह की योग्यता का उल्लेख
                                                            करें, कृपया टीम के सदस्य के रूप
                                                            में कार्यक्षमता और टीम भावना को
                                                            बढ़ाने और टीम द्वारा किये गये
                                                            कार्य की उत्तरमता पर भी टीप दें ।
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="vyaktiGatSambandh"
                                                            value={employee.employee?.levelDetails[0].data.vyaktiGatSambandh} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.vyaktiGatSambandh && (
                                                            <small className="text-danger">
                                                                {errors.vyaktiGatSambandh}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-1">
                                                    <Col md="6">
                                                        <Label> <strong>7. आम जनता के साथ संबंध <br />
                                                        </strong> अधिकारी की आम जनता
                                                            तक पहुंच और उनका
                                                            आवश्यकताओं के प्रति संवेदनशीलता.

                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="aamJantaSeSambandh"
                                                            value={employee.employee?.levelDetails[0].data.aamJantaSeSambandh} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.aamJantaSeSambandh && (
                                                            <small className="text-danger">
                                                                {errors.aamJantaSeSambandh}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-4">
                                                    <Col>
                                                        <h3><strong>(स)अतिरिक्त गुण (विशेषताएँ) -</strong></h3>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-3">
                                                    <Col md="6">
                                                        <Label> <strong>1. योजना बनाने की योग्यता <br />
                                                        </strong>क्या अधिकारी में समस्याओं,
                                                            कार्य की आवश्यकताओं का पूर्व
                                                            अनुमान लगाकर तदनुसार योजना बनाना
                                                            और संभावित व्यय उपलब्ध कराने की - योग्यता है ।
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="yojnaBananeYogyta"
                                                            value={employee.employee?.levelDetails[0].data.yojnaBananeYogyta} // Use employee data here
                                                            className="form-control"

                                                            readOnly

                                                        />
                                                        {errors.yojnaBananeYogyta && (
                                                            <small className="text-danger">
                                                                {errors.yojnaBananeYogyta}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Col>
                                                        <Label><strong>2.</strong> निरीक्षण की योग्यता
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 ml-4">
                                                    <Col md="3">
                                                        <Label>
                                                            <Input
                                                                type="checkbox"
                                                                name="samuchitBatwara"
                                                                value={employee.employee?.levelDetails[0].data.samuchitBatwara}
                                                                disabled
                                                                readOnly
                                                                checked={employee.employee?.levelDetails[0].data.samuchitBatwara === true}

                                                            />
                                                            <strong>1.</strong> कार्य की समूचित बंटवारा
                                                        </Label>
                                                    </Col>
                                                    <Col md="4">
                                                        <Label>
                                                            <Input
                                                                type="checkbox"
                                                                name="kamiyoKaChunav"
                                                                value={employee.employee?.levelDetails[0].data.kamiyoKaChunav}
                                                                disabled
                                                                checked={employee.employee?.levelDetails[0].data.kamiyoKaChunav === true}

                                                            />
                                                            <strong>2.</strong> कार्य करवाने के लिये उचित कर्मियों का चुनाव
                                                        </Label>
                                                    </Col>
                                                    <Col md="3">
                                                        <Label>
                                                            <Input
                                                                type="checkbox"
                                                                name="margDarshan"
                                                                value={employee.employee?.levelDetails[0].data.margDarshan}
                                                                checked={employee.employee?.levelDetails[0].data.margDarshan === true}
                                                                disabled

                                                            />
                                                            <strong>3.</strong> कार्य करने में मार्गदर्शन
                                                        </Label>
                                                    </Col>
                                                    <Col md="2">
                                                        <Label>
                                                            <Input
                                                                type="checkbox"
                                                                name="karyaKiSamiksha"
                                                                disabled
                                                                value={employee.employee?.levelDetails[0].data.karyaKiSamiksha}
                                                                checked={employee.employee?.levelDetails[0].data.karyaKiSamiksha === true}

                                                            />
                                                            <strong>4.</strong> कार्य की समीक्षा
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <hr />
                                                <Row className="pl-2 pr-4">
                                                    <Col>
                                                        <h3><i> <u>भाग-चार (सामान्य)</u></i></h3>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-2">
                                                    <Col md="6">
                                                        <Label> <strong></strong>निष्ठा
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="nishtha"
                                                            value={employee.employee?.levelDetails[0].data.nishtha} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.nishtha && (
                                                            <small className="text-danger">
                                                                {errors.nishtha}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-2">
                                                    <Col md="3">
                                                        <Label> <strong></strong>श्रेणी
                                                        </Label>
                                                        <Input
                                                            type="select"
                                                            id="ratingSelect"
                                                            name="shreni"
                                                            value={employee.employee?.levelDetails[0].data.shreni}
                                                            className="form-control"
                                                            readOnly


                                                            required
                                                        >
                                                            <option value="">विकल्प चुने </option>
                                                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                            <option value="अच्छा">अच्छा</option>
                                                            <option value="औसत">औसत</option>
                                                            <option value="औसत से कम">औसत से कम</option>

                                                        </Input>
                                                        {errors.shreni && (
                                                            <small className="text-danger">
                                                                {errors.shreni}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-3 mt-2">
                                                    <Col md="12" >
                                                        <Label><strong>
                                                        </strong>उत्कृष्ट श्रेणीकरण तक न
                                                            किया जाये जब तक कि
                                                            अपवाद स्वरूप गुण और
                                                            कार्य संपादन न देखा गया हो,
                                                            ऐसी श्रेणी का आधार भी
                                                            स्पष्ट बताया जाना चाहिए.
                                                        </Label>
                                                        <Input
                                                            type="textarea"
                                                            name="remark"
                                                            maxLength={500}
                                                            style={{ height: "100px" }}
                                                            value={employee.employee?.levelDetails[0].data.remark} // Use employee data here
                                                            className="form-control"
                                                            readOnly

                                                        />
                                                        {errors.remark && (
                                                            <small className="text-danger">
                                                                {errors.remark}
                                                            </small>
                                                        )}
                                                    </Col>


                                                </Row>

                                            </Col>
                                        </Row>

                                    </>}
                                    <hr />

                                    <Row>
                                        <Col>
                                            {employee.employee.levelIndex >= 1 && employee.employee.preDefinedLevels[1].status === true && <>
                                                <Row className="mb-3">
                                                    <Col>
                                                        <h2 style={{ textAlign: "center" }}>
                                                            समीक्षक अधिकारी की टिप्पणी
                                                        </h2>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="3">
                                                        <Label>श्रेणीकरण</Label>
                                                        <Input
                                                            name="shreniKaran1"
                                                            type="select"
                                                            id="recordsPerPage"
                                                            value={employee.employee?.levelDetails[1].data.shreniKaran1}
                                                            readOnly

                                                        >
                                                            <option value="">चुनें</option>
                                                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                            <option value="अच्छा">अच्छा</option>
                                                            <option value="साधारण">साधारण</option>
                                                            <option value="घटिया">घटिया</option>
                                                        </Input>
                                                        {errors.shreniKaran1 && (
                                                            <small className="text-danger">
                                                                {errors.shreniKaran1}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="12">
                                                        <Label>रिमार्क</Label>
                                                        <Input
                                                            name="remark1"
                                                            style={{ height: "100px" }}

                                                            readOnly
                                                            type="textarea"
                                                            value={employee.employee?.levelDetails[1].data.remark1}

                                                        />
                                                        {errors.remark1 && (
                                                            <small className="text-danger">{errors.remark1}</small>
                                                        )}
                                                    </Col>
                                                </Row>

                                            </>}
                                            {employee.employee.levelIndex >= 2 && employee.employee.preDefinedLevels[2].status === true && <>
                                                <hr />
                                                <Row className="mb-3">
                                                    <Col>
                                                        <h2 style={{ textAlign: "center" }}>
                                                            <u>स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                        </h2>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="3">
                                                        <Label>श्रेणीकरण</Label>
                                                        <Input
                                                            name="shreniKaran2"
                                                            type="select"
                                                            readOnly
                                                            id="recordsPerPage"
                                                            value={employee.employee?.levelDetails[2].data.shreniKaran2}

                                                        >
                                                            <option value="">चुनें</option>
                                                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                            <option value="अच्छा">अच्छा</option>
                                                            <option value="साधारण">साधारण</option>
                                                            <option value="घटिया">घटिया</option>
                                                        </Input>
                                                        {errors.shreniKaran2 && (
                                                            <small className="text-danger">
                                                                {errors.shreniKaran2}
                                                            </small>
                                                        )}
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="12">
                                                        <Label>रिमार्क</Label>
                                                        <Input
                                                            name="remark2"
                                                            style={{height:"100px"}}
                                                            readOnly
                                                            type="textarea"
                                                            value={employee.employee?.levelDetails[2].data.remark2}

                                                        />
                                                        {errors.remark2 && (
                                                            <small className="text-danger">{errors.remark2}</small>
                                                        )}
                                                    </Col>
                                                </Row>
                                            </>}
                                        </Col>
                                    </Row>

                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};


ACRformForRegistrarPart4Final.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRformForRegistrarPart4Final;
