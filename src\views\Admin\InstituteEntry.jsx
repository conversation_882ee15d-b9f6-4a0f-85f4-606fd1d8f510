import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
const defaultErrors = {
  name: "",
  collegeEmail: "",
  contactPerson: "",
  contactNumber: "",
  divison: "",
  district: "",
  aisheCode: "",
  isLead: "",
};
const InstituteEntry = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [errors, setErrors] = useState(defaultErrors);
  const [formData, setFormData] = useState({
    aisheCode: "",
    name: "",
    collegeEmail: "",
    isLead: "0",
    contact<PERSON>erson: "",
    contactNumber: "",
    divison: "",
    district: "",
  });

  const validateFields = () => {
    const newErrors = {};

    Object.keys(formData).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if there are no errors
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email format validation
    return regex.test(email);
  };
  function togglePreviewModal() {
    // Check if form is valid
    if (validateFields()) {
      // Open modal if form is valid
      setPreviewModal(!previewModal);
    } else {
      // Form is invalid, don't show modal

      SwalMessageAlert(
        "Please fill out all fields before submitting.",
        "warning"
      );
    }
  }
  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" });
    if (name === "collegeEmail") {
      if (!validateEmail(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          collegeEmail: "Please enter a valid email address.",
        }));
      }
    }
  };

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const handleDivisionChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      divison: value,
    });
  };

  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({
      ...formData,
      district: value,
    });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
  };

  const submitCollegeData = async (body) => {
    return axios.post(`${endPoint}/api/college/add-college`, body, {
      headers: {
        "Content-Type": "application/json",
        'web-url': window.location.href,
        Authorization: `Bearer ${token}`,
      },
    });
  };
  const [previewModal, setPreviewModal] = useState(false);

  const formatFieldValue = (key, value) => {
    if (key === "collegeType") {
      return value === "1" ? "Government" : "Private";
    }
    if (key === "isLead") {
      return value === "1" ? "Yes" : "No"; // Convert isLead value to Yes/No
    }
    if (key === "regDate") {
      return new Date(value).toLocaleDateString();
    }
    return value || "Not provided";
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    //Validate form fields
    if (!validateFields()) {
      return;
    }

    const body = {
      ...formData,
      divison: String(formData.divison),
      district: String(formData.district),
      isLead: String(formData.isLead),
    };
    // console.log(body);
    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        e.target.disabled = true;
        setTimeout(() => {
          e.target.disabled = false;
        }, 5000);

        const response = await submitCollegeData(body);

        if (response.status === 201) {
          setFormData({
            name: "",
            university: "",
            collegeEmail: "",
            contactPerson: "",
            contactNumber: "",
            divison: "",
            establishYear: "",
            district: "",
            vidhansabha: "",
            aisheCode: "",
            regDate: "",
            collegeType: "",
            isLead: "0",
            collegeUrl: "",
            address: "",
            lat: "",
            long: "",
            area: "",
            isTribal: "",
            degreeTypes: "",
          });

          SwalMessageAlert("Institute Added Successfully", "success");
          setTimeout(() => window.location.reload(), 5000);
        }
      } else {
        SwalMessageAlert("Adding for College failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert("An error occurred. Please try again later.");
      // Uncomment the navigation line after testing
      // navigate("admin/university");
    }
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">ADD INSTITUTE</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-aisheCode"
                          >
                            AISHE Code
                          </label>
                          <Input
                            name="aisheCode"
                            id="input-aisheCode"
                            autoComplete="pope"
                            placeholder="AISHE CODE"
                            type="text"
                            value={formData.aisheCode}
                            onChange={handleInputChange}
                          />
                          {errors.aisheCode && (
                            <p style={{ color: "red" }}>{errors.aisheCode}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            Institute Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="Institute Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                          />
                          {errors.name && (
                            <p style={{ color: "red" }}>{errors.name}</p>
                          )}
                        </FormGroup>
                      </Col>

                      {/* <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-university"
                          >
                            Choose University
                          </label>
                          <Input
                            name="university"
                            id="input-university"
                            placeholder="Choose University"
                            type="select"
                            value={formData.university}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete

                          >
                            <option value="">Select University</option>
                            {university &&
                              university.length > 0 &&
                              university.map((type, index) => (
                                <option key={index} value={type._id}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.university && <p style={{ color: "red" }}>{errors.university}</p>}
                        </FormGroup>
                      </Col> */}

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-collegeEmail"
                          >
                            Institute Email
                          </label>
                          <Input
                            name="collegeEmail"
                            id="input-collegeEmail"
                            placeholder="Institute Email"
                            type="email"
                            autoComplete="pope"
                            value={formData.collegeEmail}
                            onChange={handleInputChange}
                          />
                          {errors.collegeEmail && (
                            <p style={{ color: "red" }}>
                              {errors.collegeEmail}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          {/* <Label className='Label'>Is Lead College</Label> */}
                          <label
                            className="form-control-label"
                            htmlFor="input-universityType"
                          >
                            Is Lead Institute
                          </label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead"
                                value="1"
                                checked={formData.isLead === "1"}
                                onChange={handleInputChange}
                              />
                              <Label check>Yes</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead"
                                value="0"
                                checked={formData.isLead === "0"}
                                onChange={handleInputChange}
                              />
                              <Label check>No</Label>
                            </FormGroup>
                            {errors.isLead && (
                              <p style={{ color: "red" }}>{errors.isLead}</p>
                            )}
                          </div>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactPerson"
                          >
                            Contact Person
                          </label>
                          <Input
                            name="contactPerson"
                            id="input-contactPerson"
                            placeholder="Contact Person"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactPerson}
                            onChange={handleInputChange}
                          />
                          {errors.contactPerson && (
                            <p style={{ color: "red" }}>
                              {errors.contactPerson}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactNumber"
                          >
                            Contact Number
                          </label>
                          <Input
                            name="contactNumber"
                            id="input-contactNumber"
                            placeholder="Contact Number"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactNumber}
                            maxLength={10}
                            onChange={(e) => {
                              const value = e.target.value;

                              // Check if the input is empty
                              if (value === "") {
                                handleInputChange(e);
                                return;
                              }

                              // Regex to match numbers starting with 6, 7, 8, or 9
                              const validStartRegex = /^[6-9]/;

                              if (validStartRegex.test(value)) {
                                // If valid, pass 'true' as the second argument to handleInputChange
                                handleInputChange(e);
                              } else {
                                // Show alert if the input starts with invalid numbers
                                // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                SwalMessageAlert(
                                  " Mobile number must start with digits 6, 7, 8, or 9.",
                                  "warning"
                                );
                              }
                            }}
                          />
                          {errors.contactNumber && (
                            <p style={{ color: "red" }}>
                              {errors.contactNumber}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-division"
                          >
                            Division
                          </label>
                          <Input
                            name="divison"
                            id="input-division"
                            type="select"
                            value={formData.divison}
                            onChange={handleDivisionChange}
                          >
                            <option value="">Select Division</option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option key={index} value={type.divisionCode}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.divison && (
                            <p style={{ color: "red" }}>{errors.divison}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            District
                          </label>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            onChange={handleDistrictChange}
                          >
                            <option value="" disabled>
                              Select Division
                            </option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option key={index} value={type.LGDCode}>
                                  {type.districtNameEng}
                                </option>
                              ))}
                            {/* Add more districts as needed */}
                          </Input>
                          {errors.district && (
                            <p style={{ color: "red" }}>{errors.district}</p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    {/* <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-vidhansabha"
                          >
                            Vidhan Sabha
                          </label>
                          <Input
                            name="vidhansabha"
                            id="input-vidhansabha"
                            type="select"
                            value={formData.vidhansabha}
                            onChange={handleInputChange}
                          >
                            <option value="" disabled>
                              Select Vidhansabha
                            </option>
                            {vidhansabha &&
                              vidhansabha.length > 0 &&
                              vidhansabha.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.ConstituencyNumber}
                                >
                                  {type.ConstituencyName}
                                </option>
                              ))}
                          </Input>
                          {errors.vidhansabha && (
                            <p style={{ color: "red" }}>{errors.vidhansabha}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-establishYear"
                          >
                            Establishment Year
                          </label>
                          <Input
                            name="establishYear"
                            id="input-establishYear"
                            placeholder="Establishment Year"
                            type="number"
                            autoComplete="pope"
                            value={formData.establishYear}
                            onChange={handleInputChange}
                          />
                          {errors.establishYear && (
                            <p style={{ color: "red" }}>
                              {errors.establishYear}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-regDate"
                          >
                            Registration Date
                          </label>
                          <Input
                            name="regDate"
                            id="input-regDate"
                            placeholder="Registration Date"
                            type="date"
                            autoComplete="pope"
                            value={formData.regDate}
                            onChange={handleInputChange}
                          />
                          {errors.regDate && (
                            <p style={{ color: "red" }}>{errors.regDate}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityType"
                          >
                            Institute Type
                          </label>
                          <Input
                            name="collegeType"
                            id="input-collegeType"
                            type="select"
                            value={formData.collegeType}
                            onChange={handleInputChange}
                          >
                            <option value="">Select Institute Type</option>
                            <option value="1">Govt.</option>
                            <option value="2">Private</option>
                          </Input>
                          {errors.collegeType && (
                            <p style={{ color: "red" }}>{errors.collegeType}</p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-isTribal"
                          >
                            Is Tribal Institute
                          </label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isTribal"
                                id="input-tribal-yes"
                                value="true"
                                checked={formData.isTribal === "true"}
                                onChange={handleInputChange}
                              />
                              <Label check>Yes</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isTribal"
                                id="input-tribal-no"
                                value="false"
                                checked={formData.isTribal === "false"}
                                onChange={handleInputChange}
                              />
                              <Label check>No</Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-degreeTypes"
                          >
                            Degree Types
                          </label>
                          <Input
                            type="select"
                            name="degreeTypes"
                            id="input-degreeTypes"
                            value={formData.degreeTypes}
                            onChange={handleInputChange}
                          >
                            <option value="">Select College Type</option>
                            <option value="UG">Under Graduate</option>
                            <option value="PG">Post Graduate</option>
                          </Input>
                          {errors.degreeTypes && (
                            <p style={{ color: "red" }}>{errors.degreeTypes}</p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-lat"
                          >
                            Latitude
                          </label>
                          <Input
                            name="lat"
                            id="input-lat"
                            placeholder="Latitude"
                            type="text"
                            autoComplete="pope"
                            value={formData.lat}
                            onChange={handleInputChange}
                          />
                          {errors.lat && (
                            <p style={{ color: "red" }}>{errors.lat}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-long"
                          >
                            Longitude
                          </label>
                          <Input
                            name="long"
                            id="input-long"
                            placeholder="Longitude"
                            type="text"
                            autoComplete="pope"
                            value={formData.long}
                            onChange={handleInputChange}
                          />
                          {errors.long && (
                            <p style={{ color: "red" }}>{errors.long}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-area"
                          >
                            Area (in meter)
                          </label>
                          <Input
                            name="area"
                            id="input-area"
                            placeholder="Area"
                            type="text"
                            autoComplete="pope"
                            value={formData.area}
                            onChange={handleInputChange}
                          />
                          {errors.area && (
                            <p style={{ color: "red" }}>{errors.area}</p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col xs="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-collegeUrl"
                          >
                            Institute URL
                          </label>
                          <Input
                            name="collegeUrl"
                            id="input-collegeUrl"
                            autoComplete="pope"
                            placeholder="College URL"
                            type="url"
                            value={formData.collegeUrl}
                            onChange={handleInputChange}
                          />
                          {errors.collegeUrl && (
                            <p style={{ color: "red" }}>{errors.collegeUrl}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col xs="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-address"
                          >
                            Address
                          </label>
                          <Input
                            name="address"
                            id="input-address"
                            placeholder="Address"
                            // type="text"
                            as="textarea"
                            value={formData.address}
                            onChange={handleInputChange}
                          />
                          {errors.address && (
                            <p style={{ color: "red" }}>{errors.address}</p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row> */}
                  </div>
                  <Row className="align-items-center">
                    <Col xs="8"></Col>
                    <Col className="text-right" xs="4">
                      <Button color="primary" onClick={togglePreviewModal}>
                        Preview & Submit
                      </Button>
                    </Col>
                  </Row>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* ------- preview modal---------- */}

        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Preview of Institute Entry</h2>
            <h5 className="text-danger mt-2">
              Note: Please check all fields carefully before submitting
            </h5>
          </ModalHeader>
          <ModalBody style={{}}>
            <div>
              {/* Institute Name and University */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">AISHE Code:</label>
                    <input
                      type="text"
                      value={formData.aisheCode}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label
                      className="form-control-label"
                      htmlFor="input-address"
                    >
                      Institute Name :
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Institute Email:
                    </label>
                    <input
                      type="text"
                      value={formData.collegeEmail}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Contact Info and Establishment Year */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Is Lead Institute:
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue("isLead", formData.isLead)}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Person:
                    </label>
                    <input
                      type="text"
                      value={formData.contactPerson}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Number:
                    </label>
                    <input
                      type="text"
                      value={formData.contactNumber}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Registration Date and Institute Type */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">Division:</label>

                    <input
                      type="text"
                      value={
                        division.find(
                          (type) =>
                            String(type.divisionCode) ===
                            String(formData.divison)
                        )?.name || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">District:</label>
                    <input
                      type="text"
                      value={
                        district.find(
                          (type) =>
                            String(type.LGDCode) === String(formData.district)
                        )?.districtNameEng || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Division, District, and Vidhan Sabha */}
              <Row className="mb-4">
              </Row>
            </div>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button
              color="secondary"
              onClick={togglePreviewModal}
              style={{ borderRadius: "5px" }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{
                borderRadius: "5px",
                backgroundColor: "#007bff",
                border: "none",
              }}
            >
              Submit
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default InstituteEntry;
