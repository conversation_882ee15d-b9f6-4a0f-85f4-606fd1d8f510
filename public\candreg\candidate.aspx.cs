﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Security.Policy;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

public partial class Admin_CandMaster : System.Web.UI.Page
{
    MyDbOps dbops = new MyDbOps();
    public string updateurl = string.Empty;
    protected void Page_Load(object sender, EventArgs e)
    {

        if (Session["userID"] == null)
        {
            Response.Redirect("~//candreg_login.aspx");
        }
        else
        {
            if (!IsPostBack)
            {
                string userId = Session["userID"].ToString();
                GetCandidateId();
                //IsReadNirdesh(userId);
                IsFinalCompleted(userId);
                btnredirect.Visible = false;
            }
        }
    }
    //public void IsReadNirdesh(string rmn)
    //{
    //    SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
    //    using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
    //    {
    //        conn.Open();
    //        string qry = @"select is_readnirdesh from tbl_nirdesh where Registration_No = @regno";
    //        SqlCommand cmd = new SqlCommand(qry, conn);
    //        cmd.Parameters.AddWithValue("@regno", rmn);
    //        DataTable dtShops = new DataTable();
    //        using (SqlDataAdapter da = new SqlDataAdapter(cmd))
    //        {
    //            da.Fill(dtShops);
    //        }
    //        if (dtShops.Rows.Count > 0)
    //        {
    //            if (dtShops.Rows[0][0].ToString() == "Y")
    //            {
    //                chknirdesh.Checked = true;
    //                chknirdesh.Enabled = false;
    //            }
    //        }
    //        conn.Close();
    //        dtShops.Dispose();
    //    }
    //}
    public void IsFinalCompleted(string rmn)
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select IsCompleted,IsUpdated from tbl_CandidateMaster where RegistrationNo=@rmn";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.AddWithValue("@rmn", rmn);
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                if (dtShops.Rows[0][0].ToString() == "Y" && dtShops.Rows[0][1].ToString() != "Y")
                {
                    dvMsg.Visible = false;
                    dvfinal.Visible = true;
                    dvupdate.Visible = true;
                }
                else if (dtShops.Rows[0][0].ToString() == "N" && dtShops.Rows[0][1].ToString() != "Y")
                {
                    dvMsg.Visible = true;
                    dvfinal.Visible = false;
                    dvupdate.Visible = true;
                }
                else if(dtShops.Rows[0][0].ToString() == "Y" && dtShops.Rows[0][1].ToString() == "Y")
                {
                    dvMsg.Visible = false;
                    dvfinal.Visible = true;
                    dvupdate.Visible = false;
                }
            }
            //else
            //{
            //    dvregform.Visible = true;
            //    dvfinal.Visible = false;
            //}
            conn.Close();
            dtShops.Dispose();
        }
    }
//    protected void chknirdesh_CheckedChanged(object sender, EventArgs e)
//    {
//        if (chknirdesh.Checked)
//        {
//            SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
//            using (SqlConnection conNew = new SqlConnection(builder.ConnectionString))
//            {
//                conNew.Open();
//                SqlCommand command = conNew.CreateCommand();
//                SqlTransaction transaction;
//                transaction = conNew.BeginTransaction("Higher EducationTransaction");
//                command.Connection = conNew;
//                command.Transaction = transaction;
//                try
//                {
//                    command.CommandText = @"IF NOT EXISTS(select Registration_No from tbl_nirdesh WHERE Registration_No = @regno) BEGIN 
//INSERT INTO tbl_nirdesh (Registration_No,is_readnirdesh,insertdate) VALUES (@regno,@isread,getdate()) END";
//                    command.Parameters.Add("@regno", SqlDbType.VarChar).Value = Session["userID"].ToString();
//                    command.Parameters.Add("@isread", SqlDbType.VarChar).Value = "Y";
//                    command.ExecuteNonQuery();
//                    command.Parameters.Clear();
//                    transaction.Commit();
//                    Response.Redirect("registration.aspx",false);
//                }
//                catch (Exception ex)
//                {
//                    LogClass.InsertDetails(this.Page.ToString(), ex.ToString());
//                    if (transaction != null)
//                    {
//                        transaction.Rollback();
//                        string jv = "<script>alert('Please Try Again');</script>";
//                        ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
//                        return;
//                    }
//                }
//            }
//        }
//    }

    protected void chkupdate_CheckedChanged(object sender, EventArgs e)
    {
        if(chkupdate.Checked)
        {
            btnredirect.Visible = true;
        }
        else
        {
            btnredirect.Visible = false;
        }
    }
    public void GetCandidateId()
    {
        string userID = Session["userID"].ToString();
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select Id from tbl_CandidateMaster where RegistrationNo=@userid";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@userid", SqlDbType.NVarChar).Value = userID.Trim();
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                updateurl = "edit_registration.aspx?Id=" + dtShops.Rows[0][0].ToString();
            }
            conn.Close();
            dtShops.Dispose();
        }
    }

    protected void btnredirect_Click(object sender, EventArgs e)
    {
        string userID = Session["userID"].ToString();
        string id = string.Empty;
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select Id from tbl_CandidateMaster where RegistrationNo=@userid";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@userid", SqlDbType.NVarChar).Value = userID.Trim();
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                id = dtShops.Rows[0][0].ToString();
            }
            conn.Close();
            dtShops.Dispose();
        }
        if (!string.IsNullOrEmpty(id))
        {
            Response.Redirect("edit_registration.aspx?Id=" + id, false);
        }
    }
}