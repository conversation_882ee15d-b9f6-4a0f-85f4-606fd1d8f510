import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Con<PERSON>er,
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON>dal,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
} from "reactstrap";
import { FormControl, Form } from "react-bootstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import { Link } from "react-router-dom";

const SeatExtensionList = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const userId = sessionStorage.getItem("id");
  const userType = sessionStorage.getItem("userType");
  const [college, setCollege] = useState([]);
  const [subjectData, setSubjectData] = useState([]);
  const [seatData, setSeatData] = useState([]);

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };

    fetchSubjectList();
    const getAllData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/seat-extension/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setSeatData(response.data);

        } else {
          SwalMessageAlert("NO Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    getAllData();
  }, [endPoint, token]);

  // console.log(typeof seatData, "Getting Seat Data Type");


  const modifiedData = seatData.flatMap((data) => {
    return data.subjectDetails.map((subjectDetail) => ({
      _id: data._id,
      collegeName: data.collegeName,
      collegeId: data.collegeId,
      sessionYear: data.sessionYear,
      courseList: subjectDetail.courseList,
      subjectList: subjectDetail.subjectList,
      subjectTypes: subjectDetail.subjectTypes,
      applicationNo: subjectDetail.applicationNo,
      CurrentSeatNo: subjectDetail.CurrentSeatNo,
      RequestedSeatNo: subjectDetail.RequestedSeatNo,
      afterExtensionTotalSeatNo: subjectDetail.afterExtensionTotalSeatNo,
      resoucesAvailability: subjectDetail.resoucesAvailability,
      status: data.status,
      createdAt: data.createdAt,
      principal: data.collegeData.contactPerson,
      principalNo: data.collegeData.contactNumber,
    }));
  });

  // Now modifiedData will contain the new array with repeated fields
  // console.log(modifiedData);

  const [courseData, setCourseData] = useState([]);
  useEffect(() => {
    const fetchCourseList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-course`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setCourseData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    fetchCourseList();
  }, [endPoint, token]);


  const columns = [
    {
      name: "College Name / Session Year",
      cell: (seatData) => (
        <div>
          <div className="mb-2">
            {seatData.collegeName}
            <br />
            <b>{seatData.sessionYear}</b>
          </div>
          <div className="mb-2"> <strong>
            Applied Date : </strong>
            {formatDate(seatData.createdAt)}
            <br />
          </div>
        </div>
      ),
      wrap: true,
      width: "200px",
    },

    {
      name: "Seat Details",
      cell: (seatData) => (
        <div>
          <table className="w-full border-collapse border border-gray-300">
            <thead>
              <tr>
                <th className="border border-gray-300 px-2 py-1">
                  संकाय का नाम
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  विषय
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  संचालन का प्रकार
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  आवेदनों की संख्या
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  वर्तमान सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  मांग की गई सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  वृद्धि पश्चात् सीट
                </th>
                <th className="border border-gray-300 px-2 py-1">
                  भौतिक संसाधन
                </th>
              </tr>
            </thead>
            <tbody>
              {seatData &&
                seatData.subjectDetails.map((a) => (
                  <tr key={a._id || a.applicationNo}>
                    <td className="border border-gray-300 px-2 py-1">
                      {
                        a.courseList
                      }
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.subjectList}

                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.subjectTypes}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.applicationNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.CurrentSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.RequestedSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.afterExtensionTotalSeatNo}
                    </td>
                    <td className="border border-gray-300 px-2 py-1">
                      {a.resoucesAvailability ? "Yes" : "No"}
                    </td>
                  </tr>
                ))}
            </tbody>
          </table>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Action",
      cell: (seatData, index) => (
        <div>
          <div className="mb-2">

            
            {seatData.status === true ? (
              <>
                <Button
                  className="btn btn-sm btn-danger m-1"
                  onClick={() => handleUnFinalize(seatData._id)}
                >
                  UnFinalize
                </Button>
              </>
            ) : (
              <>
                <Button className="btn btn-sm btn-warning m-1 ">UnFinalized</Button>
              </>

        
            )}
               <Button className="btn btn-sm btn-info m-3" onClick={() => handlePrintIndividual(seatData._id)}>
                <i className="fa fa-print me-1"></i> Print
              </Button>
          </div>
        </div>
      ),
      wrap: true,
      width: "150px",
    },
  ];
  const handleUnFinalize = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/seat-extension/unfinalize/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert(response.data.msg, "success");
        setTimeout(() => window.location.reload(), 3000);

      } else {
        SwalMessageAlert(response.data.msg, "error");
        setTimeout(() => window.location.reload(), 3000);

      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const [filterText, setFilterText] = useState("");
  const filteredData = seatData.filter((item) => {
    const filterTextLower = filterText.toLowerCase();
    return (
      item.collegeName &&
      item.collegeName.toLowerCase().includes(filterTextLower)
    );
  });

  const setSeatIncrementEnable = async (type) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/seat-subject/enable?type=${type}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const status = response.data.status
        const message =
          type === "seat" ? status !== false ? "सीट वृद्धि प्रारंभ हुआ" : "सीट वृद्धि समाप्त हुआ" : status !== false ? "नवीन विषय प्रारंभ हुआ" : "नवीन विषय समाप्त हुआ";
        SwalMessageAlert(message, "success");
        setTimeout(() => window.location.reload(), 1000);

      } else {
        SwalMessageAlert("Updation Failed", "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const [SeatSubjectEnableData, setSeatSubjectEnableData] = useState([]);
  useEffect(() => {
    const getSeatSubjectEnableData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/seat-subject/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          }
        });
        if (response.status === 200) {
          setSeatSubjectEnableData(response.data);
        } else {
          SwalMessageAlert("No Data Found", "error");
        }
      } catch (error) {
        handleApiError(error);
      }
    }
    getSeatSubjectEnableData();
  }, [endPoint, token]);
  
const handlePrintIndividual = (id) => {
  try {
    const printContent = seatData.find((data) => data._id === id);
    console.log("Print Content", JSON.stringify(seatData, null, 2), printContent);
    if (!printContent) {
      SwalMessageAlert("No data found for this College.", "error");
      return;
    }

    let rowsHTML = '';
    const subjectDetails = printContent.subjectDetails || [];
    const college = printContent.collegeData || [];
    subjectDetails.forEach((subject, index) => {
      rowsHTML += `
  <tr>
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${1}</td>` : ""}
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${printContent.createdAt ? new Date(printContent.createdAt).toLocaleDateString('hi-IN') : 'N/A'}</td>` : ""}
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${printContent.collegeName || 'N/A'}</td>` : ""}
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${subject.sessionYear || printContent.sessionYear || 'N/A'}</td>` : ""}
    
    <td>${subject.courseList || 'N/A'}</td>
    <td>${subject.subjectList || 'N/A'}</td>
    <td>${subject.subjectTypes || 'N/A'}</td>
    <td>${subject.applicationNo || 'N/A'}</td>
    <td>${subject.CurrentSeatNo || 'N/A'}</td>
    <td>${subject.RequestedSeatNo || 'N/A'}</td>
    <td>${subject.afterExtensionTotalSeatNo || 'N/A'}</td>
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${college.contactPerson || 'N/A'}</td>` : ""}
    ${index === 0 ? `<td rowspan="${subjectDetails.length}">${college.contactNumber || 'N/A'}</td>` : ""}
  </tr>
`;
    });

    let printHTML = `
      <html>
        <head>
          <title>${printContent.sessionYear || 'N/A'} Seat Extension ${printContent.collegeName || 'N/A'}</title>
          <style>
            @page {
              size: A4 landscape;
              margin: 20px;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 0;
              font-size: 12px;
            }
            .print-container {
              width: 100%;
              background: #fff;
              padding: 20px;
              box-sizing: border-box;
            }
            .header {
              text-align: center;
              margin-bottom: 20px;
            }
            .header h1 {
              font-size: 16px;
              margin: 5px 0;
              font-weight: bold;
            }
            .header h2 {
              font-size: 14px;
              margin: 5px 0;
              font-weight: normal;
            }
            .college-info {
              font-size: 13px;
              margin-top: 10px;
              margin-bottom: 10px;
            }
            .college-info b {
              font-weight: bold;
            }
            .data-table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 20px;
            }
            .data-table th,
            .data-table td {
              border: 1px solid #000;
              padding: 6px;
              text-align: center;
              vertical-align: middle;
              font-size: 11px;
            }
            .data-table th {
              background-color: #f0f0f0;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="header">
              <h1>शासकीय महाविद्यालयों में संचालित कक्षा/संकाय/विषय के लिए निर्धारित सीट संख्या में सीट वृद्धि हेतु प्राप्त</h1>
              <h2>ऑनलाइन आवेदन पत्र</h2>
            </div>
            <table class="data-table">
              <thead>
                <tr>
                  <th>क्र.</th>
                  <th>आवेदन का दिनांक</th>
                  <th>महाविद्यालय का नाम</th>
                  <th>शैक्षणिक सत्र</th>
                  <th>संकाय का नाम</th>
                  <th>विषय</th>
                  <th>संचालन का प्रकार</th>
                  <th>आवेदनों की संख्या</th>
                  <th>वर्तमान सीट</th>
                  <th>मांगी गई सीट</th>
                  <th>वृद्धि उपरांत सीट</th>
                  <th>प्राचार्य का नाम</th>
                  <th>प्राचार्य मोबाइल</th>
                </tr>
              </thead>
              <tbody>
                ${rowsHTML}
              </tbody>
            </table>
          </div>
        </body>
      </html>
    `;

    const printWindow = window.open('', '_blank');
    printWindow.document.write(printHTML);
    printWindow.document.close();
    printWindow.print();

  } catch (error) {
    SwalMessageAlert(`Print Error: ${error.message}`, "error");
  }
};




  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    const printContent = document.getElementById('print-container').innerHTML;
    const printDocument = printWindow.document;

    printDocument.write(`
        <html>
          <head>
            <title>Print Form</title>
            <style>
              @page { 
                size: A4 portrait; /* Set to A4 landscape */
                margin: 20px; 
              }
              body { font-family: Arial, sans-serif; }
              .print-container {
                width: 21cm; /* Width for landscape */
                height: 29.7cm; /* Height for landscape */
                background: #fff;
              }
              .form-field {
                margin-bottom: 15px;
              }
              .form-field label {
                display: block;
                margin-bottom: 5px;
                font-weight: bold;
              }
              .form-field input {
                width: 100%;
                padding: 8px;
                box-sizing: border-box;
              }
              h1 {
                font-size: 24px;
                margin-bottom: 20px;
              }
              p {
                font-size: 16px;
                margin-bottom: 10px;
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              ${printContent}
            </div>
          </body>
        </html>
      `);
    printDocument.close();
    printWindow.print();
  };

  const exportToExcel = async () => {
    try {
      if (modifiedData.length > 0) {  // Check if data is available
        let tableHTML = `
                  <table border="1">
                    <thead>
                      <tr>
                        <th>क्र.</th>
                        <th>आवेदन का दिनांक </th>
                        <th>महाविद्यालय का नाम </th>
                        <th>शैक्षणिक सत्र</th>
                        <th>संकाय का नाम</th>
                        <th>विषय </th>
                        <th>संचालन का प्रकार</th>
                        <th>गत वर्ष में प्रवेश हेतु प्राप्त आवेदनों की संख्या</th>
                        <th>प्रथम वर्ष / प्रथम सेमेस्टर में निर्धारित सीट संख्या</th>
                        <th> वृद्धि हेतु मांग की गई सीट संख्या</th>
                        <th>सीट वृद्धि उपरांत निर्धारित कुल सीट संख्या</th>
                        <th>प्राचार्य का नाम</th>
                        <th>प्राचार्य का मोबाईल नं.</th>
                      </tr>
                    </thead>
                    <tbody>
                `;

        modifiedData.forEach((detail, index) => {
          tableHTML += `            
                      <tr>
                        <td>${index + 1}</td>
                            <td>${formatDate(detail.createdAt)}</td>
                            <td>${detail.collegeName}</td>
                            <td>${detail.sessionYear}</td>
                            <td>${detail.courseList}</td>
                            <td>${detail.subjectList}</td>
                            <td>${detail.subjectTypes}</td>
                            <td>${detail.applicationNo}</td>
                            <td>${detail.CurrentSeatNo}</td>
                            <td>${detail.RequestedSeatNo}</td>
                            <td>${detail.afterExtensionTotalSeatNo}</td>
                            <td>${detail.principal}</td>
                            <td>${detail.principalNo}</td>
                      </tr>
                    `;
        });

        tableHTML += "</tbody></table>";

        const excelFileContent = `
                  <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
                    <head><!--[if gte mso 9]><xml>
                      <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                          <x:ExcelWorksheet>
                            <x:Name>Employees</x:Name>
                            <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                          </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                      </x:ExcelWorkbook>
                    </xml><![endif]--></head>
                    <body>${tableHTML}</body>
                  </html>
                `;

        const utf8BOM = "\uFEFF";
        const blob = new Blob([utf8BOM + excelFileContent], {
          type: "application/vnd.ms-excel;charset=utf-8;"
        });
        const date = new Date().toLocaleDateString();
        const downloadLink = document.createElement("a");
        downloadLink.href = URL.createObjectURL(blob);
        downloadLink.download = `New_Course_${date}.xls`;
        downloadLink.click();
      } else {
        SwalMessageAlert("No data available for export.", "warning");
      }
    } catch (error) {
      SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
    }
  };


  // console.log("Getting Modified Data", modifiedData);


  return (
    <>
      <Header />

      <Container className="mt--7" fluid>
        <div style={{ display: "none" }} id="print-container" >
          <CardHeader>
            <Row
              style={{
                justifyContent: "center", // Center horizontally
                display: "flex", // Use flexbox for alignment
              }}
            >
              <Col></Col>

              <Col></Col>
            </Row>
            <Row
              style={{
                justifyContent: "center",
                display: "flex",
                marginTop: "10px", // Add some spacing between rows
              }}
            >
              <Col md="12" className="text-center">
                <span
                  style={{
                    fontSize: "18px",
                    fontWeight: "bolder",
                    display: "block",
                    textAlign: "center",
                    marginBottom: "20px"
                  }}
                >
                  शासकीय महाविद्यालयों में संचालित कक्षा/संकाय/विषय के लिए निर्धारित सीट संख्या में सीट वृद्धि हेतु प्राप्त ऑनलाइन आवेदन पत्र
                </span>
              </Col>
            </Row>
          </CardHeader>

          <Row>
            <table className='table-uneven' style={{ width: '99%', borderCollapse: 'collapse' }}>
              <thead>
                <tr
                  style={{
                    border: '1px solid black',
                    fontSize: "12px",
                    textAlign: "center",
                    padding: "10px",
                    verticalAlign: "middle",
                  }}
                >
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    क्र.
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    आवेदन का  दिनांक
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    महाविद्यालय का नाम
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    शैक्षणिक सत्र
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    संकाय का नाम
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    विषय
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    संचालन का प्रकार
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    गत वर्ष में प्रवेश हेतु प्राप्त आवेदनों की संख्या
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    प्रथम वर्ष / प्रथम सेमेस्टर में निर्धारित सीट संख्या
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    वृद्धि हेतु मांग की गई सीट संख्या
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    सीट वृद्धि उपरांत निर्धारित कुल सीट संख्या
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    प्राचार्य का नाम
                  </th>
                  <th style={{ border: '1px solid black', margin: "0" }}>
                    प्राचार्य का मोबाईल नं.
                  </th>
                </tr>
              </thead>

              <tbody>
                {modifiedData && modifiedData.length > 0 ? (
                  modifiedData.map((detail, index) => (
                    <tr style={{
                      border: '1px solid black',
                      fontSize: "12px",
                      textAlign: "center",
                      padding: "10px",
                      verticalAlign: "middle",
                    }} key={index}>

                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {index + 1}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {formatDate(detail.createdAt)}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.collegeName}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.sessionYear}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.courseList}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.subjectList}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.subjectTypes}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.applicationNo}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.CurrentSeatNo}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.RequestedSeatNo}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.afterExtensionTotalSeatNo}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.principal}
                      </td>
                      <td
                        style={{ border: '1px solid black', margin: "0", fontSize: "12px" }}
                      >
                        {detail.principalNo}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr
                    style={{
                      border: '1px solid black',
                      fontSize: "12px",
                      textAlign: "center",
                      padding: "10px",
                      verticalAlign: "middle",
                    }} >
                    <td colSpan="12">निरंक</td>
                  </tr>
                )}
              </tbody>
            </table>
          </Row>
        </div>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <h3 className="mb-0">Seat Extension List</h3>

            {/* Switch Buttons */}
            <div className="ml-3 d-flex align-items-center">
              <div className="d-flex align-items-center pl-2 me-3">
                <Button className="btn-success" style={{ backgroundColor: SeatSubjectEnableData[0]?.status === false ? "green" : "red" }} onClick={() => setSeatIncrementEnable("seat")}>(सीट वृद्धि) {SeatSubjectEnableData[0]?.status === false ? "प्रारंभ करें" : "समाप्त करें"}  </Button>
              </div>


            </div>
            <FormControl
              type="text"
              placeholder="Search College..."
              className="ml-auto"
              value={filterText}
              onChange={(e) => setFilterText(e.target.value)}
              style={{ width: "250px", borderRadius: "30px" }}
            />
            <Col>
              <Button onClick={handlePrint} className="btn-sm btn-warning">Print</Button>
              <Button onClick={exportToExcel} className="btn-sm btn-danger">Excel</Button>

            </Col>

          </CardHeader>

          <CardBody>
            <DataTable
              columns={columns}
              data={filteredData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              striped
              customStyles={{
                header: {
                  style: { backgroundColor: "#f8f9fa", fontWeight: "bold" },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff",
                    borderBottom: "1px solid #ddd",
                  },
                  onHoverStyle: { backgroundColor: "#ffff99" },
                },
              }}
            />
          </CardBody>
        </Card>
      </Container>
    </>
  );
};

export default SeatExtensionList;
