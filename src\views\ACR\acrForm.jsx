import { useEffect, useState } from "react";
import Header from "../../components/Headers/Header.jsx";
import ACRFormClass3 from "./acrFormClass3.jsx";
import axios from "axios";
import ACRFormClass4 from "./class4/acrFormClass4.jsx";
import ACRFormProfessor from "./professor/acrFormProfessor.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import ACRFormUGPG from "./principal/acrFormUGPG.jsx";
import ACRForLibrerian from "./librerian/acrFormLibrerian.jsx";
import ACRformForSportsOfficer from "./sportsOfficer/acrFormSportsOfficer.jsx";
import ACRformForRegistrar from "./Registrar/acrFormRegistrar.jsx";
import ACRForDirector from "./directorateACR/acrFormForDirecotors.jsx";
import { useNavigate } from "react-router-dom";





const ACRForm = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const [employee, setEmployee] = useState(null);


  const navigate = useNavigate();

  useEffect(() => {
    const fetchEmployeeDetails = async () => {
      try {
        const getEmployeeDetails = await axios.get(`${endPoint}/api/employee/all-details/${id}`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (getEmployeeDetails.status === 200) {
          setEmployee(getEmployeeDetails.data[0]);
          // console.log("getting Employee in ACR Form ",getEmployeeDetails.data[0]);


        } else {
          SwalMessageAlert(getEmployeeDetails.data.msg, "error");
        }
      } catch (error) {
        SwalMessageAlert(`${error}. Please try again later.`, "error");
      }
    };
    fetchEmployeeDetails();
  }, []);

  const [acrFiled, setAcrFiled] = useState(false);

  useEffect(() => {
    const fetchACRFiledOrNot = async () => {
      try {
        const existingACR = await axios.get(`${endPoint}/api/acr/check-single-acr/${id}`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (existingACR.status === 200) {
          const filedOrNot = existingACR.data.length > 0;
          setAcrFiled(filedOrNot);
          //console.log("getting Filled or not in  ACR Form ", filedOrNot);

          if(filedOrNot === true){
            SwalMessageAlert("ACR already Filed","success");
            setTimeout(() => {
              navigate('/admin/acr-list');
            }, 3000);
          }


        } else {
          SwalMessageAlert(existingACR.data.msg, "error");
        }
      } catch (error) {
        SwalMessageAlert(`${error}. Please try again later.`, "error");
      }
    };
    fetchACRFiledOrNot();
  }, []);


  return (
    <>
      <Header />
      {acrFiled === false && <>
        {employee && employee.classDetails.className === 'Class 3' ? <><ACRFormClass3 employee={employee} /></> : ""}
        {employee && employee.classDetails.className === 'Class 4' ? <><ACRFormClass4 employee={employee} /></> : ""}
        {employee && (employee.designationDetails.designation === 'Professor' || employee.designationDetails.designation === 'Assistant Professor') ? <><ACRFormProfessor employee={employee} /></> : ""}
        {employee && (employee.designationDetails.designation === 'UG Principal' || employee.designationDetails.designation === 'PG Principal') ? <><ACRFormUGPG employee={employee} /></> : ""}
        {employee && (employee.designationDetails.designation === 'Librarian') ? <><ACRForLibrerian employee={employee} /></> : ""}
        {employee && (employee.designationDetails.designation === 'Sports Officer') ? <><ACRformForSportsOfficer employee={employee} /></> : ""}
        {employee && (employee.designationDetails.designation === 'Registrar') ? <><ACRformForRegistrar employee={employee} /></> : ""}
        {employee && (
          employee.designationDetails.designation === 'Additional Director' ||
          employee.designationDetails.designation === 'Joint Director (F)' ||
          employee.designationDetails.designation === 'Joint Director' ||
          employee.designationDetails.designation === 'Assistant Director (F)' ||
          employee.designationDetails.designation === 'Deputy Director') ? <><ACRForDirector employee={employee} /></> : ""}

      </>}


    </>
  );
};

export default ACRForm;