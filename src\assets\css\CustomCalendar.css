/* Container styles */
.calendar-container {
    text-align: center;
    background-color: #f7f9fc;
    padding: 20px;
  }
  
  .calendar-container h2 {
    font-size: 24px;
    margin-bottom: 20px;
  }
  
  /* Calendar header styles */
  .react-calendar__navigation {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #007bff; /* Blue header */
    color: white;
    padding: 10px;
  }
  
  .react-calendar__navigation button {
    color: white;
  }
  
  /* Weekday labels */
  .react-calendar__month-view__weekdays {
    background-color: #007bff; /* Blue weekdays row */
    color: white;
    padding: 10px 0;
  }
  
  .react-calendar__month-view__weekdays__weekday abbr {
    text-decoration: none;
  }
  
  /* Day tiles */
  .react-calendar__tile {
    padding: 15px;
    background-color: white;
    border-radius: 5px;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    transition: background-color 0.2s ease;
  }
  
  .react-calendar__tile:enabled:hover,
  .react-calendar__tile:enabled:focus {
    background-color: #007bff; /* Blue on hover */
    color: white;
  }
  
  /* Selected day */
  .react-calendar__tile--active {
    background-color: #007bff !important; /* Blue selected day */
    color: white;
  }
  
  /* Remove border from the calendar */
  .react-calendar {
    border: none;
    width: 100%;
    max-width: 400px;
    margin: auto;
  }
  
  /* Navigation buttons */
  .react-calendar__navigation__label {
    font-size: 18px;
  }
  
  .react-calendar__navigation__arrow {
    font-size: 24px;
  }
  