import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>Header,
  CardFooter,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Table,
  Pagination,
  PaginationItem,
  PaginationLink,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
const UpdateVidhansabha = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const { id } = useParams();
  const [formData, setFormData] = useState({
    DistCode: "",
    ConstituencyNumber: "",
    ConstituencyName: "",
    ConstituencyNameHindi: "",
  });
  useEffect(()=>{
    const fetchVidhansabhaData = async () => {
        try {
          const response = await axios.get(`${endPoint}/api/vidhansabha/get-single-vidhansabha/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
          if (response.status === 200) {  
            const data = response.data;// Convert to YYYY-MM-DD
            setFormData({
                ...data,
            });
          } else {
            alert("No District Found. Please try again.");
          }
        } catch (error) {
          console.error("An error occurred while fetching the data:", error);
          alert("An error occurred. Please try again later.");
        }
      };
      // Call the function
      fetchVidhansabhaData();
  }, [id, endPoint, token])

  const [district, setDistrict] = useState([]);
  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          //   // console.log(response.data);

          setDistrict(response.data);
        } else {
          alert("Failed to District  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDistrict();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };
  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();

    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.DistCode,
      formData.ConstituencyNumber,
      formData.ConstituencyName,
      formData.ConstituencyNameHindi,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    try {
      const body = {
        DistCode: String(formData.DistCode),
        ConstituencyNumber: String(formData.ConstituencyNumber),
        ConstituencyName: String(formData.ConstituencyName),
        ConstituencyNameHindi: String(formData.ConstituencyNameHindi),
      };
      const response = await axios.put(
        `${endPoint}/api/vidhansabha/update-vidhansabha/${id}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
          DistCode: "",
          ConstituencyNumber: "",
          ConstituencyName: "",
          ConstituencyNameHindi: "",
        });
        window.location.reload();
      } else {
        alert("Vidhansabha Already Exists.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred while submitting the form:");
    }
  };


  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Edit Vidhansabha</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-DistCode"
                          >
                            Select District
                          </label>
                          <Input
                            name="DistCode"
                            id="input-DistCode"
                            type="select"
                            value={formData.DistCode}
                            onChange={handleInputChange}
                          >
                            <option value="">Select District</option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option key={index} value={type.LGDCode} selected={type.LGDCode === String(formData.DistCode) ? true : false}>
                                  {type.districtNameEng}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyNumber"
                          >
                            Constituency Number
                          </label>
                          <Input
                            name="ConstituencyNumber"
                            id="input-ConstituencyNumber"
                            placeholder="Constituency Number"
                            type="number"
                            value={formData.ConstituencyNumber}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyName"
                          >
                            Constituency Name
                          </label>
                          <Input
                            name="ConstituencyName"
                            id="input-ConstituencyName"
                            placeholder="Constituency Name"
                            type="text"
                            value={formData.ConstituencyName}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-ConstituencyNameHindi"
                          >
                            Constituency Name in Hindi
                          </label>
                          <Input
                            name="ConstituencyNameHindi"
                            id="input-ConstituencyNameHindi"
                            placeholder="Constituency Name in Hindi"
                            type="text"
                            value={formData.ConstituencyNameHindi}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateVidhansabha;
