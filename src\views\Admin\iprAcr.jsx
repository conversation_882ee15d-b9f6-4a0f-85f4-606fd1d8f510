import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  Container,
  Col,
  Row,
  Table,
  Modal,
  <PERSON><PERSON><PERSON>eader,
  <PERSON>dal<PERSON>ody,
  Card<PERSON>ody,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>ner,
  <PERSON>ge,
  FormGroup,
  Label,
  Input,
} from "reactstrap";
import { FormControl } from "react-bootstrap";
import Select from "react-select";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import { Link } from "react-router-dom";
import NavImage from "../../assets/img/theme/user-icon.png";
const IPRACR = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const userId = sessionStorage.getItem("id");
  const userType = sessionStorage.getItem("userType");
  const [empCode, setEmpCode] = useState(null);
  const [filterText, setFilterText] = useState("");

  const [collegeInput, setCollegeInput] = useState("");
  const [employee, setEmployee] = useState([]);
  //   const handleInputChange = (event) => {
  //     const value = event.target.value;
  //     setEmpCode(value);
  //     checkEmployeeCode(value);
  //   };
  //   const checkEmployeeCode = async (code) => {
  //     try {
  //       const response = await axios.get(
  //         `${endPoint}/api/employee/get-single-employee?empCode=${code}`,
  //         {
  //           headers: {
  //             "Content-Type": "application/json",
  //             Authorization: `Bearer ${token}`,
  //           },
  //         }
  //       );

  //       if (response.status === 200) {
  //         const data = response.data;
  //         setEmployee(data);
  //         // if (data.msg === "Employee Not Found") {
  //         //     setEmployee(false);
  //         // } else {
  //         //     setEmployee(true);
  //         // }
  //       } else {
  //         alert("Failed to fetch Employee data. Please try again.");
  //       }
  //     } catch (error) {
  //       console.error("An error occurred while fetching the data:", error);
  //       alert("An error occurred. Please try again later.");
  //     }
  //   };
  const [college, setCollege] = useState([]);
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
  }, [endPoint, token]);
  const collegeOptions =
    (college &&
      college.length > 0 &&
      college?.map((type) => ({
        value: type._id,
        label: `${type.name} (${type.aisheCode})`,
      }))) ||
    [];
  const [collegeID, setCollegeID] = useState(null);
  const handleCollegeChange = async (e) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/employee/college-wise/${e}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setEmployee(response.data.getAllEmployeeCollegeWise);
      } else {
        alert("Login failed. Please check your credentials and try again.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const [iprLastFiveYearDetails, setIPRLastFiveYearDetails] = useState([]);
  const [acrLastFiveYearDetails, setACRLastFiveYearDetails] = useState([]);
  useEffect(() => {
    const getIPRLastFiveYearDetails = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/ipr-details`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setIPRLastFiveYearDetails(data);
        }
      } catch (error) {
        SwalMessageAlert(error.message, "error");
      }
    };
    const getACRLastFiveYearDetails = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/acr-details`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setACRLastFiveYearDetails(data);
        }
      } catch (error) {
        SwalMessageAlert(error.message, "error");
      }
    };
    getIPRLastFiveYearDetails();
    getACRLastFiveYearDetails();
  }, [endPoint]);
  const columns = [
    {
      name: "Action",
      cell: (employee) => {
        const isIPRPresent = iprLastFiveYearDetails.some(
          (detail) => detail.employeeId === employee._id
        );
        const isACRPresent = acrLastFiveYearDetails.some(
          (detail) => detail.employeeId === employee._id
        );
        return (
          <>
            {isIPRPresent ? (
              <span
                className="btn btn-success btn-sm"
                onClick={() => toggleModal(employee._id)}
              >
                IPR Found
              </span>
            ) : (
              <span
                className="btn btn-primary btn-sm"
                onClick={() => toggleModal(employee._id)}
              >
                Add IPR
              </span>
            )}
            {isACRPresent ? (
              <span
                className="btn btn-success btn-sm"
                onClick={() => toggleACRModal(employee._id)}
              >
                ACR Found
              </span>
            ) : (
              <span
                className="btn btn-warning btn-sm"
                onClick={() => toggleACRModal(employee._id)}
              >
                Add ACR
              </span>
            )}
          </>
        );
      },
    },
    {
      name: "Basic Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {employee.title} {employee.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {employee.email}
          </div>
          <div className="mb-2">
            <strong>Contact: </strong>
            {employee.contact}
          </div>
          <div className="mb-2">
            <strong>Emp Code: </strong>
            <strong className="badge-primary">{employee.empCode}</strong>
          </div>
        </div>
      ),
      sortable: true,
      sortFunction: (rowA, rowB) =>
        parseInt(rowA.empCode) - parseInt(rowB.empCode),
      wrap: true,
    },
    {
      name: "Class Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>College: </strong>
            {employee.collegeDetails["name"]}
          </div>
          <div className="mb-2">
            <strong>Class: </strong>
            {employee.class_details["className"]}
          </div>
          <div className="mb-2">
            <strong>Designation: </strong>
            {employee.designation_details["designation"]}
          </div>
        </div>
      ),
      wrap: true,
    },
  ];
  const filteredData = Array.isArray(employee)
    ? employee.filter((item) => {
        if (item?.activeStatus === true) {
          const filterTextLower = filterText.toLowerCase();

          if (filterTextLower === "verified") {
            return item.verified === true;
          } else if (
            filterTextLower === "not verified" ||
            filterTextLower === "not"
          ) {
            return item.verified === false;
          }

          return (
            (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
            (item.empCode &&
              item.empCode.toLowerCase().includes(filterTextLower)) ||
            (item.contact &&
              item.contact.toString().toLowerCase().includes(filterTextLower))
          );
        }
      })
    : []; // Return an empty array if `employee` is not an array
  const [employeeId, setEmployeeId] = useState(null);
  const [isModalOpen, setImageIsModalOpen] = useState(false);
  const [IprDetailsData, setIprDetailsData] = useState(false);
  const [IprSubmitButton, setIprSubmitButton] = useState(true);
  const toggleModalImage = () => {
    setImageIsModalOpen(!isModalOpen);
  };
  const toggleModal = async (id) => {
    setEmployeeId(id);
    try {
      const response = await axios.get(
        `${endPoint}/api/ipr-single-details?employeeId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setIprDetails(response.data.iprDetails);
        setImageIsModalOpen(!isModalOpen);
        setIprSubmitButton(false);
      } else {
        setImageIsModalOpen(!isModalOpen);
      }
    } catch (error) {
      setImageIsModalOpen(!isModalOpen);
    }
  };
  const [iprDetails, setIprDetails] = useState(
    Array.from({ length: 6 }, (_, i) => ({
      year: 2018 + i,
      value: "", // Stores the selected value (Yes/No)
    }))
  );
  const handleSelectChange = (index, selectedValue) => {
    const updatedDetails = [...iprDetails];
    updatedDetails[index].value = selectedValue;
    setIprDetails(updatedDetails);
  };
  const handleSubmitIPR = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    

    try {
      const body = {
        iprDetails: iprDetails,
        employeeId: employeeId,
      };
      const response = await axios.post(`${endPoint}/api/ipr-details`, body, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        SwalMessageAlert(response.data.msg, "success");
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      SwalMessageAlert(error.message, "error");
    }
    toggleModal(); // Close the modal after submission
  };

  const [ACRModalOpen, setACRModalOpen] = useState(false);
  const [ACRSubmitButton, setACRSubmitButton] = useState(true);
  const toggleACRModals = () => {
    setACRModalOpen(!ACRModalOpen);
  };
  const toggleACRModal = async (id) => {
    setEmployeeId(id);
    try {
      const response = await axios.get(
        `${endPoint}/api/acr-single-details?employeeId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setAcrDetails(response.data.details);
        setACRModalOpen(!ACRModalOpen);
        setACRSubmitButton(false);
      } else {
        setACRModalOpen(!ACRModalOpen);
      }
    } catch (error) {
      setACRModalOpen(!ACRModalOpen);
    }
  };
  const [acrDetails, setAcrDetails] = useState(
    Array.from({ length: 6 }, (_, i) => ({
      year: 2018 + i,
      yesNo: "",
      grade: "",
    }))
  );

  const handleInputChangeACR = (index, field, value) => {
    setAcrDetails((prevDetails) =>
      prevDetails.map((item, i) =>
        i === index ? { ...item, [field]: value } : item
      )
    );
  };

  const handleSubmitACR = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    try {
      const body = {
        details: acrDetails,
        employeeId: employeeId,
      };
      const response = await axios.post(`${endPoint}/api/acr-details`, body, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        SwalMessageAlert(response.data.msg, "success");
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      SwalMessageAlert(error.message, "error");
    }
    toggleACRModal();
  };

  const years = [2018, 2019, 2020, 2021, 2022, 2023];
  const grades = ["क+", "क", "ख", "ग", "घ"];
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <h3 className="mb-0">
              Employee List (College / Employee Code Wise)
            </h3>
          </CardHeader>
          <CardBody>
            <Row>
              <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-className">Select College</Label>
                  <Select
                    options={collegeOptions}
                    onChange={(selectedOption) =>
                      handleCollegeChange(selectedOption?.value || "")
                    }
                    placeholder="Select College"
                  />
                </FormGroup>
              </Col>
              {/* <Col lg="4">
                <FormGroup>
                  <Label htmlFor="input-className">Enter Employee Code</Label>
                  <Input
                    type="text"
                    className="form-control empCode"
                    placeholder="Enter Employee Code"
                    value={empCode}
                    onChange={handleInputChange}
                  />
                </FormGroup>
              </Col> */}
            </Row>
            <Row>
              <FormControl
                type="text"
                placeholder="Search Employee..."
                className="ml-auto"
                value={filterText}
                onChange={(e) => setFilterText(e.target.value)}
                style={{ width: "250px", borderRadius: "30px" }}
              />
              <DataTable
                columns={columns}
                data={filteredData}
                pagination
                paginationPerPage={10}
                highlightOnHover
                striped
                sortable // Enable sorting
                defaultSortField="name" // Sort by the 'name' column initially
                defaultSortAsc={true} // Ascending order
                customStyles={{
                  header: {
                    style: {
                      backgroundColor: "#f8f9fa", // Light background color for header
                      fontWeight: "bold",
                    },
                  },
                  rows: {
                    style: {
                      backgroundColor: "#fff", // Row color
                      borderBottom: "1px solid #ddd",
                    },
                    // Apply hover effect through the :hover pseudo-class directly in custom CSS
                    onHoverStyle: {
                      backgroundColor: "#ffff99", // Hover color
                    },
                  },
                }}
              />
            </Row>
          </CardBody>
        </Card>
        <Modal
          isOpen={isModalOpen}
          toggle={toggleModalImage}
          style={{ textAlign: "center" }}
        >
          <ModalHeader toggle={toggleModalImage}>
            Add Last 5 Year IPR Details
          </ModalHeader>
          <ModalBody>
            {iprDetails.map((item, index) => (
              <div
                key={index}
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "1rem",
                }}
              >
                <input
                  type="text"
                  value={item.year}
                  readOnly
                  style={{ flex: 1, marginRight: "1rem", textAlign: "center" }}
                />
                <select
                  value={item.value}
                  onChange={(e) => handleSelectChange(index, e.target.value)}
                  style={{ flex: 1 }}
                >
                  <option value="">Select</option>
                  <option value="Yes">Yes</option>
                  <option value="No">No</option>
                </select>
              </div>
            ))}
          </ModalBody>
          <ModalFooter>
            <Button
              color="primary"
              onClick={handleSubmitIPR}
              style={{ display: IprSubmitButton ? "" : "none" }}
            >
              Submit
            </Button>
            <Button color="secondary" onClick={toggleModalImage}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
        <Modal
          isOpen={ACRModalOpen}
          toggle={toggleACRModals}
          style={{ textAlign: "center" }}
        >
          <ModalHeader toggle={toggleACRModals}>
            Add Last 5 Year ACR Details
          </ModalHeader>
          <ModalBody>
            <Col sm="12">
              <Row>
                <form>
                  {years.map((year, index) => (
                    <div key={year} className="mb-3">
                      <Col sm="12">
                        <Row>
                          <Col sm="4">
                            {/* Year Dropdown */}
                            <div className="mb-2">
                              <label>Year</label>
                              <input
                                className="form-control"
                                type="text"
                                value={year}
                                readOnly
                                style={{
                                  flex: 1,
                                  marginRight: "1rem",
                                  textAlign: "center",
                                }}
                              />
                              {/* <select
                                className="form-control"
                                value={acrDetails[index]?.year || year}
                                onChange={(e) =>
                                  handleInputChangeACR(
                                    index,
                                    "year",
                                    e.target.value
                                  )
                                }
                              >
                                <option value="">Select Year</option>
                                {years.map((yearOption) => (
                                  <option key={yearOption} value={yearOption}>
                                    {yearOption}
                                  </option>
                                ))}
                              </select> */}
                            </div>
                          </Col>
                          {/* Yes/No Dropdown */}
                          <Col sm="4">
                            <div className="mb-2">
                              <label>Submitted</label>
                              <select
                                className="form-control"
                                value={acrDetails[index]?.yesNo || ""}
                                onChange={(e) =>
                                  handleInputChangeACR(
                                    index,
                                    "yesNo",
                                    e.target.value
                                  )
                                }
                              >
                                <option value="">Select</option>
                                <option value="Yes">Yes</option>
                                <option value="No">No</option>
                              </select>
                            </div>
                          </Col>

                          {/* Grade Dropdown */}
                          <Col sm="4">
                            <div className="mb-2">
                              <label>Remark</label>
                              <select
                                className="form-control"
                                value={acrDetails[index]?.grade || ""}
                                onChange={(e) =>
                                  handleInputChangeACR(
                                    index,
                                    "grade",
                                    e.target.value
                                  )
                                }
                              >
                                <option value="">Select Grade</option>
                                {grades.map((grade) => (
                                  <option key={grade} value={grade}>
                                    {grade}
                                  </option>
                                ))}
                              </select>
                            </div>
                          </Col>
                        </Row>
                      </Col>
                    </div>
                  ))}
                  <Button
                    color="primary"
                    onClick={handleSubmitACR}
                    style={{ display: ACRSubmitButton ? "" : "none" }}
                  >
                    Submit
                  </Button>
                  <Button color="secondary" onClick={toggleACRModals}>
                    Cancel
                  </Button>
                </form>
              </Row>
            </Col>
          </ModalBody>
        </Modal>
      </Container>
    </>
  );
};

export default IPRACR;
