import { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormControl,
  Row,
  Col,
  Button,
} from "react-bootstrap";
import { <PERSON><PERSON>, ModalHeader, ModalBody, Badge } from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
import NavImage from "../../assets/img/theme/user-icon.png";
const EmployeeListOld = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [employee, setEmployee] = useState([]);
  const [filterText, setFilterText] = useState("");

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setEmployee(response.data.getAllEmployeeCollegeWise);
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchCollege();
  }, [endPoint, token]);

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
           {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };

  // Handle Pagination

  const changePassword = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/change-password?id=${id}&key=Employee`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data; // Check the status code to ensure success
        copyToClipboard(data.password);
        SwalMessageAlert("Password Change Successfully", "success");
      } else {
        SwalMessageAlert("Password Change Failed", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const deleteEmployee = async (employee) => {
    const result = await Swal.fire({
      title: `Are you sure you want to delete ${employee.name}?`,
      html: `<strong>Emp Code: ${employee.empCode}</strong>`,
      text: "You won't be able to revert this!",
      imageUrl:
        employee.encodedImage &&
        employee.faceVerified !== false &&
        employee.encodedImage !== ""
          ? `data:image/png;base64,${employee.encodedImage}`
          : NavImage,
      imageWidth: 100,
      imageHeight: 100,
      imageAlt: "Employee Photo",
      // icon: "warning",
      input: "text", // Adds a text input field
      inputPlaceholder: "Enter reason for deletion...",
      inputAttributes: {
        "aria-label": "Reason for deletion",
      },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, keep it",
      preConfirm: (reason) => {
        if (!reason) {
          Swal.showValidationMessage("Reason for delete is required");
        }
        return reason;
      },
    });

    if (result.isConfirmed) {
      const reason = result.value; // Capture the reason entered by the user

      try {
        // Update status to 6 with cancellation reason using PUT method
        const response = await axios.put(
          // `${endPoint}/api/delete-employee?id=${id}&key=Employee`,
          `${endPoint}/api/delete-employee?id=${employee._id}&key=Employee`,
          { reason },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          //const data = response.data;
          SwalMessageAlert("Employee Deleted Successfully", "success");
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          SwalMessageAlert(
            "Delete Employee Failed. Please try again!",
            "error"
          );
        }
      } catch (error) {
        SwalMessageAlert(
          "An error occurred while Deleting Employee. Please try again later!",
          "error"
        );
        console.error("An error occurred while fetching the data:", error);
      }
    }
  };

  const [base64Image, setBase64Image] = useState("");
  const [isImageModalOpen, setImageIsModalOpen] = useState(false);
  function handleConvertAndDisplay(base64) {
    const sampleBase64Image = `data:image/png;base64,${base64}`;
    setBase64Image(sampleBase64Image);
    setImageIsModalOpen(true);
  }
  const toggleImageModal = () => {
    setImageIsModalOpen(!isImageModalOpen);
  };

  function copyTextFallback(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      // console.log("Text copied using fallback!");
    } catch (err) {
      console.error("Fallback copy failed:", err);
    }
    document.body.removeChild(textArea);
  }
  const copyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => console.log("Text copied to clipboard!"))
        .catch((err) => console.error("Failed to copy text:", err));
    } else {
      copyTextFallback(text);
    }
  };

  const columns = [
    {
      name: "Action",
      cell: (employee) => (
        <div>
          <Link to={`/admin/update-employee/${employee._id}`}>
            <button
              className="btn btn-warning mr-1 mt-1 btn-sm"
              title="Edit Employee Details"
            >
              Edit Basic detail
            </button>
          </Link>
          <br />{" "}
          {employee.verified === true ? (
            <span className="btn btn-success  mr-1 mt-1 btn-sm">Verified</span>
          ) : (
            <span className="btn btn-danger btn-sm">Not Verified</span>
          )}
          <br />
          &nbsp; &nbsp;
          <button
            title="Change Password"
            className="btn btn-primary ml--2 mr-1 mt-1 btn-sm"
            onClick={() => changePassword(employee._id)}
          >
            <span className="fa fa-lock"></span>
          </button>
          <button
            title="Delete Employee"
            className="btn btn-danger  mr-1 mt-1 btn-sm"
            onClick={() => deleteEmployee(employee)}
          >
            <span className="fa fa-trash"></span>
          </button>
          <br />
        </div>
      ),
    },
    {
      name: "Profile Details",
      cell: (employee) => (
        <div>
          {(employee.designation_details.designation === "PG Principal" ||
            employee.designation_details.designation === "UG Principal") && (
            <Badge
              style={{
                color: "white",
                backgroundColor: "blue",
                marginTop: "8px",
                fontSize: "12px",
              }}
            >
              {employee.designation_details.designation} profile should be
              update by Pracharya Section
            </Badge>
          )}

          {employee.designation_details.designation != "PG Principal" &&
            employee.designation_details.designation != "UG Principal" && (
              <Link to={`/admin/profile-employee/${employee._id}`}>
                <button
                  title="Profile"
                  className="btn btn-warning  mr-1 mt-1 btn-sm"
                >
                  Edit Profile
                </button>
              </Link>
            )}
          <div className="mb-2">
            {employee.profileDetails &&
            employee.profileDetails.isFinalSubmit === true ? (
              <span className="btn btn-success  mr-1 mt-1 btn-sm">
                Final Submitted
              </span>
            ) : (
              <span className="btn btn-danger  mr-1 mt-1 btn-sm">
                Not Submmited
              </span>
            )}
          </div>
          <div className="mb-2">
            {employee.profileDetails &&
            employee.profileDetails.isVerified === true ? (
              <span className="btn btn-success  mr-1 mt-1 btn-sm">
                Profile Verified
              </span>
            ) : (
              <span className="btn btn-danger  mr-1 mt-1 btn-sm">
                Not Verified
              </span>
            )}
          </div>
          <div className="mb-2">
            <Link to={`/admin/profile-print/${employee._id}`}>
              <Button
                className=" btn btn-sm mr-2"
                style={{ backgroundColor: "orange" }}
              >
                <i className="fas fa-print"></i>
              </Button>
            </Link>
          </div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Photo",
      cell: (employee) => <FetchImageRowData empCode={employee.empCode} />

      // employee.face_attendances.uploadPath &&
      // employee.face_attendances.verified !== false &&
      // employee.face_attendances.uploadPath !== "" ? (
      //   <img
      //     src={`http://localhost:3001/lmsbackend/${employee.face_attendances.uploadPath}`}
      //     //onClick={() => handleConvertAndDisplay(employee.encodedImage)}
      //     style={{ width: "50px" }}
      //   />
      //   // <img
      //   //   src={`data:image/png;base64,${employee.encodedImage}`}
      //   //   onClick={() => handleConvertAndDisplay(employee.encodedImage)}
      //   //   style={{ width: "50px" }}
      //   // />
      // ) : (
      // ),
    },
    {
      name: "Basic Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {employee.title} {employee.name}
          </div>
          <div className="mb-2">
            <strong>Gender: </strong>
            {employee.gender}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {employee.email}
          </div>
          <div className="mb-2">
            <strong>Contact: </strong>
            {employee.contact}
          </div>
          <div className="mb-2">
            <strong>Emp Code: </strong>
            <strong className="badge-primary">{employee.empCode}</strong>
          </div>
        </div>
      ),
      sortable: true,
      sortFunction: (rowA, rowB) =>
        parseInt(rowA.empCode) - parseInt(rowB.empCode),
      wrap: true,
    },
    {
      name: "Class Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>College: </strong>
            {employee.collegeDetails.name}
          </div>
          <div className="mb-2">
            <strong>Class: </strong>
            {employee.class_details["className"]}
          </div>
          <div className="mb-2">
            <strong>Designation: </strong>
            {employee.designation_details["designation"]}
          </div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Division/District/Vidhansabha",
      selector: (row) =>
        `${getDivisionName(row.divison)} / ${row.districtName} / ${
          row.vidhansabhaName
        }`,
    },
  ];

  const FetchImageRowData = ({ empCode }) => {
    const [rowData, setRowData] = useState(null);
    const [loading, setLoading] = useState(true);
  
    useEffect(() => {
      const fetchData = async () => {
        try {
          const response = await axios.get(`${endPoint}/api/employee/fetch-image?empCode=${empCode}`,{
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
          const data = response.data;
          // console.log(data);
          setRowData(data[0]);
        } catch (error) {
          console.error("Error fetching row data:", error);
        } finally {
          setLoading(false);
        }
      };
  
      fetchData();
    }, [empCode]);
  
    if (loading) {
      return <span>Loading...</span>;
    }
  
    return rowData ? (
      <img
        src={`${endPoint}/${rowData.uploadPath}`}
        style={{ width: "50px", cursor: "pointer" }}
        alt="Employee Photo"
      />
    ) : (
      <img
        src={NavImage}
        style={{ width: "50px" }}
        alt="Default Placeholder"
      />
    );
  };
  const sortedData = employee.sort((a, b) => {
    const nameA = a.name.toLowerCase();
    const nameB = b.name.toLowerCase();
    if (nameA < nameB) return -1; // a comes before b
    if (nameA > nameB) return 1; // a comes after b
    return 0; // a and b are equal
  });

  const filteredData = employee.filter((item) => {
    if (item?.activeStatus === true) {
      const filterTextLower = filterText.toLowerCase();

      // Check for "Verified" and "Not Verified"
      if (filterTextLower === "verified") {
        return item.verified === true; // Only include verified employees
      } else if (filterTextLower === "not verified") {
        return item.verified === false; // Only include verified employees
      } else if (filterTextLower === "not") {
        return item.verified === false; // Only include verified employees
      }

      // Default filtering for name, empCode, and contact
      return (
        (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
        (item.empCode &&
          item.empCode.toLowerCase().includes(filterTextLower)) ||
        (item.contact &&
          item.contact.toString().toLowerCase().includes(filterTextLower))
      );
    }
  });
  const exportToExcel = async (e) => {
    e.preventDefault();
    if (employee.length > 0) {
      let tableHTML = `
      <table>
        <thead>
          <tr>
            <th>Sno</th>
            <th>Employee Name</th>
            <th>Employee Code</th>
            <th>Email</th>
            <th>Contact</th>
            <th>Division</th>
            <th>District</th>
            <th>Vidhansabha</th>
            <th>Work Type</th>
            <th>Class</th>
            <th>Designation</th>
            <th>Address</th>
          </tr>
        </thead>
        <tbody>
    `;
      employee
        .sort((a, b) => {
          // Ensure case-insensitive comparison for Division
          const nameA = a.name.toLowerCase();
          const nameB = b.name.toLowerCase();
          if (nameA < nameB) return -1; // a comes before b
          if (nameA > nameB) return 1; // a comes after b
          return 0; // a and b are equal
        })
        .forEach((details, subIndex) => {
          tableHTML += "<tr>";
          tableHTML += `<td>${subIndex + 1}</td>`;
          tableHTML += `<td>${details.name}</td>`;
          tableHTML += `<td>${details.empCode}</td>`;
          tableHTML += `<td>${details.email}</td>`;
          tableHTML += `<td>${details.contact}</td>`;
          tableHTML += `<td>${getDivisionName(details.divison)}</td>`;
          tableHTML += `<td>${details.districtName}</td>`;
          tableHTML += `<td>${details.vidhansabhaName}</td>`;
          tableHTML += `<td>${details.workType}</td>`;
          tableHTML += `<td>${
            details.class_details["className"] || "N/A"
          }</td>`;
          tableHTML += `<td>${
            details.designation_details["designation"] || "N/A"
          }</td>`;
          tableHTML += `<td>${details.address}</td>`;
          tableHTML += "</tr>";
        });
      tableHTML += "</tbody></table>";

      const excelFileContent = `
    <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
      <head><!--[if gte mso 9]><xml>
        <x:ExcelWorkbook>
          <x:ExcelWorksheets>
            <x:ExcelWorksheet>
              <x:Name>Sheet1</x:Name>
              <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
            </x:ExcelWorksheet>
          </x:ExcelWorksheets>
        </x:ExcelWorkbook>
      </xml><![endif]-->
      </head>
      <body>${tableHTML}</body>
    </html>
  `;
      const blob = new Blob([excelFileContent], {
        type: "application/vnd.ms-excel;charset=utf-8;",
      });
      const date = new Date().toLocaleDateString();
      const downloadLink = document.createElement("a");
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = `Employee_Report_${date}.xls`;
      downloadLink.click();
    }
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <Col xs="6">
                  <h3 className="mb-0">Employee List</h3>
                </Col>
                <Col className="text-right" xs="4">
                  <Button
                    color="primary"
                    href="#"
                    size="sm"
                    onClick={exportToExcel}
                  >
                    Generate Report
                  </Button>
                </Col>
                <FormControl
                  type="text"
                  placeholder="Search Employee..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                />
              </CardHeader>
              <CardBody>
                <DataTable
                  columns={columns}
                  data={filteredData}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  sortable // Enable sorting
                  defaultSortField="name" // Sort by the 'name' column initially
                  defaultSortAsc={true} // Ascending order
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa", // Light background color for header
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff", // Row color
                        borderBottom: "1px solid #ddd",
                      },
                      // Apply hover effect through the :hover pseudo-class directly in custom CSS
                      onHoverStyle: {
                        backgroundColor: "#ffff99", // Hover color
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>

          <Modal
            isOpen={isImageModalOpen}
            toggle={toggleImageModal}
            style={{ textAlign: "center" }}
          >
            <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
            <ModalBody>
              <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
            </ModalBody>
          </Modal>
        </Row>
      </Container>
    </>
  );
};

export default EmployeeListOld;
