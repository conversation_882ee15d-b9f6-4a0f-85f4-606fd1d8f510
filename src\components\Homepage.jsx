import { Link } from "react-router-dom";
import "../assets/css/authNavBar.css";
import logo from "../assets/img/brand/CGGov.png";
// reactstrap components
import {
  <PERSON>ton,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Toast,
  Row,
  ToastBody,
  ToastHeader,
  Col,
  Container,
  UncontrolledCollapse,
  NavbarBrand,
  Navbar,
  NavItem,
  NavLink,
  Nav,
  Modal,
  ModalHeader,
  ModalBody,
} from "reactstrap";
import { useState, useEffect } from "react";
import "../assets/css/Login.css";
import { FaUser } from "react-icons/fa";
import axios from "axios";
import SwalMessageAlert from "../utils/sweetAlertMessage";
import Help from "../views/Help";

const Homepage = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const [userType, allUserType] = useState([]);
  const [formData, setFormData] = useState({
    userType: "",
    username: "",
    password: "",
  });

  const [CaptchaText, setCaptchaText] = useState(""); // Captcha input value
  const [generatedCaptcha, setGeneratedCaptcha] = useState(""); // Captcha display text
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState("");
  const [toastType, setToastType] = useState(""); // 'success' or 'error'
  const [captchaImage, setCaptchaImage] = useState(""); // Image data URL

  // State to control whether the sidebar is open or closed
  const [isSidebarOpen, setSidebarOpen] = useState(false);

  // Function to toggle the sidebar open/close
  const toggleSidebar = () => {
    setSidebarOpen(!isSidebarOpen);
  };

  // Generate a random Captcha when component mounts
  useEffect(() => {
    const generateCaptcha = () => {
      const captcha = Math.floor(Math.random() * 900000) + 100000; // Generates a random 6-digit number
      setGeneratedCaptcha(captcha.toString());
    };

    generateCaptcha(); // Call the function to set the initial Captcha

    // Get User Types from the API
    const fetchData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/userType`, {
          headers: { "Content-Type": "application/json" },
        });
        allUserType(response.data.getAllUserType);
      } catch (error) {
        console.error("An error occurred while fetching user types:", error);
      }
    };
    fetchData();
  }, [endPoint]);
  const [placeHolder, setPlaceHolder] = useState("");


  const handleInputChange = (e) => {


    const { name, value } = e.target;
    

    if (name === "userType") {
      switch (value) {
        case "State Admin":
          setPlaceHolder("Enter your Username");
          break;
        case "Employee":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Lead College":
        case "Institute":
          setPlaceHolder("Enter your AISHE Code");
          break;
        case "Director":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Commissioner":
          setPlaceHolder("Enter your Employee Code");
          break;
        default:
          setPlaceHolder("");
      }
    }
    if (name === "Captcha") {
      setCaptchaText(value);
    } else {
      setFormData({ ...formData, [name]: value });
    }       
  };


  const handleInputChange2 = (e) => {

    console.log("Hitting 2nd");
    
    const { name, value } = e.target;
    if (name === "userType") {
      switch (value) {
        case "State Admin":
          setPlaceHolder("Enter your Username");
          break;
        case "Employee":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Lead College":
        case "Institute":
          setPlaceHolder("Enter your AISHE Code");
          break;
        case "Director":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Commissioner":
          setPlaceHolder("Enter your Employee Code");
          break;
        default:
          setPlaceHolder("");
      }
    }
 
      setPasswordFormData({ ...PasswordFormData, [name]: value });
    
    if (name === "mobile") {
      if (PasswordFormData.userType === "") {
        SwalMessageAlert("Please Select User Type!", "error");
      }
    }
    if (name === "otp") {
      setPasswordFormData((prev) => ({ ...prev, [name]: value }));
    }
    if (name === "newPassword") {
      validatePassword(value);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    

    // Basic form validation
    if (!formData.userType) {
      SwalMessageAlert("Please select a user type!", "error");
      return;
    }
    if (!formData.username) {
      SwalMessageAlert("Please enter your User Name!", "error");
      return;
    }
    if (!formData.password) {
      SwalMessageAlert("Please enter your password!", "error");
      return;
    }
    const expectedCaptcha = generatedCaptcha;
    if (CaptchaText !== expectedCaptcha) {
      setToastMessage("Captcha does not match!");
      setToastType("error");
      setToastVisible(true);
      generateCaptchaText(); // Regenerate CAPTCHA on failure
      setCaptchaText("");
      return; // Stop further processing
    }
    try {
      const response = await axios.post(`${endPoint}/api/login`, formData, {
        headers: { "Content-Type": "application/json" },
      });
      if (response.status === 200) {
        const token = response.data.token;
        const id = response.data.id;
        sessionStorage.setItem("id", id);
        sessionStorage.setItem("authToken", token);
        sessionStorage.setItem("name", response.data.username);
        sessionStorage.setItem("userType", response.data.userType);
        sessionStorage.setItem("type", response.data.type);
        sessionStorage.setItem("status", response.data.status);
        sessionStorage.setItem("empId", response.data.empCode);

        // // console.log(response.data.designation, "Getting Designation");
        // // console.log(response.data.designationName);

        sessionStorage.setItem("designation", response.data.designation);
        sessionStorage.setItem("designationName", response.data.designationName);
        
        sessionStorage.setItem('sectionName', response.data.sectionName);
        if (response.data.type === 4) {
          window.location.replace("admin/leave-dashboard");
        } else {
          window.location.replace("admin/dashboard");
        }
        // window.location.reload();
      } else if (response.status === 201) {
        generateCaptchaText();
        SwalMessageAlert(response.data.msg, "error");
      } else {
        generateCaptchaText();
        SwalMessageAlert(response.data.msg, "error");
        window.location.reload();
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      window.location.reload();
    }
  };
  const renderCaptchaImage = (captcha) => {
    const canvas = document.createElement("canvas");
    canvas.width = 150;
    canvas.height = 50;
    const ctx = canvas.getContext("2d");

    const gradient = ctx.createLinearGradient(
      0,
      0,
      canvas.width,
      canvas.height
    );
    gradient.addColorStop(0, "#007BFF");
    gradient.addColorStop(1, "#28A745");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < 5; i++) {
      ctx.beginPath();
      ctx.arc(
        Math.random() * canvas.width,
        Math.random() * canvas.height,
        Math.random() * 10 + 5, // Random size
        0,
        2 * Math.PI
      );
      ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255
        }, 0.3)`;
      ctx.fill();
    }
    ctx.font = "bold 24px Arial, sans-serif";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    const textX = canvas.width / 2;
    const textY = canvas.height / 2;

    captcha.split("").forEach((char, index) => {
      const charX = textX - (captcha.length / 2 - index) * 20 + 10;
      const charRotation = (Math.random() - 0.5) * 0.4;

      ctx.save();
      ctx.translate(charX, textY);
      ctx.rotate(charRotation);
      ctx.fillStyle = "white";
      ctx.fillText(char, 0, 0);
      ctx.restore();
    });
    for (let i = 0; i < 8; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255
        }, 0.4)`;
      ctx.lineWidth = Math.random() * 2 + 1;
      ctx.stroke();
    }
    setCaptchaImage(canvas.toDataURL());
  };
  const generateCaptchaText = () => {
    const chars = "1234567890";
    let captcha = "";
    for (let i = 0; i < 6; i++) {
      captcha += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setGeneratedCaptcha(captcha);
    renderCaptchaImage(captcha);
  };

  useEffect(() => {
    generateCaptchaText(); // Generate CAPTCHA when the component loads
  }, []);

  //Forget Password
  const [PasswordFormData, setPasswordFormData] = useState({
    userType: "",
    username: "",
    otp: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isOtpVisible, setIsOtpVisible] = useState(false);
  const [isOtpInputVisible, setIsOtpInputVisible] = useState(false);
  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);
  const [previewPasswordModal, setPreviewPasswordModal] = useState(false);
  const togglePreviewPasswordModal = () =>
    setPreviewPasswordModal(!previewPasswordModal);
  const handleSubmitOpt = async () => {
    try {
      //alert(PasswordFormData.mobile)
      const response = await axios.get(
        `${endPoint}/api/send-message?username=${PasswordFormData.username}&userType=${PasswordFormData.userType}`,
        {
          headers: { "Content-Type": "application/json" },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        if (data.msg === "OTP sent successfully") {
          SwalMessageAlert(`OTP sent successfully`, "success");
          setIsOtpVisible(true);
          setIsOtpInputVisible(true);
        }
      } else {
        SwalMessageAlert(
          `${response.data.msg}! Please Try After Some Time`,
          "error"
        );
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    }
  };
  // const handleResendOpt = async (e) => {
  //   try {
  //     //alert(PasswordFormData.mobile)
  //     const response = await axios.get(`${endPoint}/api/resend-otp?mobile=${PasswordFormData.mobile}&userType=${PasswordFormData.userType}`, {
  //       headers: { "Content-Type": "application/json" },
  //     });
  //     if (response.status === 200) {

  //     }
  //     // if (response.status === 200) {
  //     //   // console.log(response);
  //     //   // window.location.reload();
  //     // } else if (response.status === 201) {
  //     //   SwalMessageAlert(response.data.msg, "error");
  //     // } else {
  //     //   SwalMessageAlert(response.data.msg, "error");
  //     //   window.location.reload();
  //     // }
  //   } catch (error) {
  //     console.error("An error occurred while submitting the form:", error);
  //     alert("An error occurred. Please try again later.");
  //     //window.location.reload();
  //   }
  // };

  const handleCheckOpt = async () => {
    const body = {
      userType: PasswordFormData.userType,
      username: PasswordFormData.username,
      otp: PasswordFormData.otp,
    };
    try {
      const response = await axios.post(`${endPoint}/api/validate-opt`, body, {
        headers: { "Content-Type": "application/json" },
      });
      if (response.status === 200) {
        //const data = response.data;
        setPreviewPasswordModal(true);
        setPreviewModal(false);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    }
  };



  const handleSubmitPassword = async () => {
    const allValid = Object.values(validationRules).every((rule) => rule);

    if (!allValid) {
      SwalMessageAlert("Password does not meet the requirements.", "error");
      return;
    }

    if (PasswordFormData.newPassword !== PasswordFormData.confirmPassword) {
      SwalMessageAlert("Passwords do not match.", "error");
      return;
    }
    const body = {
      userType: PasswordFormData.userType,
      username: PasswordFormData.username,
      password: PasswordFormData.newPassword,
      confirmPassword: PasswordFormData.confirmPassword,
      otp: PasswordFormData.otp,
    };
    try {
      const response = await axios.put(
        `${endPoint}/api/forget-password`,
        body,
        {
          headers: { "Content-Type": "application/json" },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        SwalMessageAlert(data.msg, "success");
        setPreviewPasswordModal(false);
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      } else {
        SwalMessageAlert(`An error occurred. ${response.data.msg}`, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      // setTimeout(() => {
      //   window.location.reload();
      // }, 5000);
    }
  };

  const [showPassword, setShowPassword] = useState({
    password: false,
    newPassword: false,
    confirmPassword: false,
  });
  const toggleShowPassword = (field) => {
    setShowPassword((prevState) => ({
      ...prevState,
      [field]: !prevState[field],
    }));
  };
  const [validationRules, setValidationRules] = useState({
    minLength: false,
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  const validatePassword = (password) => {
    const rules = {
      minLength: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[@$!%*?&]/.test(password),
    };

    setValidationRules(rules);
  };
  return (
    <>
      <div className="main-content">
        <Navbar
          className="navbar-top navbar-horizontal navbar-dark"
          expand="md"
        >
          <Container fluid className="px-4">
            <NavbarBrand style={{ display: "flex" }} to="/" tag={Link}>
              <img
                alt="..."
                src={logo}
                style={{ height: "60px" }} // Change to your desired height
              />
            </NavbarBrand>
            <h1 className="Title">
              Department of Higher Education <br /> Chhattisgarh
            </h1>
            {/* <Button className="navbar-toggler" id="navbar-collapse-main">
              <span className="navbar-toggler-icon" />
            </Button> */}
            <UncontrolledCollapse navbar toggler="#navbar-collapse-main">
              <div className="navbar-collapse-header d-md-none">
                <Row>
                  <Col className="collapse-brand" xs="6">
                    <Link to="/">
                      <img alt="..." src={logo} />
                    </Link>
                  </Col>
                  <Col className="collapse-close" xs="6">
                    <button
                      className="navbar-toggler"
                      id="navbar-collapse-main"
                    >
                      <span />
                      <span />
                    </button>
                  </Col>
                </Row>
              </div>
              <Nav className="ml-auto" navbar>
                <NavItem>
                  <NavLink className="nav-link-icon" to="/register" tag={Link}>
                    <i className="ni ni-circle-08" />
                    <span className="nav-link-inner--text">Register</span>
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink className="nav-link-icon" to="/" tag={Link}>
                    <i className="ni ni-key-25" />
                    <span className="nav-link-inner--text">Login</span>
                  </NavLink>
                </NavItem>
                {/* Button after login link */}
                <NavItem>
                  {/* Button to toggle the sidebar */}
                  <NavLink onClick={toggleSidebar} className="nav-link-icon">
                    <i className="fas fa-question-circle" />
                    <span className="nav-link-inner--text"> Help</span>
                  </NavLink>
                </NavItem>
                <NavItem>
                  {/* Button to toggle the sidebar */}
                  <NavLink className="nav-link-icon" to="/attendance" tag={Link}>
                    <i className="fas fa-question-circle" />
                    <span className="nav-link-inner--text"> Attendance</span>
                  </NavLink>
                </NavItem>
              </Nav>
            </UncontrolledCollapse>
          </Container>
        </Navbar>

        <div className="header bg-gradient-info py-7 py-lg-8">
          <div className="separator separator-bottom separator-skew zindex-100">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="fill-default"
                points="2560 0 2560 100 0 100"
              />
            </svg>
          </div>
        </div>

        {/* Page content */}
        <Container className="mt--8 pb-5">
          <Row className="justify-content-center">
            <Col lg="5" md="7">
              <Card className="bg-secondary shadow border-0">
                <CardHeader className="bg-transparent pb-5">
                  <div className="text-center mt-2 mb-3">
                    <FaUser
                      style={{
                        height: "80px",
                        width: "60px",
                        color: "#003ca7",
                        marginTop: "10px",
                      }}
                    />
                  </div>
                  <Container>
                    <div className="header-body text-center mb-3">
                      <Row className="justify-content-center">
                        <Col lg="10" md="0">
                          <p className="WelcomeText">WELCOME</p>
                        </Col>
                      </Row>
                    </div>
                  </Container>
                  <div
                    className="text-center text-muted"
                    style={{ marginBottom: "-40px" }}
                  >
                    <small>Sign in with credentials</small>
                  </div>
                </CardHeader>
                <CardBody className="px-lg-5 py-lg-5">
                  <Form role="form" onSubmit={handleSubmit}>
                    <FormGroup className="mb-3">
                      <Input
                        name="userType"
                        type="select"
                        value={formData.userType}
                        style={{ color: "#404446" }}
                        onChange={handleInputChange}
                      >
                        <option value="" disabled>
                          Select User Type
                        </option>
                        {userType &&
                          userType.length > 0 &&
                          userType.map((type, index) => (
                            <option key={index} value={type.UserType}>
                              {type.UserType} ({type.UserTypeHin})
                            </option>
                          ))}
                      </Input>
                    </FormGroup>
                    <FormGroup className="mb-3">
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-single-02" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="username"
                          placeholder="User Name"
                          type="text"
                          style={{ color: "#404446" }}
                          value={formData.username}
                          onChange={handleInputChange}
                          autoComplete="off"
                        />
                      </InputGroup>
                    </FormGroup>
                    <FormGroup>
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-lock-circle-open" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="password"
                          placeholder="Password"
                          type={showPassword.password ? "text" : "password"}
                          value={formData.password}
                          style={{ color: "#404446" }}
                          onChange={handleInputChange}
                        />
                        <InputGroupAddon addonType="append">
                          <InputGroupText
                            style={{ cursor: "pointer" }}
                            onClick={() => toggleShowPassword("password")}
                          >
                            <i
                              className={
                                showPassword.password
                                  ? "fas fa-eye-slash"
                                  : "fas fa-eye"
                              }
                            />
                          </InputGroupText>
                        </InputGroupAddon>
                      </InputGroup>
                    </FormGroup>
                    <Row>
                      <Col>
                        {toastVisible && (
                          <Toast>
                            <ToastHeader
                              toggle={() => setToastVisible(false)}
                            ></ToastHeader>
                            <ToastBody
                              style={{
                                color: toastType === "error" ? "red" : "green",
                                fontSize: "12px",
                              }}
                            >
                              {toastMessage}
                            </ToastBody>
                          </Toast>
                        )}
                      </Col>
                    </Row>
                    <Row>
                      <Col xs="5" className="text-center">
                        {captchaImage && (
                          <img src={captchaImage} alt="CAPTCHA" />
                        )}
                      </Col>
                      <Col xs="2" className="text-center">
                        {/* Refresh Icon */}
                        <button
                          type="button"
                          onClick={generateCaptchaText}
                          style={{
                            background: "none",
                            border: "none",
                            color: "#007bff",
                            fontSize: "1.5rem",
                            cursor: "pointer",
                          }}
                          aria-label="Refresh CAPTCHA"
                          title="Refresh CAPTCHA"
                        >
                          <i className="fas fa-sync-alt" />
                        </button>
                      </Col>
                      <Col xs="5">
                        <FormGroup>
                          <InputGroup className="input-group-alternative">
                            <Input
                              name="Captcha"
                              placeholder="Enter Captcha"
                              type="text"
                              value={CaptchaText}
                              style={{ color: "#404446" }}
                              onChange={handleInputChange}
                              autoComplete="off" // Disable autocomplete
                              autoCorrect="off" // Disable autocorrect
                              spellCheck="false" // Disable spellcheck
                            />
                          </InputGroup>
                        </FormGroup>
                      </Col>
                      <Col>
                        <button
                          type="button"
                          onClick={generateCaptchaText}
                          style={{
                            background: "none",
                            border: "none",
                            color: "blue",
                            cursor: "pointer",
                          }}
                        ></button>
                      </Col>
                    </Row>
                    <div className="text-center">
                      <Button className="my-4" color="primary" type="submit">
                        Sign in
                      </Button>
                    </div>
                  </Form>
                </CardBody>
                <Row className="mt-3">
                  <Col xs="6">
                    <a
                      className="text-light"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                    >
                      <small
                        onClick={togglePreviewModal}
                        style={{ color: "black" }}
                      >
                        Forgot Password?
                      </small>
                    </a>
                  </Col>
                  {/* <Col className="text-right" xs="6">
                    <Link className="text-light" to="/auth/register">
                      <small>Create new account</small>
                    </Link>
                  </Col> */}
                </Row>
              </Card>

              <Modal isOpen={previewModal} toggle={togglePreviewModal}>
                <ModalHeader toggle={togglePreviewModal}>
                  <h2>Forget Password</h2>
                </ModalHeader>
                <ModalBody>
                  <Form role="form">
                    <FormGroup className="mb-3">
                      <Input
                        name="userType"
                        type="select"
                        value={PasswordFormData.userType}
                        style={{ color: "#404446" }}
                        onChange={handleInputChange2}
                        disabled={isOtpInputVisible}
                      >
                        <option value="" disabled>
                          Select User Type
                        </option>
                        {userType &&
                          userType.length > 0 &&
                          userType.map((type, index) => (
                            <option key={index} value={type.UserType}>
                              {type.UserType} ({type.UserTypeHin})
                            </option>
                          ))}
                      </Input>
                    </FormGroup>
                    <FormGroup className="mb-3">
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-single-02" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="username"
                          placeholder={placeHolder}
                          type="text"
                          value={PasswordFormData.mobile}
                          style={{ color: "#404446" }}
                          autoComplete="off"
                          onChange={handleInputChange2}
                          disabled={isOtpInputVisible}
                        />
                      </InputGroup>
                    </FormGroup>
                    <FormGroup
                      className="mb-3"
                      style={{ display: isOtpVisible ? "block" : "none" }}
                    >
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-single-02" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="otp"
                          placeholder="Enter Otp"
                          type="text"
                          value={PasswordFormData.otp}
                          style={{ color: "#404446" }}
                          autoComplete="off"
                          onChange={handleInputChange2}
                        />
                      </InputGroup>
                    </FormGroup>
                    <Row>
                      <div className="text-center">
                        <Button
                          className="btn btn-sm btn-primary"
                          color="primary"
                          type="button"
                          onClick={handleSubmitOpt}
                          style={{ display: isOtpVisible ? "none" : "block" }}
                        >
                          Get OTP
                        </Button>
                        <Button
                          className="btn btn-sm btn-warning"
                          color="primary"
                          type="button"
                          onClick={handleSubmitOpt}
                          style={{ display: isOtpVisible ? "block" : "none" }}
                        >
                          Resend OTP
                        </Button>
                        <Button
                          className="btn btn-sm btn-warning"
                          color="primary"
                          type="button"
                          onClick={handleCheckOpt}
                          style={{ display: isOtpVisible ? "block" : "none" }}
                        >
                          Submit
                        </Button>
                      </div>
                    </Row>
                  </Form>
                </ModalBody>
              </Modal>
              <Modal
                isOpen={previewPasswordModal}
                toggle={togglePreviewPasswordModal}
              >
                <ModalHeader toggle={togglePreviewPasswordModal}>
                  <h2>Change Password</h2>
                </ModalHeader>
                <ModalBody>
                  <Form role="form">
                    <FormGroup className="mb-3">
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-lock-circle-open" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="newPassword"
                          placeholder="New Password"
                          type={showPassword.newPassword ? "text" : "password"}
                          value={PasswordFormData.newPassword}
                          style={{ color: "#404446" }}
                          autoComplete="off"
                          onChange={handleInputChange2}
                        />
                        <InputGroupAddon addonType="append">
                          <InputGroupText
                            style={{ cursor: "pointer" }}
                            onClick={() => toggleShowPassword("newPassword")}
                          >
                            <i
                              className={
                                showPassword.newPassword
                                  ? "fas fa-eye-slash"
                                  : "fas fa-eye"
                              }
                            />
                          </InputGroupText>
                        </InputGroupAddon>
                      </InputGroup>
                      {/* Password Validation Rules */}
                      <ul style={{ listStyle: "none", paddingLeft: 0 }}>
                        <li
                          style={{
                            color: validationRules.minLength ? "green" : "red",
                          }}
                        >
                          {validationRules.minLength ? "✔" : "✖"} Minimum 8
                          characters
                        </li>
                        <li
                          style={{
                            color: validationRules.uppercase ? "green" : "red",
                          }}
                        >
                          {validationRules.uppercase ? "✔" : "✖"} At least 1
                          uppercase letter
                        </li>
                        <li
                          style={{
                            color: validationRules.lowercase ? "green" : "red",
                          }}
                        >
                          {validationRules.lowercase ? "✔" : "✖"} At least 1
                          lowercase letter
                        </li>
                        <li
                          style={{
                            color: validationRules.number ? "green" : "red",
                          }}
                        >
                          {validationRules.number ? "✔" : "✖"} At least 1 number
                        </li>
                        <li
                          style={{
                            color: validationRules.specialChar
                              ? "green"
                              : "red",
                          }}
                        >
                          {validationRules.specialChar ? "✔" : "✖"} At least 1
                          special character
                        </li>
                      </ul>
                    </FormGroup>
                    <FormGroup className="mb-3">
                      <InputGroup className="input-group-alternative">
                        <InputGroupAddon addonType="prepend">
                          <InputGroupText>
                            <i className="ni ni-lock-circle-open" />
                          </InputGroupText>
                        </InputGroupAddon>
                        <Input
                          name="confirmPassword"
                          placeholder="Confirm New Password"
                          type={
                            showPassword.confirmPassword ? "text" : "password"
                          }
                          value={PasswordFormData.confirmPassword}
                          style={{ color: "#404446" }}
                          autoComplete="off"
                          onChange={handleInputChange2}
                        />
                        <InputGroupAddon addonType="append">
                          <InputGroupText
                            style={{ cursor: "pointer" }}
                            onClick={() =>
                              toggleShowPassword("confirmPassword")
                            }
                          >
                            <i
                              className={
                                showPassword.confirmPassword
                                  ? "fas fa-eye-slash"
                                  : "fas fa-eye"
                              }
                            />
                          </InputGroupText>
                        </InputGroupAddon>
                      </InputGroup>
                    </FormGroup>
                    <Row>
                      <div className="text-center">
                        <Button
                          className="btn btn-sm btn-primary"
                          color="primary"
                          type="button"
                          onClick={handleSubmitPassword}
                        >
                          Submit
                        </Button>
                      </div>
                    </Row>
                  </Form>
                </ModalBody>
              </Modal>
            </Col>
          </Row>
        </Container>
      </div>
      <div>
        {/* Include the sidebar and pass the open/close state */}
        <Help isOpen={isSidebarOpen} />
      </div>

      <footer className="py-5">
        <Container>
          <Row className="align-items-center justify-content-xl-between">
            <Col xl="12">
              <div className="copyright text-center text-xl-left text-muted">
                © {new Date().getFullYear()}{" "}
                <a className="font-weight-bold ml-1" href="#" target="_blank">
                  NIC
                </a>
              </div>
            </Col>
            {/* <Col xl="6">
              <Nav className="nav-footer justify-content-center justify-content-xl-end">
                <NavItem>
                  <NavLink
                    href="#"
                    target="_blank"
                  >
                    Creative Tim
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                    href="#"
                    target="_blank"
                  >
                    About Us
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                    href="#"
                    target="_blank"
                  >
                    Blog
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink
                    href="#"
                    target="_blank"
                  >
                    MIT License
                  </NavLink>
                </NavItem>
              </Nav>
            </Col> */}
          </Row>
        </Container>
      </footer>
    </>
  );
};

export default Homepage;
