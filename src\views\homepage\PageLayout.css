/* PageLayout.css */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #f0f1f7;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
  }
  
  .navbar-light .navbar-nav .nav-link {
    color: #eef3ed;
    padding: 0.5rem 1rem;
    font-size:20px;
    
  }
  
  .carousel {
    margin-bottom: 2rem;
  }
  
  .carousel-inner img {
    width: 100%;
    height: auto;
  }
  
  .notices-section {
    background: #912a2a;;
    padding: 2rem;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
  }
  
  .notice-title {
    color: #333;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    font-weight: 600;
  }
  
  .notice-subtitle {
    color: #444;
    margin-bottom: 1rem;
    font-size: 1.25rem;
  }
  
  .notice-list {
    list-style: none;
    padding-left: 0;
  }
  
  .notice-list li {
    margin-bottom: 0.5rem;
  }
  
  .notice-list a {
    color: #0056b3;
    text-decoration: none;
  }
  
  .notice-list a:hover {
    text-decoration: underline;
  }
  
  .file-size {
    color: #e0263959;
    font-size: 0.875rem;
    margin-left: 0.5rem;
  }
  
  
  /* Full-width Container */
.container-fluid {
  width: 100%;
  padding: 0;
}


body {
  margin-bottom: 150px; /* Prevent content overlap with the footer */
  background-color: rgb(250, 250, 248);
}

/* Navbar */
/* .navbar-primary {
  height: 80px;
  padding: 10px 10px;
  display: flex;
  align-items: center;
  background-color: #0056b3;
} */

.navbar-primary .navbar-brand img {
  height: 40px;
  margin-right: 10px;
}

.navbar-primary .navbar-brand div {
  font-size: 16px;
  color: white;
}

.navbar-nav .nav-link.active, .navbar-nav .nav-link.show {
  color: rgb(245, 243, 236) !important;
}

/* Additional styling for the content */
.notices-section {
  padding: 2rem;
  background: #f9f9f9;
}
/* Full-width Container */
.container-fluid {
  width: 100%;
  padding: 0;
}


/* Additional styling for the content */
.notices-section {
  padding: 2rem;
  background: #f9f9f9;
}
/* Footer Styling */
/* .fixed-footer {
  position: fixed;
  bottom: 0;
  width: 100%;
  background-color: #0056b3;
  color: white;
  text-align: center;
  padding: 10px 0;
  z-index: 1000;
} */

/* Scroll to Top Button Styling */
.scroll-top {
  position: fixed;
  bottom: 60px; /* Adjusted to give space above the footer */
  right: 20px;
  background-color: #0056b3;
  color: white;
  border: none;
  border-radius: 50%;
  padding: 10px 15px;
  font-size: 18px;
  cursor: pointer;
  display: none; /* Initially hidden */
  z-index: 999;
}

/* Scroll to Top Button Visible */
.scroll-top.show {
  display: block; /* Display button when scrolled down */
}



/* Ensure the parent container spans the full width */
.row {
  margin: 0 auto;
  padding: 0;
 }
 
 /* Left and Right Images */
 .col-md-4 img {
  object-fit: cover; /* Ensures image fits its container */
  max-width: 100%;
  height: auto;
 }
 
 /* Important Notices Section */
 .notices-section {
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
 }
 
 /* Responsive adjustments */
 @media (max-width: 314px) {
  .col-md-4 {
      margin-bottom: 20px;
  }
 }
 body {
  overflow: auto; /* Allows the body to scroll if content overflows */
}
@media (max-width: 576px) {
  h1 {
      font-size: 1.5rem; /* Adjust heading size for small screens */
  }
  .btn {
      width: 100%; /* Make buttons full width on small screens */
  }
  .notices-section {
      padding: 1rem; /* Adjust padding for notices section */
  }
}
.container {
  max-width: 1200px; /* Example width, adjust as necessary */
  margin: 0 auto; /* Centers the container */
  padding: 20px; /* Adds space around the content */
}

/* Ensure sections have margin and padding */
section {
  margin: 20px 0;
  padding: 10px;
}
 /* Custom styles for the Navbar toggle button */
.navbar-toggler {
  background-color: rgb(44, 36, 36); /* Change the background color to maroon */
  border: none; /* Remove border if you want a clean look */
}

.navbar-toggler-icon {
  background-image: none; /* Remove the default icon */
}

.fixed-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background-color: #0056b3;
  color: white;
  text-align: center;
  padding: 10px 0;
  z-index: 1000;
}
.container, .container-fluid {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  overflow-x: hidden;
}
body::-webkit-scrollbar {
  width: 0; /* Hide scrollbar */
}

* {
  box-sizing: border-box;
  max-width: 100%;
}
.navbar-primary {
  height: 80px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between; /* Ensure spacing between logo and links */
  background-color: #003d82; /* Darker shade for better contrast */
  width: 100%;
  /* position: fixed; */
  top: 0;
  left: 0;
  z-index: 1050; /* Keep navbar on top */
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2); /* Add slight shadow */
}

.navbar-brand img {
  height: 50px;
  margin-right: 10px;
}

.navbar-nav {
  display: flex;
  align-items: center;
  margin-left: auto;
}

.navbar-nav .nav-link {
  color: rgb(255, 255, 255) !important;
  font-size: 18px;
  padding: 10px 15px;
}

.navbar-nav .nav-link:hover {
  color: #c7f1ca !important;
  text-decoration: underline;
}


/* Responsive Navbar */
@media (max-width: 768px) {
  .navbar-nav {
    flex-direction: column;
    background: rgba(68, 58, 58, 0.8);
    width: 100%;
    /* position:absolute; */
    top: 150px;
    left: 0;
  }

  .navbar-nav .nav-link {
    padding: 15px;
    text-align: center;
  }
}



.container {
  max-width: 100%;
  padding: 0;
  margin: 0;
  overflow: hidden !important;

}
html, body {
  overflow-x: hidden !important;
  margin: 0;
  padding: 0;
  width: 100%;
}
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  font-size: 18px;
  border-radius: 50%;
  cursor: pointer;
  display: none; /* Initially hidden */
  z-index: 9999; /* Ensure it’s above other content */
}

.scroll-to-top.show {
  display: block;
}
@media (max-width: 576px) {
  .scroll-to-top {
    bottom: 10px; /* Adjust for mobile devices */
    right: 10px;  /* Adjust for mobile devices */
    font-size: 14px; /* Smaller button for small screens */
  }
}

.scroll-to-top {
  z-index: 9999; /* Ensure it's always on top */
}

.scroll-to-top:hover {
  background-color: darkorange;
  transform: scale(1.1);
}
.content-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.content-text {
  padding: 1rem;
}
/* Main heading styles */
.welcome-heading {
  font-size: 2.5rem;
  font-weight: 700;
  color: #ffc107;
  text-align: center;
  padding: 1rem 0;
  position: relative;
  margin: 1rem 0;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* Decorative underline */
.welcome-heading::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 4px;
  background: linear-gradient(90deg, transparent, #DAD425, transparent);
}

/* Decorative side elements */
.welcome-heading::before {
  content: '✦✦✦';
  color: #DAD425;
  position: absolute;
  left: 50%;
  top: 0%;
  transform: translate(-50%, -50%);
  font-size: 1.2rem;
  opacity: 1.1;
}

/* Container for the entire heading section */
.heading-container {
  position: relative;
  /* padding: 1rem 0; */
  overflow: hidden;
}

/* Subtle background decoration */
.heading-container::before,
.heading-container::after {
  content: '';
  position: absolute;
  width: 100px;
  height: 100px;
  background: linear-gradient(45deg, #ffc107 0%, transparent 100%);
  opacity: 0.1;
  border-radius: 50%;
  z-index: -1;
}


/* Responsive adjustments */
@media (max-width: 768px) {
  .welcome-heading {
    font-size: 2rem;
    padding: 1.5rem 0;
  }
  
  .welcome-heading::after {
    width: 100px;
  }
}

/* Optional animation on scroll */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-heading {
  animation: fadeInUp 0.8s ease-out forwards;
}

/* PageLayout.css */
.page-wrapper {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Navbar Styles */
.navbar-primary {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.brand-text {
  display: flex;
  flex-direction: column;
  color: white;
}

.brand-primary {
  font-size: 1.2rem;
  font-weight: bold;
}

.brand-secondary {
  font-size: 0.9rem;
  font-weight: bold;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Banner Styles */
.banner-carousel {
  margin-top: 60px;
}

.banner-container {
  width: 100%;
  height: 0;
  /* padding-bottom: 40%; */
  position: relative;
  overflow: hidden;
}

.banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Content Styles */
.main-content {
  flex: 1;
  padding-bottom: 2rem;
}

.content-section {
  padding: 2rem 1rem;
}

.heading-container {
  text-align: center;
  margin-bottom: 2rem;
}

.welcome-heading {
  color: #0056b3;
  font-size: 1.4rem;
  font-weight: bold;
}

.content-image-wrapper {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.content-text {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  height: 100%;
}

.notices-section {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  height: 100%;
}

.notice-title {
  color: #0056b3;
  font-size: 1.5rem;
  margin-bottom: 1rem;
  text-align: center;
}

.notice-list {
  list-style: none;
  padding-left: 0;
}

.notice-list li {
  margin-bottom: 0.5rem;
}

.notice-list a {
  color: #0056b3;
  text-decoration: none;
}

.notice-list a:hover {
  text-decoration: underline;
}

/* Scroll to Top Button */


/* Footer Styles */
.site-footer {
  background: #0056b3;
  color: white;
  text-align: center;
  padding: 1rem;
  margin-top: auto;
}

/* Mobile Responsive Styles */
@media (max-width: 767px) {
  .navbar-logo {
      height: 40px;
  }

  .brand-primary {
      font-size: 1rem;
  }

  .brand-secondary {
      font-size: 0.8rem;
  }

  .banner-container {
      padding-bottom: 60%;
  }

  .welcome-heading {
      font-size: 1.5rem;
  }

  .content-section {
      padding: 1rem 0.5rem;
  }

  .notices-section {
      margin-top: 1rem;
  }
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.gradient-custom-2 {
  /* Fallback for old browsers */
  background: #89dafa;

  /* Chrome 10-25, Safari 5.1-6 */
  background: -webkit-linear-gradient(to right, #87CEEB, #00BFFF, #1E90FF, #4fe7fc);

  /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
  background: linear-gradient(to right, #87CEEB, #6ed7fa, #4ca0f5, #68b4f3);

}


.gradient-form {
  min-height: 100vh;
  display: flex;
  align-items: stretch;
}

.carousel-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: transform 0.3s ease-in-out;
}

.carousel-image:hover {
  transform: scale(1.05);
}

.h-100 {
  height: 100%;
}

.flex-grow-1 {
  flex-grow: 1;
}

@media (min-width: 768px) {
  .gradient-form {
    height: 100vh !important;
  }
}
@media (min-width: 769px) {
  .gradient-custom-2 {
    border-top-right-radius: .3rem;
    border-bottom-right-radius: .3rem;
  }
}

.higher-education {
  margin: 20px; 
  padding: 20px; 
  background-color: #f9f9f9; 
  border-radius: 8px; 
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); 
  margin-left:20px;
}

.section-title {
  color: #007bff; 
  text-align: center;
  font-size: 24px; 
  margin-bottom: 20px; 
}

.intro-text, .info-text {
  font-family: 'Arial', sans-serif; 
  line-height: 1.6; 
  font-size: 16px; 
  color: #333; 
  margin-bottom: 20px; 
}

.intro-text {
  font-weight: 500
}

/* .info-text {
  font-style: italic; 
  
} */
/* Ensure container does not overflow */
.banner-container {
  width: 100%;
  max-width: 1200px;
  height: 416px;
  margin: 0 auto;
  overflow: hidden;
  background-color: #003d82;
}
.banner-image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* No cropping, image pura dikhega */
}
@media screen and (max-width: 1200px) {
  .heading-container {
    text-align: center;
    padding: 20px;
  }

  .welcome-heading {
    font-size: 1.8rem;
  }

  .notice-title {
    font-size: 1.5rem;
  }

  .intro-text {
    font-size: 1rem;
  }

  .custom-modal {
    width: 90% !important;
    max-width: 600px;
  }
}

@media screen and (max-width: 768px) {
  .heading-container {
    padding: 15px;
  }

  .welcome-heading {
    font-size: 1.5rem;
  }

  .notice-title {
    font-size: 1.3rem;
  }

  .intro-text {
    font-size: 0.9rem;
  }

  .custom-modal {
    width: 100% !important;
    max-width: 500px;
  }

  .login-container {
    width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .heading-container {
    padding: 10px;
  }

  .welcome-heading {
    font-size: 1.3rem;
  }

  .notice-title {
    font-size: 1.2rem;
  }

  .intro-text {
    font-size: 0.8rem;
  }

  .custom-modal {
    width: 100% !important;
    max-width: 400px;
  }

  .login-container {
    width: 100%;
  }
}

