﻿using System;
using System.Collections.Generic;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Data;
using System.Data.SqlClient;
using AjaxControlToolkit.HTMLEditor.ToolbarButton;
using System.Net;

public partial class candreg_ApplicationPrintOfCandidate : System.Web.UI.Page
{
    MyDbOps dbops = new MyDbOps();
    protected void Page_Load(object sender, EventArgs e)
    {
        if (Session["userID"] == null)
        {
            Response.Redirect("~//candreg_login.aspx");
        }
        else
        {
            checkdata();
            string regno = string.Empty;
            if (Session["userID"] != null)
            {
                regno = Session["userID"].ToString();
            }
            try
            {
                SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
                using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
                {
                    conn.Open();
                    SqlCommand cmd = new SqlCommand("SP_selectCandidateRegistration", conn);
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.Add("@RegistrationNo", SqlDbType.NVarChar).Value = regno;

                    SqlDataReader dr;
                    dr = cmd.ExecuteReader();
                    if (dr.Read())
                    {
                        if (string.IsNullOrWhiteSpace(dr["First_nameH"].ToString()))
                            Response.Redirect("candreg_login.aspx");
                        else
                        {
                            lblRegistrationNo.Text = dr["application_no"].ToString();
                            lblformfilldate.Text = Convert.ToDateTime(dr["Createddate"].ToString()).ToString("dd-MM-yyyy");

                            //LblApplicationNo.Text = Session["ApplicationNo"].ToString();
                            lblCandidateName.Text = dr["First_nameH"].ToString() + " " + dr["Middle_nameH"].ToString() + " " + dr["Last_NameH"].ToString() + "(" + dr["First_nameE"].ToString() + " " + dr["Middle_nameE"].ToString() + " " + dr["Last_NameE"].ToString() + ")";
                            LblDOB.Text = Convert.ToDateTime(dr["DOB"].ToString()).ToString("dd-MM-yyyy");
                            lblAge.Text = dr["age"].ToString();
                            LblMotherName.Text = dr["Mother_name"].ToString() + "(" + dr["Mother_nameE"].ToString() + ")";
                            LblGender.Text = dr["GenderNameHin"].ToString();
                            LblFatherName.Text = dr["Relative_name"].ToString() + "(" + dr["Relative_nameE"].ToString() + ")";
                            LblCast.Text = dr["CasteNameHin"].ToString();

                            string marrigestatus = string.Empty;
                            marrigestatus = dr["maritalstatus"].ToString();

                            if (marrigestatus == "1")
                                lblmarritalstatus.Text = "विवाहित";
                            else if (marrigestatus == "2")
                                lblmarritalstatus.Text = "अविवाहित";

                            string castid = dr["CasteID"].ToString();

                            if (castid == "1")
                            {
                                divcastifstscobc.Visible = false;

                            }
                            else
                            {
                                lblcastesrno.Text = dr["castesrno"].ToString();
                                lblcastdistname.Text = dr["castdistname"].ToString();
                                lblcastename.Text = dr["castename"].ToString();
                                lblcastedate.Text = Convert.ToDateTime(dr["castedate"].ToString()).ToString("dd-MM-yyyy");


                                divcastifstscobc.Visible = true;
                            }

                            LblMobileNo.Text = dr["mobileno"].ToString();
                            lblEmail.Text = dr["email"].ToString();
                            lblpresentAddress.Text = dr["Address"].ToString();
                            lblpresentstate.Text = dr["stateName"].ToString();
                            lbldistpresent.Text = dr["DistName"].ToString();

                            lblPresentPincode.Text = dr["pincode"].ToString();
                            LblPermanentAddress.Text = dr["Per_Address"].ToString();

                            lbldistparmanet.Text = dr["PerDist"].ToString();

                            lblpermanentState.Text = dr["PerState"].ToString();
                            LblpermanentPincode.Text = dr["Per_Pincode"].ToString();
                            //string strphoto = dr["photopath"].ToString().Replace("candreg/", "");
                            string strphoto = string.Empty;
                            if (dr["photopath"].ToString().Contains("candregfile/"))
                            {
                                strphoto = dr["photopath"].ToString().Replace("candregfile/", "");
                            }
                            else if (dr["photopath"].ToString().Contains("candreg/"))
                            {
                                strphoto = dr["photopath"].ToString().Replace("candreg/", "");
                            }
                            else
                            {
                                strphoto = dr["photopath"].ToString();
                            }
                            imgphoto.ImageUrl = strphoto;

                            string strsign = string.Empty;
                            if (dr["Signpath"].ToString().Contains("candregfile/"))
                            {
                                strsign = dr["Signpath"].ToString().Replace("candregfile/", "");
                            }
                            else if (dr["Signpath"].ToString().Contains("candreg/"))
                            {
                                strsign = dr["Signpath"].ToString().Replace("candreg/", "");
                            }
                            else
                            {
                                strsign = dr["Signpath"].ToString();
                            }
                            imgsign.ImageUrl = strsign;

                            string IsGovEmp = dr["IsGovEmp"].ToString();
                            if (IsGovEmp == "Y")
                                lblisGovtservice.Text = "हां";
                            else if (IsGovEmp == "N")
                                lblisGovtservice.Text = "नहीं";

                            string IsServiceEmp = dr["IsServiceEmp"].ToString();
                            if (IsServiceEmp == "Y")
                                lblexserviceman.Text = "हां";
                            else if (IsServiceEmp == "N")
                                lblexserviceman.Text = "नहीं";

                            string IsDisabled = dr["IsDisabled"].ToString();
                            if (IsDisabled == "Y")
                            {
                                lblIsDisabled.Text = "हां";
                                dfndisabletype.Visible = true;
                                lbldisablestype.Text = dr["DisabledType"].ToString();
                            }
                            else if (IsDisabled == "N")
                            {
                                dfndisabletype.Visible = false;
                                lblIsDisabled.Text = "नहीं";
                            }


                            string attendantpost = dr["attendantpost"].ToString();
                            string otherpost = dr["otherpost"].ToString();
                            string appliedpost = string.Empty;
                            if (attendantpost == "Y")
                            {
                                lblAppliedAttendent.Text = "1. प्रयोगशाला परिचारक";
                            }
                            if (otherpost == "Y")
                            {
                                lblotherpost.Text = "2.भृत्य/चौकीदार/स्वीपर";
                                divprathmikta.Visible = true;
                                lblbhrity.Text = dr["servantpriority"].ToString();
                                lblchaukidar.Text = dr["watchmanpriority"].ToString();
                                lblsweepar.Text = dr["sweeperpriority"].ToString();

                            }
                            else if (otherpost == "N")
                            {
                                divprathmikta.Visible = false;
                            }

                            string isIntercastMarrigepromotion = dr["is_intercastemarriagepromotion"].ToString();
                            if (isIntercastMarrigepromotion == "Y")
                                lblinertercast.Text = "हां";
                            else if (isIntercastMarrigepromotion == "N")
                                lblinertercast.Text = "नहीं";

                            string is_govawards = dr["is_govawards"].ToString();
                            if (is_govawards == "Y")
                                lblpuraskar.Text = "हां";
                            else if (is_govawards == "N")
                                lblpuraskar.Text = "नहीं";

                            string is_defenceservices = dr["is_defenceservices"].ToString();
                            if (is_defenceservices == "Y")
                                lblrakshakarmi.Text = "हां";
                            else if (is_defenceservices == "N")
                                lblrakshakarmi.Text = "नहीं";

                            string is_bonafidecand = dr["is_bonafidecand"].ToString();
                            if (is_bonafidecand == "Y")
                                lbl1975viyatname.Text = "हां";
                            else if (is_bonafidecand == "N")
                                lbl1975viyatname.Text = "नहीं";

                            string is_certifiedindian = dr["is_certifiedindian"].ToString();
                            if (is_certifiedindian == "Y")
                                lblis_certifiedindian.Text = "हां";
                            else if (is_certifiedindian == "N")
                                lblis_certifiedindian.Text = "नहीं";

                            string is_sepereated = dr["is_sepereated"].ToString();
                            if (is_sepereated == "Y")
                                lblis_sepereated.Text = "हां";
                            else if (is_sepereated == "N")
                                lblis_sepereated.Text = "नहीं";

                            string is_excluded = dr["is_excluded"].ToString();
                            if (is_excluded == "Y")
                                lblis_excluded.Text = "हां";
                            else if (is_excluded == "N")
                                lblis_excluded.Text = "नहीं";

                            string is_terminated = dr["is_terminated"].ToString();
                            if (is_terminated == "Y")
                                lblis_terminated.Text = "हां";
                            else if (is_terminated == "N")
                                lblis_terminated.Text = "नहीं";

                            string is_guilty = dr["is_guilty"].ToString();
                            if (is_guilty == "Y")
                                lblisaccused.Text = "हां";
                            else if (is_guilty == "N")
                                lblisaccused.Text = "नहीं";

                            lblexamcenterprefrence.Text = dr["distprefrence"].ToString();


                        }
                        dr.Close();
                    }
                }
            }
            catch (Exception ex)
            {
                var ErrorPara = new
                {
                    errorPageAT = "Error in CandidateApplicationPrint.aspx at PageLoad",
                    Errormsg = ex.Message,
                    ExStackTrace = ex.StackTrace,
                    ErrorDateTIme = DateTime.Now.ToString()
                };
            }
        }
    }
    public void checkdata()
    {
        int Id = 0;
        string regno = string.Empty;
        if (Session["userID"] != null)
        {
            regno = Session["userID"].ToString();
        }
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conn = new SqlConnection(builder.ConnectionString))
        {
            conn.Open();
            string qry = @"select Id,IsCompleted from tbl_CandidateMaster where (DistrictCCode is null or DistrictCCode='' or First_NameH is null or 
First_nameH='' or First_nameE is null or First_nameE='' or GenderID is null or GenderID='' or DOB is null or DOB='' or age is null or age=''
or Relative_name is null or Relative_name='' or Relative_nameE is null or Relative_nameE='' or mother_name is null or Mother_name='' or 
Mother_namE is null or Mother_NameE='' or CasteID is null or CasteID='' or Address is null or Address='' or PinCode is null or PinCode='' or 
Per_DistrictCCode is null or Per_DistrictCCode='' or Per_Address is null or Per_Address='' or Per_Pincode is null or Per_Pincode='' or 
Photopath is null or Photopath='' or Signpath is null or Signpath='' or maritalstatus is null or maritalstatus='' or IsDisabled is null or 
IsDisabled='' or dist_preferencecode is null or dist_preferencecode='') and RegistrationNo=@regno";
            SqlCommand cmd = new SqlCommand(qry, conn);
            cmd.Parameters.Add("@regno", SqlDbType.VarChar).Value = regno;
            DataTable dtShops = new DataTable();
            using (SqlDataAdapter da = new SqlDataAdapter(cmd))
            {
                da.Fill(dtShops);
            }
            if (dtShops.Rows.Count > 0)
            {
                if (dtShops.Rows[0]["IsCompleted"].ToString() == "Y")
                {
                    updateiscompleted(int.Parse(dtShops.Rows[0]["Id"].ToString()));
                }
            }
            conn.Close();
            dtShops.Dispose();
        }
    }
    public void updateiscompleted(int Id)
    {
        SqlConnectionStringBuilder builder = new SqlConnectionStringBuilder(dbops.ReturnTRConHigher());
        using (SqlConnection conNew = new SqlConnection(builder.ConnectionString))
        {
            conNew.Open();
            SqlCommand command = conNew.CreateCommand();
            SqlTransaction transaction;
            transaction = conNew.BeginTransaction("Higher EducationTransaction");
            command.Connection = conNew;
            command.Transaction = transaction;
            bool check = false;
            try
            {
                command.CommandText = @"update tbl_CandidateMaster set IsCompleted='N', application_no=NULL where Id=@Id";
                command.Parameters.Add("@Id", SqlDbType.BigInt).Value = Id;
                command.ExecuteNonQuery();
                command.Parameters.Clear();

                transaction.Commit();
                check = true;
            }
            catch (Exception ex)
            {
                LogClass.InsertDetails(this.Page.ToString(), ex.ToString());
                if (transaction != null)
                {
                    transaction.Rollback();
                    string jv = "<script>alert('Please Try Again');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    return;
                }
            }
            finally
            {
                if (check == true)
                {
                    string jv = "<script>alert('कृपया अपने द्वारा भरे गए डेटा को ध्यानपूर्वक जांच लें!');</script>";
                    ScriptManager.RegisterClientScriptBlock(this, typeof(Page), "alert", jv, false);
                    Response.Redirect("edit_registration.aspx?Id=" + Id);
                }
            }
        }
    }

    protected void btnBack_Click(object sender, EventArgs e)
    {
        Response.Redirect("candidate.aspx");
    }
}
