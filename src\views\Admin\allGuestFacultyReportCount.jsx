import { useEffect, useState } from "react";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>er,
    Card<PERSON>ody,
    Container,
    Row,
    Col,
    Button,
} from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const AllGuestFacultyReportCount = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");

    const [data, setData] = useState([]);
    const [colleges, setColleges] = useState([]);

    const [district, setDistrict] = useState([]);
    useEffect(() => {
        const fetchDistrict = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/district/get-all-district`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    //   // console.log(response.data);

                    setDistrict(response.data);
                } else {
                    alert("Failed to District  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDistrict();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token]);


    const [division, setDivision] = useState([]);
    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    const data = response.data;
                    setDivision(data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };

        getDivision();
    }, [endPoint, token]);

    useEffect(() => {
        const fetchAllGuestFaculty = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/getCollegeWiseGuestFacultyCount`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    setData(response.data);
                } else {
                    SwalMessageAlert("Failed to fetch guest faculty data. Please try again.", "warning");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                SwalMessageAlert("An error occurred. Please try again later.", "error");
            }
        };



        const fetchAllColleges = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-all-college`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    setColleges(response.data);
                } else {
                    SwalMessageAlert("Failed to fetch college data. Please try again.", "warning");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                SwalMessageAlert("An error occurred. Please try again later.", "error");
            }
        };

        fetchAllGuestFaculty();
        fetchAllColleges();
    }, [endPoint, token]);

    const exportToExcel = async () => {
        try {
            if (colleges.length > 0) {  // Check if college data is available
                let tableHTML = `
                  <table border="1">
                    <thead>
                      <tr>
                        <th>S.No</th>
                        <th>College Name</th>
                        <th>Registered Faculty</th>
                        <th>Verified Faculty</th>
                      </tr>
                    </thead>
                    <tbody>
                `;

                // Merge colleges and guest faculty data
                const mergedData = colleges.map(col => {
                    const matchedCollege = data.find(d => d.collegeId === col._id);
                    return {
                        collegeId: col._id,
                        collegeName: col.name,
                        guestFacultyCount: matchedCollege ? matchedCollege.guestFacultyCount : 0,
                        verifiedGuestFacultyCount: matchedCollege ? matchedCollege.verifiedGuestFacultyCount : 0,
                    };
                });

                mergedData.forEach((faculty, index) => {
                    tableHTML += `            
                      <tr>
                        <td>${index + 1}</td>
                        <td>${faculty.collegeName}</td>
                        <td>${faculty.guestFacultyCount}</td>
                        <td>${faculty.verifiedGuestFacultyCount}</td>
                      </tr>
                    `;
                });

                tableHTML += "</tbody></table>";

                const excelFileContent = `
                  <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
                    <head><!--[if gte mso 9]><xml>
                      <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                          <x:ExcelWorksheet>
                            <x:Name>Guest Faculty Report</x:Name>
                            <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                          </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                      </x:ExcelWorkbook>
                    </xml><![endif]--></head>
                    <body>${tableHTML}</body>
                  </html>
                `;

                const blob = new Blob([excelFileContent], { type: "application/vnd.ms-excel;charset=utf-8;" });
                const date = new Date().toLocaleDateString();
                const downloadLink = document.createElement("a");
                downloadLink.href = URL.createObjectURL(blob);
                downloadLink.download = `AllGuestFacultyReportCount_${date}.xls`;
                downloadLink.click();
            } else {
                SwalMessageAlert("No data available for export.", "warning");
            }
        } catch (error) {
            SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
        }
    };

    const getDivisionName = (value) => {
        const divisionObj = division.find((div) => div.divisionCode === value);
        return divisionObj ? divisionObj.name : "Unknown Division";
    };


    const getDistrictName = (value) => {
        const districtObj = district.find((div) => div.LGDCode === value);
        return districtObj ? districtObj.districtNameEng : "Unknown Division";
    };

    const columns = [
        {
            name: "S.No",
            selector: (row, index) => index + 1,
            sortable: false,
        },
        {
            name: "Division",
            selector: (row) => {
                return getDivisionName(row.division);
            },
            sortable: true,
        },
        {
            name: "District",
            selector: (row) => {
                return getDistrictName(row.district);
            },
            sortable: true,
        },

        {
            name: "College Name",
            selector: (row) => row.collegeName,
            sortable: true,
             cell: (row) => (
                <span style={{ color: row.guestFacultyCount === 0 ? "red" : "black",  }}>
                    {row.collegeName}
                </span>
            ),
        },
        {
            name: "Registered Faculty",
            selector: (row) => row.guestFacultyCount,
            sortable: true,
             cell: (row) => (
                <span style={{ color: row.guestFacultyCount === 0 ? "red" : "black"}}>
                    {row.guestFacultyCount}
                </span>
            ),
        },
        {
            name: "Verified Faculty",
            selector: (row) => row.verifiedGuestFacultyCount,
            sortable: true,
        },
    ];




    return (
        <>
            <Header />

            {/* Page content */}
            <Container className="mt--7" fluid>
                {/* Table to display guest faculty counts */}
                <Row className="mt-5">
                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                                <Col xs="6">
                                    <h3 className="mb-0">All Guest Faculty Report Count</h3>
                                </Col>
                                <Col className="text-right" xs="4">
                                    <Button
                                        color="primary"
                                        size="sm"
                                        onClick={exportToExcel}
                                    >
                                        Export To Excel
                                    </Button>
                                </Col>
                            </CardHeader>
                        </Card>
                    </Col>
                </Row>

                <Row className="mt-4">
                    <Col lg="12">
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">Guest Faculty Counts List</h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <DataTable
                                    columns={columns}
                                    data={colleges.map(col => {
                                        const matchedCollege = data.find(d => d.collegeId === col._id);
                                        return {
                                            collegeId: col._id,
                                            collegeName: col.name,
                                            district: col.district,
                                            division: col.divison,
                                            guestFacultyCount: matchedCollege ? matchedCollege.guestFacultyCount : 0,
                                            verifiedGuestFacultyCount: matchedCollege ? matchedCollege.verifiedGuestFacultyCount : 0,
                                        };
                                    })}
                                    pagination
                                    highlightOnHover
                                    striped
                                />
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default AllGuestFacultyReportCount;
