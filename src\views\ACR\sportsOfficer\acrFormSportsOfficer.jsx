import { useState, useEffect } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    Label,
    Button,
    Input,
} from "reactstrap";

import Swal from "sweetalert2";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";
import axios from "axios";


const ACRformForSportsOfficer = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);

    // // console.log(employee, "employeeGetting Employee");


    const [formData, setFormData] = useState({
        employeeId: employee._id,
        employeeName: employee.name,
        collegeName: employee.collegeDetails.name,
        collegeId: employee.collegeDetails._id,
        vartmanVetan: employee.currentSalary,
        title: employee.title,
        gender: employee.gender,
        secondaryName: "",
        pitaPatiKaNaam: "",
        dateOfBirth: "",
        saikshanikYogyta: "",
        niyuktiDinank: "",
        niyuktiPrakar: "",
        varisthShreniParitDinak: "",
        prawarShreniParitDinank: "",
        sewaKalMahavidyalayaPadankit: "",
        vicharDhinYogdan: "",
    })
    const [errors, setErrors] = useState({});


    const [appointment, setAppointment] = useState([]);
    useEffect(() => {
        const getAppointment = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/get-appointment-type`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data;
                    setAppointment(data);
                }
            } catch (error) {
                console.error("Error fetching appointment data:", error);
                alert("Failed to load appointment data.");
            }
        };

        getAppointment(); // Call the function inside useEffect
    }, [endPoint, token]); // Dependencies


    const handleInputChange = (e) => {
        const { name, value } = e.target;

        const regex = /^[a-zA-Z\s'-]*$/;

        // Validate the input for father's name
        if ((name === "secondaryName" || name === "pitaPatiKaNaam") && !regex.test(value)) {
            setErrors((prevErrors) => ({
                ...prevErrors,
                [name]: "Do Not Use Special Character।", // "Please do not use special characters."
            }));
            return; // Exit the function if the input is invalid
        } else {
            // Clear the error if the input is valid
            setErrors((prevErrors) => ({
                ...prevErrors,
                [name]: "",
            }));
        }

        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" });
    };

    const handleFinalSubmit = async (e) => {


        // Validation function to check for required fields
        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {

                pitaPatiKaNaam: "",
                dateOfBirth: "",
                saikshanikYogyta: "",
                niyuktiDinank: "",
                niyuktiPrakar: "",
                varisthShreniParitDinak: "",
                prawarShreniParitDinank: "",
                sewaKalMahavidyalayaPadankit: "",
                vicharDhinYogdan: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";
                    }
                }
            }

            return newErrors;
        };



        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {

            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert("स्व-प्रतिवेदन सफल हुआ", "success");
                    setTimeout(() => window.location.reload(), 3000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };

    const currentDate = new Date();
    const currentToday = currentDate.toISOString().split('T')[0];

    const minDate = new Date();
    minDate.setFullYear(currentDate.getFullYear() - 18);

    // Calculate the maximum date (65 years ago)
    const maxDate = new Date();
    maxDate.setFullYear(currentDate.getFullYear() - 65);

    // Format dates to YYYY-MM-DD for the input
    const formattedMinDate = minDate.toISOString().split('T')[0];
    const formattedMaxDate = maxDate.toISOString().split('T')[0];

    return (
        <>
            <Container className="mt--7" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2">कार्यालय, आयुक्त, उच्च शिक्षा, छत्तीसगढ़ रायपुर</h2>
                                <h2> गोपनीय प्रतिवेदन प्रपत्र </h2>
                                <h3>31 मार्च {year} को समाप्त होने वाले वर्ष के लिये</h3>
                                <br /><h2>क्रीड़ा अधिकारी संवर्ग</h2>


                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong> नाम
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={formData.employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                {formData.gender === "Female" &&
                                                    <Col md="3" className="mt--4">
                                                        <Label><strong>1.(अ)</strong>(महिला अधिकारी विवाह के पूर्व का नाम भी लिखें)</Label>
                                                        <Input
                                                            type="text"
                                                            name="secondaryName"
                                                            value={formData.secondaryName} // Use employee data here
                                                            className="form-control"
                                                            onChange={handleInputChange}
                                                        />
                                                        {errors.secondaryName && (
                                                            <small className="text-danger">
                                                                {errors.secondaryName}
                                                            </small>
                                                        )}
                                                    </Col>}
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पिता/पति का नाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="pitaPatiKaNaam"
                                                        value={formData.pitaPatiKaNaam} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.pitaPatiKaNaam && (
                                                        <small className="text-danger">
                                                            {errors.pitaPatiKaNaam}
                                                        </small>
                                                    )}

                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong> जन्म तिथि</Label>
                                                    <Input
                                                        type="date"
                                                        name="dateOfBirth"
                                                        value={formData.dateOfBirth} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                        min={formattedMaxDate} // Set the minimum date to 65 years ago
                                                        max={formattedMinDate}
                                                    />
                                                    {errors.dateOfBirth && (
                                                        <small className="text-danger">
                                                            {errors.dateOfBirth}
                                                        </small>
                                                    )}

                                                </Col>

                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="3">
                                                    <Label>
                                                        <strong>4.</strong> शैक्षणिक योग्यता:
                                                    </Label>
                                                    <Input
                                                        type="select" // Change the type to "select"
                                                        name="saikshanikYogyta"
                                                        value={formData.saikshanikYogyta}
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                                                        <option value="12th">12th</option>
                                                        <option value="UG">UG</option>
                                                        <option value="PG">PG</option>
                                                        <option value="Others">Others</option>
                                                    </Input>
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(अ )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति का दिनांक
                                                    </Label>
                                                    <Input
                                                        type="date"
                                                        name="niyuktiDinank"
                                                        max={currentToday}
                                                        value={formData.niyuktiDinank} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.niyuktiDinank && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiDinank}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(ब )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति प्रकार
                                                    </Label>
                                                    <Input
                                                        type="select"
                                                        name="niyuktiPrakar"
                                                        value={formData.niyuktiPrakar} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    ><option value="">
                                                            No options available
                                                        </option>
                                                        {appointment &&
                                                            appointment.length > 0 &&
                                                            appointment.map((type, index) => (
                                                                <option
                                                                    key={index}
                                                                    value={type.appointment}
                                                                >
                                                                    {type.appointment}
                                                                </option>
                                                            ))}
                                                    </Input>
                                                    {errors.niyuktiPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiPrakar}
                                                        </small>
                                                    )}
                                                </Col>

                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col>
                                            <Row className="d-block mt-3 mb-3">
                                                <Col>
                                                    <Row>
                                                        <Col>
                                                            <Label><strong>6.</strong> वेतनमान
                                                            </Label>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(अ) वर्तमान वेतनमान
                                                            </Label>
                                                            <Input
                                                                type="text"
                                                                name="vartmanVetan"
                                                                value={formData.vartmanVetan} // Use employee data here
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                                readOnly
                                                            />
                                                            {errors.vartmanVetan && (
                                                                <small className="text-danger">
                                                                    {errors.vartmanVetan}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(ब) वरिष्ठ श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="varisthShreniParitDinak"
                                                                value={formData.varisthShreniParitDinak} // Use employee data here
                                                                className="form-control"
                                                                max={currentToday}
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.varisthShreniParitDinak && (
                                                                <small className="text-danger">
                                                                    {errors.varisthShreniParitDinak}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(स) प्रवर श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="prawarShreniParitDinank"
                                                                value={formData.prawarShreniParitDinank} // Use employee data here
                                                                max={currentToday}
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.prawarShreniParitDinank && (
                                                                <small className="text-danger">
                                                                    {errors.prawarShreniParitDinank}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>7.</strong> वर्ष में किस महाविद्यालय में पदस्थ रहे.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="sewaKalMahavidyalayaPadankit"
                                                        value={formData.sewaKalMahavidyalayaPadankit} // Use employee data here
                                                        className="form-control"
                                                        maxLength={150}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.sewaKalMahavidyalayaPadankit && (
                                                        <small className="text-danger">
                                                            {errors.sewaKalMahavidyalayaPadankit}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt--4">
                                                    <Label><strong>8. </strong>विचाराधीन वर्ष में खेलकूद आदि की व्यवस्था बढ़ाने में प्रमुख महयोग / प्रयास
                                                        ( क्रीडा अधिकारी उन्हें सौपे गये कार्यों के निष्पादन प्रतिवेदन संलग्न करें)
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="vicharDhinYogdan"
                                                        maxLength={150}
                                                        value={formData.vicharDhinYogdan} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.vicharDhinYogdan && (
                                                        <small className="text-danger">
                                                            {errors.vicharDhinYogdan}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Button color="success" onClick={handleFinalSubmit}>
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRformForSportsOfficer.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,

        gender: PropTypes.string,
        title: PropTypes.string,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRformForSportsOfficer;
