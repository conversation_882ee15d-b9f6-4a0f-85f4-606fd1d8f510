import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>er,
  Container,
  Col,
  Row,
  Input,
  Table,
  Badge,
  Pagination,
  PaginationItem,
  PaginationLink,
  Modal,
  <PERSON>dal<PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "reactstrap";
import { BsDownload, BsEye } from "react-icons/bs";
import Header from "../../components/Headers/Header.jsx";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";
import axios from "axios";
import formatDate from "../../utils/formateDate.jsx";

const LeaveList = () => {
  const empId = sessionStorage.getItem("id");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;

  const today = new Date();

  //#region  For Pegination
  const [currentPage, setCurrentPage] = useState(1);
  const [applications, setApplications] = useState([]);
  const [filteredApplications, setFilteredApplications] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [sortConfig, setSortConfig] = useState({
    key: "",
    direction: "ascending",
  });
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [modal, setModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [user, setUser] = useState([]);
  const [clgInfo, setClgInfo] = useState([]);

  const [itemsPerPage, setRecordsPerPage] = useState(5); // Default value

  const handleChange = (event) => {
    const value = Number(event.target.value);
    setRecordsPerPage(value);
  };

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/applied_Leaves/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data.reverse();
          setApplications(data);
          setFilteredApplications(data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchApplications();
  }, [endPoint, empId, token]);

  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredApplications.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredApplications.length / itemsPerPage);

  const handlePageChange = (pageNumber) => {
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setCurrentPage(pageNumber);
    }
  };
  //#endregion

  const [movement, setMovement] = useState("");
  const [checkMovement, setCheckMovement] = useState(false);
  const [applicationForMovement, setApplicationForMovement] = useState([]);

  const toggleMovement = async (item) => {
    // console.log("Getting Application ID", item);
    setApplicationForMovement(item);

    try {
      const response = await axios.get(
        `${endPoint}/api/leave/movement/${item._id}`,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data.reverse();
        // console.log(data, "Getting Data");
        setMovement(data);
      } else {
        alert("Data Not Fetched.");
      }
    } catch (error) {
      console.error("An error occurred while Getting Data:", error);
      alert("An error occurred. Please try again later.");
    }

    // setApplicationForMovement(item);
    setCheckMovement(!checkMovement);
  };

  useEffect(() => {
    const fetchCollegeInfo = async () => {
      if (user && user.college) {
        // Check if user and college are defined

        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-college/${user.college}`,
            {
              headers: {
                "Content-Type": "application/json",
                "web-url": window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setClgInfo(response.data);
          } else {
            alert("College Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    };
    fetchCollegeInfo();
  }, [user]);

  // For Export EXCEL
  const exportToExcel = () => {
    try {
      if (!currentItems || currentItems.length === 0) {
        SwalMessageAlert("No data available for export.", "warning");
        return;
      }

      // Create HTML table string with the same headers as your table
      let tableHTML = `
      <table border="1">
        <thead>
          <tr>
            <th>Sno</th>
            <th>Current Status (स्थिति)</th>
            <th>Application ID (क्रमांक)</th>
            <th>Application Date (आवेदन  दिनांक)</th>
            <th>Leave Type</th>
            <th>Leave Reason</th>
            <th>From Date ( दिनांक से )</th>
            <th>To Date ( दिनांक तक )</th>
          </tr>
        </thead>
        <tbody>
    `;

      // Format each row based on currentItems data
      currentItems.forEach((item, index) => {
        // List the action texts as placeholders since buttons can't export
        const actionsText = [
          "Movement",
          "Remark",
          item.leaveStatus !== 5 &&
          item.leaveStatus !== 4 &&
          item.leaveStatus !== 6
            ? "Cancel"
            : "Cancel (Disabled)",
          "Preview",
          item.uploadFile ? "Download File" : "No File",
          item.recieptGenrated ? "Receipt Generated" : "",
        ]
          .filter(Boolean)
          .join(", ");

        // Derive status string matching your badge logic
        let statusText = "Error";
        switch (item.leaveStatus) {
          case 1:
            statusText = "Pending";
            break;
          case 2:
            statusText =
              item.designation === "UG Principal" ||
              item.designation === "PG Principal"
                ? "Pending"
                : "Forwarded";
            break;
          case 3:
            statusText = "Approved";
            break;
          case 4:
            statusText = "Rejected";
            break;
          case 5:
            statusText = "Cancelled";
            break;
          case 6:
            statusText = "Applied for Cancel";
            break;
        }

        // Format dates using your formatDate function or fallback
        const format = (date) =>
          typeof formatDate === "function"
            ? formatDate(date)
            : new Date(date).toLocaleDateString();

        tableHTML += `
        <tr>
          <td>${indexOfFirstItem + index + 1}</td>
          <td>${statusText}</td>
          <td>${item.applicationId}</td>
          <td>${format(item.appliedDate)}</td>
          <td>${
            item.leaveType === "Restricted Holiday"
              ? "Optional Holiday"
              : item.leaveType
          } </td>
          <td>${item.reason}</td>
          <td>${format(item.fromDate)}</td>
          <td>${format(item.tillDate)}</td>
        </tr>
      `;
      });

      tableHTML += "</tbody></table>";

      const excelFileContent = `
      <html xmlns:o="urn:schemas-microsoft-com:office:office"
            xmlns:x="urn:schemas-microsoft-com:office:excel" 
            xmlns="http://www.w3.org/TR/REC-html40">
        <head>
          <!--[if gte mso 9]>
          <xml>
            <x:ExcelWorkbook>
              <x:ExcelWorksheets>
                <x:ExcelWorksheet>
                  <x:Name>Leave Applications</x:Name>
                  <x:WorksheetOptions>
                    <x:DisplayGridlines/>
                  </x:WorksheetOptions>
                </x:ExcelWorksheet>
              </x:ExcelWorksheets>
            </x:ExcelWorkbook>
          </xml>
          <![endif]-->
        </head>
        <body>${tableHTML}</body>
      </html>`;

      const blob = new Blob([excelFileContent], {
        type: "application/vnd.ms-excel;charset=utf-8;",
      });
      const date = new Date().toLocaleDateString().replace(/\//g, "-");
      const downloadLink = document.createElement("a");
      downloadLink.href = URL.createObjectURL(blob);
      downloadLink.download = `LeaveApplicationsReport_${date}.xls`;
      downloadLink.click();
    } catch (error) {
      SwalMessageAlert(`Data export failed: ${error.message}`, "error");
    }
  };

  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUser(data);
        } else {
          alert("Data Not Fetched.");
          // navigate('/auth/Register');
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
        // navigate('/auth/Register');
      }
    };
    fetchEmployeeData();
  }, [token, empId, endPoint]);

  //#region Sorting Logic
  const handleSort = (key) => {
    let direction = "ascending";
    if (sortConfig.key === key && sortConfig.direction === "ascending") {
      direction = "descending";
    }
    setSortConfig({ key, direction });
  };

  //#endregion

  //#region Filtering Logic
  useEffect(() => {
    const filteredItems = filteredApplications.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilteredApplications(filteredItems);
    setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, applications]);

  //#region Filter Logic
  const handleFilterChange = (Id) => {
    const status = parseInt(Id);
    // console.log(status);

    setSelectedStatus(status);
    if (status) {
      const filtered = applications.filter((app) => app.leaveStatus === status);
      setFilteredApplications(filtered);
    } else {
      setFilteredApplications(applications); // Reset to all applications if no filter
    }
    setCurrentPage(1); // Reset to first page on filter change
  };

  const getStatusCount = (status) => {
    return applications.filter((app) => app.leaveStatus === status).length;
  };

  const statusCode = selectedStatus;
  useEffect(() => {
    if (statusCode >= 1 && statusCode <= 3) {
      setCurrentStep(statusCode - 1); // Convert status code to zero-based index
    }
  }, [statusCode]);

  const steps = [
    { label: "Employee", icon: "fa-user" },
    { label: "Office", icon: "fa-solid fa-briefcase" },
    { label: "Directorate", icon: "fa-solid fa-user-shield" },
  ];

  const handleShowDetails = (remark1, remark2, remark3) => {
    Swal.fire({
      html: `<div style="text-align: left; max-height: 300px; overflow-y: auto;"><h3><b>Remark By User</h3><p> ${remark1}<p>
           </br> <h3><b>Remark By Principal</b></h3><p> ${remark2}<p>
           </br> <h3><b>Remark By Directorate</b></h3><p> ${remark3}<p></div>`,
      showCloseButton: true,
      // showCancelButton: false,
      confirmButtonText: "Close",
    });
  };

  const handleCancel = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure you want to cancel?",
      text: "You won't be able to revert this!",
      icon: "warning",
      input: "text", // Adds a text input field
      inputPlaceholder: "Enter reason for cancellation...",
      inputAttributes: {
        "aria-label": "Reason for cancellation",
      },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, cancel it!",
      cancelButtonText: "No, keep it",
      preConfirm: (reason) => {
        if (!reason) {
          Swal.showValidationMessage("Reason for cancellation is required");
        }
        return reason;
      },
    });

    if (result.isConfirmed) {
      const reason = result.value; // Capture the reason entered by the user

      try {
        // Update status to 6 with cancellation reason using PUT method
        const response = await axios.put(
          `${endPoint}/api/leave/apply_cancel/${id}`,
          { reason },
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          Swal.fire(
            "Applied for Cancel!",
            "Your request has been sent with your reason.",
            "success"
          );
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        }
      } catch (error) {
        // Handle error response here
        Swal.fire("Error!", "There was an error updating your status.", error);
      }
    }
  };
  const toggle = (status) => {
    setModal(!modal);
    setSelectedStatus(status);
  };

  const statuses = [
    { valueOf: "", label: `All (${applications.length})`, color: "blue" },
    ...(!(
      applications[0]?.designation === "UG Principal" ||
      applications[0]?.designation === "PG Principal"
    )
      ? [
          {
            valueOf: "1",
            label: `Pending (${getStatusCount(1)})`,
            color: "orange",
          },
        ]
      : [
          {
            valueOf: "2",
            label: `Pending (${getStatusCount(2)})`,
            color: "orange",
          },
        ]),
    ...(!(
      applications[0]?.designation === "UG Principal" ||
      applications[0]?.designation === "PG Principal"
    )
      ? [
          {
            valueOf: "2",
            label: `Forwarded (${getStatusCount(2)})`,
            color: "gray",
          },
        ]
      : []),
    {
      valueOf: "3",
      label: `Approved (${getStatusCount(3)})`,
      color: "darkgreen",
    },
    { valueOf: "4", label: `Rejected (${getStatusCount(4)})`, color: "red" },
    {
      valueOf: "5",
      label: `Cancelled (${getStatusCount(5)})`,
      color: "skyblue",
    },
    {
      valueOf: "6",
      label: `Applied for Cancel (${getStatusCount(6)})`,
      color: "orange",
    },
  ];

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <div className="col">
            <Card className="shadow">
              <CardHeader className="border-0">
                <h3 className="mb-0">Leave List / अवकाश आवेदन सूची</h3>
                <Row className="mt-3">
                  <Col xs={9}>
                    {statuses.map((status) => (
                      <button
                        className="btn-sm"
                        key={status.valueOf}
                        onClick={() => handleFilterChange(status.valueOf)}
                        style={{
                          backgroundColor: status.color,
                          color: "white",
                          border: "none",
                          borderRadius: "5px",
                          padding: "10px",
                          margin: "5px",
                          cursor: "pointer",
                          fontWeight: "bolder",
                        }}
                      >
                        {status.label}
                      </button>
                    ))}
                  </Col>
                  <Col md={3}>
                    <Input
                      type="text"
                      placeholder="Search..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    />

                    <Button
                      className="btn-success btn-sm"
                      onClick={exportToExcel}
                    >
                      Export To Excel
                    </Button>
                  </Col>
                </Row>
              </CardHeader>

              <Table
                className="align-items-center hover table-flush"
                responsive
              >
                <thead className="thead-light">
                  <tr>
                    <th onClick={() => handleSort("sno")}>
                      Sno <br />
                      ./ क्र.
                    </th>
                    <th>
                      Movement / Remark / Cancel <br /> view / Download / Action
                      Letter
                    </th>
                    <th>
                      Current Status / <br /> वर्तमान स्थिति
                    </th>
                    <th onClick={() => handleSort("applicationId")}>
                      Application ID <br /> (आवेदन <br /> क्रमांक <br />/
                      दिनांक)
                    </th>

                    <th onClick={() => handleSort("leaveType")}>
                      Leave <br /> Type <br /> (अवकाश <br /> का प्रकार) / <br />{" "}
                      Reason <br /> (कारण)
                    </th>

                    <th onClick={() => handleSort("fromDate")}>
                      Date <br /> Between <br /> (दिनांक से - तक)
                    </th>
                    {/* <th>
                      Leave <br /> Status <br /> (अवकाश <br /> की स्थिति)
                    </th> */}
                  </tr>
                </thead>
                <tbody className="hover">
                  {currentItems &&
                    currentItems.length > 0 &&
                    currentItems.map((item, index) => (
                      <tr key={item.id}>
                        <td>
                          {" "}
                          <span style={{ fontWeight: "bolder" }}>
                            {indexOfFirstItem + index + 1}
                          </span>
                        </td>

                        <td>
                          <button
                            className="btn-sm m-2 btn-info"
                            onClick={() => toggleMovement(item)}
                          >
                            Movement
                          </button>

                          <button
                            className="btn m-2 btn-sm btn-primary"
                            onClick={() =>
                              handleShowDetails(
                                item.remarkByUser,
                                item.remarkByPrincipal,
                                item.remarkByDirector
                              )
                            }
                          >
                            Remark
                          </button>
                          
                            <Button
                              className="btn-sm m-2"
                              style={{
                                backgroundColor: "#ff4e67",
                                color: "white",
                              }}
                              onClick={() => handleCancel(item._id)}
                              disabled={
                                item.leaveStatus === 5 ||
                                item.leaveStatus === 4 ||
                                item.leaveStatus === 6 || (item.leaveStatus=== 3 && new Date(item.tillDate) < new Date())
                              }
                            >
                              Cancel 
                            </Button>
                        
                          <br />

                          <Link to={`/admin/Preview/${item._id}`}>
                            <Button
                              className="btn-sm m-2"
                              style={{ backgroundColor: "yellow" }}
                            >
                              <BsDownload size={18} />
                              <BsEye size={18} />
                            </Button>
                          </Link>

                          <a
                            href={`https://heonline.cg.nic.in/${item.uploadFile}`}
                            download
                          >
                            <Button className="btn-sm btn-primary">
                              {item.uploadFile ? (
                                <BsDownload size={18} />
                              ) : (
                                "No File"
                              )}
                            </Button>
                          </a>

                          {item.recieptGenrated === true && (
                            <Link to={`/admin/Genrated/${item._id}`}>
                              <Button
                                className=" btn btn-sm m-2"
                                style={{ backgroundColor: "orange" }}
                              >
                                <BsDownload size={18} />
                                <BsEye size={18} />
                              </Button>
                            </Link>
                          )}
                        </td>
                        <td>
                          <h2>
                            <Badge
                              className="badge-sm"
                              onClick={
                                item.leaveStatus === 1 || item.leaveStatus === 2
                                  ? () => toggle(item.leaveStatus)
                                  : null
                              }
                              style={{
                                fontWeight: "bolder",
                                color: "white",
                                backgroundColor:
                                  item.leaveStatus === 3
                                    ? "green" // Approved background color
                                    : item.leaveStatus === 4
                                    ? "red" // Rejected background color
                                    : item.leaveStatus === 1
                                    ? "#f3c70c" // Pending background color
                                    : item.leaveStatus === 5
                                    ? "blue"
                                    : item.leaveStatus === 2
                                    ? "#ff8a00"
                                    : item.leaveStatus === 6
                                    ? "#a5945a"
                                    : "lavender",
                              }}
                            >
                              {item.leaveStatus === 3 ? (
                                "Approved"
                              ) : item.leaveStatus === 4 ? (
                                "Rejected"
                              ) : item.leaveStatus === 6 ? (
                                "Applied for Cancel"
                              ) : item.leaveStatus === 1 ? (
                                <>
                                  <Spinner
                                    size="sm"
                                    color="white"
                                    style={{ marginRight: "8px" }}
                                  />
                                  Pending
                                </>
                              ) : item.leaveStatus === 2 ? (
                                item.designation === "UG Principal" ||
                                item.designation === "PG Principal" ? (
                                  "Pending"
                                ) : (
                                  "Forwarded"
                                )
                              ) : item.leaveStatus === 5 ? (
                                "Cancelled"
                              ) : (
                                "Error"
                              )}
                            </Badge>
                          </h2>
                        </td>
                        <td>
                          <span style={{ fontWeight: "bolder" }}>
                            {item.applicationId}{" "}
                          </span>{" "}
                          <br />({formatDate(item.appliedDate)})
                        </td>
                        <td>
                          <span style={{ fontWeight: "bolder" }}>
                            {item.leaveType === "Restricted Holiday"
                              ? "Optional Holiday"
                              : item.leaveType}{" "}
                          </span>
                          <br /> ({item.reason})
                        </td>
                        <td>
                          <strong>
                            ({formatDate(item.fromDate)}) <br /> to <br /> (
                            {formatDate(item.tillDate)}) <br />{" "}
                            <span
                              style={{ fontWeight: "bolder", color: "red" }}
                            >
                              {item.dayCount} Days
                            </span>
                          </strong>
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table>
              <Modal
                style={{ maxWidth: "1000px", minWidth: "400px" }}
                isOpen={checkMovement}
                toggle={toggleMovement}
              >
                <ModalHeader toggle={toggleMovement}>
                  <h3>
                    File Movement Of ({applicationForMovement.applicationId}){" "}
                  </h3>
                </ModalHeader>
                <ModalBody>
                  <Table
                    className="align-items-center table-flush"
                    responsive
                    bordered
                  >
                    <thead className="thead-light">
                      <tr>
                        <th>Sno./ क्र.</th>
                        <th>
                          Action/ <br />
                          Sent By
                        </th>
                        <th>Section Name</th>
                        <th>Action Date</th>
                        <th>Day Taken</th>
                        <th>Currently With</th>
                        <th>Status</th>
                        <th>Remarks</th>
                      </tr>
                    </thead>
                    <tbody>
                      {movement && movement.length > 0 ? (
                        movement.map((item, index) => (
                          <tr key={item._id}>
                            <td className="text-center">{index + 1}</td>
                            <td className="text-left">{item.directorName}</td>
                            <td className="text-left">{item.sectionName}</td>
                            <td className="text-center">
                              {formatDate(item.actionDate)}
                            </td>
                            <td className="text-center">{item.dayTaken}</td>
                            <td className="text-center">
                              <strong
                                style={{
                                  fontWeight: "bolder",
                                  fontSize: "14px",
                                }}
                              >
                                {item.sendToDirector}
                              </strong>
                              {index === 0 && (
                                <i
                                  className="fa fa-check fa-xl"
                                  style={{ marginLeft: "10px", color: "green" }}
                                  aria-hidden="true"
                                ></i>
                              )}
                            </td>
                            <td>
                              <h2>
                                <Badge
                                  className="badge-sm"
                                  onClick={
                                    item.status === 1 || item.status === 2
                                      ? () => toggle(item.status)
                                      : null
                                  }
                                  style={{
                                    fontWeight: "bolder",
                                    color: "white",
                                    backgroundColor:
                                      item.status === 3 &&
                                      item.isRecieptGenrated === true
                                        ? "green" // Approved background color
                                        : item.status === 3 &&
                                          item.isRecieptGenrated === false
                                        ? "#ff8a00"
                                        : item.status === 4 &&
                                          item.isRecieptGenrated === true
                                        ? "red" // Rejected background color
                                        : item.status === 4 &&
                                          item.isRecieptGenrated === false
                                        ? "#ff8a00"
                                        : item.status === 1
                                        ? "#f3c70c" // Pending background color
                                        : item.status === 6
                                        ? "#ff8a00"
                                        : item.status === 5
                                        ? "blue"
                                        : item.status === 2
                                        ? "#ff8a00"
                                        : "lavender",
                                  }}
                                >
                                  {item.status === 3 &&
                                  item.isRecieptGenrated === true ? (
                                    "Approved"
                                  ) : item.status === 3 &&
                                    item.isRecieptGenrated === false ? (
                                    "Actioned by Director"
                                  ) : item.status === 4 &&
                                    item.isRecieptGenrated === true ? (
                                    "Rejected"
                                  ) : item.status === 4 &&
                                    item.isRecieptGenrated === false ? (
                                    "Actioned by Director"
                                  ) : item.status === 6 ? (
                                    "approve Cancel"
                                  ) : item.status === 1 ? (
                                    <>
                                      <Spinner
                                        size="sm"
                                        color="white"
                                        style={{ marginRight: "8px" }}
                                      />
                                      Pending at principal
                                    </>
                                  ) : item.status === 2 ? (
                                    <>
                                      {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                      Forwarded
                                    </>
                                  ) : item.status === 5 ? (
                                    "Cancelled"
                                  ) : (
                                    "Error"
                                  )}
                                </Badge>
                              </h2>
                            </td>
                            <td className="text-left">
                              {item.remarksFileMovment}
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan="6" className="text-center">
                            No Data Found
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </Table>
                </ModalBody>
                <ModalFooter>
                  <Button color="secondary" onClick={toggleMovement}>
                    Close
                  </Button>
                  {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                </ModalFooter>
              </Modal>
              <Modal isOpen={modal} toggle={toggle}>
                <ModalHeader toggle={toggle}>Status</ModalHeader>
                <ModalBody>
                  <div className="main">
                    <ul
                      style={{
                        display: "flex",
                        justifyContent: "space-around",
                      }}
                    >
                      {steps.map((step, index) => (
                        <li
                          key={index}
                          style={{
                            listStyle: "none",
                            display: "flex",
                            flexDirection: "column",
                            alignItems: "center",
                          }}
                        >
                          <i
                            className={`icons awesome fa-solid ${step.icon}`}
                            style={{ fontSize: "25px", color: "#1b761b" }}
                          ></i>
                          <div
                            className={`step ${
                              index === currentStep ? "active" : ""
                            }`}
                            style={{
                              height: "30px",
                              width: "30px",
                              borderRadius: "50%",
                              backgroundColor:
                                index <= currentStep ? "#1b761b" : "#d7d7c3",
                              margin: "16px 0 10px",
                              display: "grid",
                              placeItems: "center",
                              color: "ghostwhite",
                              position: "relative",
                              cursor: "default", // Change cursor to default since clicking is disabled
                            }}
                          >
                            <p
                              style={{
                                fontSize: "18px",
                                display:
                                  index <= currentStep ? "none" : "block",
                              }}
                            >
                              {index + 1}
                            </p>
                            <i
                              className="awesome fa-solid fa-check"
                              style={{
                                display: index <= currentStep ? "flex" : "none",
                              }}
                            ></i>
                          </div>
                          <p
                            className="label"
                            style={{
                              fontFamily: "sans-serif",
                              letterSpacing: "1px",
                              fontSize: "14px",
                              fontWeight: "bold",
                              color: "#1b761b",
                            }}
                          >
                            {step.label}
                          </p>
                        </li>
                      ))}
                    </ul>
                  </div>
                </ModalBody>
                <ModalFooter>
                  <Button color="secondary" onClick={toggle}>
                    Close
                  </Button>
                  {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                </ModalFooter>
              </Modal>
              <CardFooter className="py-4">
                <div>
                  <label htmlFor="recordsPerPage">
                    No. of Records Per Page:{" "}
                  </label>
                  <select
                    id="recordsPerPage"
                    value={itemsPerPage}
                    onChange={handleChange}
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>
                <nav aria-label="...">
                  <Pagination className="pagination justify-content-end mb-0">
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem
                        key={i}
                        className={currentPage === i + 1 ? "active" : ""}
                      >
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(i + 1);
                          }}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </div>
        </Row>
      </Container>
    </>
  );
};

export default LeaveList;
