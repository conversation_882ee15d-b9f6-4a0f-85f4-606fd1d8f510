import { useEffect, useState } from "react";

import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Modal,
  ModalHeader,
  ModalBody,
  Container,
  Row,
  Col,
  Nav,
  NavItem,
  NavLink,
  TabContent,
  TabPane, Table
} from "reactstrap";
// core components
import Header from "../../components/Headers/Header.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import axios from "axios";
import formatDate from "../../utils/formateDate.jsx";
import classnames from "classnames"; // For toggling active class
import { FaDownload } from "react-icons/fa";
import { string } from "prop-types";

const Profile = () => {
  const id = sessionStorage.getItem("id");
  const [user, setUser] = useState([]);
  const [clgInfo, setClgInfo] = useState([]);
  const [activeTab, setActiveTab] = useState("1");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const [base64Image, setBase64Image] = useState("");
  const [classData, setClassData] = useState([]);
  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);
  // const [division,] = useState([]);
  const [designationData, setDesignationData] = useState([]);
  const [disabiltyType, setDisabiltyType] = useState([]);
  const [districtAll, setDistrictAll] = useState([]);
  const [stateWiseDistrict, setstateWiseDistrict] = useState([]);
  const [isImageModalOpen, setImageIsModalOpen] = useState(false);
  const [userProfie, setUserProfile] = useState([]);

  const [division, setDivision] = useState([]);


  function handleConvertAndDisplay(base64) {
    const sampleBase64Image = `data:image/png;base64,${base64}`;
    setBase64Image(sampleBase64Image);
    setImageIsModalOpen(true);
  }
  const toggleTab = (tab) => {
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  };
  const toggleImageModal = () => {
    setImageIsModalOpen(!isImageModalOpen);
  };
  const [payLevel, setPayLevel] = useState([]); // For table data
  const [basicPay, setBasicPay] = useState([]);

  useEffect(() => {
    const fetchPayLevel = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-pay-level`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setPayLevel(response.data); // ✅ Correctly updating PayLevel state
        } else {
          SwalMessageAlert("No Pay Level Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching Pay Level data:", error);
      }
    };

    fetchPayLevel();
  }, [endPoint, token]); // Dependencies
  useEffect(() => {
    const getBasicPay = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-all-basic-pay`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setBasicPay(response.data);
        }
      } catch (error) {
        console.error("Error fetching Basic Pay data:", error);
        alert("Failed to load Basic Pay data.");
      }
    };

    getBasicPay();
  }, [endPoint, token]); // Dependencies

  useEffect(() => {
    const getEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee-byId/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          setUser(data);
        }
      } catch (error) {
        console.error("Error fetching Employee data:", error);
        alert("Failed to load Employee data.");
      }
    };
    getEmployee();
  }, [endPoint]);
  const getClassName = (value) => {
    // console.log(value, classData, "adfjkahsd");
    const classObj = classData.find((classItem) => classItem._id === value);
    return classObj ? classObj.className : "Unknown Class Name";
  };
  useEffect(() => {
    const fetchCollegeInfo = async () => {

      if (user && user.college) { // Check if user and college are defined

        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-college/${user.college}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setClgInfo(response.data);
          } else {
            alert("College Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    };
    fetchCollegeInfo();
  }, [user]);

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/division/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    }
    fetchDesignation();
    fetchClassData();
    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies
  const [selectedDistrict, setSelectedDistrict] = useState(null); // Selected District

  useEffect(() => {
    const getDistrictAll = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrictAll(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 1.");
      }
    };
    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);


        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 2.");
      }
    };

    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
    getDistrict();
    getDistrictAll();
    getDistrictAllStateWise();
  }, [endPoint, token]); // Dependencies

  const getDistrictAllStateWise = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-alldistrict-state`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setstateWiseDistrict(data);
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
      alert("Failed to load district data 3.");
    }
  };
  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${userProfie.academicInformation?.workplaceDistrict}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    fetchVidhansabha();
  }, [endPoint, token,userProfie]);

  // Fetch disability types
  useEffect(() => {
    const fetchDisabilityList = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-disability-types`,
          {
            headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setDisabiltyType(response.data);
        } else {
          SwalMessageAlert("No Disability Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDisabilityList();
  }, [endPoint, token]); // Dependencies
  const [subjectData, setSubjectData] = useState([]);

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchSubjectList();
  }, [endPoint, token]);

  const [stateName, setStateName] = useState([]); // For table data
  useEffect(() => {
    const fetchStateList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-all-state`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setStateName(response.data);
        } else {
          SwalMessageAlert("No State Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchStateList();
  }, [endPoint, token]); // Dependencies

  const formatDateForInput = (dateString) => {
    if (!dateString) return ''; // Return empty if no date

    let date;

    // Handle cases where dateString is already a Date object
    if (dateString instanceof Date) {
      date = dateString;
    } else if (typeof dateString === 'string' && dateString.includes('-')) {
      // Handle YYYY-MM-DD format
      date = new Date(dateString);
    } else if (typeof dateString === 'string' && dateString.includes('/')) {
      // Handle DD/MM/YYYY or MM/DD/YYYY format
      const parts = dateString.split('/');
      if (parts.length === 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1; // Months are 0-based in JS
        const year = parseInt(parts[2], 10);
        date = new Date(year, month, day);
      }
    } else {
      // Attempt to parse other formats
      date = new Date(dateString);
    }

    if (isNaN(date)) return ''; // Invalid date check

    // Format as DD/MM/YYYY
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Month is 0-based
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  };



  const [appointment, setAppointment] = useState([]);
  useEffect(() => {
  const getAppointment = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-appointment-type`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setAppointment(data);
      }
    } catch (error) {
      console.error("Error fetching appointment data:", error);
      alert("Failed to load appointment data.");
    }
  };

  getAppointment(); // Call the function inside useEffect
}, [endPoint, token]);



  useEffect(() => {
    const getEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/get-emp-profile?empId=${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUserProfile(data);

        }
      } catch (error) {
        console.error("Error fetching Employee data:", error);
        alert("Failed to load Employee data.");
      }
    };
    if (user && user.empCode) {
      getEmployee();
    }
  }, [user]);
  return (
    <>
      <Header />
      {/* Page content */}
      <Container className="mt-7" fluid>
        <Modal
          isOpen={isImageModalOpen}
          toggle={toggleImageModal}
          style={{ textAlign: "center" }}
        >
          <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
          <ModalBody>
            <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
          </ModalBody>
        </Modal>
        <Row>
          <Col className="order-xl-1 mb-5 mb-xl-0" xl="3">
            <Card style={{ boxShadow: "1px 5px 10px 3px black" }}>
              <Row className="justify-content-center md-7">
                <Col className="order-lg-2" lg="3">
                  <div className="card-profile-image">
                    <a href="#pablo" onClick={(e) => e.preventDefault()}>
                      {user.encodedImage && user.faceVerified !== false && user.encodedImage !== "" ? (
                        <img
                          src={`data:image/png;base64,${user.encodedImage}`}
                          onClick={() => handleConvertAndDisplay(user.encodedImage)}
                          style={{
                            width: "220px",
                            height: "180px", // Ensure height matches width for a perfect circle
                            borderRadius: "100%", // Make the image round
                            objectFit: "cover", // Ensure the image covers the area without distortion
                          }}
                          alt="User  Profile" // Add alt text for accessibility
                        />
                      ) : (
                        <img
                          src={NavImage}
                          style={{
                            width: "220px",
                            height: "180px", // Ensure height matches width for a perfect circle
                            borderRadius: "100%", // Make the image round
                            objectFit: "cover", // Ensure the image covers the area without distortion
                          }}
                          alt="Default Profile" // Add alt text for accessibility
                        />
                      )}
                    </a>
                  </div>
                </Col>
              </Row>
              <CardHeader className="text-center border-0 pt-8 pt-md-4 pb-0 pb-md-4 mt-5">

              </CardHeader>
              <br />
              <hr className="my-4 primary" />

              <CardBody className="pt-0 pt-md-0">
                <div className="text-center mt--3">
                  <h3 className="font-weight-bolder mb-1"> {/* Reduced margin-bottom */}
                    {user.name} <br />
                  </h3>
                </div>
                <div className="h5 mt-1 text-center text-primary"> {/* Reduced margin-top */}

                  {user.designationDetails?.designation}
                </div>
                <div className="h5 text-center mb--4 mt--2">
                  <h3 className="mb-1"> {/* Reduced margin-bottom */}
                    <span style={{ color: "black", fontSize: "12px", fontWeight: "bolder" }}> Employee Code - </span> (<span style={{ color: "red", fontSize: "14px" }}>{user.empCode}</span>) <br />
                    <span className="font-weight-bold">
                      {user.activeStatus === true ? (
                        <span style={{ fontSize: "14px", color: "green" }}>Active</span>
                      ) : (
                        <span style={{ fontSize: "18px", color: "red" }}>Inactive</span>
                      )}
                    </span>
                  </h3>
                </div>

                <hr />

                <div className="text-center mb--4 mt--4">
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <a href={`mailto:${user.email}`}>{user.email}</a>
                  </div>
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <a href={`mailto:${user.contact}`}>{user.contact}</a>
                  </div>
                </div>
                <hr />

                <div className="text-center mb-4 mt--4">
                  <div>
                    <i className="ni education_hat mr-2" />
                    <span className="h5 font-weight-800">{user.collegeDetails?.name}</span>
                  </div>
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <i className="ni location_pin mr-2" />
                    Residential Address - {user.address}
                  </div>
                  <hr className="my-4" />

                  <div className="d-block justify-content-center mt--2">
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Registered: <span className="text-primary">{formatDate(user.createdAt)}</span>
                      </span>
                    </div>
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Last Updated : <span className="text-primary">{formatDate(user.updatedAt)}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </Col>

          <Col className="order-xl-2" xl="9">
            <Card style={{ boxShadow: "1px 5px 10px 3px black" }}>
              <CardHeader className="bg-white border-0 text-center">
                <h2 className="mb-0 text-primary ">
                  <strong>
                    {" "}
                    <i className="ni ni-single-02 text-primary"></i>{" "}
                    Employee Profile
                  </strong>
                </h2>

              </CardHeader>
              <CardBody>
                <Nav tabs style={{ display: "flex", justifyContent: "left", gap: "3px" }}>
                  <NavItem >
                    <NavLink
                      className={classnames({ active: activeTab === "1" })}
                      onClick={() => toggleTab("1")}
                      style={{
                        backgroundColor: activeTab === "1" ? "#007bff" : "transparent",
                        color: activeTab === "1" ? "white" : "#007bff",
                        borderColor: activeTab === "1" ? "#007bff" : "#dee2e6",
                        cursor: "pointer",
                      }}
                    >
                      Personal Information

                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames("rounded-0", { active: activeTab === "2" })}
                      onClick={() => toggleTab("2")}
                      style={{
                        backgroundColor: activeTab === "2" ? "#007bff" : "transparent",
                        color: activeTab === "2" ? "white" : "#007bff",
                        borderColor: activeTab === "2" ? "#007bff" : "#dee2e6",
                        cursor: "pointer",
                      }}
                    >
                      Academic Information
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "3" })}
                      onClick={() => toggleTab("3")}
                      style={{
                        backgroundColor: activeTab === "3" ? "#007bff" : "transparent",
                        color: activeTab === "3" ? "white" : "#007bff",
                        borderColor: activeTab === "3" ? "#007bff" : "#dee2e6",
                        cursor: "pointer",
                      }}
                    >
                      Employee Information
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "4" })}
                      onClick={() => toggleTab("4")}
                      style={{
                        backgroundColor: activeTab === "4" ? "#007bff" : "transparent",
                        color: activeTab === "4" ? "white" : "#007bff",
                        borderColor: activeTab === "4" ? "#007bff" : "#dee2e6",
                        cursor: "pointer",
                      }}
                    >
                      Present Position
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "5" })}
                      onClick={() => toggleTab("5")}
                      style={{
                        backgroundColor: activeTab === "5" ? "#007bff" : "transparent",
                        color: activeTab === "5" ? "white" : "#007bff",
                        borderColor: activeTab === "5" ? "#007bff" : "#dee2e6",
                        cursor: "pointer",
                      }}
                    >
                      Other Informations
                    </NavLink>
                  </NavItem>

                </Nav>
                {/* Tab Content */}
                <TabContent activeTab={activeTab} className="mt-4">
                  <TabPane tabId="1">
                    <div className="container ">
                      <h3 className="mb-4 bg-info m-3 border rounded text-center text-white p-2" style={{ fontSize: '1.0rem' }}>
                        <i className="ni ni-single-02 text-white "></i> Personal Information
                      </h3>
                      <table className="table w-100 ">
                        <Row>

                          <Col className="col-4 p-2" style={{ fontSize: '13px' }}><strong>Employee Code :</strong>  <strong className=" text-primary ml-2"> {user.empCode}</strong></Col>


                          <Col className="col-4  p-2" style={{ fontSize: '13px' }}><strong>Employee Name : </strong> <strong className=" text-primary ml-2"> {user.name}</strong> </Col>


                          <Col className="col-4  p-2" style={{ fontSize: '13px' }}><strong>Email ID: </strong> <strong className="text-primary ml-2">{user.email}</strong></Col>

                          <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />


                          <Col className="col-4  p-2" style={{ fontSize: '13px' }} >  <strong>Mobile No:  </strong>  <strong className="text-primary ml-2">{user.contact}</strong></Col>

                          <Col className="col-4  p-2" style={{ fontSize: '13px' }}><strong> Gender : </strong> <strong className=" text-primary ml-2"> {user.gender}</strong></Col>

                          <Col className="col-4  p-2" style={{ fontSize: '13px' }}>
                            <strong> Date of Birth: </strong>
                            <strong className=" text-primary ml-2">{formatDateForInput(userProfie?.personalInformation?.employeeDOB)}</strong><br></br><h5>(DD/MM/YYYY)</h5> </Col>

                          <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          <Col md="4" className="  p-2" style={{ fontSize: '13px' }}><strong>Category: </strong> <strong className=" text-primary ml-2"> {userProfie.personalInformation?.employeeCategory}</strong></Col>
                          <Col md="4" className="  p-2" style={{ fontSize: '13px' }}>
                            <strong>Home State:</strong>
                            <strong className=" text-primary ml-2">
                              {userProfie.personalInformation?.homeDistrict}</strong></Col>

                          {userProfie.personalInformation?.homeDistrict === "CG" && (<>                              <Col md="4" className=" p-2" style={{ fontSize: '13px' }}>
                            <strong>CG District : </strong> <strong className=" text-primary ml-2">
                              {districtAll.find((type) => String(type._id) === String(userProfie.personalInformation?.cgDistrict))?.districtNameEng || "N/A"}
                            </strong></Col>
                            <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />
                          </>

                          )}

                          {userProfie.personalInformation?.homeDistrict === "other" && (<>
                            <Col md="4" className=" p-2" style={{ fontSize: '13px' }}><strong> State : </strong> <strong className=" text-primary ml-2">
                              {stateName.find((type) => String(type.STATEID) === String(userProfie.personalInformation?.state))?.STATTENAME || "N/A"}
                            </strong></Col>
                            <Col md="4" className=" p-2" style={{ fontSize: '13px' }}><strong>Other District:</strong> <strong className=" text-primary ml-2">
                              {stateWiseDistrict.find((type) => String(type._id) === String(userProfie.personalInformation?.otherDistrict))?.allDistrictName || "N/A"}

                            </strong></Col></>
                          )}

                          <Col md="4" className=" p-2" style={{ fontSize: '13px' }}><strong> Disability:</strong> <strong className=" text-primary ml-2">{userProfie.personalInformation?.disability === "true" ? "Yes" : "No"}</strong></Col>


                          {userProfie.personalInformation?.disability === "true" && (
                            <>
                              <Col md="4" className=" p-2" style={{ fontSize: '13px' }}><strong>Type of Disability :  </strong>

                                <strong className=" text-primary ml-2">{userProfie.personalInformation?.disabiltyTypes}</strong>

                              </Col>
                            </>
                          )}

                          <Col md="8" className="p-2  " style={{ fontSize: '13px' }}>
                            <strong>Permanent Home Address:</strong>
                            <strong className="ml-3 text-primary">{user.address}</strong>

                          </Col>




                        </Row>
                      </table>
                    </div>
                  </TabPane>

                  <TabPane tabId="2">
                    <div className="container border rounded shadow">
                      <h3 className="mb-4 bg-info m-2 border rounded text-white text-center p-2" style={{ fontSize: '1rem' }}>
                        <i className="ni ni-hat-3 text-danger"></i> Academic Information
                      </h3>
                      <table className="table w-100">
                        <tbody>
                          <tr>
                            <td className="col-3 ml-2" style={{ fontSize: '13px' }}>
                              <strong> Category, in which service joined:</strong>
                            </td>
                            <td className="col-3 ml-2 text-primary" style={{ fontSize: '13px' }} ><strong>{userProfie.academicInformation?.appointmentCategory}</strong></td>

                            <td className="col-3 ml-2"><strong style={{ fontSize: '13px' }}> Qualification:</strong></td>
                            <td className="col-3 ml-2 text-primary" style={{ fontSize: '13px' }}><strong>{userProfie.academicInformation?.educationQualification}</strong></td>
                          </tr>

                          <tr>
                            {userProfie.academicInformation?.educationQualification === "Other" && (
                              <>
                                <td className="col-3 ml-2" style={{ fontSize: '13px' }}><strong >Other Qualification:</strong></td>
                                <td className="col-3 ml-2 text-primary"><strong>{userProfie.academicInformation?.qualificationDetails}</strong></td>
                              </>
                            )}
                            <td className="col-3 ml-2" style={{ fontSize: '13px' }}><strong> Workplace Division:</strong></td>
                            <td className="col-3 ml-2 text-primary" style={{ fontSize: '13px' }}>
                              <strong>
                                {division.find((type) => String(type.divisionCode) === String(userProfie.academicInformation?.workplaceDivision))?.name}
                              </strong>
                            </td>
                          </tr>

                          <tr>
                            <td className="col-3 ml-2" style={{ fontSize: '13px' }}><strong> Workplace District:</strong></td>
                            <td className="col-3 ml-2 text-primary" style={{ fontSize: '13px' }}>
                              <strong>
                                {district.find((type) => String(type.LGDCode) === String(userProfie.academicInformation?.workplaceDistrict))?.districtNameEng}
                              </strong>
                            </td>

                            <td className="col-3 ml-2" style={{ fontSize: '13px' }}><strong> Workplace Vidhansabha:</strong></td>
                            <td className="col-3 ml-2 text-primary" style={{ fontSize: '13px' }}><strong>

                              {vidhansabha.find((type) => String(type.ConstituencyNumber) === String(userProfie.academicInformation?.workplaceVidhansabha))?.ConstituencyName}
                            </strong></td>
                          </tr>


                        </tbody>
                      </table>
                    </div>
                  </TabPane>

                  <TabPane tabId="3">
                    <div>
                      <h3 className="mb-4 bg-info m-2 p-2 border rounded text-white text-center" style={{ fontSize: '1rem' }}>
                        <i className="ni ni-briefcase-24 text-primary"></i> Employee Information
                      </h3>



                      <table className="table w-100 ">
                        <Row style={{ fontSize: '13px' }} >
                          <Col md="6">
                            <label> <strong> Class:</strong>  <strong className="text-primary ml-2">{getClassName(userProfie.employeeInformation?.employeeClass)}</strong> </label>

                          </Col>
                          <Col md="6">
                            <label><strong> Employee Type:</strong> <strong className="text-primary ml-2">{userProfie.employeeInformation?.employeeType}</strong></label>

                          </Col>
                          <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          <Col md="6">
                            <label><strong> Pay Scale Type:</strong> <strong className="text-primary ml-2">{userProfie.employeeInformation?.payScaleType}</strong></label>

                          </Col>

                          {userProfie.employeeInformation?.payScaleType === "UGC" && (<>

                            <Col md="6">

                              <label><strong>Current AGP :</strong> <strong className=" text-primary">{userProfie.employeeInformation?.CurrentAGP}</strong></label>
                            </Col>
                            <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          </>)}

                          {userProfie.employeeInformation?.CurrentAGP === "7000" && (
                            <>
                              <Col md="6">

                                <label ><strong>  Date of Getting AGP 7000 :</strong> <strong className=" text-primary">{formatDateForInput(userProfie.employeeInformation?.dateOfAGPSevenThous)}</strong></label>

                              </Col>


                              <Col md="6">
                                <label ><strong>  Basic pay in 7th Pay Scale <br />after getting AGP 7000 :</strong>
                                  <strong className=" text-primary">{userProfie.employeeInformation?.basicPayscaleSevenAGP}</strong>
                                </label>

                              </Col>
                              <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                            </>)}
                          {userProfie.employeeInformation?.CurrentAGP === "8000" && (<>
                            <Col md="6">

                              <label>  <strong>  Date of Getting AGP 8000 :</strong>
                                <strong className=" text-primary">{formatDateForInput(userProfie.employeeInformation?.dateOfAGPEightThous)}</strong>
                              </label>
                            </Col>

                            <Col md="6">

                              <label><strong> Basic pay in 7th Pay Scale<br></br> after getting AGP 8000 :</strong>
                                <strong className=" text-primary">{userProfie.employeeInformation?.basicPayscaleEightAGP} </strong>
                              </label>

                            </Col>
                            <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          </>)}
                          {userProfie.employeeInformation?.CurrentAGP === "9000" && (<>
                            <Col md="6">
                              <label className="" style={{ fontSize: '13px' }}><strong>Date of getting AGP 9000 :</strong>
                                <strong className="text-primary">{formatDateForInput(userProfie.employeeInformation?.dateOfAGPNineThous)}</strong>
                              </label>

                            </Col>

                            <Col md="6">
                              <label className="" style={{ fontSize: '13px' }}><strong>Basic pay in 7th Pay Scale<br></br> after getting AGP 9000 :</strong></label>
                              <strong className=" text-primary">{userProfie.employeeInformation?.basicPayscaleNineAGP} </strong>
                            </Col>
                            <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          </>)}



                          <Col md="6" style={{ fontSize: '13px' }}>
                            <strong> Retirement Date:</strong> <strong className=" ml-3 text-primary">{formatDateForInput(userProfie.employeeInformation?.retirementDate)}
                            </strong>
                          </Col>
                          <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          <Col md="6" style={{ fontSize: '13px' }}><strong> Designation of First Regular Appointment:</strong>
                            <strong className=" ml-3 text-primary">
                              {designationData.find((type) => String(type._id) === String(userProfie.employeeInformation?.firstRegularAppointmentDesignation))?.designation || "N/A"}
                            </strong>
                          </Col>

                          <Col className="col-6" style={{ fontSize: '13px' }}><strong> Appointment Type:</strong>
                            <strong className="ml-3 text-primary">
                              {userProfie.employeeInformation?.appointmentType}
                            </strong>
                          </Col>
                          <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                          {userProfie.employeeInformation?.appointmentType === "Anukampa" && (<>
                            <Col className="col-6" style={{ fontSize: '13px' }}><strong> Date of Anukampa Niyukti:</strong>
                              <strong className="ml-3 text-primary"> {formatDateForInput(userProfie.employeeInformation?.dateOfAnu)}</strong>

                            </Col>

                          </>
                          )}

                          {userProfie.employeeInformation?.appointmentType === "Samviliyan" && (<>

                            <Col className="col-6 " style={{ fontSize: '13px' }}><strong>Date of Samviliyan:</strong>
                              <strong className="ml-3 text-primary">{formatDateForInput(userProfie.employeeInformation?.dateOfSam)}</strong>
                            </Col>

                          </>
                          )}

                          {userProfie.employeeInformation?.appointmentType === "Ad hoc" && (
                            <>
                              <Col className=" col-6"><strong> Date of Adhoc Appointment:</strong>
                                <strong className="ml-3 text-primary" >{formatDateForInput(userProfie.employeeInformation?.dateOfAdhoc)}</strong>

                              </Col>
                              <Col className=" col-6"><strong> Date of Regular Appointment:</strong>
                                <strong className="ml-3 text-primary">{formatDateForInput(userProfie.employeeInformation?.dateofRegularApponitment)}</strong>
                              </Col>
                              <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                            </>
                          )}
                          {/* <tr className="p-3 m-2"> */}
                          {getClassName(userProfie.employeeInformation?.employeeClass) ===
                            "Class 3" ||
                            getClassName(userProfie.employeeInformation?.employeeClass) ===
                            "Class 4" ? (
                            <>
                              <Col className=" col-6"><strong> Upload 12th Marksheet:</strong>
                                <a href={`https://heonline.cg.nic.in/${userProfie.employeeInformation?.uploadsMarksheet}`} download>
                                  <Button className="btn-sm btn-success ml-3" >
                                    {userProfie.employeeInformation?.uploadsMarksheet ? <FaDownload size={12} /> : "No File"}
                                  </Button>
                                </a>
                              </Col>
                            </>

                          ) : null}
                          {getClassName(userProfie.employeeInformation?.employeeClass) ===
                            "Class 3" ? (<>
                              <Col className="col-6"><strong> Accounts Trained:</strong>
                                <strong className="ml-3 text-primary">{userProfie.employeeInformation?.IsAccountsTrained === "true" ? "Yes" : "No"}</strong>
                              </Col>
                              <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                            </>
                          ) : null}
                          {userProfie.employeeInformation?.IsAccountsTrained === "true" && (
                            <>
                              <Col className=" col-6"><strong > Accounts Training Pass?</strong>
                                <strong className="ml-3 text-primary">
                                  {userProfie.employeeInformation?.IsAccountsTrainingPass === "true" ? "Yes" : "No"}</strong>
                              </Col>
                            </>
                          )}
                          {userProfie.employeeInformation?.IsAccountsTrainingPass === "true" && (
                            <>
                              <Col className=" col-6"><strong> Upload Accounts Training Certificate:</strong>
                                <a href={`https://heonline.cg.nic.in/${userProfie.employeeInformation?.uploadAccTCertificate}`} download>
                                  <Button className="btn-sm btn-success ml-3" >
                                    {userProfie.employeeInformation?.uploadAccTCertificate ? <FaDownload size={12} /> : "No File"}
                                  </Button>
                                </a>
                              </Col>
                              <hr style={{ width: "100%", margin: "5px 0", padding: "1" }} />

                            </>
                          )}

                          {userProfie.employeeInformation?.appointmentType === "Anukampa" && (<>
                            <Col className="col-6" style={{ fontSize: '13px' }}><strong> Typing Pass<br /><h5>(if appointment type - Anukampa)</h5> </strong>
                              <strong className="ml-3 text-primary"> {userProfie.employeeInformation?.TypingPass === "true" ? "Yes" : "No"}</strong>

                            </Col>
                          </>)}
                          {userProfie.employeeInformation?.TypingPass === "true" && (<>
                            <Col className="col-6 " style={{ fontSize: '13px' }}><strong> Upload Typing Pass Certificate</strong>
                              <a href={`https://heonline.cg.nic.in/${userProfie.employeeInformation?.uploadTypingPassCertificate}`} download>
                                <Button className="btn-sm btn-success ml-3" >
                                  {userProfie.employeeInformation?.uploadTypingPassCertificate ? <FaDownload size={12} /> : "No File"}
                                </Button>
                              </a>
                            </Col>
                          </>)}
                        </Row>

                      </table>


                    </div>
                  </TabPane>


                  <TabPane tabId="4">
                    <div >
                      <h3 className="mb-4 bg-info m-2 p-2 border rounded text-white text-center" style={{ fontSize: '1rem' }}>
                        <i className="ni ni-badge text-primary"></i> Present Position
                      </h3>


                      <table className="table w-100 table-responsive ">

                        <tbody>
                          <tr>
                            <td className="p-2" style={{ fontSize: '13px' }}><strong>Present Post (Designation) :</strong>
                              <strong className="ml-3 text-primary" >
                                {designationData.find((type) => String(type._id) === String(userProfie.presentInformation?.presentPost))?.designation || "N/A"}

                              </strong>
                            </td>
                            {userProfie.presentInformation?.firstRegularAppointmentDesignation !==
                              userProfie.presentInformation?.presentPost && (
                                <td className=" p-2" style={{ fontSize: '13px' }}><strong>Date of joining present post : </strong>
                                  <strong className="ml-4 text-primary">{formatDateForInput(userProfie.presentInformation?.dateOfPresentClgPosting)}</strong></td>
                              )}

                          </tr>
                          <tr>
                            <td className=" p-2 " style={{ fontSize: '13px' }}><strong> Date of First Appointment:</strong>
                              <strong className="ml-4 text-primary">{formatDateForInput(userProfie.presentInformation?.appointmentDate)}</strong></td>
                            <td className=" p-2" style={{ fontSize: '13px' }}><strong>Date of posting in Present College</strong>
                              <strong className="ml-4 text-primary">{formatDateForInput(userProfie.presentInformation?.dateOfPresentClgPosting)}</strong></td>

                          </tr>
                          <tr>
                            <td className=" p-2" style={{ fontSize: '13px' }}><strong>Name of present college  :</strong>
                              <strong className="ml-4 text-primary">{userProfie.presentInformation?.collegeName}</strong></td>
                            <td className=" p-2" style={{ fontSize: '13px' }}><strong>Probation Complete Status :</strong>
                              <strong className="ml-4 text-primary">{String(userProfie.presentInformation?.probationCompleteStatus)}</strong></td>

                          </tr>
                          <tr>


                            {userProfie.presentInformation?.probationCompleteStatus ===
                              "completion" && (<>
                                <td className=" p-2" style={{ fontSize: '13px' }}><strong>Date of Completion : </strong>
                                  <strong className="ml-4 text-primary">{formatDateForInput(userProfie.presentInformation?.dateofCompletion)}</strong> </td>
                                <td></td>
                              </>
                              )}
                          </tr>
                          <tr>
                            {userProfie.presentInformation?.probationCompleteStatus ===
                              "onProbation" && (<>
                                <td className=" p-2" style={{ fontSize: '13px' }}><strong> Police Verification :</strong>
                                  <strong className="ml-4 text-primary">{String(userProfie.presentInformation?.policeVerification)}</strong></td></>)}
                            {/* {userProfie.presentInformation?.policeVerification === "Completed" && (<>
                              <td className=" p-2" style={{ fontSize: '13px' }}>
                                <strong >Upload Police Verification : </strong>     <a href={`https://heonline.cg.nic.in/${userProfie.presentInformation?.uploadPoliceverification}`} download>
                                  <Button className="btn-sm btn-success ml-3" >
                                    {userProfie.presentInformation?.uploadPoliceverification ? <FaDownload size={12} /> : "No File"}
                                  </Button>
                                </a>
                              </td></>)} */}
                          </tr>
                          <tr>
                            <td className=" p-2" style={{ fontSize: '13px' }}><strong> Present Basic Pay (7th Pay) :</strong>
                              <strong className="ml-4 text-primary">

                                {basicPay.find((type) => String(type._id) === String(userProfie.presentInformation?.presentBasicpay))?.basicPayScale || "N/A"}

                              </strong>
                            </td>
                            <td className=" p-2" style={{ fontSize: '13px' }}>
                              <strong>Present Pay Level (7th Pay) :</strong>
                              <strong className="ml-4 text-primary">{userProfie.presentInformation?.presentPayLevel}</strong>
                            </td>

                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </TabPane>


                  <TabPane tabId="5">
                    <div className="container border rounded shadow">
                      <h3 className="mb-4 bg-info m-2 border rounded text-white text-center p-2" style={{ fontSize: '1rem' }}>
                        <i className="ni ni-collection text-primary"></i> Other Information
                      </h3>

                      <table className="table w-100">
                        <tbody>


                          <tr>
                            <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong >Availed 1st Timescale Pay :</strong></td>

                            <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.firstTimeScale === "true" ? "Yes" : "No"}</strong></td>

                            {userProfie.otherInformation?.firstTimeScale === "true" && (
                              <>
                                <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong> Order Date of 1st Time Scale</strong></td>

                                <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.firstScaleDate)}</strong></td>
                              </>)}
                          </tr>
                          <tr>
                            <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Availed 2nd Timescale Pay</strong></td>
                            <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.secondTimeScale === "true" ? "Yes" : "No"}</strong></td>
                            {userProfie.otherInformation?.secondTimeScale === "true" && (
                              <>
                                <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Order Date of 2nd Time Scale</strong></td>
                                <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.secondScaleDate)}</strong></td>
                              </>
                            )}
                          </tr>
                          <tr>
                            <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Availed 3rd Timescale Pay</strong></td>
                            <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.thirdTimeScale === "true" ? "Yes" : "No"}</strong></td>
                            {userProfie.otherInformation?.thirdTimeScale === "true" && (<>
                              <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Order Date of 3rd Time Scale</strong></td>
                              <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.thirdScaleDate)}</strong></td>
                            </>)}
                          </tr>
                        </tbody>
                      </table>
                      <h3 className="mb-4 bg-info m-2 border rounded text-white text-center">
                        <i className="ni ni-collection text-primary"></i> Qualification Details
                      </h3>
                      <table className="table w-100">
                        <tbody>
                          <tr>
                            <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Latest Seniority List (Antim): </strong></td>
                            <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.latestSeniorityList}</strong></td>
                            {userProfie.otherInformation?.employeeType === "TEACHING" && (
                              <>
                                <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Subject (if Worktype - Teaching): </strong></td>
                                <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.subject}
                                </strong></td> </>)}
                          </tr>
                          <tr>
                            {userProfie.otherInformation?.employeeType === "TEACHING" && (<>
                              <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>M. Phil (if Worktype - Teaching): </strong></td>
                              <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.Mphil === "true" ? "Yes" : "No"}</strong></td>
                            </>)}
                            {userProfie.otherInformation?.Mphil === "true" && (<>
                              <td className="col-2  p-2" style={{ fontSize: '13px' }}><strong>Subject of M.Phil (if Mphil): </strong></td>
                              <td className="col-4  p-2" style={{ fontSize: '13px' }}><strong className=" text-primary">
                                {userProfie.otherInformation?.subjectOfMphil}

                              </strong></td></>)}
                          </tr>
                          <tr>
                            {userProfie.otherInformation?.Mphil === "true" && (<>
                              <td className="col-2" style={{ fontSize: '13px' }}><strong> M.Phil award date/year (if Mphil)</strong></td>
                              <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.MphilAwardDate)}</strong></td>
                            </>)}
                            <td className="col-2" style={{ fontSize: '13px' }}><strong>Phd</strong></td>
                            <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">
                              {userProfie.otherInformation?.phd === "true" ? "Yes" : "No"}
                            </strong></td>
                          </tr>
                          {userProfie.otherInformation?.phd === "true" && (<>
                            <tr>

                              <td className="col-2" style={{ fontSize: '13px' }}><strong> Subject of Phd (if Phd)</strong></td>
                              <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">
                                {userProfie.otherInformation?.subjectOfPhd}

                              </strong></td>
                              <td className="col-2" style={{ fontSize: '13px' }}><strong>Phd notification date</strong></td>
                              <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.phdNotifyDate)}</strong></td>
                            </tr></>)}
                          <tr>
                            <td className="col-2" style={{ fontSize: '13px' }}><strong>DSc/DLitt</strong></td>
                            <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.DscOrDlit === "true" ? "Yes" : "No"}</strong></td>
                            {userProfie.otherInformation?.DscOrDlit === "true" && (<>
                              <td className="col-2" style={{ fontSize: '13px' }}><strong> Subject of DSc/DLitt</strong></td>
                              <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">
                                {userProfie.otherInformation?.subjectOfDscOrDlit}
                              </strong></td></>)}
                          </tr>
                          <tr>
                            {userProfie.otherInformation?.DscOrDlit === "true" && (<>
                              <td className="col-2" style={{ fontSize: '13px' }}><strong> DSc/DLitt notification date</strong></td>
                              <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.DscOrDlitNotifyDate)}</strong></td>
                            </>)}
                            <td className="col-2" style={{ fontSize: '13px' }}><strong> NET / SET</strong></td>
                            <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{userProfie.otherInformation?.NetSet === "true" ? "Yes" : "No"}</strong></td>

                          </tr><tr>
                            {userProfie.otherInformation?.NetSet === "true" && (
                              <>
                                <td className="col-2" style={{ fontSize: '13px' }}><strong>NET/SET Certificate Date</strong></td>
                                <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary">{formatDateForInput(userProfie.otherInformation?.NetSetCertificateDate)}</strong></td>
                              </>
                            )}
                            <td></td>
                            <td></td>
                          </tr>


                        </tbody>
                      </table>

                      <table className="table w-100">
                        <tbody>


                          <tr>


                            <td className="col-2" style={{ fontSize: '13px' }}><strong > Court Case Status :</strong></td>
                            <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary"> {userProfie.otherInformation?.courtCaseStatus}</strong></td>
                            <td className="col-2" style={{ fontSize: '13px' }}><strong>Pending Deaprtmental <br></br>Enquire / Prosecution
                              Case :</strong></td>
                            <td className="col-4" style={{ fontSize: '13px' }}><strong className=" text-primary" >
                              {userProfie.otherInformation?.PendingDeptEnquireCase === "true" ? "Yes" : "No"}
                            </strong></td>
                          </tr>

                        </tbody>
                      </table>
                    </div>
                  </TabPane>

                </TabContent>
                {/* Custom Styling */}

              </CardBody>
            </Card>
          </Col>

        </Row>
      </Container>
    </>
  );
};

export default Profile;