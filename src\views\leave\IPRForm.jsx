import { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import {
  Form,
  Input,
  Container,
  Row,
  <PERSON>ton,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  FormGroup,
  Label,
  Col,
  Table,
} from "reactstrap";
import { Card, CardHeader, CardBody } from "react-bootstrap";
import loadable from "@loadable/component";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import Swal from "sweetalert2";
const Header = loadable(() => import("../../components/Headers/Header.jsx"));
const isArrayEmpty = (arr) => Array.isArray(arr) && arr.length === 0;
const isCurrentDateInRange = (startDate, endDate) => {
  const currentDate = new Date();
  return currentDate >= new Date(startDate) && currentDate <= new Date(endDate);
};

const IPRForm = () => {
  const navigate = useNavigate();
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const userId = sessionStorage.getItem("id");

  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [vivranDate, setvivranDate] = useState(lastYear);
  // // console.log(vivranDate);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newIprFormModalOpen, setnewIprFormModalOpen] = useState(false);
  const toggleModal = () => setIsModalOpen((prev) => !prev);

  const [iprFiled, setIPRFiledData] = useState([]);


  useEffect(() => {
    const fetchFiledIpr = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/ipr/get-employee-wise/${userId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          if (data.iprFinalSubmit) {
            Swal.fire({
              position: "top-end",
              icon: "success",
              title: `Your property report for year ${lastYear} Submitted Successfully`,
              showConfirmButton: false,
              timer: 5000,
            });
            setIsModalOpen(false);
            setTimeout(() => {
              navigate("/admin/iprlist/");
            }, 5000);
          } else {
            setnewIprFormModalOpen(true);
          }
          setIPRFiledData(data);
          setFormData({ ...data, property: data.properties });
          setTableData(data.properties);
        } else {
          setIsModalOpen(true);
        }
      } catch (error) {
        if (error.response) {
          const errorMessage =
            error.response.data?.msg ||
            "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }
    };
    fetchFiledIpr();
  }, [token, endPoint, userId]);

  
  useEffect(() => {
    const fetchDateRange = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/ipr-date/logs?limit=1`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          const isDateInRange = isCurrentDateInRange(
            data.dateFrom,
            data.dateTo
          );
          if (isDateInRange) {
            if (!isArrayEmpty(iprFiled)) {
              if (
                iprFiled.iprFinalSubmit !== undefined ||
                (!iprFiled.iprFinalSubmit &&
                  iprFiled.iprAccepted !== undefined) ||
                !iprFiled.iprAccepted
              ) {
                setnewIprFormModalOpen(true);
              } else {
                setIsModalOpen(true);
              }
            }
          } else {
            const dateTo = new Date(data.dateTo);
            const currentDate = new Date();

            if (isNaN(dateTo)) {
              Swal.fire({
                position: "top-end",
                icon: "error",
                title: "Invalid IPR date format.",
                showConfirmButton: false,
                timer: 5000,
              });
              setIsModalOpen(false);
              setTimeout(() => {
                navigate("/admin/leave-dashboard/");
              }, 5000);
            }
            const yearDateTo = dateTo.getFullYear();
            const yearCurrent = currentDate.getFullYear();
            if (yearDateTo === yearCurrent) {
              Swal.fire({
                position: "top-end",
                icon: "error",
                title:
                  "With In Given Time You Not Filled IPR.Please contact to the Administrator.",
                showConfirmButton: false,
                timer: 5000,
              });
              setIsModalOpen(false);
              setTimeout(() => {
                navigate("/admin/leave-dashboard/");
              }, 5000);
            } else {
              Swal.fire({
                position: "top-end",
                icon: "error",
                title: "IPR Form Not Activated By State Admin.",
                showConfirmButton: false,
                timer: 5000,
              });
              setIsModalOpen(false);
              setTimeout(() => {
                navigate("/admin/leave-dashboard/");
              }, 5000);
            }
          }
        } else {
          setIsModalOpen(false);
          SwalMessageAlert(
            "No Date Range Added. Please Contact to State Admin!",
            "error"
          );
          setTimeout(() => {
            navigate("/admin/iprlist/");
          }, 5000);
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    fetchDateRange();
  }, []);

  //Fetch Last IPR Employee
  const [noIPRFound, setnoIPRFound] = useState(null);

  useEffect(() => {
    const fetchLastIPR = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/ipr/last-ipr/${userId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setnoIPRFound(formatDate(data.createdAt));
        } else {
          setnoIPRFound("No IPR Found");
        }
      } catch (error) {
        handleApiError(error);
      }
    };
    fetchLastIPR();
  }, []);

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };

  const [formData, setFormData] = useState({
    employeeName: "",
    currentDesignation: "",
    officeAddress: "",
    currentSalary: "",
    nextIncrementDate: "",
    property: [
      {
        location: "",
        details: "",
        price: "",
        ownerName: "",
        ownerRelation: "",
        purchaseType: "",
        purchaseDate: "",
        sellerName: "",
        sellerDetails: "",
        incomeFromProperty: "",
        remarks: "",
      },
    ],
  });
  const handleSelectLastIpr = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/ipr/employee-last/${userId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setnewIprFormModalOpen(true);
        setFormData({ ...data, property: data.properties });
        setTableData(data.properties);
      } else {
        alert("No Last IPR Found");
        setIsModalOpen(true);
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);
  const togglePreviewnewIprFormModalOpen = () =>
    setnewIprFormModalOpen(!newIprFormModalOpen);

  const [tableData, setTableData] = useState([]);
  const handleAddRowTable1 = () => {
    if (
      !formData.location ||
      !formData.details ||
      !formData.price ||
      !formData.ownerName ||
      !formData.ownerRelation ||
      !formData.purchaseType ||
      !formData.purchaseDate ||
      !formData.sellerName ||
      !formData.sellerDetails ||
      !formData.incomeFromProperty
      ) {
      SwalMessageAlert("कृपया सभी आवश्यक जानकारी भरें।", "warning");
      return;
    }
    setTableData([...tableData, formData]);
    setFormData((prevData) => ({
      ...prevData,
      location: "",
      details: "",
      price: "",
      ownerName: "",
      ownerRelation: "",
      purchaseType: "",
      purchaseDate: "",
      sellerName: "",
      sellerDetails: "",
      incomeFromProperty: "",
      remarks: "",
    }));
  };
  const handleRemoveRowTable1 = (index) => {
    const newData = tableData.filter((_, i) => i !== index);
    setTableData(newData);
  };

  const handleCreateNewIpr = () => {
    setIsModalOpen(false);
    setnewIprFormModalOpen(true);
    setTableData([]);
  };

  const [user, setUser] = useState("");
  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee-byId/${userId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data[0];
          setUser(data);
        } else {
          SwalMessageAlert("Data Not Fetched.", "error");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchEmployeeData();
  }, [token, endPoint, userId]);
  useEffect(() => {
    if (user) {
      setFormData((prevData) => ({
        ...prevData, // Keep existing form data
        employeeName: user.name, // Set the employeeName from user object
        currentDesignation: user["designationDetails"].designation,
        officeAddress: user["collegeDetails"].address,
        currentSalary: user.currentSalary,
        nextIncrementDate: user.nextIncrementDate,
      }));
    }
  }, [user]);



  const handleNoProperty = async () => {
    const result = await Swal.fire({
      title: 'Confirmation',
      text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं कि आपके पास कोई संपत्ति नहीं है?",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, submit',
      cancelButtonText: 'No, cancel'
    });

    if (result.isConfirmed) {
      const body = {
        employeeId: userId,
        employeeName: formData.employeeName,
        currentDesignation: formData.currentDesignation,
        officeAddress: formData.officeAddress,
        currentSalary: formData.currentSalary,
        nextIncrementDate: formData.nextIncrementDate,
        property: null,
      };


      try {
        const response = await axios.post(
          `${endPoint}/api/ipr/add`,
          { ...body },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert(response.data.msg, "success");
          setTimeout(() => {
            window.location.reload();
          }, 5000);
          setFormData({
            employeeName: "",
            currentDesignation: "",
            officeAddress: "",
            currentSalary: "",
            nextIncrementDate: "",
            property: [
              {
                location: "",
                details: "",
                price: "",
                ownerName: "",
                ownerRelation: "",
                purchaseType: "",
                purchaseDate: "",
                sellerName: "",
                sellerDetails: "",
                incomeFromProperty: "",
                remarks: "",
              },
            ],
          });
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }

      } catch (error) {
        if (error.response) {
          const errorMessage =
            error.response.data?.msg || "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }

      // console.log("User  confirmed they don't have any property.");
    } else {
      // console.log("User  canceled the action.");
    }
  };

  const handleSubmit = (action) => async (e) => {
    // if (tableData && isArrayEmpty(tableData)) {
    //   SwalMessageAlert("कृपया सभी आवश्यक जानकारी भरें।", "warning");


    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    //   return;
    // }
    const Property = tableData.map((item) => ({
      location: item.location, // Default to empty string if undefined
      details: item.details,
      price: item.price,
      ownerName: item.ownerName,
      ownerRelation: item.ownerRelation,
      purchaseType: item.purchaseType,
      purchaseDate: item.purchaseDate,
      sellerName: item.sellerName,
      sellerDetails: item.sellerDetails,
      incomeFromProperty: item.incomeFromProperty,
      remarks: item.remarks,
    }));

    const body = {
      employeeId: userId,
      employeeName: formData.employeeName,
      currentDesignation: formData.currentDesignation,
      officeAddress: formData.officeAddress,
      currentSalary: formData.currentSalary,
      nextIncrementDate: formData.nextIncrementDate,
      property: Property,
    };
    try {
      if (action === "create") {
        const response = await axios.post(
          `${endPoint}/api/ipr/add`,
          { ...body },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert(response.data.msg, "success");
          setTimeout(() => {
            window.location.reload();
          }, 5000);
          setFormData({
            employeeName: "",
            currentDesignation: "",
            officeAddress: "",
            currentSalary: "",
            nextIncrementDate: "",
            property: [
              {
                location: "",
                details: "",
                price: "",
                ownerName: "",
                ownerRelation: "",
                purchaseType: "",
                purchaseDate: "",
                sellerName: "",
                sellerDetails: "",
                incomeFromProperty: "",
                remarks: "",
              },
            ],
          });
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      } else {
        const response = await axios.put(
          `${endPoint}/api/ipr/update/${iprFiled._id}`,
          { ...body },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("IPR Submitted Successfully", "success");
          setTimeout(() => {
            window.location.reload();
          }, 5000);
          setFormData({
            employeeName: "",
            currentDesignation: "",
            officeAddress: "",
            currentSalary: "",
            nextIncrementDate: "",
            property: [
              {
                location: "",
                details: "",
                price: "",
                ownerName: "",
                ownerRelation: "",
                purchaseType: "",
                purchaseDate: "",
                sellerName: "",
                sellerDetails: "",
                incomeFromProperty: "",
                remarks: "",
              },
            ],
          });
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };


const mandtory ={
  color :"red",
  fontSize:"20px"
}



  const handleFinalSubmit = (id) => async () => {
    try {
      if (tableData && isArrayEmpty(tableData)) {
        SwalMessageAlert("कृपया सभी आवश्यक जानकारी भरें।", "warning");
        return;
      }
      const Property = tableData.map((item) => ({
        location: item.location, // Default to empty string if undefined
        details: item.details,
        price: item.price,
        ownerName: item.ownerName,
        ownerRelation: item.ownerRelation,
        purchaseType: item.purchaseType,
        purchaseDate: item.purchaseDate,
        sellerName: item.sellerName,
        sellerDetails: item.sellerDetails,
        incomeFromProperty: item.incomeFromProperty,
        remarks: item.remarks,
      }));

      const body = {
        employeeId: userId,
        employeeName: formData.employeeName,
        currentDesignation: formData.currentDesignation,
        officeAddress: formData.officeAddress,
        currentSalary: formData.currentSalary,
        nextIncrementDate: formData.nextIncrementDate,
        property: Property,
      };
      const iprID = id;
      const response = await axios.put(
        `${endPoint}/api/ipr/status-final-update/${iprID}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert(response.data.msg, "success");
        setTimeout(() => {
          window.location.reload();
        }, 5000);
        setFormData({
          employeeName: "",
          currentDesignation: "",
          officeAddress: "",
          currentSalary: "",
          nextIncrementDate: "",
          property: [
            {
              location: "",
              details: "",
              price: "",
              ownerName: "",
              ownerRelation: "",
              purchaseType: "",
              purchaseDate: "",
              sellerName: "",
              sellerDetails: "",
              incomeFromProperty: "",
              remarks: "",
            },
          ],
        });
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Modal
          isOpen={isModalOpen}
          toggle={toggleModal}
          backdrop="static"
          keyboard={false}
        >
          <ModalHeader>
            IPR / अचल संपत्ति विवरण{" "}
            <span style={{ color: "red" }}>( Last IPR : {noIPRFound} )</span>
          </ModalHeader>
          <Form>
            <ModalBody>
              <Row className="justify-content-center">
                <FormGroup>
                  <div
                    className="row justify-content-around align-items-center"
                    style={{ gap: "15px" }}
                  >
                    <ul>
                      {noIPRFound !== "No IPR Found" ? (
                        <>
                          <li>
                            <FormGroup
                              check
                              className="d-flex align-items-center"
                            >
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-old"
                                value="1"
                                onClick={handleSelectLastIpr}
                              />
                              <Label
                                for="input-lead-old"
                                check
                                className="ml-2"
                              >
                                पिछला अचल संपत्ति विवरण चुने / Select From Old
                                IPR
                              </Label>
                            </FormGroup>
                          </li>
                          <li>
                            <FormGroup
                              check
                              className="d-flex align-items-center"
                            >
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-new"
                                value="0"
                                onClick={handleCreateNewIpr}
                              />
                              <Label
                                for="input-lead-new"
                                check
                                className="ml-2"
                              >
                                नया अचल संपत्ति विवरण भरे / Create New
                              </Label>
                            </FormGroup>
                          </li>
                          <li>
                            <FormGroup
                              check
                              className="d-flex align-items-center"
                            >
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-new"
                                value="0"
                                onClick={handleNoProperty}
                              />
                              <Label
                                for="input-lead-new"
                                check
                                className="ml-2"
                              >
                                संपत्ति न होने की स्थिति में / If No Property
                              </Label>
                            </FormGroup>
                          </li>
                        </>
                      ) : (
                        <>
                          <li>
                            <FormGroup
                              check
                              className="d-flex align-items-center"
                            >
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-new"
                                value="0"
                                onClick={handleCreateNewIpr}
                              />
                              <Label
                                for="input-lead-new"
                                check
                                className="ml-2"
                              >
                                नया अचल संपत्ति विवरण भरे / Create New
                              </Label>
                            </FormGroup>
                          </li>
                          <li>
                            <FormGroup
                              check
                              className="d-flex align-items-center"
                            >
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead-new"
                                value="0"
                                onClick={handleNoProperty}
                              />
                              <Label
                                for="input-lead-new"
                                check
                                className="ml-2"
                              >
                                संपत्ति न होने की स्थिति में / If No Property
                              </Label>
                            </FormGroup>
                          </li>
                        </>
                      )}
                    </ul>
                  </div>
                </FormGroup>
              </Row>
            </ModalBody>
          </Form>
        </Modal>
        <Modal
          style={{ maxWidth: "80%", minWidth: "800px" }}
          isOpen={newIprFormModalOpen}
          toggle={toggleModal}
          backdrop="static"
          keyboard={false}
        >
          <Form>
            <ModalBody>
              <Card>
                <CardHeader style={{ lineBreak: "25px" }}>
                  <Row style={{ justifyContent: "center" }}>
                    <Col className="text-center">
                      <span style={{ fontSize: "20px", fontWeight: "bolder" }}>
                        प्रपत्र - एक
                      </span>
                    </Col>
                  </Row>
                  <Row style={{ justifyContent: "center" }}>
                    <Col className="text-center">
                      <span style={{ fontSize: "18px", fontWeight: "bolder" }}>
                        'शासकीय सेवक का अचल सम्पत्ति का वार्षिक विवरण : - 31.12.
                        {vivranDate} की स्थिति में '
                      </span>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody style={{ lineHeight: "10px" }}>
                  <Form onSubmit={handleSubmit}>
                    <h4
                      className="text-danger text-left"
                      style={{ lineHeight: "18px" }}
                    >
                      Note : <br /> 1. * कृपया एक समय में केवल एक ही संपत्ति की जानकारी
                      दें ! <br />
                      2. * सभी जानकारी भरना अनिवार्य है |
                      {" "}
                    </h4>
                    <br />
                    {(iprFiled && !isArrayEmpty(iprFiled)) ||
                      (isArrayEmpty(iprFiled) &&
                        iprFiled.iprFinalSubmit !== false) ? (
                      <Table bordered style={{ lineHeight: "20px" }}>
                        <tbody>
                          <tr>
                            <td>
                              संभाग, जिला तथा ग्राम का नाम जिसमे संपत्ति स्थित
                              हो <span style={mandtory}>*</span> <br />
                              <textarea
                                name="location"
                                value={formData.location}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              संपत्ति का नाम तथा ब्यौरा गृह तथा भूमि अन्य भवन <span style={mandtory}>*</span> {" "}
                              <br />
                              <textarea
                                name="details"
                                value={formData.details}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              वर्तमान मूल्य{" "}
                              <span className="font-weight-bold text-info">
                                (₹)
                              </span><span style={mandtory}>*</span>{" "}
                              : <br />
                              <Input
                                type="text"
                                name="price"
                                value={formData.price}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              यदि स्वयं के नाम पर न हों तो बताइये कि वह जिसके
                              नाम पर धारित है <span style={mandtory}>*</span><br />
                              <textarea
                                name="ownerName"
                                value={formData.ownerName}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              और उनका शासकीय कर्मचारी से क्या सम्बन्ध है <span style={mandtory}>*</span>{" "}
                              <br />
                              <textarea
                                name="ownerRelation"
                                value={formData.ownerRelation}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              उसे किस प्रकार अर्जित किया गया ,पट्टी बंधक ,विरासत
                              भेंट या अन्य किसी प्रकार से <span style={mandtory}>*</span> <br />
                              <textarea
                                name="purchaseType"
                                value={formData.purchaseType}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              अर्जन वर्ष <span style={mandtory}>*</span> {formData.purchaseDate}<br />
                              <select
                                name="purchaseDate"
                                value={formData.purchaseDate}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              >
                                <option value="">-- वर्ष चुनें --</option>
                                {Array.from({ length: 50 }, (_, index) => {
                                  const year = new Date().getFullYear() - index; // Current year minus index
                                  return (
                                    <option key={year} value={year}>
                                      {year}
                                    </option>
                                  );
                                })}
                              </select>
                            </td>
                            <td>
                              जिससे अर्जन की गयी उसका नाम <span style={mandtory}>*</span> <br />
                              <textarea
                                name="sellerName"
                                value={formData.sellerName}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              जिससे अर्जन की गयी उसका ब्यौरा <span style={mandtory}>*</span> <br />
                              <textarea
                                name="sellerDetails"
                                value={formData.sellerDetails}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                          </tr>
                          <tr>
                            <td>
                              संपत्ति से वार्षिक आय <span style={mandtory}>*</span> <br />
                              <Input
                                type="text"
                                name="incomeFromProperty"
                                value={formData.incomeFromProperty}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              अभियुक्ति : <br />
                              <textarea
                                name="remarks"
                                value={formData.remarks}
                                onChange={handleInputChange}
                                style={{
                                  border: "2px solid gray",
                                  borderRadius: "5px",
                                  padding: "10px",
                                  color: "gray",
                                  width: "100%",
                                }}
                              />
                            </td>
                            <td>
                              <Col sm={6} md={2}>
                                <Button
                                  color="primary"
                                  onClick={handleAddRowTable1}
                                >
                                  Add Row
                                </Button>
                                <br />
                                <br />
                              </Col>
                            </td>
                          </tr>
                        </tbody>
                      </Table>
                    ) : (
                      ""
                    )}

                    <br />
                    <br />
                    <Row>
                      <Table striped responsive bordered hover>
                        <thead>
                          <tr
                            style={{
                              fontSize: "12px",
                              textAlign: "center",
                              padding: "0px",
                              verticalAlign: "middle",
                            }}
                          >
                            <th>हटायें / क्र. </th>
                            <th>
                              संभाग, जिला तथा ग्राम का नाम जिसमे संपत्ति स्थित
                              हो
                            </th>
                            <th>
                              संपत्ति का नाम <br /> तथा ब्यौरा
                            </th>
                            <th>
                              वर्तमान <br /> मूल्य
                            </th>
                            <th>
                              जिसके नाम पर <br /> धारित है
                            </th>
                            <th>
                              शासकीय कर्मचारी से <br /> सम्बन्ध
                            </th>
                            <th>
                              अर्जन का <br /> प्रकार
                            </th>
                            <th>
                              अर्जन की <br /> तारीख
                            </th>
                            <th>
                              जिससे अर्जन की गयी <br /> उसका नाम
                            </th>
                            <th>
                              जिससे अर्जन की गयी <br /> उसका ब्यौरा
                            </th>
                            <th>
                              संपत्ति से वार्षिक <br /> आय
                            </th>
                            <th>अभियुक्ति</th>
                          </tr>
                        </thead>
                        <tbody>
                          {tableData && tableData.length > 0 && tableData.map((row, index) => (
                            <tr key={index}>
                              <td>
                                <Button
                                  className="btn btn-sm"
                                  color="danger"
                                  onClick={() => handleRemoveRowTable1(index)}
                                >
                                  X
                                </Button>{" "}
                                &nbsp; {index + 1}
                              </td>
                              <td>{row.location}</td>
                              <td>{row.details}</td>
                              <td>{row.price}</td>
                              <td>{row.ownerName}</td>
                              <td>{row.ownerRelation}</td>
                              <td>{row.purchaseType}</td>
                              <td>{row.purchaseDate}</td>
                              <td>{row.sellerName}</td>
                              <td>{row.sellerDetails}</td>
                              <td>{row.incomeFromProperty}</td>
                              <td>{row.remarks}</td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Row>
                    <br />
                    <Button color="primary" onClick={togglePreviewModal}>
                      Preview
                    </Button>
                    <Link to={`/admin/leave-dashboard/`}>
                      <Button
                        color="secondary"
                        onClick={togglePreviewnewIprFormModalOpen}
                      >
                        Cancel
                      </Button>
                    </Link>
                    {iprFiled &&
                      !isArrayEmpty(iprFiled) &&
                      iprFiled.iprFinalSubmit !== true ? (
                      <>
                        <Button
                          color="warning"
                          onClick={handleFinalSubmit(iprFiled._id)}
                        >
                          Final Submit
                        </Button>
                      </>
                    ) : (
                      ""
                    )}
                  </Form>
                  <Modal
                    isOpen={previewModal}
                    toggle={togglePreviewModal}
                    style={{
                      maxWidth: "1200px", // Adjust modal width
                      width: "90%", // Responsive width
                      textAlign: "center", // Center all text content
                    }}
                  >
                    <ModalHeader
                      style={{
                        justifyContent: "center",
                        textAlign: "center",
                        display: "flex", // Ensure centering
                        marginLeft: "140px", // Stack items vertically
                      }}
                      toggle={togglePreviewModal}
                    >
                      <CardHeader>
                        <Row
                          style={{
                            justifyContent: "center", // Center horizontally
                            display: "flex", // Use flexbox for alignment
                          }}
                        >
                          <Col></Col>
                          <Col className="text-center">
                            <span
                              style={{
                                fontSize: "24px",
                                fontWeight: "bolder",
                                display: "block", // Ensure block-level behavior
                              }}
                            >
                              प्रपत्र - एक
                            </span>
                          </Col>
                          <Col></Col>
                        </Row>
                        <Row
                          style={{
                            justifyContent: "center",
                            display: "flex",
                            marginTop: "10px", // Add some spacing between rows
                          }}
                        >
                          <Col md="12" className="text-center">
                            <span
                              style={{
                                fontSize: "24px",
                                fontWeight: "bolder",
                                display: "block",
                              }}
                            >
                              'शासकीय सेवक का अचल सम्पत्ति का वार्षिक विवरण : -
                              31.12.{vivranDate} की स्थिति में'
                            </span>
                          </Col>
                        </Row>
                      </CardHeader>
                    </ModalHeader>
                    <ModalBody>
                      <table
                        className="table"
                        style={{ lineHeight: "5px", textAlign: "left" }}
                      >
                        <tbody>
                          <tr>
                            <td>1.अधिकारी / कर्मचारी का पूरा नाम :</td>
                            <td>{formData.employeeName}</td>
                          </tr>
                          <tr>
                            <td>2.वर्तमान पद :</td>
                            <td>{formData.currentDesignation}</td>
                          </tr>
                          <tr>
                            <td>3.वर्तमान वेतन : </td>
                            <td>{formData.currentSalary}</td>
                          </tr>
                          <tr>
                            <td>4.कार्यालय / महाविद्यालय का नाम / पता :</td>
                            <td>{formData.officeAddress}</td>
                          </tr>
                          <tr>
                            <td>5.अगली वेतन वृद्धि की तारीख :</td>
                            <td>
                              {formData.nextIncrementDate
                                ? formatDate(formData.nextIncrementDate)
                                : ""}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                      <Row>
                        <Table striped responsive bordered hover>
                          <thead>
                            <tr
                              style={{
                                fontSize: "12px",
                                textAlign: "center",
                                padding: "0px",
                                verticalAlign: "middle",
                              }}
                            >
                              <th style={{ padding: "0", margin: "0" }}>
                                क्र.
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                संभाग, जिला तथा ग्राम का नाम <br /> जिसमे संपत्ति स्थित
                                हो
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                संपत्ति का नाम तथा <br /> ब्यौरा गृह तथा <br />{" "}
                                भूमि अन्य भवन .
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                वर्तमान मूल्य{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                यदि स्वयं के नाम <br /> पर न हों तो बताइये{" "}
                                <br /> कि वह जिसके <br /> नाम पर धारित है{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                और उनका <br />
                                शासकीय कर्मचारी <br /> से क्या सम्बन्ध है{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                उसे किस प्रकार <br /> अर्जित किया गया ,<br />{" "}
                                पट्टी बंधक ,विरासत भेंट
                                <br /> या अन्य किसी प्रकार से{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                अर्जन की तारीख{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                जिससे अर्जन की <br />
                                गयी उसका नाम{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                जिससे अर्जन की <br /> गयी उसका ब्यौरा{" "}
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                संपत्ति से <br />
                                वार्षिक आय
                              </th>
                              <th style={{ padding: "0", margin: "0" }}>
                                अभियुक्ति{" "}
                              </th>
                            </tr>
                          </thead>
                          <tbody>
                            {tableData && tableData.length > 0 ? (
                              tableData.map((detail, index) => (
                                <tr key={index}>
                                  {/* {location: '', details: '', price: '', ownerName: '', ownerRelation: '', purchaseType: '', purchaseDate: '', sellerName: '', sellerDetails: "", incomeFromProperty: '', remarks: '' } */}
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {index + 1}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.location}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.details}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.price}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.ownerName}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.ownerRelation}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.purchaseType}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.purchaseDate}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.sellerName}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.sellerDetails}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.incomeFromProperty}
                                  </td>
                                  <td
                                    style={{
                                      fontSize: "14px",
                                      backgroundColor: "white",
                                    }}
                                  >
                                    {detail.remarks}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td colSpan="12">निरंक </td>
                              </tr>
                            )}
                          </tbody>
                        </Table>
                      </Row>
                    </ModalBody>
                    <ModalFooter>
                      <Button color="secondary" onClick={togglePreviewModal}>
                        Cancel
                      </Button>
                      {iprFiled && !isArrayEmpty(iprFiled) ? (
                        <>
                          <Button
                            color="primary"
                            onClick={handleSubmit("update")}
                            style={{
                              display:
                                iprFiled &&
                                  !isArrayEmpty(iprFiled) &&
                                  iprFiled.iprFinalSubmit
                                  ? "none"
                                  : "",
                            }}
                          >
                            Update
                          </Button>
                        </>
                      ) : (
                        <>
                          <Button
                            color="primary"
                            onClick={handleSubmit("create")}
                            style={{
                              display:
                                iprFiled &&
                                  !isArrayEmpty(iprFiled) &&
                                  iprFiled.iprFinalSubmit
                                  ? "none"
                                  : "",
                            }}
                          >
                            Submit
                          </Button>
                        </>
                      )}
                    </ModalFooter>
                  </Modal>
                </CardBody>
              </Card>
            </ModalBody>
          </Form>
        </Modal>
      </Container>
    </>
  );
};

export default IPRForm;
