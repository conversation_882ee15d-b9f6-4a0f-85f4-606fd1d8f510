import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Container,
  Col,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  Spinner,
} from "reactstrap";
import ReactModal from "react-modal";
import Webcam from "react-webcam";
import * as faceapi from "face-api.js";
import "@tensorflow/tfjs";
import "./markAttendance.css";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Swal from "sweetalert2";
import { useNavigate } from "react-router-dom";
ReactModal.setAppElement("#root");

const OpenMarkAttendance = () => {
  //   const endPoint = import.meta.env.VITE_API_URL;
  const endPoint = "https://heonline.cg.nic.in/lmsbackend/";
  const token = sessionStorage.getItem("authToken");
  const [isModalEmpCodeOpen, setIsModalEmpCodeOpen] = useState(true);
  const toggleModalEmpCodeOpen = () => setIsModalEmpCodeOpen(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const toggleModal = () => setIsModalOpen(false);
  const [isFaceDetected, setIsFaceDetected] = useState(false);
  const [isLightOk, setIsLightOk] = useState(true);
  const [blinkCount, setBlinkCount] = useState(0);
  const [distanceCount, setDistanceCount] = useState(0);
  const [lastBlinkTime, setLastBlinkTime] = useState(0);
  const webcamRef = useRef(null);
  const frameCountRef = useRef(0);
  const canvasRef = useRef(null);
  const navigate = useNavigate();

  useEffect(() => {
    const loadModels = async () => {
      try {
        // console.log("Loading models...");
        await faceapi.nets.ssdMobilenetv1.loadFromUri("/models");
        await faceapi.nets.faceLandmark68Net.loadFromUri("/models");
        await faceapi.nets.faceRecognitionNet.loadFromUri("/models");
        // console.log("Models loaded successfully.");
      } catch (error) {
        console.error("Error loading models:", error);
      }
    };
    loadModels();
  }, []);

  const calculateEAR = (eye) => {
    const vertical1 = Math.hypot(eye[1].x - eye[5].x, eye[1].y - eye[5].y);
    const vertical2 = Math.hypot(eye[2].x - eye[4].x, eye[2].y - eye[4].y);
    const horizontal = Math.hypot(eye[0].x - eye[3].x, eye[0].y - eye[3].y);
    return (vertical1 + vertical2) / (2 * horizontal);
  };

  const checkLighting = (video) => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frame = context.getImageData(0, 0, canvas.width, canvas.height);
    const data = frame.data;
    let totalBrightness = 0;
    for (let i = 0; i < data.length; i += 4) {
      const brightness =
        0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];
      totalBrightness += brightness;
    }
    // console.log(totalBrightness);
    const avgBrightness = totalBrightness / (data.length / 4);
    setIsLightOk(avgBrightness > 50);
  };

  const isRealFace = (landmarks) => {
    const leftEye = landmarks.getLeftEye();
    const rightEye = landmarks.getRightEye();
    const mouth = landmarks.getMouth();

    const eyeDistance = Math.hypot(
      leftEye[0].x - rightEye[3].x,
      leftEye[0].y - rightEye[3].y
    );
    const mouthWidth = Math.hypot(
      mouth[0].x - mouth[6].x,
      mouth[0].y - mouth[6].y
    );

    return eyeDistance > mouthWidth * 1.5;
  };

  const REAL_FACE_WIDTH = 0.15;
  const FOCAL_LENGTH = 500;

  const processVideo = async () => {
    if (
      webcamRef.current &&
      webcamRef.current.video &&
      webcamRef.current.video.readyState === 4
    ) {
      const video = webcamRef.current.video;
      if (frameCountRef.current % 50 === 0) {
        checkLighting(video);
      }
      const detections = await faceapi
        .detectAllFaces(video)
        .withFaceLandmarks();
      if (detections.length === 1) {
        const canvas = canvasRef.current;
        canvas.width = 650;
        canvas.height = 650;
        const context = canvas.getContext("2d");
        context.clearRect(0, 0, canvas.width, canvas.height);
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const cameraCircleRadius = Math.min(centerX, centerY);
        context.beginPath();
        context.arc(centerX, centerY, cameraCircleRadius, 0, Math.PI * 2);
        context.strokeStyle = "blue";
        context.lineWidth = 2;
        context.stroke();
        const facesInCircle = detections.filter((detection) => {
          const { x, y, width, height } = detection.detection.box;
          const boxCorners = [
            { x, y },
            { x: x + width, y },
            { x, y: y + height },
            { x: x + width, y: y + height },
          ];
          return boxCorners.every((corner) => {
            const distance = Math.hypot(corner.x - centerX, corner.y - centerY);
            return distance <= cameraCircleRadius;
          });
        });
        if (facesInCircle.length === 1) {
          const landmarks = facesInCircle[0].landmarks;
          const { width } = facesInCircle[0].detection.box;
          const distance = (REAL_FACE_WIDTH * FOCAL_LENGTH) / width;
          setDistanceCount(distance);
          if (isRealFace(landmarks) && distance < 0.6 && distance > 0.4) {
            setIsFaceDetected(true);
            context.strokeStyle = "green";
            context.lineWidth = 2;
            context.stroke();
            const leftEye = landmarks.getLeftEye();
            const rightEye = landmarks.getRightEye();
            const leftEAR = calculateEAR(leftEye);
            const rightEAR = calculateEAR(rightEye);
            const avgEAR = (leftEAR + rightEAR) / 2;
            let EAR_THRESHOLD = 0.22;
            if (!isLightOk) {
              EAR_THRESHOLD = 0.18;
            }
            const currentTime = new Date().getTime();
            if (avgEAR < EAR_THRESHOLD) {
              if (currentTime - lastBlinkTime > 200) {
                setBlinkCount((prev) => prev + 1);
                setLastBlinkTime(currentTime);
              }
            } else {
              setLastBlinkTime(currentTime);
            }
          } else {
            setIsFaceDetected(false);
            context.strokeStyle = "red";
            context.lineWidth = 2;
            context.stroke();
          }
        } else {
          setIsFaceDetected(false);
        }
        frameCountRef.current += 1;
      } else {
        setIsFaceDetected(false);
      }
    }
    requestAnimationFrame(processVideo);
  };

  //   const captureCircularArea = () => {
  //     const canvas = canvasRef.current;

  //     if (!canvas) {
  //       console.error("Canvas is not available!");
  //       return;
  //     }

  //     const context = canvas.getContext("2d");
  //     const video = webcamRef.current?.video;

  //     if (!video || video.readyState !== 4) {
  //       console.error("Video is not ready!");
  //       return;
  //     }

  //     // Ensure canvas dimensions match the video feed
  //     canvas.width = video.videoWidth;
  //     canvas.height = video.videoHeight;

  //     // Draw the video frame onto the canvas
  //     context.drawImage(video, 0, 0, canvas.width, canvas.height);

  //     // Center of the circular area
  //     const centerX = canvas.width / 2;
  //     const centerY = canvas.height / 2;
  //     const cameraCircleRadius = Math.min(centerX, centerY);

  //     // Create a new canvas to hold the circular image
  //     const circularCanvas = document.createElement("canvas");
  //     circularCanvas.width = cameraCircleRadius * 2; // Diameter
  //     circularCanvas.height = cameraCircleRadius * 2; // Diameter
  //     const circularContext = circularCanvas.getContext("2d");

  //     // Draw the circular area onto the new canvas
  //     circularContext.beginPath();
  //     circularContext.arc(
  //       cameraCircleRadius, // Center X of circular canvas
  //       cameraCircleRadius, // Center Y of circular canvas
  //       cameraCircleRadius * 0.9, // Radius (optional padding adjustment)
  //       0,
  //       Math.PI * 2
  //     );
  //     circularContext.closePath();
  //     circularContext.clip(); // Clip to the circular area

  //     // Adjust source position to copy the circular region from the original canvas
  //     circularContext.drawImage(
  //       canvas,
  //       centerX - cameraCircleRadius, // Source X (top-left corner of the circular area)
  //       centerY - cameraCircleRadius, // Source Y (top-left corner of the circular area)
  //       cameraCircleRadius * 2, // Source width (diameter of the circle)
  //       cameraCircleRadius * 2, // Source height (diameter of the circle)
  //       0,
  //       0,
  //       circularCanvas.width, // Destination width
  //       circularCanvas.height // Destination height
  //     );

  //     // Get the circular image data URL with high quality
  //     const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0); // Quality set to maximum
  //     // console.log("Captured Circular Image Data URL:", circularImageSrc);

  //     // Display the captured circular image (optional)
  //     const imgElement = document.createElement("img");
  //     imgElement.src = circularImageSrc;
  //     document.body.appendChild(imgElement); // Append the image to the body or any container
  //   };

  const handleStart = () => {
    setIsModalOpen(true);
    setTimeout(() => {
      requestAnimationFrame(processVideo);
    }, 100);
  };

  const [isModalRegisterOpen, setIsModalRegisterOpen] = useState(false);
  const toggleRegisterModal = () => setIsModalRegisterOpen(false);
  const handleRegisterFace = () => {
    setEmpCode(empCode);
    setIsModalRegisterOpen(true);
    setTimeout(() => {
      requestAnimationFrame(processVideo);
    }, 100);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setIsModalRegisterOpen(false);
  };

  const [loading, setLoading] = useState(false);

  const handleCapture = async () => {
    const canvas = canvasRef.current;

    if (!canvas) {
      console.error("Canvas is not available!");
      return;
    }

    const context = canvas.getContext("2d");
    const video = webcamRef.current?.video;

    if (!video || video.readyState !== 4) {
      console.error("Video is not ready!");
      return;
    }
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const cameraCircleRadius = Math.min(centerX, centerY);
    const circularCanvas = document.createElement("canvas");
    circularCanvas.width = cameraCircleRadius * 2;
    circularCanvas.height = cameraCircleRadius * 2;
    const circularContext = circularCanvas.getContext("2d");
    circularContext.beginPath();
    circularContext.arc(
      cameraCircleRadius,
      cameraCircleRadius,
      cameraCircleRadius * 0.9,
      0,
      Math.PI * 2
    );
    circularContext.closePath();
    circularContext.clip();
    circularContext.drawImage(
      canvas,
      centerX - cameraCircleRadius,
      centerY - cameraCircleRadius,
      cameraCircleRadius * 2,
      cameraCircleRadius * 2,
      0,
      0,
      circularCanvas.width,
      circularCanvas.height
    );
    const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0);
    if (circularImageSrc) {
      setLoading(true);
      try {
        // console.log(empCode);
        const formData = new FormData();
        const response = await fetch(circularImageSrc);
        const blob = await response.blob();
        formData.append("image", blob, "captured-image.jpg");
        formData.append("username", empCode);
        const apiResponse = await fetch(
          "https://heonline.cg.nic.in/face-attendance/api/recognize",
          {
            method: "POST",
            body: formData,
          }
        );
        if (apiResponse.status === 200) {
          const data = await apiResponse.json();
          if (Number(data.recognized_user) === Number(empCode)) {
            try {
              const response = await axios.get(
                `https://heonline.cg.nic.in/lmsbackend/api/attendance/add?empCode=${empCode}`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    "web-url": window.location.href,
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              if (response.status === 200) {
                Swal.fire(
                  `Attendance Marked for ${empCode} Successfully!`,
                  "success",
                  "success"
                );
                setIsModalOpen(false);
              } else {
                Swal.fire(
                  `Attendance Marked Failed. Due to ${response.data.msg}!`,
                  "error",
                  "error"
                );
                setIsModalOpen(false);
              }
            } catch (error) {
              Swal.fire(
                `Attendance Marked Failed. Due to ${error.message}!`,
                "error",
                "error"
              );
              setIsModalOpen(false);
            }
          } else {
            Swal.fire(
              `Attendance Marked Failed. Due to ${data.recognized_user}!`,
              "error",
              "error"
            );
            setIsModalOpen(false);
          }
        } else {
          Swal.fire(
            `Attendance Marked failed. Due to ${apiResponse}!`,
            "error",
            "error"
          );
          setIsModalOpen(false);
        }
      } catch (error) {
        Swal.fire(
          `Attendance Marked Failed. Due to ${error.message}!`,
          "error",
          "error"
        );
        setIsModalOpen(false);
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRegisterCapture = async () => {
    const canvas = canvasRef.current;
    if (!canvas) {
      console.error("Canvas is not available!");
      return;
    }

    const context = canvas.getContext("2d");
    const video = webcamRef.current?.video;
    if (!video || video.readyState !== 4) {
      console.error("Video is not ready!");
      return;
    }
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const cameraCircleRadius = Math.min(centerX, centerY);

    const circularCanvas = document.createElement("canvas");
    circularCanvas.width = cameraCircleRadius * 2;
    circularCanvas.height = cameraCircleRadius * 2;
    const circularContext = circularCanvas.getContext("2d");

    circularContext.beginPath();
    circularContext.arc(
      cameraCircleRadius,
      cameraCircleRadius,
      cameraCircleRadius * 0.9,
      0,
      Math.PI * 2
    );
    circularContext.closePath();
    circularContext.clip();

    circularContext.drawImage(
      canvas,
      centerX - cameraCircleRadius,
      centerY - cameraCircleRadius,
      cameraCircleRadius * 2,
      cameraCircleRadius * 2,
      0,
      0,
      circularCanvas.width,
      circularCanvas.height
    );
    const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0);
    //// console.log("Captured Circular Image Data URL:", circularImageSrc);
    //const imageSrc = webcamRef.current.getScreenshot();
    if (circularImageSrc) {
      setLoading(true);
      try {
        const formData = new FormData();
        const response = await fetch(circularImageSrc);
        const blob = await response.blob();
        const empCode = document.querySelector(".usernamee").value;
        formData.append("file", blob, "captured-image.jpg");
        const apiResponse = await fetch(
          `https://heonline.cg.nic.in/face-attendance/api/upload/training?username=${empCode}`,
          {
            method: "POST",
            body: formData,
          }
        );
        if (apiResponse.status === 201) {
          const data = await apiResponse.json();
          if (Number(data.message) === Number(empCode)) {
            Swal.fire(
              `Employee Code ${empCode} Face Registered Successfully`,
              "success",
              "success"
            );
            setIsModalRegisterOpen(false);
          } else {
            SwalMessageAlert(
              `Face registeration failed. Due to ${data.recognized_user}!`,
              "error",
              "error"
            );
            setIsModalRegisterOpen(false);
          }
        } else {
          SwalMessageAlert(
            `Face registeration failed. Due to ${apiResponse.json()}!`,
            "error",
            "error"
          );
          setIsModalRegisterOpen(false);
        }
      } catch (error) {
        SwalMessageAlert(
          `Face registeration failed. Due to ${error.message}!`,
          "error",
          "error"
        );
        setIsModalRegisterOpen(false);
      } finally {
        setLoading(false);
      }
    }
  };

  const [empCode, setEmpCode] = useState(null);
  const handleInputChange = (event) => {
    const value = event.target.value;
    setEmpCode(value);
    if (value.length === 11 && /^\d{11}$/.test(value)) {
      checkEmployeeCode(value);
    }
  };
  const [markAttendanceButton, setMarkAttendanceButton] = useState(false);

  const checkEmployeeCode = async (code) => {
    try {
      const response = await axios.get(
        `${endPoint}api/get-employee-details?empCode=${code}`,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const data = response.data;
        if (data.msg === "Employee Not Found") {
          setMarkAttendanceButton(false);
        } else {
          setMarkAttendanceButton(true);
        }
      } else {
        alert("Failed to fetch Employee data. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Modal
          isOpen={isModalEmpCodeOpen}
          onRequestClose={handleCloseModal}
          style={{ maxWidth: "30%" }}
        >
          <ModalHeader onRequestClose={handleCloseModal}>
            <h2>Enter Employee Code</h2>
            <Button color="primary" onClick={handleRegisterFace} size="sm">
              Register Your Face
            </Button>
          </ModalHeader>
          <ModalBody>
            <Col lg="12">
              <CardBody>
                <input
                  type="text"
                  inputMode="numeric"
                  pattern="\d*"
                  maxLength={11}
                  className="form-control username"
                  placeholder="Enter Employee Code"
                  value={empCode}
                  onChange={handleInputChange}
                />
                {/* <span
                  className="danger"
                  style={{
                    display: markAttendanceButton === true ? "none" : "",
                    fontSize: "15px",
                    color: "red",
                  }}
                >
                  Employee Not Found
                </span> */}
                <br />
                <Button
                  color="success"
                  style={{
                    display: markAttendanceButton !== true ? "none" : "",
                  }}
                  onClick={handleStart}
                >
                  Mark Attendance
                </Button>
              </CardBody>
            </Col>
            <br />
          </ModalBody>
          <ModalFooter>
            <Button
              color="secondary"
              onClick={() => {
                navigate("/");
              }}
            >
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
        <Modal
          isOpen={isModalOpen}
          onRequestClose={handleCloseModal}
          style={{ maxWidth: "50%" }}
        >
          <ModalHeader onRequestClose={handleCloseModal}>
            <h2>Capture Photo</h2>
          </ModalHeader>
          <ModalBody>
            <Col lg="12">
              <CardBody>
                <div className="camera-popup">
                  <div
                    className={`camera-circle ${
                      isFaceDetected ? "green" : "red"
                    }`}
                  >
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      videoConstraints={{
                        facingMode: "user",
                      }}
                      style={{
                        position: "absolute",
                        top: -100,
                        left: -200,
                        zIndex: 1,
                        transform: "scaleX(-1)",
                      }}
                      screenshotFormat="image/jpeg"
                    />
                    <canvas
                      ref={canvasRef}
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        zIndex: 1,
                      }}
                    />
                  </div>
                  {isLightOk ? (
                    <div className="status">Lighting is ok</div>
                  ) : (
                    <div className="status error">
                      Error: Insufficient Lighting
                    </div>
                  )}
                  {distanceCount < 0.6 && distanceCount > 0.4 ? (
                    <div className="status">Face Detected</div>
                  ) : (
                    <div className="status error">Face Not Detected</div>
                  )}
                  <br />
                  <div className="">
                    {isLightOk && isFaceDetected ? (
                      <Button
                        className="btn btn-sm btn-success"
                        onClick={handleCapture}
                        disabled={loading}
                      >
                        {loading ? (
                          <Spinner size="sm" color="light" /> // Show spinner
                        ) : (
                          "Click"
                        )}
                      </Button>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </CardBody>
            </Col>
            <br />
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={toggleModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
        <Modal
          isOpen={isModalRegisterOpen}
          onRequestClose={handleCloseModal}
          style={{ maxWidth: "50%" }}
        >
          <ModalHeader onRequestClose={handleCloseModal}>
            <h2>Capture Photo</h2>
          </ModalHeader>
          <ModalBody>
            <Col lg="12">
              <CardBody>
                <input type="text" className="form-control usernamee" />
                <div className="camera-popup">
                  <div
                    className={`camera-circle ${
                      isFaceDetected ? "green" : "red"
                    }`}
                  >
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      videoConstraints={{
                        facingMode: "user",
                      }}
                      style={{
                        position: "absolute",
                        top: -100,
                        left: -200,
                        zIndex: 1,
                        transform: "scaleX(-1)",
                      }}
                      screenshotFormat="image/jpeg"
                    />
                    <canvas
                      ref={canvasRef}
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        zIndex: 1,
                      }}
                    />
                  </div>
                  {isLightOk ? (
                    <div className="status">Lighting is ok</div>
                  ) : (
                    <div className="status error">
                      Error: Insufficient Lighting
                    </div>
                  )}
                  {distanceCount < 0.6 && distanceCount > 0.4 ? (
                    <div className="status">Face Detected</div>
                  ) : (
                    <div className="status error">Face Not Detected</div>
                  )}
                  <br />
                  <div className="">
                    {isLightOk && isFaceDetected ? (
                      <Button
                        className="btn btn-sm btn-success"
                        onClick={handleRegisterCapture}
                        disabled={loading}
                      >
                        {loading ? (
                          <Spinner size="sm" color="light" /> // Show spinner
                        ) : (
                          "Click"
                        )}
                      </Button>
                    ) : (
                      ""
                    )}
                  </div>
                  <div>
                    {/* Your existing components */}
                    {/* <button onClick={captureCircularArea}>
                      Capture Circular Area
                    </button> */}
                  </div>
                </div>
              </CardBody>
            </Col>
            <br />
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={toggleRegisterModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default OpenMarkAttendance;
