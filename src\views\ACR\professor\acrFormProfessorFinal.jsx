import { useState } from "react";
import PropTypes from "prop-types";
import {
  Card,
  CardHeader,
  CardBody,
  Row,
  Col,
  Label,
  Input,
  Table,
} from "reactstrap";
const ACRFormProfessorFinal = (employee) => {
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);

  const [errors, setErrors] = useState({});



  return (
    <>
      <Row>
        <Col>
          <Card className="shadow pb-4">
            <CardHeader
              className="bg-white border-0"
              style={{ textAlign: "center" }}
            >
              <h2 className="mb-0">
                कार्यालय, आयुक्त, उच्च शिक्षा, छत्तीसगढ़ रायपुर
              </h2>
              <h3 className="mb-0">
                गोपनीय प्रतिवेदन प्रपत्र
                <br />
                31 मार्च {lastYearDate} को समाप्त होने वाले वर्ष के लिये
                <br />
                प्रतिवेदित अधिकारी प्राध्यापक / सहायक प्राध्यापक द्वारा भरा जाए
              </h3>
            </CardHeader>
            <CardBody>
              <div className="mb-4">
                <Row className="mb-3">
                  <Table>
                    <tbody>
                      <tr>
                        <h2 style={{ textAlign: "center" }}>प्रतिवेदक</h2>
                      </tr>
                      <tr>
                        <td>
                          <Row>
                            <Col>
                              <Card className="shadow pb-4">
                                <CardHeader
                                  className="bg-white border-0"
                                  style={{ textAlign: "center" }}
                                ></CardHeader>
                                <CardBody>
                                  <div className="mb-4">
                                    <Row className="mb-3">
                                      <Col md="3">
                                        <Label>पूरा नाम</Label>
                                        <Input
                                          type="text"
                                          name="employeeName"
                                          value={employee.employee?.basicDetails[0].employeeName}
                                          readOnly
                                          className="form-control"
                                        />
                                      </Col>

                                      {employee.employee?.basicDetails[0].gender === "Female" &&

                                        <Col md="3">
                                          <Label>
                                            महिला अधिकारी विवाह के पूर्व का नाम भी लिखें
                                          </Label>
                                          <Input
                                            type="text"
                                            name="employeeSecondaryName"
                                            readOnly
                                            className="form-control"
                                            value={employee.employee?.basicDetails[0].employeeSecondaryName}

                                          />

                                        </Col>}
                                      <Col md="3">
                                        <Label>पिता अथवा पति का नाम </Label>
                                        <Input
                                          type="text"
                                          name="fatherName"
                                          value={employee.employee?.basicDetails[0].fatherName}
                                          readOnly
                                          className="form-control"

                                        />

                                      </Col>
                                      <Col md="3">
                                        <Label>जन्म तिथि</Label>
                                        <Input
                                          type="date"
                                          name="dateofBirth"
                                          value={employee.employee?.basicDetails[0].dateofBirth}
                                          readOnly
                                          className="form-control"


                                        />

                                      </Col>
                                    </Row>
                                    <Row className="mb-3">
                                      <Col md="6">
                                        <Label style={{ fontWeight: "bold" }}>शैक्षणिक अर्हता एवं वर्ष</Label>

                                        <Row>
                                          <div className="col-sm-6">
                                            <Label>स्नातक</Label>
                                            <Input
                                              readOnly
                                              type="text"
                                              name="shenikVarsh"
                                              value={employee.employee?.basicDetails[0].shenikVarsh}
                                              className="form-control"

                                            />

                                          </div>
                                          <div className="col-sm-6">
                                            <Label>एम.फिल</Label>
                                            <Input
                                              readOnly
                                              type="text"
                                              name="shenikSubject"
                                              value={employee.employee?.basicDetails[0].shenikSubject}
                                              className="form-control"

                                            />

                                          </div>

                                          <div className="col-sm-6">
                                            <Label>स्नातकोत्तर</Label>
                                            <Input
                                              readOnly
                                              type="text"
                                              name="shenikVarsh"
                                              value={employee.employee?.basicDetails[0].shenikVarsh}
                                              className="form-control"

                                            />

                                          </div>
                                          <div className="col-sm-6">
                                            <Label>पीएच.डी</Label>
                                            <Input
                                              type="text"
                                              name="shenikSubject"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].shenikSubject}
                                              className="form-control"

                                            />

                                          </div>

                                        </Row>
                                      </Col>
                                      <Col md="6">
                                        <Label style={{ fontWeight: "bold" }}>वेतन व वेतनमान</Label>
                                        <Row>
                                          <div className="col-sm-6">
                                            <Label>वेतन</Label>
                                            <Input
                                              type="number"
                                              readOnly
                                              name="currentSalary"
                                              value={employee.employee?.basicDetails[0].currentSalary}
                                              className="form-control"

                                            />

                                          </div>
                                          <div className="col-sm-6">
                                            <Label>वेतनमान</Label>
                                            <Input
                                              name="payscale"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].payscale}
                                              className="form-control"

                                            >
                                            </Input>

                                          </div>
                                        </Row>

                                      </Col>
                                    </Row>
                                    <hr />
                                    <Row className="mt-3 mb-3">
                                      <Col>
                                        <h3>
                                          <u> महाविद्यालयीन सेवा प्रारंभ करने की जानकारी</u>
                                        </h3>
                                      </Col>
                                    </Row>
                                    <Row>
                                      <Col md="12">
                                        <Label>प्रथम नियुक्ति का पद, प्रकार एवं दिनांक :</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <div className="col-sm-4">
                                              <Label>पद</Label>
                                              <Input
                                                type="text"
                                                readOnly
                                                name="prathamNiyuktiDesignation"
                                                value={employee.employee?.basicDetails[0].prathamNiyuktiDesignation}
                                                className="form-control"

                                              >


                                              </Input>

                                            </div>
                                            <div className="col-sm-4">
                                              <Label>प्रकार</Label>
                                              <Input
                                                type="text"
                                                name="prakar"
                                                readOnly
                                                value={employee.employee?.basicDetails[0].prakar}
                                                className="form-control"

                                              >
                                              </Input>

                                            </div>
                                            <div className="col-sm-4">
                                              <Label>दिनांक</Label>
                                              <Input
                                                type="date"
                                                readOnly
                                                name="prathanNiyuktiDate"
                                                value={employee.employee?.basicDetails[0].prathanNiyuktiDate}
                                                className="form-control"

                                              />

                                            </div>
                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="4">
                                        <Label>नियमित नियुक्ति का दिनाँक :</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <div className="col-sm-12">
                                              <Label>दिनांक</Label>
                                              <Input
                                                type="date"
                                                name="niyamitNiyuktiDate"
                                                readOnly
                                                value={employee.employee?.basicDetails[0].niyamitNiyuktiDate}
                                                className="form-control"

                                              />

                                            </div>
                                          </Row>
                                        </div>
                                      </Col>
                                      <Col md="8">
                                        <Label>वर्तमान पद एवं नियुक्ति दिनांक :</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <div className="col-sm-6">
                                              <Label>पद</Label>
                                              <Input
                                                type="text"

                                                name="designation"
                                                value={employee.employee?.basicDetails[0].designation}
                                                className="form-control"
                                                readOnly

                                              />

                                            </div>
                                            <div className="col-sm-6">
                                              <Label>दिनांक</Label>
                                              <Input
                                                type="date"
                                                name="vartamanNiyuktiDate"
                                                readOnly
                                                value={employee.employee?.basicDetails[0].vartamanNiyuktiDate}
                                                className="form-control"

                                              />

                                            </div>
                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="12">
                                        <Label style={{ fontWeight: "bolder" }}>
                                          वर्ष में किस-किस संस्था में पदस्थ रहे, अवधि का भी उल्लेख
                                          करें :

                                        </Label><br />
                                        <Label>(यदि एक से अधिक संस्था में कार्य किया हो तो प्रत्येक
                                          संस्था की कार्य अवधि के लिए पृथक फार्म भरा जाये)</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <div className="col-sm-4">
                                              <Label>(i)</Label>
                                              <Input
                                                type="textarea"
                                                maxLength={200}
                                                name="padastOne" readOnly
                                                value={employee.employee?.basicDetails[0].padastOne}
                                                className="form-control"

                                              />

                                            </div>
                                            <div className="col-sm-4">
                                              <Label>(ii)</Label>
                                              <Input
                                                type="textarea"
                                                maxLength={200}
                                                readOnly
                                                name="padastTwo"

                                                value={employee.employee?.basicDetails[0].padastTwo}
                                                className="form-control"

                                              />

                                            </div>
                                            <div className="col-sm-4">
                                              <Label>(iii)</Label>
                                              <Input
                                                type="textarea"
                                                name="padastThree"
                                                readOnly
                                                maxLength={200}
                                                value={employee.employee?.basicDetails[0].padastThree}
                                                className="form-control"

                                              />

                                            </div>
                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="12">
                                        <Label style={{ fontWeight: "bolder" }}>
                                          प्रतिवेदित अधिकारी द्वारा समीक्षा अवधि में किये गए कार्य
                                          की जानकारी : <br />
                                        </Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <Table>
                                              <thead>
                                                <tr>
                                                  <th>क्र.</th>
                                                  <th>पढ़ाई गई कक्षा का स्तर</th>
                                                  <th>सेक्शन की संख्या</th>
                                                  <th>कुल छात्र संख्या</th>
                                                  <th>व्याख्यान</th>
                                                  <th>प्रायोगिक</th>
                                                  <th>टिटोरियल</th>
                                                  <th>विशेष कोचिंग</th>
                                                </tr>
                                              </thead>
                                              <tbody>
                                                {employee.employee?.basicDetails[0].samikshaAwadhiKarya.map((data, index) => (
                                                  <tr key={index}>
                                                    <th>{index + 1}</th>
                                                    <th>{data.level}</th>
                                                    <th>
                                                      <Input
                                                        type="number" readOnly
                                                        name="sectionCount"
                                                        value={data.sectionCount}
                                                        className="form-control"
                                                      />
                                                    </th>
                                                    <th>
                                                      <Input
                                                        type="number"
                                                        name="studentCount"
                                                        value={data.studentCount}
                                                        readOnly
                                                        className="form-control"
                                                      />
                                                    </th>
                                                    <th>
                                                      <Input
                                                        type="number"
                                                        name="vyakhyan"
                                                        value={data.vyakhyan}
                                                        readOnly
                                                        className="form-control"
                                                      />
                                                    </th>
                                                    <th>
                                                      <Input
                                                        type="number"
                                                        name="prayogik"
                                                        value={data.prayogik}
                                                        readOnly
                                                        className="form-control"
                                                      />
                                                    </th>
                                                    <th>
                                                      <Input
                                                        type="number"
                                                        name="tutorial"
                                                        value={data.tutorial}
                                                        readOnly
                                                        className="form-control"
                                                      />
                                                    </th>
                                                    <th>
                                                      <Input
                                                        type="number"
                                                        name="visheshCoaching"
                                                        readOnly
                                                        value={data.visheshCoaching}
                                                        className="form-control"

                                                      />
                                                    </th>
                                                  </tr>
                                                ))}
                                              </tbody>
                                            </Table>
                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="3">
                                        <Label>क्या उपस्थिति पंजी नियमित भरी गई</Label>
                                        <Input
                                          type="select" // Change the type to "select"
                                          name="panjiNiyamit"
                                          value={employee.employee?.basicDetails[0].panjiNiyamit} // Use employee data here
                                          className="form-control"
                                          readOnly

                                        >
                                          <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                                          <option value="Han">हाँ</option> {/* Yes option */}
                                          <option value="Nahi">नहीं</option> {/* No option */}
                                        </Input>

                                      </Col>
                                      <Col md="3">
                                        <Label>क्या उपस्थिति पंजी प्राचार्य को सौंपी गई</Label>
                                        <Input
                                          type="select"
                                          readOnly
                                          name="panjiPracharya"
                                          value={employee.employee?.basicDetails[0].panjiPracharya} // Use employee data here
                                          className="form-control"

                                        >
                                          <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                                          <option value="Han">हाँ</option> {/* Yes option */}
                                          <option value="Nahi">नहीं</option> {/* No option */}
                                        </Input>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="12">
                                        <Label>
                                          वर्ष के दौरान आपके द्वारा किये गए शोध कार्य का विवरण
                                        </Label>
                                        <Input
                                          type="textarea"
                                          name="shodKaryaKaVivran"
                                          style={{ height: "100px" }}
                                          maxLength={400}
                                          readOnly
                                          value={employee.employee?.basicDetails[0].shodKaryaKaVivran}
                                          className="form-control"

                                        />

                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="12">
                                        <Label>
                                          प्रकाशित कार्य का विवरण
                                        </Label>
                                        <Input
                                          type="textarea"
                                          name="prakashitKaryaVivran"
                                          readOnly
                                          value={employee.employee?.basicDetails[0].prakashitKaryaVivran}
                                          className="form-control"

                                        />


                                      </Col>
                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="12">
                                        <Label style={{ fontWeight: "bolder" }}>
                                          कितने छात्रों को शोध कार्य हेतु मार्गदर्शन किये
                                        </Label>
                                        <Row>
                                          <Col>
                                            <Label>एम. फिल. के कितने छात्रों को</Label>
                                            <Input
                                              type="number"
                                              readOnly
                                              name="mphilStudentCount"
                                              value={employee.employee?.basicDetails[0].mphilStudentCount}
                                              className="form-control"

                                            />

                                          </Col>
                                          <Col>
                                            <Label>पीएच.डी. के कितने छात्रों को</Label>
                                            <Input
                                              type="number"
                                              name="phdStudentCount"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].phdStudentCount}
                                              className="form-control"

                                            />

                                          </Col>
                                          <Col md="4">
                                            <Label>कितने कमजोर छात्रों को विशेष कोचिंग दी</Label>
                                            <Input
                                              type="number"
                                              name="weakStudent"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].weakStudent} // Use employee data here
                                              className="form-control"

                                            />

                                          </Col>
                                        </Row>

                                      </Col>

                                    </Row>
                                    <br />
                                    <Row>
                                      <Col md="6">
                                        <Label>वर्ष में कितनी नई पुस्तकों का अध्ययन किया (पुस्तकों का नाम व लेखकों का नाम लिखे )</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <Label></Label>
                                            <Input
                                              type="textarea"
                                              maxLength={400}
                                              style={{ height: "150px" }}
                                              name="bookName"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].bookName} // Use employee data here
                                              className="form-control"

                                            />

                                          </Row>
                                        </div>
                                      </Col>
                                      <Col md="6">
                                        <Label>
                                          वर्ष के दौरान लिए गए अवकाश की प्रकृति एवं दिवस
                                        </Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <Input
                                              type="textarea"
                                              maxLength={400}
                                              readOnly
                                              style={{ height: "150px" }}
                                              name="leaveStatus"
                                              value={employee.employee?.basicDetails[0].leaveStatus} // Use employee data here
                                              className="form-control"

                                            />

                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <br />
                                    <Row className="mt-3 mb-3">
                                      <Col>
                                        <h3>
                                          <u> शैक्षणेत्तर कार्यों का संक्षिप्त विवरण</u>
                                        </h3>
                                      </Col>
                                    </Row>
                                    <Row>
                                      <Col md="6">
                                        <Label>एन.सी.सी.</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <Input
                                              type="textarea"
                                              name="ncc"
                                              readOnly
                                              value={employee.employee?.basicDetails[0].ncc} // Use employee data here
                                              className="form-control"

                                            />

                                          </Row>
                                        </div>
                                      </Col>

                                      <Col md="6">
                                        <Label>एन. एस. एस.</Label>
                                        <div className="col-sm-12">
                                          <Row>
                                            <Input
                                              type="textarea"
                                              readOnly
                                              name="nss"
                                              value={employee.employee?.basicDetails[0].nss} // Use employee data here
                                              className="form-control"

                                            />

                                          </Row>
                                        </div>
                                      </Col>
                                    </Row>
                                    <Row className="mt-3">
                                      <Col md="12" >
                                        <Label style={{ fontWeight: "bolder" }}>
                                          परीक्षा संचालन </Label>
                                        <br />
                                        <Label>

                                          (महाविद्यालयीन परीक्षा संचालन में क्या कार्य किया कार्य
                                          की प्रकृति एवं कितने दिन इस कार्य का संचालन किया)
                                        </Label>
                                        <Input
                                          type="textarea"
                                          readOnly
                                          name="parikshaSanchalan"
                                          style={{ height: "100px" }}
                                          maxLength={500}
                                          value={employee.employee?.basicDetails[0].parikshaSanchalan} // Use employee data here
                                          className="form-control"

                                        />

                                      </Col>
                                    </Row>
                                    <br />

                                    <Row>
                                      <Col md="6">
                                        <Label>
                                          महाविद्यालय प्रशासन के लिए किये गए कार्य
                                          (जैसे अनुशासन, जांच कार्य, छात्र संघ आदि)
                                        </Label>

                                        <Input
                                          type="textarea"
                                          name="collegeWork"
                                          value={employee.employee?.basicDetails[0].collegeWork} // Use employee data here
                                          className="form-control"
                                          readOnly

                                        />


                                      </Col>
                                      <Col md="6">
                                        <Label>
                                          अन्य कार्य  (जैसे- खेल संबंधी, सेमीनार आदि)
                                        </Label>
                                        <Input
                                          type="textarea"
                                          name="otherWork"
                                          readOnly
                                          value={employee.employee?.basicDetails[0].otherWork} // Use employee data here
                                          className="form-control"

                                        />

                                      </Col>
                                    </Row>
                                  </div>
                                </CardBody>
                              </Card>
                            </Col>
                          </Row>
                        </td>
                      </tr>
                    </tbody>
                  </Table>
                </Row>
                {employee.employee.levelIndex >= 0 && employee.employee.preDefinedLevels[0].status === true && <>
                  <Row className="mb-3">
                    <Col>
                      <h2 style={{ textAlign: "center" }}>प्रथम मतंकन</h2>
                      <h3 style={{ textAlign: "center" }}>
                        भाग दो <br />
                        (प्रतिवेदक अधिकारी की अभ्युक्ति) <br />
                        प्रतिवेदित अधिकारी द्वारा भाग-1 में दिये स्वमूल्यांकन पर
                        टीप
                      </h3>
                    </Col>
                  </Row>
                  <Row className="mb-3">
                    <Col md="12">
                      <Label>
                        क्या प्रतिवेदक अधिकारी के स्वमूल्यांकन में उल्लेखित किसी
                        बिन्दु से आप असहमत हैं? यदि हाँ तो विवरण दें
                      </Label>
                      <Input
                        type="textarea"
                        maxLength={400}
                        readOnly
                        name="prativedakAdhikariSvamulyankan"
                        value={employee.employee?.levelDetails[0].data.prativedakAdhikariSvamulyankan}
                        className="form-control"

                      />
                      {errors.prativedakAdhikariSvamulyankan && (
                        <small className="text-danger">
                          {errors.prativedakAdhikariSvamulyankan}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-3">
                    <Col md="6">
                      <Label>
                        महाविद्यालय उत्तरदायित्वों को समय पर पूर्ण करने में रुचि{" "}
                      </Label>
                      <Input
                        type="textarea"
                        maxLength={200}
                        readOnly

                        name="samayParPurna"
                        value={employee.employee?.levelDetails[0].data.samayParPurna}
                        className="form-control"

                      />
                      {errors.samayParPurna && (
                        <small className="text-danger">
                          {errors.samayParPurna}
                        </small>
                      )}
                    </Col>
                    <Col md="6">
                      <Label>क्या इन्होने आवंटित कोर्स पूर्ण किया</Label>
                      <Input
                        type="textarea"
                        maxLength={200}
                        readOnly

                        name="avantitCourse"
                        value={employee.employee?.levelDetails[0].data.avantitCourse}
                        className="form-control"

                      />
                      {errors.avantitCourse && (
                        <small className="text-danger">
                          {errors.avantitCourse}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Label style={{ fontWeight: "bolder" }}>छात्रों के लिये प्रयास:</Label> <br />
                  <Row className="mb-3">

                    <Col md="6">
                      <Label>
                        देदिप्यमान छात्रों के लिये क्या प्रयास किये
                      </Label>
                      <Input
                        type="textarea"
                        maxLength={200}
                        readOnly

                        name="dedipyaMaan"
                        value={employee.employee?.levelDetails[0].data.dedipyaMaan}
                        className="form-control"

                      />
                      {errors.dedipyaMaan && (
                        <small className="text-danger">
                          {errors.dedipyaMaan}
                        </small>
                      )}
                    </Col>
                    <Col md="6">

                      <Label>कमजोर छात्रों के लिये क्या प्रयास किये</Label>
                      <Input
                        type="textarea"
                        name="weakStudent"
                        readOnly

                        maxLength={200}
                        value={employee.employee?.levelDetails[0].data.weakStudent}
                        className="form-control"

                      />
                      {errors.weakStudent && (
                        <small className="text-danger">
                          {errors.weakStudent}
                        </small>
                      )}


                    </Col>
                  </Row>
                  <Row className="mb-3">
                    <Col md="6">
                      <Label>उपरोक्तं के क्या परिणाम रहे</Label>
                      <Input
                        type="textarea"
                        readOnly
                        name="uprokParinam"
                        maxLength={200}
                        value={employee.employee?.levelDetails[0].data.uprokParinam}
                        className="form-control"

                      />
                      {errors.uprokParinam && (
                        <small className="text-danger">
                          {errors.uprokParinam}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-3">
                    <Col md="6">
                      <Label>
                        प्राचार्य द्वारा सौपे गये कार्यों में क्या तत्परता रही
                      </Label>
                      <Input
                        type="textarea"
                        readOnly
                        name="pracharyaDwaraKaryo"
                        maxLength={200}
                        value={employee.employee?.levelDetails[0].data.pracharyaDwaraKaryo}
                        className="form-control"

                      />
                      {errors.pracharyaDwaraKaryo && (
                        <small className="text-danger">
                          {errors.pracharyaDwaraKaryo}
                        </small>
                      )}
                    </Col>
                    <Col md="6">
                      <Label>निष्ठा</Label>
                      <Input
                        type="textarea"
                        maxLength={200}
                        readOnly
                        name="nishta"
                        value={employee.employee?.levelDetails[0].data.nishta}
                        className="form-control"

                      />
                      {errors.nishta && (
                        <small className="text-danger">{errors.nishta}</small>
                      )}
                    </Col>
                  </Row>

                  <Row className="mb-3">
                    <Col md="3">
                      <Label>प्रतिवेदक अधि का समग्र मूल्यांकन</Label>
                      <Input
                        name="shreniKaran"
                        type="select"
                        readOnly
                        id="recordsPerPage"
                        value={employee.employee?.levelDetails[0].data.shreniKaran}

                      >
                        <option value="">चुने</option>
                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                        <option value="अच्छा">अच्छा</option>
                        <option value="साधारण">साधारण</option>
                        <option value="घटिया">घटिया</option>
                      </Input>
                      {errors.shreniKaran && (
                        <small className="text-danger">
                          {errors.shreniKaran}
                        </small>
                      )}
                    </Col>
                  </Row>

                  <Row className="mb-3">
                    <Col md="12">
                      <Label>रिमार्क</Label>
                      <Input
                        name="remark"
                        maxLength={500}
                        readOnly
                        type="textarea"
                        style={{ height: "100px" }}
                        value={employee.employee?.levelDetails[0].data.remark}

                      />
                      {errors.remark && (
                        <small className="text-danger">{errors.remark}</small>
                      )}
                    </Col>
                  </Row>
                </>}
                {employee.employee.levelIndex >= 1 && employee.employee.preDefinedLevels[1].status === true && <>
                  <Row className="mb-3">
                    <Col>
                      <h2 style={{ textAlign: "center" }}>
                        समीक्षक अधिकारी की टिप्पणी
                      </h2>
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="3">
                      <Label>क्या आप प्रतिवेदक अधिकारी के मूल्यांकन से सहमत हैं</Label>
                      <Input
                        name="mulyankanSehmat"
                        type="select"
                        id="recordsPerPage"
                        readOnly
                        value={employee.employee?.levelDetails[1].data.mulyankanSehmat}

                      >
                        <option value="">चुने</option>
                        <option value="हाँ">हाँ</option>
                        <option value="नहीं">नहीं</option>
                      </Input>
                      {errors.mulyankanSehmat && (
                        <small className="text-danger">
                          {errors.mulyankanSehmat}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="12">
                      <Label>यदि नहीं, तो कारणों सहित अपना अभिमत दें।</Label>
                      <Input
                        name="abhimatDe"
                        style={{ height: "100px" }}
                        type="textarea"
                        maxLength={400}
                        readOnly
                        value={employee.employee?.levelDetails[1].data.abhimatDe}

                      />
                      {errors.abhimatDe && (
                        <small className="text-danger">{errors.abhimatDe}</small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="3">
                      <Label>श्रेणीकरण</Label>
                      <Input
                        name="shreniKaran1"
                        type="select"
                        readOnly
                        id="recordsPerPage"
                        value={employee.employee?.levelDetails[1].data.shreniKaran1}

                      >
                        <option value="">चुनें</option>
                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                        <option value="अच्छा">अच्छा</option>
                        <option value="साधारण">साधारण</option>
                        <option value="घटिया">घटिया</option>
                      </Input>
                      {errors.shreniKaran1 && (
                        <small className="text-danger">
                          {errors.shreniKaran1}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="12">
                      <Label>रिमार्क</Label>
                      <Input
                        name="remark1"
                        style={{ height: "100px" }}
                        type="textarea"
                        maxLength={400}
                        readOnly
                        value={employee.employee?.levelDetails[1].data.remark1}

                      />
                      {errors.remark1 && (
                        <small className="text-danger">{errors.remark1}</small>
                      )}
                    </Col>
                  </Row>
                </>}
                {employee.employee.levelIndex >= 2 && employee.employee.preDefinedLevels[2].status === true && <>
                  <Row className="mb-3">
                    <Col>
                      <h2 style={{ textAlign: "center" }}>
                        स्वीकृतकर्ता अधिकारी की टिप्पणी
                      </h2>
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="3">
                      <Label>श्रेणीकरण</Label>
                      <Input
                        name="shreniKaran2"
                        readOnly
                        type="select"
                        id="recordsPerPage"
                        value={employee.employee?.levelDetails[2].data.shreniKaran2}
                        >
                        <option value="">चुनें</option>
                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                        <option value="अच्छा">अच्छा</option>
                        <option value="साधारण">साधारण</option>
                        <option value="घटिया">घटिया</option>
                      </Input>
                      {errors.shreniKaran2 && (
                        <small className="text-danger">
                          {errors.shreniKaran2}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    <Col md="12">
                      <Label>रिमार्क</Label>
                      <Input
                        name="remark2"
                        style={{ height: "100px" }}
                        readOnly
                        type="textarea"
                        maxLength={400}
                        value={employee.employee?.levelDetails[2].data.remark2}
                        />
                      {errors.remark2 && (
                        <small className="text-danger">{errors.remark2}</small>
                      )}
                    </Col>
                  </Row>
                </>}
              </div>
            </CardBody>
          </Card>
        </Col>
      </Row>
    </>
  );
};

ACRFormProfessorFinal.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormProfessorFinal;
