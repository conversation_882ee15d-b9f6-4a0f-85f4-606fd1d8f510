import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>,
    CardHeader,
    CardBody,
    FormControl,
    Col,
    Button,
} from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const CollegeChargeList = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");

    const [division, setDivision] = useState([]);
    const [filterText, setFilterText] = useState("");

    const [college, setCollege] = useState([]);
    useEffect(() => {
        const fetchCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-all-college`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    setCollege(response.data);
                } else {
                    alert("Failed to fetch College data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchCollege();
    }, [endPoint, token]);

    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    setDivision(response.data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };

        getDivision();
    }, [endPoint, token]);

    const getDivisionName = (value) => {
        const divisionObj = division.find((div) => div.divisionCode === value);
        return divisionObj ? divisionObj.name : "Unknown Division";
    };

    const columns = [

        {
            name: "Basic Details",
            cell: (row) => (
                <div>
                    <div className="mb-2">
                        <strong>Name: </strong>
                        {row.name}
                    </div>
                    <div className="mb-2">
                        <strong>Email: </strong>
                        {row.collegeEmail}
                    </div>
                    <div className="mb-2">
                        <strong>AISHE Code: </strong>
                        <strong className="badge-primary">{row.aisheCode}</strong>
                    </div>
                </div>
            ),
            wrap: true,
        },
        {
            name: "Contact Details",
            cell: (row) => (
                <div>
                    <div className="mb-2">
                        <strong>Contact Person: </strong>
                        {row.contactPerson}
                    </div>
                    <div className="mb-2">
                        <strong>Mobile: </strong>
                        {row.contactNumber}
                    </div>
                    <div className="mb-2">
                        <strong>Institute Type: </strong>
                        {row.collegeType === "1" ? "GOVERNMENT" : "PRIVATE"}
                    </div>
                </div>
            ),
            wrap: true,
        },
        {
            name: "Principal Details",
            cell: (row) => (
                <div>
                    <div className="mb-2">
                        <strong>Is Principal Posted :  </strong>
                        {row.isPrincipalPosted === true ? "Yes" : "No"}
                    </div>
                    <div className="mb-2">
                        <strong>Charge Type : </strong>
                        {row.principalType === undefined ? "Not Selected Yet" : row.principalType}
                    </div>
                    {row.principalType === "Additional Charge" && <>
                        < div className="mb-2">
                            <strong>Additional Principal Name : </strong>
                            {row.prinicipalName}
                        </div>
                        < div className="mb-2">
                            <strong>Additional Principal No : </strong>
                            {row.principalNo}
                        </div>
                    </>
                    }
                    {row.principalType === "In-charge" && <>
                        < div className="mb-2">
                            <strong>In-Charge Principal Name : </strong>
                            {row.prinicipalName}
                        </div>
                        < div className="mb-2">
                            <strong>In-Charge Principal No : </strong>
                            {row.principalNo}
                        </div>
                    </>
                    }
                    {row.principalType === "Regular" && <>
                        < div className="mb-2">
                            <strong>Regular Principal Name : </strong>
                            {row.prinicipalName}
                        </div>
                        < div className="mb-2">
                            <strong>Regular Principal No : </strong>
                            {row.principalNo}
                        </div>
                    </>
                    }
                </div >
            ),
            wrap: true,
        },
        // { name: "University", selector: (row) => row.universityName },
        {
            name: "Division/District",
            selector: (row) =>
                `${getDivisionName(row.divison)} / ${row.districtName}`,
        },
    ];

    // Function to sort data alphabetically by name
    const sortedData = college.sort((a, b) => {
        const nameA = a.districtName.toLowerCase();
        const nameB = b.districtName.toLowerCase();
        if (nameA < nameB) return -1; // a comes before b
        if (nameA > nameB) return 1;  // a comes after b
        return 0; // a and b are equal
    });


    

    
    

    const filteredData = college.filter((item) => {
        const filterTextLower = filterText.toLowerCase();

        // Check for "Verified" and "Not Verified"
        if (filterTextLower === "verified") {
            return item.status === true; // Only include verified employees
        } else if (filterTextLower === "not verified") {
            return item.status === false; // Only include not verified employees
        } else if (filterTextLower === "not") {
            return item.status === false; // Only include not verified employees
        }

        // Default filtering for name, empCode, and contact
        return (
            (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
            (item.empCode && item.empCode.toLowerCase().includes(filterTextLower)) ||
            (item.contact &&
                item.contact.toString().toLowerCase().includes(filterTextLower))
        );
    });

    const [verifiedData, setVerifiedData] = useState([]);

    const exportToExcel = async (e) => {
        e.preventDefault();
        await axios
            .get(`${endPoint}/api/college/get-all-verified-college`, {
                headers: {
                    "Content-Type": "application/json",
                    'web-url': window.location.href,
                    Authorization: `Bearer ${token}`,
                },
            })
            .then((response) => {
                const fetchedData = response.data.collegeData; // Use response data directly
                setVerifiedData(fetchedData); // Update the state for future reference

                if (fetchedData.length > 0) {
                    let tableHTML = `
          <table>
            <thead>
              <tr>
                <th>Division</th>
                <th>District</th>
                <th>College Name</th>
                <th>Principal Name</th>
                <th>Principal Mobile No.</th>
                <th>AISHE Code</th>
                <th>Email</th>
              </tr>
            </thead>
            <tbody>
        `;
                    fetchedData.sort((a, b) => {
                        // Ensure case-insensitive comparison for Division
                        const divisionA = a.Division.toLowerCase();
                        const divisionB = b.Division.toLowerCase();
                        if (divisionA < divisionB) return -1; // a comes before b
                        if (divisionA > divisionB) return 1;  // a comes after b
                        return 0; // a and b are equal
                    }).forEach((a) => {
                        tableHTML += "<tr>";
                        tableHTML += `<td>${a.Division}</td>`;
                        tableHTML += `<td>${a.District}</td>`;
                        tableHTML += `<td>${a["College Name"]}</td>`;
                        tableHTML += `<td>${a["Principal Name"]}</td>`;
                        tableHTML += `<td>${a["Principal Mobile No."]}</td>`;
                        tableHTML += `<td>${a["AISHE Code"]}</td>`;
                        tableHTML += `<td>${a.Email}</td>`;
                        tableHTML += "</tr>";
                    });
                    tableHTML += "</tbody></table>";

                    const excelFileContent = `
          <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
            <head><!--[if gte mso 9]><xml>
              <x:ExcelWorkbook>
                <x:ExcelWorksheets>
                  <x:ExcelWorksheet>
                    <x:Name>Sheet1</x:Name>
                    <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                  </x:ExcelWorksheet>
                </x:ExcelWorksheets>
              </x:ExcelWorkbook>
            </xml><![endif]-->
            </head>
            <body>${tableHTML}</body>
          </html>
        `;
                    const blob = new Blob([excelFileContent], {
                        type: "application/vnd.ms-excel;charset=utf-8;",
                    });
                    const date = new Date().toLocaleDateString();
                    const downloadLink = document.createElement("a");
                    downloadLink.href = URL.createObjectURL(blob);
                    downloadLink.download = `Verified_College_Report_${date}.xls`;
                    downloadLink.click();
                } else {
                    SwalMessageAlert("No data available for export.", "warning");
                }
            })
            .catch((error) => {
                SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
            });
    };

    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Card className="shadow">
                    <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                        <Col xs="6">
                            <h3 className="mb-0">Institute List</h3>
                        </Col>
                        <Col className="text-right" xs="4">
                            <Button
                                color="primary"
                                href="#"
                                onClick={exportToExcel}
                                size="sm"
                            >
                                Generate Verified Report
                            </Button>
                        </Col>
                        <FormControl
                            type="text"
                            placeholder="Search Institute..."
                            className="ml-auto"
                            value={filterText}
                            onChange={(e) => setFilterText(e.target.value)}
                            style={{ width: "250px", borderRadius: "30px" }}
                        />
                    </CardHeader>
                    <CardBody>
                        <DataTable
                            columns={columns}
                            data={filteredData}
                            pagination
                            paginationPerPage={10}
                            highlightOnHover
                            stripedsortable={sortedData}
                            defaultSortField="name" // Sort by the 'name' column initially
                            defaultSortAsc={true} // Ascending order
                            customStyles={{
                                header: {
                                    style: {
                                        backgroundColor: "#f8f9fa", // Light background color for header
                                        fontWeight: "bold",
                                    },
                                },
                                rows: {
                                    style: {
                                        backgroundColor: "#fff", // Row color
                                        borderBottom: "1px solid #ddd",
                                    },
                                    // Apply hover effect through the :hover pseudo-class directly in custom CSS
                                    onHoverStyle: {
                                        backgroundColor: "#ffff99", // Hover color
                                    },
                                },
                            }}
                        />
                    </CardBody>
                </Card>
            </Container>
        </>
    );
};

export default CollegeChargeList;
