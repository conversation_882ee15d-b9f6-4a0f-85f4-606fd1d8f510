import { useEffect, useState } from "react";

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Modal,
  ModalHeader,
  ModalBody,
  Container,
  Row,
  Col,
} from "reactstrap";
// core components
import Header from "../../components/Headers/Header.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import axios from "axios";
import formatDate from "../../utils/formateDate.jsx";


const ProfileCopyForBackup = () => {



  const id = sessionStorage.getItem("id");
  const [user, setUser] = useState([]);
  const [clgInfo, setClgInfo] = useState([]);

  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const [base64Image, setBase64Image] = useState("");
  const [isImageModalOpen, setImageIsModalOpen] = useState(false);
  function handleConvertAndDisplay(base64) {
    const sampleBase64Image = `data:image/png;base64,${base64}`;
    setBase64Image(sampleBase64Image);
    setImageIsModalOpen(true);
  }

  const toggleImageModal = () => {
    setImageIsModalOpen(!isImageModalOpen);
  };


  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUser(data);

        } else {
          alert("Data Not Fetched.");
          // navigate('/auth/Register');
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
        // navigate('/auth/Register');
      }
    };
    fetchEmployeeData();
  }, [token]);

  useEffect(() => {
    const fetchCollegeInfo = async () => {

      if (user && user.college) { // Check if user and college are defined

        try {
          const response = await axios.get(
            `${endPoint}/api/college/get-college/${user.college}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            setClgInfo(response.data);
          } else {
            alert("College Not Found.");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      }
    };
    fetchCollegeInfo();
  }, [user]);

  return (
    <>
      <Header />
      {/* Page content */}
      <Container className="mt--7" fluid>
        <Modal
          isOpen={isImageModalOpen}
          toggle={toggleImageModal}
          style={{ textAlign: "center" }}
        >
          <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
          <ModalBody>
            <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
          </ModalBody>
        </Modal>
        <Row>
          <Col className="order-xl-1 mb-5 mb-xl-0" xl="3">
            <Card style={{boxShadow:"1px 5px 10px 3px black"}}>
              <Row className="justify-content-center md-7">
                <Col className="order-lg-2" lg="3">
                  <div className="card-profile-image">
                    <a href="#pablo" onClick={(e) => e.preventDefault()}>
                      {user.encodedImage && user.faceVerified !== false && user.encodedImage !== "" ? (
                        <img
                          src={`data:image/png;base64,${user.encodedImage}`}
                          onClick={() => handleConvertAndDisplay(user.encodedImage)}
                          style={{
                            width: "220px",
                            height: "180px", // Ensure height matches width for a perfect circle
                            borderRadius: "100%", // Make the image round
                            objectFit: "cover", // Ensure the image covers the area without distortion
                          }}
                          alt="User  Profile" // Add alt text for accessibility
                        />
                      ) : (
                        <img
                          src={NavImage}
                          style={{
                            width: "220px",
                            height: "180px", // Ensure height matches width for a perfect circle
                            borderRadius: "100%", // Make the image round
                            objectFit: "cover", // Ensure the image covers the area without distortion
                          }}
                          alt="Default Profile" // Add alt text for accessibility
                        />
                      )}
                    </a>
                  </div>
                </Col>
              </Row>
              <CardHeader className="text-center border-0 pt-8 pt-md-4 pb-0 pb-md-4 mt-5">

              </CardHeader>
              <br />
              <hr className="my-4 primary" />

              <CardBody className="pt-0 pt-md-0">
                <div className="text-center mt--3">
                  <h3 className="font-weight-bolder mb-1"> {/* Reduced margin-bottom */}
                    {user.name} <br />
                  </h3>
                </div>
                <div className="h5 mt-1 text-center"> {/* Reduced margin-top */}
                  {user.designationName}
                </div>
                <div className="h5 text-center mb--4 mt--2">
                  <h3 className="mb-1"> {/* Reduced margin-bottom */}
                  <span style={{ color: "black", fontSize: "12px" , fontWeight:"bolder" }}> EMP Code - </span> (<span style={{ color: "red", fontSize: "14px" }}>{user.empCode}</span>) <br />
                    <span className="font-weight-bold">
                      {user.activeStatus === true ? (
                        <span style={{ fontSize: "14px", color: "green" }}>Active</span>
                      ) : (
                        <span style={{ fontSize: "18px", color: "red" }}>Inactive</span>
                      )}
                    </span>
                  </h3>
                </div>

                <hr />

                <div className="text-center mb--4 mt--4">
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <a href={`mailto:${user.email}`}>{user.email}</a>
                  </div>
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <a href={`mailto:${user.contact}`}>{user.contact}</a>
                  </div>
                </div>
                <hr />

                <div className="text-center mb-4 mt--4">
                  <div>
                    <i className="ni education_hat mr-2" />
                    <span className="h5 font-weight-800">{clgInfo.name}</span>
                  </div>
                  <div className="h5 font-weight-600 mb-1"> {/* Reduced margin-bottom */}
                    <i className="ni location_pin mr-2" />
                    Residential Address - {user.address}
                  </div>
                  <hr className="my-4" />

                  <div className="d-block justify-content-center mt--2">
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Registered: <span className="text-primary">{formatDate(user.createdAt)}</span>
                      </span>
                    </div>
                    <div>
                      <span style={{ fontSize: "16px", fontWeight: `bolder` }}>
                        Last Updated : <span className="text-primary">{formatDate(user.updatedAt)}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </CardBody>
            </Card>
          </Col>

          <Col className="order-xl-2" xl="9">
          <Card style={{boxShadow:"1px 5px 10px 3px black"}}>
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">My Profile</h3>
                  </Col>
                  {/* <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#pablo"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col> */}
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <h6 className="heading-small text-muted mb-4">
                    User information
                  </h6>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-username"
                          >
                            Username
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.name}
                            id="input-username"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-email"
                          >
                            Email address
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.email}
                            id="input-email"
                            placeholder="<EMAIL>"
                            type="email"
                            disabled
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                  </div>
                  <hr className="my-4" />
                  {/* Address */}
                  <h6 className="heading-small text-muted mb-4">
                    Contact information
                  </h6>
                  <div className="pl-lg-4">
                    <Row>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-address"
                          >
                            Contact Number
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.contact}
                            id="input-address"
                            placeholder="Home Address"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            District Name
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.districtName}
                            id="input-district"
                            placeholder="district"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            Vidhansabha Name
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.vidhansabhaName}
                            id="input-district"
                            placeholder="district"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col md="12">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-address"
                          >
                            Address
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.address}
                            id="input-address"
                            placeholder="Home Address"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    {/* <Row>
                      <Col lg="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-city"
                          >
                            City
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue="New York"
                            id="input-city"
                            placeholder="City"
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-country"
                          >
                            Country
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue="United States"
                            id="input-country"
                            placeholder="Country"
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-country"
                          >
                            Postal code
                          </label>
                          <Input
                            className="form-control-alternative"
                            id="input-postal-code"
                            placeholder="Postal code"
                            type="number"
                          />
                        </FormGroup>
                      </Col>
                    </Row> */}
                  </div>
                  <hr className="my-4" />
                  {/* Description */}
                  <h6 className="heading-small text-muted mb-4">Other Details</h6>
                  <div className="pl-lg-4">
                    <Row>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                          >
                            Employee At
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.employeeType}
                            id="input-address"
                            placeholder="Home Address"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            Work Type
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.workType}
                            id="input-district"
                            placeholder="district"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                      <Col md="4">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            Designation Name
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={user.designationName}
                            id="input-district"
                            placeholder="district"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>

                    </Row>
                    <Row>
                      <Col md="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            Institute Name
                          </label>
                          <Input
                            className="form-control-alternative"
                            defaultValue={clgInfo.name}
                            id="input-district"
                            placeholder="district"
                            disabled
                            type="text"
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>

        </Row>
      </Container>
    </>
  );
};

export default ProfileCopyForBackup;
