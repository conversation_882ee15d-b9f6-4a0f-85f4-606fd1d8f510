body {
    color: #444;
    font-family: "Open Sans", sans-serif;
}

a {
    color: #007bff;
    text-decoration: none;
    transition: 0.5s;
}

    a:hover,
    a:active,
    a:focus {
        color: #0b6bd3;
        outline: none;
        text-decoration: none;
    }

#about p {
    padding: 0;
    margin: 0 0 30px 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Montserrat", sans-serif;
    font-weight: 400;
    margin: 0 0 20px 0;
    padding: 0;
}

.sirow {
    width: 100%;
}

.back-to-top {
    position: fixed;
    visibility: hidden;
    opacity: 0;
    right: 15px;
    bottom: 15px;
    z-index: 996;
    background: #007bff;
    width: 40px;
    height: 40px;
    border-radius: 50px;
    transition: all 0.4s;
}

    .back-to-top i {
        font-size: 28px;
        color: #fff;
        line-height: 0;
    }

    .back-to-top:hover {
        background: #2990ff;
        color: #fff;
    }

    .back-to-top.active {
        visibility: visible;
        opacity: 1;
    }

#header {
    height: 110px;
    z-index: 997;
    transition: all 0.5s;
    box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.3);
}

#header.header-scrolled {
    height: 90px;
}
.cg-logsadmin {margin-right:10px; width:14%; float:left; margin-top:15px; margin-bottom:0;}
.cg-logsadmin img {width:100%;}
.logo-admin {float:left; width:53%; margin-top:15px;}
.logo-admin a span {font-size: 30px; margin-left: 10px; margin-top: -10px; float: left; font-weight: 700; color: #106eea;}
.logo-admin a figure {width:25%; float:left; margin:0;}
.logo-admin a figure img {width:100%;}
.logo-admin a .logo_f {color:#000; font-weight:700;}
.form-check-label label {margin-left:10px;}

.header .logo-admin img {
    padding: 0;
    max-height: 70px;
}
.header-scrolled .logo-admin {width: 67%; margin-top:5px;}
.header-scrolled .logo-admin figure {
    width: 18%;
}

.header-scrolled .cg-logsadmin {width: 13%; margin-top:5px;}
.header-scrolled .logo-admin a span {margin-top: -10px;}
.navbar {
    padding: 0;
}

    .navbar ul {
        margin: 0;
        padding: 0;
        display: flex;
        list-style: none;
        align-items: center;
    }

    .navbar li {
        position: relative;
    }

    .navbar a,
    .navbar a:focus {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px 0 10px 30px;
        font-family: "Montserrat", sans-serif;
        font-size: 15px;
        color: #004289;
        font-weight: 500;
        white-space: nowrap;
        transition: 0.3s;
    }

        .navbar a i,
        .navbar a:focus i {
            font-size: 12px;
            line-height: 0;
            margin-left: 5px;
        }

        .navbar a:hover,
        .navbar .active,
        .navbar .active:focus,
        .navbar li:hover > a {
            color: #007bff;
        }

    .navbar .dropdown ul {
        display: block;
        position: absolute;
        left: 14px;
        top: calc(100% + 30px);
        margin: 0;
        padding: 10px 0;
        z-index: 99;
        opacity: 0;
        visibility: hidden;
        background: #fff;
        box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
        transition: 0.3s;
    }

        .navbar .dropdown ul li {
            min-width: 200px;
        }

        .navbar .dropdown ul a {
            padding: 10px 20px;
            font-size: 14px;
            text-transform: none;
        }

            .navbar .dropdown ul a i {
                font-size: 12px;
            }

            .navbar .dropdown ul a:hover,
            .navbar .dropdown ul .active:hover,
            .navbar .dropdown ul li:hover > a {
                color: #007bff;
            }

    .navbar .dropdown:hover > ul {
        opacity: 1;
        top: 100%;
        visibility: visible;
    }

    .navbar .dropdown .dropdown ul {
        top: 0;
        left: calc(100% - 30px);
        visibility: hidden;
    }

    .navbar .dropdown .dropdown:hover > ul {
        opacity: 1;
        top: 0;
        left: 100%;
        visibility: visible;
    }

@media (max-width: 1366px) {
    .navbar .dropdown .dropdown ul {
        left: -90%;
    }

    .navbar .dropdown .dropdown:hover > ul {
        left: -100%;
    }
}

.mobile-nav-toggle {
    color: #283d50;
    font-size: 28px;
    cursor: pointer;
    display: none;
    line-height: 0;
    transition: 0.5s;
}

    .mobile-nav-toggle.bi-x {
        color: #fff;
    }

@media (max-width: 991px) {
    .mobile-nav-toggle {
        display: block;
    }

    .navbar ul {
        display: none;
    }
}

.navbar-mobile {
    position: fixed;
    overflow: hidden;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(23, 35, 46, 0.9);
    transition: 0.3s;
    z-index: 999;
}

    .navbar-mobile .mobile-nav-toggle {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .navbar-mobile ul {
        display: block;
        position: absolute;
        top: 55px;
        right: 15px;
        bottom: 15px;
        left: 15px;
        padding: 10px 0;
        background-color: #fff;
        overflow-y: auto;
        transition: 0.3s;
    }

    .navbar-mobile a,
    .navbar-mobile a:focus {
        padding: 10px 20px;
        font-size: 15px;
        color: #004289;
    }

        .navbar-mobile a:hover,
        .navbar-mobile .active,
        .navbar-mobile li:hover > a {
            color: #007bff;
        }

    .navbar-mobile .dropdown ul {
        position: static;
        display: none;
        margin: 10px 20px;
        padding: 10px 0;
        z-index: 99;
        opacity: 1;
        visibility: visible;
        background: #fff;
        box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
    }

        .navbar-mobile .dropdown ul li {
            min-width: 200px;
        }

        .navbar-mobile .dropdown ul a {
            padding: 10px 20px;
        }

            .navbar-mobile .dropdown ul a i {
                font-size: 12px;
            }

            .navbar-mobile .dropdown ul a:hover,
            .navbar-mobile .dropdown ul .active:hover,
            .navbar-mobile .dropdown ul li:hover > a {
                color: #007bff;
            }

    .navbar-mobile .dropdown > .dropdown-active {
        display: block;
    }

/*--------------------------------------------------------------
# Hero Section
--------------------------------------------------------------*/
/*#hero {
  width: 100%;
  position: relative;
  background: url("../images/hero-bg.png") center bottom no-repeat;
  background-size: cover;
  padding: 200px 0 120px 0;
}*/

/*@media (max-width: 991px) {
  #hero {
    padding: 140px 0 60px 0;
  }
}

@media (max-width: 574px) {
  #hero {
    padding: 100px 0 20px 0;
  }
}

#hero .hero-img {
  width: 50%;
  float: right;
}

@media (max-width: 991px) {
  #hero .hero-img {
    width: 80%;
    float: none;
    margin: 0 auto 25px auto;
  }
}

#hero .hero-info {
  width: 50%;
  float: left;
}

@media (max-width: 991px) {
  #hero .hero-info {
    width: 80%;
    float: none;
    margin: auto;
    text-align: center;
  }
}

@media (max-width: 767px) {
  #hero .hero-info {
    width: 100%;
  }
}

#hero .hero-info h2 {
  color: #fff;
  margin-bottom: 40px;
  font-size: 48px;
  font-weight: 700;
}

#hero .hero-info h2 span {
  color: #74b5fc;
  text-decoration: underline;
}

@media (max-width: 767px) {
  #hero .hero-info h2 {
    font-size: 34px;
    margin-bottom: 30px;
  }
}

#hero .hero-info .btn-get-started,
#hero .hero-info .btn-services {
  font-family: "Montserrat", sans-serif;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
  display: inline-block;
  padding: 10px 32px;
  border-radius: 50px;
  transition: 0.5s;
  margin: 0 20px 20px 0;
  color: #fff;
}

#hero .hero-info .btn-get-started {
  background: #007bff;
  border: 2px solid #007bff;
  color: #fff;
}

#hero .hero-info .btn-get-started:hover {
  background: none;
  border-color: #fff;
  color: #fff;
}

#hero .hero-info .btn-services {
  border: 2px solid #fff;
}

#hero .hero-info .btn-services:hover {
  background: #007bff;
  border-color: #007bff;
  color: #fff;
}*/

section {
    overflow: hidden;
}

.section-header h3 {
    font-size: 36px;
    color: #283d50;
    text-align: center;
    font-weight: 500;
    position: relative;
}

.section-header p {
    text-align: center;
    margin: auto;
    font-size: 15px;
    padding-bottom: 60px;
    color: #556877;
    width: 50%;
}

@media (max-width: 767px) {
    .section-header p {
        width: 100%;
    }
}

.section-bg {
    background: #ecf5ff;
}

.breadcrumbs {
    padding: 20px 0;
    background-color: #f5faff;
    min-height: 40px;
    margin-top: 80px;
}

@media (max-width: 992px) {
    .breadcrumbs {
        margin-top: 60px;
    }
}

.breadcrumbs h2 {
    font-size: 24px;
    font-weight: 300;
    margin: 0;
}

@media (max-width: 992px) {
    .breadcrumbs h2 {
        margin: 0 0 10px 0;
    }
}

.breadcrumbs ol {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
    font-size: 14px;
}

    .breadcrumbs ol li + li {
        padding-left: 10px;
    }

        .breadcrumbs ol li + li::before {
            display: inline-block;
            padding-right: 10px;
            color: #6c757d;
            content: "/";
        }

@media (max-width: 768px) {
    .breadcrumbs .d-flex {
        display: block !important;
    }

    .breadcrumbs ol {
        display: block;
    }

        .breadcrumbs ol li {
            display: inline-block;
        }
}

#about {
    background: #fff;
    padding: 60px 0;
}

    #about .about-container .background {
        margin: 20px 0;
    }

    #about .about-container .content {
        background: #fff;
    }

    #about .about-container .title {
        color: #333;
        font-weight: 700;
        font-size: 32px;
    }

    #about .about-container p {
        line-height: 26px;
    }

        #about .about-container p:last-child {
            margin-bottom: 0;
        }

    #about .about-container .icon-box {
        background: #fff;
        background-size: cover;
        padding: 0 0 30px 0;
    }

        #about .about-container .icon-box .icon {
            float: left;
            background: #fff;
            width: 64px;
            height: 64px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            border: 2px solid #007bff;
            transition: all 0.3s ease-in-out;
        }

            #about .about-container .icon-box .icon i {
                color: #007bff;
                font-size: 24px;
                line-height: 0;
            }

        #about .about-container .icon-box:hover .icon {
            background: #007bff;
        }

            #about .about-container .icon-box:hover .icon i {
                color: #fff;
            }

        #about .about-container .icon-box .title {
            margin-left: 80px;
            font-weight: 600;
            margin-bottom: 5px;
            font-size: 18px;
        }

            #about .about-container .icon-box .title a {
                color: #283d50;
            }

        #about .about-container .icon-box .description {
            margin-left: 80px;
            line-height: 24px;
            font-size: 14px;
        }

    #about .about-extra {
        padding-top: 60px;
    }

        #about .about-extra h4 {
            font-weight: 600;
            font-size: 24px;
        }

#footer {
    background: #00428a;
    padding: 0 0 30px 0;
    color: #eee;
    font-size: 14px;
}

    #footer .footer-top {
        background: #004a99;
        padding: 60px 0 30px 0;
    }

        #footer .footer-top .footer-info {
            margin-bottom: 30px;
        }

            #footer .footer-top .footer-info h3 {
                font-size: 34px;
                margin: 0 0 20px 0;
                padding: 2px 0 2px 0;
                line-height: 1;
                font-family: "Montserrat", sans-serif;
                color: #fff;
                font-weight: 400;
                letter-spacing: 3px;
                text-transform: uppercase;
            }

            #footer .footer-top .footer-info p {
                font-size: 13px;
                line-height: 24px;
                margin-bottom: 0;
                font-family: "Montserrat", sans-serif;
                color: #ecf5ff;
            }

        #footer .footer-top .social-links a {
            font-size: 18px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            background: #007bff;
            color: #fff;
            margin-right: 4px;
            border-radius: 50%;
            text-align: center;
            width: 36px;
            height: 36px;
            transition: 0.3s;
        }

            #footer .footer-top .social-links a i {
                line-height: 0;
            }

            #footer .footer-top .social-links a:hover {
                background: #0067d5;
                color: #fff;
            }

        #footer .footer-top h4 {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
            text-transform: uppercase;
            position: relative;
            padding-bottom: 10px;
        }

        #footer .footer-top .footer-links {
            margin-bottom: 30px;
        }

            #footer .footer-top .footer-links ul {
                list-style: none;
                padding: 0;
                margin: 0;
            }

                #footer .footer-top .footer-links ul li {
                    padding: 8px 0;
                }

                    #footer .footer-top .footer-links ul li:first-child {
                        padding-top: 0;
                    }

                #footer .footer-top .footer-links ul a {
                    color: #ecf5ff;
                }

                    #footer .footer-top .footer-links ul a:hover {
                        color: #74b5fc;
                    }

        #footer .footer-top .footer-contact {
            margin-bottom: 30px;
        }

            #footer .footer-top .footer-contact p {
                line-height: 26px;
            }

        #footer .footer-top .footer-newsletter {
            margin-bottom: 30px;
        }

            #footer .footer-top .footer-newsletter input[type=email] {
                border: 0;
                padding: 6px 8px;
                width: 65%;
            }

            #footer .footer-top .footer-newsletter input[type=submit] {
                background: #007bff;
                border: 0;
                width: 35%;
                padding: 6px 0;
                text-align: center;
                color: #fff;
                transition: 0.3s;
                cursor: pointer;
            }

                #footer .footer-top .footer-newsletter input[type=submit]:hover {
                    background: #0062cc;
                }

    #footer .copyright {
        text-align: center;
        padding-top: 30px;
    }

    #footer .credits {
        text-align: center;
        font-size: 13px;
        color: #f1f7ff;
    }

        #footer .credits a {
            color: #bfddfe;
        }

            #footer .credits a:hover {
                color: #f1f7ff;
            }

.inner-pics .hero-img img {
    margin-top: 110px;
    width: 100%;
}

.login-content {
    padding: 20px;
    border: 1px solid #ffdada;
    background: #fff6f6;
}

    .login-content .alert-light {
        background: #fbf1f1;
        border: 1px solid #fdfdfe;
    }

.form-details .nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-item.show .nav-link, .nav-tabs .nav-link.active {
    color: #ffffff;
    background-color: #e55252;
    border-color: #bf2b2b #bf2b2b #fff;
    -moz-box-shadow: inset 0 -10px 10px -10px rgb(0 0 0 / 50%);
    -webkit-box-shadow: inset 0 -10px 10px -10px rgb(0 0 0 / 50%);
    box-shadow: inset 0 -10px 10px -10px rgb(0 0 0 / 50%);
}

.nav-tabs .nav-link {
    border: 1px solid #ddd;
    color: #000;
}

    .nav-tabs .nav-link:focus, .nav-tabs .nav-link:hover {
        border-color: #e55252 #e55252 #fdfdfd;
    }

.nav-tabs .nav-item {
    margin-bottom: 0;
}

.tabbing-shadow {
    padding: 20px;
    background: #fdfdfd;
    border: 1px solid #b9b9b9 !important;
    box-shadow: 0px 0px 8px rgb(0 0 0 / 30%);
    -moz-box-shadow: 0px 0px 8px rgb(0 0 0 / 30%);
    -webkit-box-shadow: 0px 0px 8px rgb(0 0 0 / 30%);
}

.heading-h2 {
    position: relative;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    font-size: 22px;
    font-weight: 700;
}

    .heading-h2::after {
        position: absolute;
        left: 0px;
        bottom: -2px;
        width: 100px;
        height: 3px;
        content: '';
        background: #f00;
    }

.light-background {
    background: #f9f9f9;
    padding: 15px;
    border: 1px solid #e9e7e7;
    margin-bottom: 20px;
}

.pic-upload {
}

.photosinfo img {
    width: 100%;
    height: 170px;
    margin-bottom: 10px;
}

.signainfo img {
    width: 100%;
    height: 50px;
    margin-bottom: 10px;
}

.file {
    position: relative;
    display: inline-block;
    cursor: pointer;
}

    .file input {
        width: 100%;
        margin: 0;
        filter: alpha(opacity=0);
        opacity: 0;
    }

.file-custom {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    z-index: 5;
    height: 30px;
    padding: 5px;
    line-height: 16px;
    color: #555;
    background-color: #fff;
    border: 0.075rem solid #e55252;
    border-radius: 0.25rem;
    box-shadow: inset 0 0.2rem 0.4rem rgb(0 0 0 / 5%);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    font-size: 13px;
}

    .file-custom:before {
        position: absolute;
        top: -0.075rem;
        right: -0.075rem;
        bottom: -0.075rem;
        z-index: 6;
        display: block;
        content: "Browse";
        height: 30px;
        padding: 5px;
        line-height: 16px;
        color: #fff;
        background-color: #e55252;
        border: 0.075rem solid #e55252;
        border-radius: 0 0.25rem 0.25rem 0;
    }

    .file-custom:after {
        content: "Choose file...";
    }
@media only screen and (max-width: 767px) {
    .logo-admin {width:100%; text-align:center;}
    .logo-admin a {display:inline-block;}
    .cg-logsadmin {margin: 10px auto 0; float: inherit; width: 25%;}
    .logo-admin a figure {width: 35%; margin: 0 auto; float: inherit;}
    .logo-admin a span {margin:0; float:inherit;}




}
