import { useState, useEffect } from "react";
import {
  <PERSON>,
  CardHeader,
  CardFooter,
  Label,
  Pagination,
  PaginationItem,
  PaginationLink,
  Table,
  Container,
  Row,
  Col,
  CardBody,
  Form,
  FormGroup,
  Input,
  Button,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import "react-calendar/dist/Calendar.css"; // Import calendar styles
import axios from 'axios'; // Import axios for HTTP requests
import MyCalendar from "../leave/Calander.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from 'sweetalert2';

const HolidayDetails = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [holidays, setHolidays] = useState([]);
  const [newHoliday, setNewHoliday] = useState({
    name: "",
    date: "",
    type: "",
  });
  const [modal, setModal] = useState(false);
  const [holidayToUpdate, setHolidayToUpdate] = useState({
    name: "",
    date: "",
    type: "",
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(5); // Change this value to set items per page


  const handleDelete = async (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axios.delete(`${endPoint}/api/holiday/delete-holiday/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          });
          if (response.status === 200) {
            Swal.fire("Deleted!", "Your item has been deleted.", "success");
            window.location.reload();
          }
        } catch (error) {
          console.error("Error deleting item:", error);
          Swal.fire("Error!", "Failed to delete the item. Please try again.", "error");
        }
      }
    })
  };

  useEffect(() => {
    const fetchHolidays = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/holiday/get-all-holiday`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          setHolidays(response.data);
        } else {
          alert("Failed to fetch Holiday data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchHolidays();
  }, [endPoint, token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setNewHoliday({ ...newHoliday, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    e.preventDefault();
    try {
      const response = await axios.post(`${endPoint}/api/holiday/add`, newHoliday, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 201) {
        SwalMessageAlert("Holiday Added Successfully", "success");
        window.location.reload();
      }
    } catch (error) {
      console.error("Error adding holiday:", error);
    }
  };

  const toggleModal = () => {
    setModal(!modal);
  };

  const formatDateForInput = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    if (isNaN(date)) return ''; // Check if the date is valid
    return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
  };

  const handleUpdateInputChange = (e) => {
    const { name, value } = e.target;

    if (name === "date") {
      setHolidayToUpdate({ ...holidayToUpdate, [name]: value });
    } else {
      setHolidayToUpdate({ ...holidayToUpdate, [name]: value });
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    try {
      const response = await axios.put(`${endPoint}/api/holiday/update-holiday/${holidayToUpdate.id}`, holidayToUpdate, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        SwalMessageAlert("Holiday Updated Successfully", "success");
        setModal(false);
        window.location.reload(); // Reload to fetch updated data
      }
    } catch (error) {
      console.error("Error updating holiday:", error);
    }
  };

  // Pagination Logic
  const indexOfLastHoliday = currentPage * itemsPerPage;
  const indexOfFirstHoliday = indexOfLastHoliday - itemsPerPage;
  const currentHolidays = holidays.slice(indexOfFirstHoliday, indexOfLastHoliday);

  const paginate = (pageNumber) => setCurrentPage(pageNumber);

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <h3 className="mb-0">Add New Holiday</h3>
              </CardHeader>
              <Form >
                <Row className="p-4">
                  <Col>
                    <Row>
                      <Col>
                        <FormGroup>
                          <Label>Holiday Name</Label>
                          <Input
                            type="text"
                            name="name"
                            value={newHoliday.name}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col>
                        <FormGroup>
                          <Label>Date</Label>
                          <Input
                            type="date"
                            name="date"
                            value={newHoliday.date}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col>
                        <FormGroup>
                          <Label>Holiday Type</Label>
                          <Input
                            type="select"
                            name="type"
                            style={{ borderRadius: "3px" }}
                            value={newHoliday.type}
                            onChange={handleInputChange}
                            required
                          >
                            < option value="">Select Type</option>
                            <option value="Govt">Govt.</option>
                            <option value="Restricted">Restricted</option>
                          </Input>
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Button color="primary" onClick={handleSubmit} >
                          Add Holiday
                        </Button>
                      </Col>
                    </Row>
                  </Col>
                </Row>
              </Form>
            </Card>
          </Col>
        </Row>
        <Row>
          <Col>
            <Card style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }} className="mt-4">
              <CardHeader className="bg-transparent">
                <Row className="align-items-center">
                  <div className="col">
                    <h2 className="text-black font-weight-bolder mb-0">
                      Government Calendar
                    </h2>
                  </div>
                </Row>
              </CardHeader>
              <CardBody>
                <Row className="pt-3">
                  <Col style={{ borderRight: "1px solid gray" }}>
                    <MyCalendar style={{ backgroundColor: "white" }} />
                  </Col>
                </Row>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Row className="mt-4">
          <div className="col">
            <Card className="shadow">
              <CardHeader className="border-0">
                <h3 className="mb-0">Holiday List <span style={{ marginLeft: "30px", color: "blue" }}>(Count -{holidays.length} )</span></h3>
              </CardHeader>
              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                    <th scope="col">S. No.</th>
                    <th scope="col">Update</th>
                    <th scope="col">Holiday Name</th>
                    <th scope="col">Date</th>
                    <th scope="col">Holiday Type</th>
                    <th scope="col">Last Updated</th>
                    {/* <th scope="col">Delete</th> */}
                  </tr>
                </thead>
                <tbody>
                  {currentHolidays && currentHolidays.length > 0 ? currentHolidays.map((holiday, index) => (
                    <tr key={holiday.id}>
                      <th scope="row">{index + 1 + indexOfFirstHoliday}</th>
                      <td>
                        <Button size="sm"
                          className="btn-success"
                          onClick={() => {
                            setHolidayToUpdate({
                              id: holiday._id,
                              name: holiday.name,
                              date: holiday.date,
                              type: holiday.type,
                            });
                            toggleModal();
                          }}
                        >
                          Update
                        </Button>
                      </td>
                      <td>{holiday.name}</td>
                      {/* <td>{new Date(holiday.date).toLocaleDateString()}</td>
                       */}

                      <td>
                        {new Date(holiday.date).toISOString().slice(0, 10).split('-').reverse().join('-')}
                      </td>

                      <td>{holiday.type}</td>
                      <td>
                        {new Date(holiday.updatedAt).toISOString().slice(0, 10).split('-').reverse().join('-')}
                      </td>

                      {/* <td>{new Date(holiday.updatedAt).toLocaleDateString()}</td> */}
                      {/* <td>
                        <Button size="sm" className="btn-danger" onClick={() => handleDelete(holiday._id)} >
                          Delete
                        </Button>
                      </td> */}
                    </tr>
                  )) : <tr>
                    <td>No Records Found</td>
                  </tr>}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <nav aria-label="...">
                  {holidays.length > 0 && (
                    <Pagination className="pagination justify-content-end mb-0" listClassName="justify-content-end mb-0">
                      <PaginationItem className={currentPage === 1 ? "disabled" : ""}>
                        <PaginationLink href="#" onClick={(e) => { e.preventDefault(); paginate(currentPage - 1); }} tabIndex="-1">
                          <i className="fas fa-angle-left" />
                          <span className="sr-only">Previous</span>
                        </PaginationLink>
                      </PaginationItem>
                      {Array.from({ length: Math.ceil(holidays.length / itemsPerPage) }, (_, index) => (
                        <PaginationItem key={index + 1} className={index + 1 === currentPage ? "active" : ''}>
                          <PaginationLink href="#" onClick={(e) => { e.preventDefault(); paginate(index + 1); }}>
                            {index + 1}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      <PaginationItem className={currentPage === Math.ceil(holidays.length / itemsPerPage) ? "disabled" : ""}>
                        <PaginationLink href="#" onClick={(e) => { e.preventDefault(); paginate(currentPage + 1); }}>
                          < i className="fas fa-angle-right" />
                          <span className="sr-only">Next</span>
                        </PaginationLink>
                      </PaginationItem>
                    </Pagination>
                  )}
                </nav>
              </CardFooter>
            </Card>
          </div>
        </Row>

        <Modal isOpen={modal} toggle={toggleModal}>
          <ModalHeader toggle={toggleModal}>Update Holiday</ModalHeader>
          <ModalBody>
            <Form >
              <FormGroup>
                <Label>Holiday Name</Label>
                <Input
                  type="text"
                  name="name"
                  value={holidayToUpdate.name}
                  onChange={handleUpdateInputChange}
                  required
                />
              </FormGroup>
              <FormGroup>
                <Label>Date</Label>
                <Input
                  type="date"
                  name="date"
                  value={formatDateForInput(holidayToUpdate.date)}
                  onChange={handleUpdateInputChange}
                  required
                />
              </FormGroup>
              <FormGroup>
                <Label>Holiday Type</Label>
                <Input
                  type="select"
                  name="type"
                  value={holidayToUpdate.type}
                  onChange={handleUpdateInputChange}
                  required
                >
                  <option value="">Select Type</option>
                  <option value="Govt">Govt.</option>
                  <option value="Restricted">Restricted</option>
                </Input>
              </FormGroup>
              <Button color="primary" onClick={handleUpdateSubmit}>
                Update Holiday
              </Button>
            </Form>
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={toggleModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};


export default HolidayDetails;