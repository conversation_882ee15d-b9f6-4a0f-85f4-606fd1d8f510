import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  Con<PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  Col,
  FormGroup,
  Badge,
  Button,
  Label,
  ButtonGroup,
  Row,
  Input,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Table,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Select from "react-select";
import TransferTimeline from "../../views/Admin/TransferTimeline.jsx";
import { BsDownload, BsEye } from "react-icons/bs";


import formatDate from "../../utils/formateDate";

const TransferedEmpList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [empData, setEmpData] = useState([]);
  const [allEmpData, setAllEmpData] = useState([]); // Full original dataset
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [startDate, setStartDate] = useState(""); // start date filter
  const [endDate, setEndDate] = useState(""); // end date filte

  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        let response;
        if (selectedCollege === null) {
          response = await axios.get(
            `${endPoint}/api/get-all/transferred-employee-all`,
            {
              headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
            }
          );
        } else {
          response = await axios.get(
            `${endPoint}/api/staged-transfered-data-byClgId/${selectedCollege}`,
            {
              headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
            }
          );
        }
        if (response.status === 200) {
          setEmpData(response.data);
          setAllEmpData(response.data);
          console.log("");
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchSubjectList();
  }, [endPoint, token, selectedCollege]);

  const [timelineModal, setTimelineModal] = useState(false);
  const toggleTimeModal = () => setTimelineModal(!timelineModal);
  const [selectedEmployee, setSelectedEmployee] = useState("");

  const columns = [
    {
      name: "S.no",
      selector: (row, index) => index + 1,
      width: "100px",
      sortable: true,
    },
    {
      name: "Transfer ID / Date",
      selector: (row) => (
        <>
          <div className="font-weight-700">{row.transferID}</div>
          <div className="font-weight-700">({formatDate(row.createdAt)})</div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Transfer Order No / Date",
      width: "180px",
      selector: (row) => (
        <>
          <div>{row.transferOrderNo}</div>
          <div style={{ fontSize: "0.85em", color: "#555" }}>
            {formatDate(row.transferOrderDate)}
          </div>
          <div>
            <strong>Carreer Timeline</strong>{" "}
            <Button
              onClick={() => {
                setSelectedEmployee(row.employeeDetails._id);
                toggleTimeModal();
              }}
              className="btn-sm bg-warning text-white"
            >
              view
            </Button>
          </div>
        </>
      ),
      sortable: true,
    },
    {
      name: "Employee Details",
      selector: (row) => (
        <div>
          <strong className="text-primary">{row.name || "N/A"}</strong>
          <br />
          <strong>Emp Code: </strong>
          {row.empCode || "N/A"}
          <br />
          <strong>S/o: </strong>
          {row.fatherName || "N/A"}
          <br />
          <strong>Designation: </strong>
          {row.designation || "N/A"}
          <br />
          <strong>Contact No: </strong>
          {row.mobileNo || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Institute Name",
      selector: (row) => (
        <div>
          <strong>From College: </strong>
          {row.transferFromCollege?.name || "N/A"}
          <br />
          <strong>Target College: </strong>
          {row.transferToCollege?.name || "N/A"}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Charge Relieving Details",
      selector: (row) => (
        <div>
          <strong>Relieving Status : </strong>
          {row.isChargeReleasingDone === true ? (
            <Badge className="bg-success text-white">Completed </Badge>
          ) : (
            <Badge className="bg-warning text-white">Pending </Badge>
          )}
          <br />
          {row.isChargeReleasingDone === true && (
            <>
              <strong>Order No :</strong>
              {row.chargeReleaseOrderNo || "N/A"}
              <br />
              <strong>Order Date :</strong>
              {formatDate(row.chargeReleaseOrderDate) || "N/A"}
              <br />
              <strong>Order Letter :</strong>            
              <a
                href={`https://heonline.cg.nic.in/${row.copyOfChargeReleaseLetter}`}
                download
              >
                <Button className="btn-sm btn-primary">
                  {row.copyOfChargeReleaseLetter ? <BsDownload color="blue"  size={18} /> : "No File"}
                </Button>
              </a>
            </>
          )}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
    {
      name: "Charge Joining Details",
      selector: (row) => (
        <div>
          <strong>Joining Status : </strong>
          {row.isJoiningDone === true ? (
            <Badge className="bg-success text-white">Completed </Badge>
          ) : (
            <Badge className="bg-warning text-white">Pending </Badge>
          )}
          <br />
          {row.isJoiningDone === true && (
            <>
              <strong>Joining Date :</strong>
              {formatDate(row.chargeJoinDate) || "N/A"}
              <br />
              <strong>Joining Letter :</strong>
              
              <a
                href={`https://heonline.cg.nic.in/${row.chargeJoiningLetter}`}
                download
              >
                <Button className="btn-sm btn-primary">
                  {row.chargeJoiningLetter ? <BsDownload color="blue"  size={18} /> : "No File"}
                </Button>
              </a>
            </>
          )}
        </div>
      ),
      sortable: true,
      grow: 2,
    },
  ];

  const handleFilterClick = (filter) => {
    setSelectedFilter(filter);

    let filteredData;
    if (filter === "all") {
      filteredData = allEmpData; // Use the full dataset
    } else if (filter === "incoming") {
      filteredData = allEmpData.filter(
        (emp) =>
          emp.transferToCollege._id.toString() === selectedCollege.toString()
      );
    } else if (filter === "outgoing") {
      filteredData = allEmpData.filter(
        (emp) =>
          emp.transferFromCollege._id.toString() === selectedCollege.toString()
      );
    }
    setEmpData(filteredData); // Update the displayed data
  };

  useEffect(() => {
    let filtered = allEmpData;
    if (searchTerm.trim() !== "") {
      filtered = filtered.filter((item) =>
        Object.keys(item).some((key) =>
          String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
    if (startDate) {
      filtered = filtered.filter(
        (item) => new Date(item.transferOrderDate) >= new Date(startDate)
      );
    }
    if (endDate) {
      filtered = filtered.filter(
        (item) => new Date(item.transferOrderDate) <= new Date(endDate)
      );
    }
    setEmpData(filtered);
  }, [searchTerm, startDate, endDate, allEmpData, setEmpData]);

  const handleStartDateChange = (e) => {
    setStartDate(e.target.value);
  };
  const handleEndDateChange = (e) => {
    setEndDate(e.target.value);
  };

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value); // Update searchTerm state
  };

  const [allColleges, setAllColleges] = useState([]);

  const collegeOptions =
    [
      { value: null, label: "Select All " },
      ...allColleges.map((college) => ({
        value: college._id,
        label: `${college.name} (${college.aisheCode})`,
      })),
    ] || [];

  useEffect(() => {
    const fetchAllColleges = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setAllColleges(response.data);
        } else {
          alert("Failed to fetch college data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchAllColleges();
  }, [endPoint, token]);

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="border-0 d-block justify-content-between align-items-center">
            <Row>
              <Col xs="3">
                <h3 className="mb-0">Transferd Employee List</h3>
              </Col>
              <Col md={3}>
                <Input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  style={{ marginBottom: "10px" }}
                />
              </Col>
              <Col md={3}>
                <Input
                  type="date"
                  placeholder="Start Date"
                  value={startDate}
                  onChange={handleStartDateChange}
                  style={{ marginBottom: "10px" }}
                  max={endDate || undefined} // prevent selecting after end date
                />
              </Col>
              <Col md={3}>
                <Input
                  type="date"
                  placeholder="End Date"
                  value={endDate}
                  onChange={handleEndDateChange}
                  style={{ marginBottom: "10px" }}
                  min={startDate || undefined} // prevent selecting before start date
                />
              </Col>
            </Row>
            <Row>
              <Col lg="3">
                <FormGroup>
                  <Label htmlFor="input-className">College Wise</Label>
                  <Select
                    name="transferToCollege"
                    options={collegeOptions}
                    onChange={(selectedOption) =>
                      setSelectedCollege(selectedOption.value)
                    }
                    placeholder="Select College"
                  />
                </FormGroup>
              </Col>

              {selectedCollege != null && (
                <>
                  <Col className="d-flex align-items-center">
                    <ButtonGroup>
                      <Button
                        color={
                          selectedFilter === "incoming"
                            ? "success"
                            : "secondary"
                        }
                        onClick={() => handleFilterClick("incoming")}
                      >
                        Incoming Transfer
                      </Button>
                      <Button
                        color={
                          selectedFilter === "outgoing"
                            ? "success"
                            : "secondary"
                        }
                        onClick={() => handleFilterClick("outgoing")}
                      >
                        Out-Going Transfer
                      </Button>
                      <Button
                        color={
                          selectedFilter === "all" ? "success" : "secondary"
                        }
                        onClick={() => handleFilterClick("all")}
                      >
                        All Transfer
                      </Button>
                    </ButtonGroup>
                  </Col>
                  <Col className="mt-4"><span  style={{fontWeight:"bolder"}}>Total {selectedFilter} Employee Count  {empData.length}</span></Col>
                </>
              )}
            </Row>
          </CardHeader>
          <CardBody>
            <DataTable
              columns={columns}
              data={empData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              defaultSortField="name"
              defaultSortAsc={true}
              customStyles={{
                header: {
                  style: {
                    backgroundColor: "#f8f9fa",
                    fontWeight: "bold",
                  },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff",
                    borderBottom: "1px solid #ddd",
                  },
                  // Apply hover effect through the :hover pseudo-class directly in custom CSS
                  onHoverStyle: {
                    backgroundColor: "#ffff99", // Hover color
                  },
                },
              }}
            />
          </CardBody>
        </Card>
        <Modal isOpen={timelineModal} toggle={toggleTimeModal}>
          <ModalHeader toggle={toggleTimeModal}>Carreer Timeline</ModalHeader>
          <ModalBody>
            {selectedEmployee && <TransferTimeline data={selectedEmployee} />}
          </ModalBody>

          <ModalFooter>
            <Button color="secondary" onClick={toggleTimeModal}>
              Close
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default TransferedEmpList;
