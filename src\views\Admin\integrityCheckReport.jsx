import { useState } from "react";
import {
  Container,
  <PERSON>,
  <PERSON>,
  But<PERSON>,
} from "reactstrap";
import DataTable from "react-data-table-component";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const TableIntegrityCheck = () => {
  const [integrityData, setIntegrityData] = useState(null);
  const [loading, setLoading] = useState(false);


// For Load Data 
const [password, setPassword] = useState("");
const [isUnlocked, setIsUnlocked] = useState(false);
const [passwordError, setPasswordError] = useState("");



  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");


const correctPassword = import.meta.env.VITE_INTEGRITY_PASSWORD || "admin123";

const handlePasswordUnlock = () => {
  if (password === correctPassword) {
    setIsUnlocked(true);
    setPasswordError("");
  } else {
    setPasswordError("Incorrect password. Please try again.");
  }
};


  const handleFetchIntegrityData = async () => {
    try {
      setLoading(true);

    const response = await axios.get(
        `${endPoint}/api/table-integrity-check-list`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      
      if (response.status === 200 && response.data.result) {
        setIntegrityData(response.data.result);
      } else {
        SwalMessageAlert("Failed to fetch integrity data. Please try again.", "warning");
      }
    } catch (error) {
      console.error("Error fetching integrity data:", error);
      SwalMessageAlert("An error occurred. Please try again later.", "error");
    } finally {
      setLoading(false);
    }
  };

  // Transform integrity data for table display
  const getTableData = () => {
    if (!integrityData) return [];
    
    return Object.entries(integrityData).map(([key, value]) => ({
      tableName: key,
      count: value.count,
      status: value.count === 0 ? "Good" : "Issues Found",
      statusIcon: value.count === 0 ? "✅" : "❌",
      ids: value.ids || [],
      data: value.data || [],
    }));
  };

  const columns = [
    {
      name: "Table Name",
      selector: (row) => row.tableName,
      sortable: true,
      cell: (row) => (
        <span className="fw-bold text-uppercase">{row.tableName}</span>
      ),
    },
    {
      name: "Integrity Status",
      selector: (row) => row.status,
      sortable: true,
      cell: (row) => (
        <div className={`d-flex align-items-center ${row.count === 0 ? 'text-success' : 'text-danger'}`}>
          <span className="me-2">{row.statusIcon}</span>
          <span className="fw-bold">{row.status}</span>
        </div>
      ),
    },
    {
      name: "Issue Count",
      selector: (row) => row.count,
      sortable: true,
      cell: (row) => (
        <span className={`fw-bold ${row.count === 0 ? 'text-success' : 'text-danger'}`}>
          {row.count}
        </span>
      ),
    },
    // {
    //   name: "Affected IDs",
    //   cell: (row) => (
    //     <div>
    //       {row.count === 0 ? (
    //         <span className="text-muted">No Issues</span>
    //       ) : (
    //         <div className="d-flex flex-wrap gap-1">
    //           {row.ids.slice(0, 3).map((id, index) => (
    //             <span key={index} className="badge bg-secondary">
    //               {id}
    //             </span>
    //           ))}
    //           {row.ids.length > 3 && (
    //             <span className="badge bg-info">
    //               +{row.ids.length - 3} more
    //             </span>
    //           )}
    //         </div>
    //       )}
    //     </div>
    //   ),
    // },
    // {
    //   name: "Actions",
    //   cell: (row) => (
    //     <div>
    //       {row.count > 0 ? (
    //         <Button
    //           size="sm"
    //           color="info"
    //           onClick={() => showDetails(row)}
    //         >
    //           View Details
    //         </Button>
    //       ) : (
    //         <span className="text-muted">No Action Needed</span>
    //       )}
    //     </div>
    //   ),
    // },
  ];

//   const showDetails = (row) => {
//     const detailsHTML = `
//       <div style="text-align: left;">
//         <h5>Table: ${row.tableName}</h5>
//         <p><strong>Total Issues:</strong> ${row.count}</p>
//         <div>
//           <strong>Affected IDs:</strong><br/>
//           ${row.ids.map(id => `<span style="display: inline-block; background: #6c757d; color: white; padding: 2px 6px; margin: 2px; border-radius: 3px; font-size: 12px;">${id}</span>`).join('')}
//         </div>
//       </div>
//     `;
    
//     SwalMessageAlert(detailsHTML, "info", true); // Assuming SwalMessageAlert supports HTML
//   };

  const customStyles = {
    header: {
      style: {
        backgroundColor: "#f8f9fa",
        fontWeight: "bold",
      },
    },
    rows: {
      style: {
        backgroundColor: "#fff",
        borderBottom: "1px solid #ddd",
        '&:hover': {
          backgroundColor: "#f5f5f5",
        },
      },
    },
    headCells: {
      style: {
        fontWeight: "bold",
        fontSize: "14px",
        backgroundColor: "#e9ecef",
      },
    },
  };

  return (
    <Container className="mt-4" fluid>

<Row className="mb-4">
  <Col md={{ size: 6, offset: 3 }} className="text-center">
    {!isUnlocked ? (
      <>
        <input
          type="password"
          className="form-control mb-2"
          placeholder="Enter password to unlock"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
        />
        <Button color="primary" onClick={handlePasswordUnlock}>
          Unlock Integrity Check
        </Button>
        {passwordError && (
          <p className="text-danger mt-2">{passwordError}</p>
        )}
      </>
    ) : (
      <Button
        color="success"
        onClick={handleFetchIntegrityData}
        disabled={loading}
        size="lg"
      >
        {loading ? (
          <>
            <span
              className="spinner-border spinner-border-sm me-2"
              role="status"
            ></span>
            Checking Table Integrity...
          </>
        ) : (
          "Check Table Integrity"
        )}
      </Button>
    )}
  </Col>
</Row>



      {/* <Row className="mb-4">
        <Col className="text-center">
          <Button 
            color="success" 
            onClick={handleFetchIntegrityData} 
            disabled={loading} 
            size="lg"
          >
            {loading ? (
              <>
                <span className="spinner-border spinner-border-sm me-2" role="status"></span>
                Checking Table Integrity...
              </>
            ) : (
              "Check Table Integrity"
            )}
          </Button>
        </Col>
      </Row> */}

      {integrityData && (
        <Row>
          <Col>
            <div className="shadow">
              <div className="bg-white border-0 p-3 d-flex justify-content-between align-items-center">
                <h3 className="mb-0">Table Integrity Check Results</h3>
                <div className="d-flex align-items-center">
                  <span className="me-3">
                    Total Tables Checked: <strong>{Object.keys(integrityData).length}</strong>
                  </span>||
                  <span className="me-3 text-success">
                    Good: <strong>{getTableData().filter(item => item.count === 0).length}</strong>
                  </span>||
                  <span className="text-danger me-3">
                    Issues: <strong>{getTableData().filter(item => item.count > 0).length}</strong>
                  </span>
                </div>
              </div>
              <div className="p-3">
                <DataTable
                  columns={columns}
                  data={getTableData()}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  customStyles={customStyles}
                  noDataComponent={
                    <div className="text-center py-4">
                      <p>No integrity data available. Click "Check Table Integrity" to run the check.</p>
                    </div>
                  }
                />
              </div>
            </div>
          </Col>
        </Row>
      )}
    </Container>
  );
};

export default TableIntegrityCheck;