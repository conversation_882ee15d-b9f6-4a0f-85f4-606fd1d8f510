import { lazy } from "react";
import loadable from "@loadable/component";
import Attendance from "../views/Attendance/markAttendance";

const UpdateDirector = loadable(() => import("../views/Admin/updateDirector"));
const InstituteUpdate = loadable(() =>
  import("../views/Admin/updateInstitute")
);
const UniversityUpdate = loadable(() =>
  import("../views/Admin/updateUniversity")
);
const MyCalendar = loadable(() => import("../views/leave/Calander"));
const EditAppliedLeave = loadable(() =>
  import("../views/leave/EditAppliedLeave")
);
const ChangePassword = loadable(() => import("../views/Admin/ChangePassword"));
const UpdateEmployee = loadable(() => import("../views/Admin/updateEmployee"));
const LeaveBalanceEntry = loadable(() => import("../views/leave/leaveBalance"));
const LeaveBalanceEntryByDirectorate = loadable(() =>
  import("../views/leave/LeaveBalanceEntryByDirectorate")
);
const UpdateDesignation = loadable(() =>
  import("../views/Admin/updateDesignation")
);
const UpdateDistrict = loadable(() => import("../views/Admin/updateDistrict"));
const UpdateVidhansabha = loadable(() =>
  import("../views/Admin/updateVidhansabha")
);
const LeaveRuleUpdate = loadable(() =>
  import("../views/leave/LeaveRulesUpdation")
);
const PrintApplication = loadable(() =>
  import("../views/leave/printApplication")
);
const PrintActionLetter = loadable(() =>
  import("../views/leave/PrintActionLetter")
);
const PrintIPR = loadable(() => import("../views/leave/PrintIprForm"));
const AddDesignation = loadable(() => import("../views/Admin/addDesignation"));
const ApplicationForDesignationWise = loadable(() =>
  import("../views/leave/ApplicationForDesignationWise")
);
const UpdateSection = loadable(() => import("../views/Admin/UpdateSection"));
const Profile = loadable(() => import("../views/Admin/Profile"));
const ErrorList = loadable(() => import("../views/ErrorLogs/ErrorList"));
const EmployeeProfile = loadable(() =>
  import("../views/Admin/employeeRegistrationClassWise")
);

const PrintEmpProfile = loadable(() =>
  import("../views/leave/printEmpProfile")
);
const NewStreamApplication = loadable(() =>
  import("../views/NavinCourese/newStreamApplication")
);
const NavinCourseGovtHelp = loadable(() =>
  import("../views/NavinCourese/NavinCourseGovtHelp")
);
const JanBhagiDariCourseUpdate = loadable(() =>
  import("../views/NavinCourese/JanBhagiDariCourseUpdate")
);
const NavinCourseGovtHelpUpdate = loadable(() =>
  import("../views/NavinCourese/NavinCourseGovtHelpUpdate")
);
const UpdateGuestFacultyForm = loadable(() =>
  import("../views/Admin/updateGuestFaculty")
);
const PaginatedTable = loadable(() => import("../views/Admin/employeeList"));
const ProfileUpdateInstitute = loadable(() =>
  import("../views/Admin/profileInstitute")
);
const InstituteProfile = loadable(() => import("../views/Admin/collegeProfile"));
const PrintCollegeProfile = loadable(() => import("../views/leave/PrintCollegeProfile"));

const IntegrityCheckReport = loadable(()=> import("../views/Admin/integrityCheckReport"));

var OtherRoutes = [
  {
    path: "/update-designation/:id",
    name: "Update Designation",
    icon: "ni ni-single-02 text-yellow",
    component: <UpdateDesignation />,
    layout: "/admin",
  },

  {
    path: "/userProfile",
    name: "User Profile",
    icon: "ni ni-single-02 text-yellow",
    component: <Profile />,
    layout: "/admin",
  },
  {
    path: "/update-section/:id",
    name: "Update Section",
    icon: "ni ni-single-02 text-yellow",
    component: <UpdateSection />,
    layout: "/admin",
  },
  {
    path: "/update-university/:id",
    name: "Update University",
    icon: "ni ni-single-02 text-yellow",
    component: <UniversityUpdate />,
    layout: "/admin",
  },
  {
    path: "/update-institute/:id",
    name: "Update University",
    icon: "ni ni-single-02 text-yellow",
    component: <InstituteUpdate />,
    layout: "/admin",
  },
  {
    path: "/update-Rules/:id",
    name: "Update Rules",
    icon: "ni ni-single-02 text-yellow",
    component: <LeaveRuleUpdate />,
    layout: "/admin",
  },
  {
    path: "/calander",
    name: "Calander",
    icon: "ni ni-check-bold text-primary",
    component: <MyCalendar />,
    layout: "/admin",
  },
  {
    path: "/update-leave-apply/:id",
    name: "Calander",
    icon: "ni ni-check-bold text-primary",
    component: <EditAppliedLeave />,
    layout: "/admin",
  },
  {
    path: "/Preview/:id",
    name: "Preview",
    icon: "ni ni-check-bold text-primary",
    component: <PrintApplication />,
    layout: "/admin",
  },
  {
    path: "/Genrated/:id",
    name: "Generated",
    icon: "ni ni-check-bold text-primary",
    component: <PrintActionLetter />,
    layout: "/admin",
  },
  {
    path: "/PreviewIPR/:id",
    name: "Preview IPR",
    icon: "ni ni-check-bold text-primary",
    component: <PrintIPR />,
    layout: "/admin",
  },
  {
    path: "/update-director/:id",
    name: "Update Director",
    icon: "ni ni-check-bold text-primary",
    component: <UpdateDirector />,
    layout: "/admin",
  },
  {
    path: "/change-password",
    name: "Change Password",
    icon: "ni ni-single-02 text-yellow",
    component: <ChangePassword />,
    layout: "/admin",
  },
  {
    path: "/update-employee/:id",
    name: "Update Employee",
    icon: "ni ni-check-bold text-primary",
    component: <UpdateEmployee />,
    layout: "/admin",
  },
  {
    path: "/addLeaveBalanceByPrincipal/:id",
    name: "Leave balance/ अवकाश खाता",
    icon: "ni ni-chart-pie-35 text-primary",
    component: <LeaveBalanceEntry />,
    layout: "/admin",
  },
  {
    path: "/LeaveBalanceEntryByDirectorate/:id",
    name: "Leave balance/ अवकाश खाता",
    icon: "ni ni-chart-pie-35 text-primary",
    component: <LeaveBalanceEntryByDirectorate />,
    layout: "/admin",
  },
  {
    path: "/update-district/:id",
    name: "Update District",
    icon: "ni ni-check-bold text-primary",
    component: <UpdateDistrict />,
    layout: "/admin",
  },
  {
    path: "/update-vidhansabha/:id",
    name: "Update Vidhansabha",
    icon: "ni ni-check-bold text-primary",
    component: <UpdateVidhansabha />,
    layout: "/admin",
  },
  {
    path: "/update-class/:id",
    name: "Update Class",
    icon: "ni ni-single-02 text-yellow",
    component: <AddDesignation />,
    layout: "/admin",
  },
  {
    path: "/applicationForDesignation",
    name: "Application ",
    icon: "ni ni-single-02 text-yellow",
    component: <ApplicationForDesignationWise />,
    layout: "/admin",
  },
  {
    path: "/nic/error-maintainence/errorlist",
    name: "Error List",
    icon: "ni ni-check-bold text-primary",
    component: <ErrorList />,
    layout: "/admin",
  },
  {
    path: "/nic/integrity-check",
    name: "Error List",
    icon: "ni ni-check-bold text-primary",
    component: <IntegrityCheckReport/>,
    layout: "/admin",
  },
  {
    path: "/profile-print/:id",
    name: "Profile Print",
    icon: "ni ni-single-02 text-yellow",
    component: <PrintEmpProfile />,
    layout: "/admin",
  },
  {
    path: "/update-course-janbhagidari/:applicationId",
    name: "Update Course",
    icon: "ni ni-single-02 text-yellow",
    component: <JanBhagiDariCourseUpdate />,
    layout: "/admin",
  },
  {
    path: "/update-course-govt/:applicationId",
    name: "Update Course",
    icon: "ni ni-single-02 text-yellow",
    component: <NavinCourseGovtHelpUpdate />,
    layout: "/admin",
  },
  {
    path: "/mark-attendance",
    name: "Mark Attendance",
    icon: "ni ni-single-02 text-yellow",
    component: <Attendance />,
    layout: "/admin",
  },

  {
    path: "/profile-employee/:id",
    name: "Mark Attendance",
    icon: "ni ni-single-02 text-yellow",
    component: <EmployeeProfile />,
    layout: "/admin",
  },
  {
    path: "/newCourse-print/:id",
    name: "New Course Print",
    icon: "ni ni-single-02 text-yellow",
    component: <NewStreamApplication />,
    layout: "/admin",
  },
  {
    path: "/new-course-govt",
    name: "New Course Govt",
    icon: "ni ni-single-02 text-yellow",
    component: <NavinCourseGovtHelp />,
    layout: "/admin",
  },
  {
    path: "/update/guest-lecture",
    name: "Update Faculty Data",
    icon: "ni ni-single-02 text-yellow",
    component: <UpdateGuestFacultyForm />,
    layout: "/admin",
  },
  {
    path: "/update/institute-profile",
    name: "Update Instittute Profile",
    icon: "ni ni-single-02 text-yellow",
    component: <ProfileUpdateInstitute />,
    layout: "/admin",
  },
  {
    path: "/profile-institute",
    name: "Institute Profile",
    icon: "ni ni-single-02 text-yellow",
    component: <InstituteProfile />,
    layout: "/admin",
  },
   {
    path: "/college-print/:id",
    name: "Institute Profile Print",
    icon: "ni ni-single-02 text-yellow",
    component: <PrintCollegeProfile />,
    layout: "/admin",
  },
];
export default OtherRoutes;
