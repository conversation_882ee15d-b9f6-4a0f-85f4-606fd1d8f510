import { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormControl,
  Row,
  Col,
} from "react-bootstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
import NavImage from "../../assets/img/theme/user-icon.png";
import formatDate from "../../utils/formateDate";
const TrashGuestFaculty = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [guestFaculty, setGuestFaculty] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [deletedFaculties, setDeletedFaculties] = useState([]);
  const [loading, setLoading] = useState(true);
  const [colleges, setColleges] = useState([]);


const [subjectData, setSubjectData] = useState([]);
      useEffect(() => {
        const fetchSubjectList = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/get-subjects`, {
                    headers: { 
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}` },
                });
                if (response.status === 200) {
                    setSubjectData(response.data);
                } else {
                    SwalMessageAlert("No Subject Data Found", "error");
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };
        fetchSubjectList();
    }, [endPoint, token]);

  useEffect(() => {
    const fetchTrashfacultys = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/trash-guest-faculty/trash-details?collegeId=${collegeId}`,
          {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const data = response.data;
          
          setDeletedFaculties(data);
        
            
        } else {
          console.warn("No deleted facultys found.");
        }
      } catch (error) {
        console.error("Error fetching deleted facultys:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrashfacultys();
  }, []);
  
  const handleRestore = async (faculty) => {
    
    Swal.fire({
      title: "Are you sure?",
      html: `<strong>OTR Code: ${faculty.OTRCode}</strong><br>You want to restore <strong>${faculty.name}</strong>?`,
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, Restore!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axios.put(
            `${endPoint}/api/restore-faculties/${faculty._id}`,
            {},
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          
          if (response.status === 200) {
            Swal.fire({
              title: "Restored!",
              text: "Guest Faculty restored successfully.",
              icon: "success",
            });
            
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } catch (error) {
          console.error("Restore error:", error);
          
         
          let errorMessage = "Failed to restore faculty.";
          
          if (error.response) {
           
            errorMessage = error.response.data.message || errorMessage;
          } else if (error.request) {
           
            errorMessage = "No response received from server.";
          } else {
            
            errorMessage = error.message || errorMessage;
          }
          
          Swal.fire({
            title: "Error!",
            text: errorMessage,
            icon: "error",
          });
        }
      }
    });
  };
  
  const columns = [
    {
      name: "Action",
      cell: (faculty) => (
        <>
        <div style={{ display: "flex", flexWrap: "wrap", gap: "6px" }}>
          <button
            title="Delete faculty"
            className="btn btn-light btn-sm text-danger "
            disabled
          >
            <span className="fa fa-trash"></span> Deleted
          </button>
          <button
            title="revert faculty"
            className="btn btn-success btn-sm"
            onClick={() => handleRestore(faculty)}
          >
            <span className="fa fa-refresh"></span> Restore 
          </button>
          </div>
        </>
      ),
    },
  
           {
  name: "Basic Details",
  wrap: true,
  style: { marginRight: "35px", minWidth: "90px", maxWidth: "170px" },
  cell: (row) => (
    <div style={{ lineHeight: "1.5" }}>
      <div><strong>Name:</strong> {row.name}</div>
      <div style={{ whiteSpace: "nowrap", overflow: "hidden", textOverflow: "ellipsis" }}>
        <strong>Email:</strong> {row.email}
      </div>
      <div><strong>Contact:</strong> {row.mobile}</div>
      <div><strong>DOB:</strong> {formatDate(row.dob)}</div>
    </div>
  ),
},

           {
               name: "Joining Date",
                 style: { marginLeft: "40px", minWidth: "130px", maxWidth: "10px" },
               cell: (row) => <div>{formatDate(row.joiningDate)}</div>,
           },
           {
               name: "Subject",
               
               selector: (row) =>
                   subjectData.find((a) => String(a._id) === String(row.subject))?.subjectName || "",
           },
           {
               name: "PHD/NET/SET/Mphil",
               selector: (row) => (
                   <div>
                       <div>{row.phd === "yes" ? `PHD Date: ${formatDate(row.phdNotificationDate)}` : null}</div>
                       <div>{row.net === "yes" ? `NET Date: ${formatDate(row.netNotificationDate)}` : null}</div>
                       <div>{row.set === "yes" ? `SET Date: ${formatDate(row.setNotificationDate)}` : null}</div>
                       <div>{row.mphil === "yes" ? `MPhil Date: ${formatDate(row.mphilNotificationDate)}` : null}</div>
                   </div>
   
               ),
           },
           {
               name: "Experience",
               selector: (row) => (
                   <div>
                       <div><strong>Years:</strong> {row.experienceYears}</div>
                       <div><strong>Months:</strong> {row.experienceMonths}</div>
                       <div><strong>Days:</strong> {row.experienceDays}</div>
                   </div>
               ),
           },
           {
               name: "Research Papers",
               selector: (row) => <b>{row.researchPapers}</b>,
           },
  ];
  const filteredData = deletedFaculties.filter((item) => {
    

    if (item.activeStatus === false) {
      const filterTextLower = filterText.toLowerCase();

      // Check for "Verified" and "Not Verified"
      if (filterTextLower === "verified") {
        return item.isVerified === true; // Only include isVerified facultys
      } else if (filterTextLower === "not Verified") {
        return item.isVerified === false; // Only include not isVerified facultys
      } else if (filterTextLower === "not") {
        return item.isVerified === false; // Only include not verified facultys
      }

      // Default filtering for name, empCode, and contact
      return (
        (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
        (item.OTRCode &&
          item.OTRCode.toLowerCase().includes(filterTextLower)) ||
        (item.mobile &&
          item.mobile.toString().toLowerCase().includes(filterTextLower))
      );
    }
  });
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <h3 className="mb-0">Deleted faculty List</h3>
                <FormControl
                  type="text"
                  placeholder="Search faculty..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                />
              </CardHeader>
              <CardBody>
                <div style={{ width: "100%", overflowX: "auto"  }}>
             <DataTable
                  columns={columns}
                  data={filteredData}
                  // filter={filteredData}
                  progressPending={loading}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                   customStyles={{
    headRow: {
      style: {
       
        // marginRight: "47px",
        marginLeft:"43px"
      },
    },
    }}
                />

                </div>
             
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default TrashGuestFaculty;
