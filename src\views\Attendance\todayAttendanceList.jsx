import React, { useEffect, useState } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Modal,
  ModalHeader,
  ModalBody,
  Button,
} from "reactstrap";
import { FormControl } from "react-bootstrap";
import loadable from "@loadable/component";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
const Header = loadable(() => import("../../components/Headers/Header.jsx"));
const AttendanceTodayList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const userType = sessionStorage.getItem("userType");
  const id = sessionStorage.getItem("id");
  const [empId, setEmpId] = useState(null);
  const [filter, setFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [attendanceData, setAttendanceData] = useState([]);
  const [attendanceEmployeeData, setAttendanceEmployeeData] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [filterTextModal, setFilterTextModal] = useState("");
  useEffect(() => {
    if (userType === "Institute") {
      fetchSingleCollege();
    }
  }, []);

  const fetchSingleCollege = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/attendance/today-institute-wise/${id}`,
        {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setAttendanceData(data);
      } else {
        SwalMessageAlert("No Institute Found", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      if (filter === "") {
        SwalMessageAlert("Please Select Filter", "error");
      }
      const queryParams = {
        filter,
        startDate: filter === "dateRange" ? startDate : undefined,
        endDate: filter === "dateRange" ? endDate : undefined,
      };

      const response = await axios.get(
        `${endPoint}/api/attendance/employee-wise-filter/${empId}`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setAttendanceEmployeeData(data);
      } else {
        SwalMessageAlert("Attendance Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };

  const calculateStayDuration = (loginTime, logoutTime) => {
    if (!loginTime || !logoutTime) {
      return "N/A";
    }
    const loginDate = new Date(loginTime);
    const logoutDate = new Date(logoutTime);
    const durationMs = logoutDate - loginDate;
    if (durationMs <= 0) {
      return "Invalid times";
    }
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    return `${hours}h ${minutes}m ${seconds}s`;
  };
  const [attendanceModal, setAttendanceModal] = useState(false);
  const toggleAttendanceModal = () => setAttendanceModal(!attendanceModal);
  const [employeeProfile, setEmployeeProfile] = useState([]);
  const [base64Image, setBase64Image] = useState("");
  function handleConvertAndDisplay(base64) {
    if (base64 === undefined) {
      setBase64Image(NavImage);
    } else {
      const sampleBase64Image = `data:image/png;base64,${base64}`;
      setBase64Image(sampleBase64Image);
    }
  }

  const [imageData, setImageData] = useState([]);
  const viewEmployeeWise = async (empId, empCode) => {
    try {
      const image = await FetchImageRowData(empCode);
      setImageData(image);
      const response = await axios.get(
        `${endPoint}/api/employee/get-single-employee?empCode=${empCode}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        handleConvertAndDisplay(data[0].encodedImage);
        setEmployeeProfile(data[0]);
        setEmpId(empId);
        setAttendanceModal(true);
        setAttendanceEmployeeData(null);
      } else {
        SwalMessageAlert("Employee Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };
  const columns = [
    {
      name: "Action",
      cell: (attendanceData) => (
        <>
          <button
            className="btn btn-primary btn-sm"
            title="Change Password"
            onClick={() =>
              viewEmployeeWise(attendanceData.empId, attendanceData.empCode)
            }
          >
            <span className="fa fa-eye"></span>
          </button>
        </>
      ),
    },
    {
      name: "Name",
      cell: (attendanceData) => (
        <>
          Name : {attendanceData.name} <br />
          Emm Code : {attendanceData.empCode}{" "}
        </>
      ),
    },
    {
      name: "Designation",
      cell: (attendanceData) => (
        <>{attendanceData.designationDetails.designation}</>
      ),
      wrap: true,
    },
    {
      name: "College Name",
      cell: (attendanceData) => <>{attendanceData.collegeDetails[0].name}</>,
      wrap: true,
    },
    {
      name: "LogIN Time",
      selector: (attendanceData) => (
        <>
          {attendanceData.loginTime
            ? new Date(attendanceData.loginTime).toLocaleString()
            : "No LogIn Time"}
        </>
      ),
    },
    {
      name: "LogOUT Time",
      selector: (attendanceData) => (
        <>
          {attendanceData.logoutTime ? (
            new Date(attendanceData.logoutTime).toLocaleString()
          ) : (
            <span style={{ color: "red" }}>No Logout Time</span>
          )}
        </>
      ),
    },
    {
      name: "Stay Duration",
      selector: (attendanceData) => (
        <>
          {calculateStayDuration(
            attendanceData.loginTime,
            attendanceData.logoutTime
          )}
        </>
      ),
    },
  ];
  const filteredData = Array.isArray(attendanceData)
    ? attendanceData.filter((item) => {
        const filterTextLower = filterText ? filterText.toLowerCase() : "";
        return (
          (item.name &&
            typeof item.name === "string" &&
            item.name.toLowerCase().includes(filterTextLower)) ||
          (item.empCode &&
            typeof item.empCode === "string" &&
            item.empCode.toLowerCase().includes(filterTextLower))
        );
      })
    : [];
  const columnsModal = [
    // {
    //   name: "Name",
    //   cell: (attendanceEmployeeData) => (
    //     <>
    //       Name : {attendanceEmployeeData.employeeDetails.name} <br />
    //       Emm Code : {attendanceEmployeeData.empCode}{" "}
    //     </>
    //   ),
    // },
    // {
    //   name: "Designation",
    //   cell: (attendanceEmployeeData) => (
    //     <>{attendanceEmployeeData.designationDetails.designation}</>
    //   ),
    //   wrap: true,
    // },
    // {
    //   name: "College Name",
    //   cell: (attendanceEmployeeData) => (
    //     <>{attendanceEmployeeData.collegeDetails.name}</>
    //   ),
    //   wrap: true,
    // },
    {
      name: "LogIN Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.loginTime
            ? new Date(attendanceEmployeeData.loginTime).toLocaleString()
            : "No LogIn Time"}
        </>
      ),
    },
    {
      name: "LogOUT Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.logoutTime ? (
            new Date(attendanceEmployeeData.logoutTime).toLocaleString()
          ) : (
            <span style={{ color: "red" }}>No Logout Time</span>
          )}
        </>
      ),
    },
    {
      name: "Stay Duration",
      selector: (attendanceEmployeeData) => (
        <>
          {calculateStayDuration(
            attendanceEmployeeData.loginTime,
            attendanceEmployeeData.logoutTime
          )}
        </>
      ),
    },
  ];
  const filteredDataModal = Array.isArray(attendanceEmployeeData)
    ? attendanceEmployeeData.filter((item) => {
        const filterTextLowerModal = filterTextModal
          ? filterTextModal.toLowerCase()
          : "";
        return (
          (item.name &&
            typeof item.name === "string" &&
            item.name.toLowerCase().includes(filterTextLowerModal)) ||
          (item.empCode &&
            typeof item.empCode === "string" &&
            item.empCode.toLowerCase().includes(filterTextLowerModal))
        );
      })
    : [];

  const FetchImageRowData = async (empCode) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/employee/fetch-image?empCode=${empCode}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      const data = response.data;
      return data[0]; // Return the first data object if multiple exist
    } catch (error) {
      console.error("Error fetching image data:", error);
      return null;
    }
  };
  // const FetchImageRowData = (empCode) => {
  //   const [rowData, setRowData] = useState(null);
  //   const [loading, setLoading] = useState(true);

  //   useEffect(() => {
  //     const fetchData = async () => {
  //       try {
  //         const response = await axios.get(
  //           `${endPoint}/api/employee/fetch-image?empCode=${empCode}`,
  //           {
  //             headers: {
  //               "Content-Type": "application/json",
  //               Authorization: `Bearer ${token}`,
  //             },
  //           }
  //         );
  //         const data = response.data;
  //         console.log(data);
  //         setRowData(data[0]);
  //       } catch (error) {
  //         console.error("Error fetching row data:", error);
  //       } finally {
  //         setLoading(false);
  //       }
  //     };

  //     fetchData();
  //   }, [empCode]);

  //   if (loading) {
  //     return <span>Loading...</span>;
  //   }

  //   return rowData ? (
  //     <img
  //       src={`${endPoint}/${rowData.uploadPath}`}
  //       style={{ width: "50px", cursor: "pointer" }}
  //       alt="Employee Photo"
  //     />
  //   ) : (
  //     <img src={NavImage} style={{ width: "50px" }} alt="Default Placeholder" />
  //   );
  // };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        {/* Form Section */}
        {/* <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Attendance Filter</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form onSubmit={handleSubmit}>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-className">
                            Employee Code / Name
                          </Label>
                          <Input
                            type="text"
                            placeholder="e.g., John Doe@EMP001"
                            value={empInput}
                            onChange={(e) => setEmpInput(e.target.value)}
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-className">
                            College AISHE / Name
                          </Label>
                          <Input
                            type="text"
                            placeholder="e.g., ABC College@AISHE123"
                            value={collegeInput}
                            onChange={(e) => setCollegeInput(e.target.value)}
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-className">Filter</Label>
                          <Input
                            type="select"
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                          >
                            <option value="">Select Filter</option>
                            <option value="today">Today</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                            <option value="dateRange">Date Range</option>
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          {filter === "dateRange" && (
                            <div>
                              <label>Start Date:</label>
                              <Input
                                type="date"
                                value={startDate}
                                onChange={(e) => setStartDate(e.target.value)}
                              />
                              <label>End Date:</label>
                              <Input
                                type="date"
                                value={endDate}
                                onChange={(e) => setEndDate(e.target.value)}
                              />
                            </div>
                          )}
                        </FormGroup>
                      </Col>
                      <button className="btn btn-sm btn-primary" type="submit">
                        Get Attendance
                      </button>
                    </Row>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row> */}
        <Row className="mt-4">
          <Col lg="12">
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <h3 className="mb-0">Today`s Attendance</h3>
                <FormControl
                  type="text"
                  placeholder="Search..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                />
              </CardHeader>
              <CardBody>
                <DataTable
                  columns={columns}
                  data={filteredData}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa",
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff",
                        borderBottom: "1px solid #ddd",
                      },
                      onHoverStyle: {
                        backgroundColor: "#ffff99",
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>
          <Modal
            isOpen={attendanceModal}
            toggle={toggleAttendanceModal}
            style={{ maxWidth: "90%" }}
          >
            <ModalHeader toggle={toggleAttendanceModal}>
              {/* <h2>Attendance Filter For : {employeeProfile.name}</h2> */}
            </ModalHeader>
            <ModalBody>
              <section style={{ backgroundColor: "#eee" }}>
                <div className="container py-5">
                  <div className="row">
                    <div className="col-lg-4">
                      <div className="card mb-4">
                        <div className="card-body text-center">
                          <img
                            src={
                              imageData && imageData.uploadPath
                                ? `${endPoint}/${imageData.uploadPath}`
                                : base64Image
                            }
                            alt="avatar"
                            className="rounded-circle img-fluid"
                            style={{ width: "150px", height: "150px" }}
                          />

                          <h5 className="my-3">
                            <b>{employeeProfile.name}</b>({" "}
                            {employeeProfile ? employeeProfile.empCode : ""} )
                          </h5>
                          <p className="text-muted mb-1">
                            {employeeProfile && employeeProfile.length > 0
                              ? employeeProfile.designationDetails.designation
                              : ""}
                          </p>
                          <p className="text-muted mb-4">
                            {employeeProfile ? employeeProfile.address : ""}
                          </p>
                          {/* <div className="d-flex justify-content-center mb-2">
                  <button type="button" className="btn btn-primary">
                    Follow
                  </button>
                  <button type="button" className="btn btn-outline-primary ms-1">
                    Message
                  </button>
                </div> */}
                        </div>
                      </div>
                      {/* <div className="card mb-4 mb-lg-0">
              <div className="card-body p-0">
                <ul className="list-group list-group-flush rounded-3">
                  <li className="list-group-item d-flex justify-content-between align-items-center p-3">
                    <i className="fas fa-globe fa-lg text-warning"></i>
                    <p className="mb-0">https://mdbootstrap.com</p>
                  </li>
                  <li className="list-group-item d-flex justify-content-between align-items-center p-3">
                    <i className="fab fa-github fa-lg text-body"></i>
                    <p className="mb-0">mdbootstrap</p>
                  </li>
                  <li className="list-group-item d-flex justify-content-between align-items-center p-3">
                    <i
                      className="fab fa-twitter fa-lg"
                      style={{ color: "#55acee" }}
                    ></i>
                    <p className="mb-0">@mdbootstrap</p>
                  </li>
                  <li className="list-group-item d-flex justify-content-between align-items-center p-3">
                    <i
                      className="fab fa-instagram fa-lg"
                      style={{ color: "#ac2bac" }}
                    ></i>
                    <p className="mb-0">mdbootstrap</p>
                  </li>
                  <li className="list-group-item d-flex justify-content-between align-items-center p-3">
                    <i
                      className="fab fa-facebook-f fa-lg"
                      style={{ color: "#3b5998" }}
                    ></i>
                    <p className="mb-0">mdbootstrap</p>
                  </li>
                </ul>
              </div>
            </div> */}
                    </div>
                    <div className="col-lg-8">
                      <div className="card mb-4">
                        <div className="card-body">
                          <div className="row">
                            <div className="col-sm-3">
                              <p className="mb-0">Full Name</p>
                            </div>
                            <div className="col-sm-9">
                              <p className="text-muted mb-0">
                                <b>
                                  {employeeProfile ? employeeProfile.name : ""}
                                </b>
                              </p>
                            </div>
                          </div>
                          <div className="row">
                            <div className="col-sm-3">
                              <p className="mb-0">Email</p>
                            </div>
                            <div className="col-sm-9">
                              <p className="text-muted mb-0">
                                <b>
                                  {employeeProfile ? employeeProfile.email : ""}
                                </b>
                              </p>
                            </div>
                          </div>
                          <div className="row">
                            <div className="col-sm-3">
                              <p className="mb-0">Mobile</p>
                            </div>
                            <div className="col-sm-9">
                              <p className="text-muted mb-0">
                                <b>
                                  {employeeProfile
                                    ? employeeProfile.contact
                                    : ""}
                                </b>
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="row">
                        <div className="col-md-12">
                          <div className="card mb-4 mb-md-0">
                            <div className="card-body">
                              <Form>
                                <div className="pl-lg-4">
                                  <Row>
                                    <Col lg="3">
                                      <FormGroup>
                                        <Label htmlFor="input-className">
                                          Filter
                                        </Label>
                                        <Input
                                          type="select"
                                          value={filter}
                                          onChange={(e) =>
                                            setFilter(e.target.value)
                                          }
                                        >
                                          <option value="">
                                            Select Filter
                                          </option>
                                          <option value="today">Today</option>
                                          <option value="weekly">
                                            Current Week
                                          </option>
                                          <option value="monthly">
                                            Current Month
                                          </option>
                                          <option value="yearly">
                                            Current Year
                                          </option>
                                          <option value="dateRange">
                                            Date Range
                                          </option>
                                        </Input>
                                      </FormGroup>
                                    </Col>
                                    <Col lg="3">
                                      <FormGroup>
                                        {filter === "dateRange" && (
                                          <div>
                                            <label>Start Date:</label>
                                            <Input
                                              type="date"
                                              value={startDate}
                                              onChange={(e) =>
                                                setStartDate(e.target.value)
                                              }
                                            />
                                          </div>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    <Col lg="3">
                                      <FormGroup>
                                        {filter === "dateRange" && (
                                          <div>
                                            <label>End Date:</label>
                                            <Input
                                              type="date"
                                              value={endDate}
                                              onChange={(e) =>
                                                setEndDate(e.target.value)
                                              }
                                            />
                                          </div>
                                        )}
                                      </FormGroup>
                                    </Col>
                                  </Row>
                                  <Button
                                    className="btn btn-sm btn-primary"
                                    onClick={handleSubmit}
                                  >
                                    Get Attendance
                                  </Button>
                                </div>
                              </Form>
                            </div>
                          </div>
                        </div>
                      </div>
                      <br />
                    </div>
                    <Col lg="12">
                      <Card className="shadow">
                        <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                          <h3 className="mb-0">Attendance List</h3>
                          <FormControl
                            type="text"
                            placeholder="Search..."
                            className="ml-auto"
                            value={filterTextModal}
                            onChange={(e) => setFilterTextModal(e.target.value)}
                            style={{ width: "250px", borderRadius: "30px" }}
                          />
                        </CardHeader>
                        <CardBody>
                          <DataTable
                            columns={columnsModal}
                            data={filteredDataModal}
                            pagination
                            paginationPerPage={10}
                            highlightOnHover
                            striped
                            customStyles={{
                              header: {
                                style: {
                                  backgroundColor: "#f8f9fa",
                                  fontWeight: "bold",
                                },
                              },
                              rows: {
                                style: {
                                  backgroundColor: "#fff",
                                  borderBottom: "1px solid #ddd",
                                },
                                onHoverStyle: {
                                  backgroundColor: "#ffff99",
                                },
                              },
                            }}
                          />
                        </CardBody>
                      </Card>
                    </Col>
                  </div>
                </div>
              </section>
              <br />
            </ModalBody>
          </Modal>
        </Row>
      </Container>
    </>
  );
};

export default AttendanceTodayList;
