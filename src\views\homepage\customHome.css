/* PageLayout.css */
.page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Navbar Styles */
.custom-navbar {
    background-color: #0056b3;
    height: 80px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-logo {
    height: 60px;
    margin-right: 15px;
}

.brand-container {
    display: flex;
    align-items: center;
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-text-primary {
    font-size: 22px;
    font-weight: bold;
    color: white;
}

.brand-text-secondary {
    font-size: 17px;
    font-weight: bold;
    color: white;
}

.navbar-links .nav-link {
    color: white !important;
    padding: 0.5rem 1rem;
    transition: color 0.3s ease;
}

.navbar-links .nav-link:hover {
    color: #ffd700 !important;
}

/* Hero Section */
.hero-section {
    margin-top: 80px;
    position: relative;
}

.hero-image {
    width: 100%;
    height: 60vh;
    object-fit: cover;
}

.hero-caption {
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem;
    border-radius: 10px;
}

/* Main Content */
.main-content {
    padding: 3rem 0;
    flex-grow: 1;
}

.section-title {
    color: #ffc107;
    text-align: center;
    margin-bottom: 3rem;
    font-weight: bold;
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.content-image img {
    width: 100%;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.content-text {
    padding: 1rem;
}

.content-text .lead {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

/* Notices Section */
.notices-section {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.notice-title {
    color: #0056b3;
    text-align: center;
    margin-bottom: 1.5rem;
}

.notice-item {
    margin-bottom: 2rem;
}

.notice-list {
    list-style: none;
    padding-left: 0;
}

.notice-list li {
    margin-bottom: 0.5rem;
}

.notice-list a {
    color: #0056b3;
    text-decoration: none;
    transition: color 0.3s ease;
}

.notice-list a:hover {
    color: #003d82;
    text-decoration: underline;
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #0056b3;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s ease;
    z-index: 1000;
}

.scroll-to-top:hover {
    background: #003d82;
}

/* Footer */
.main-footer {
    background: #0056b3;
    color: white;
    padding: 1rem 0;
    text-align: center;
    margin-top: auto;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

/* Responsive Design */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .hero-image {
        height: 40vh;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .brand-text-primary {
        font-size: 18px;
    }

    .brand-text-secondary {
        font-size: 14px;
    }
}