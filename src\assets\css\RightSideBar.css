.right-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 250px; /* Adjust as per your design */
    background-color: #f4f4f4;
    box-shadow: -2px 0px 5px rgba(0, 0, 0, 0.1);
    padding: 20px;
    z-index: 1000; /* To ensure it's above the content */
  }
  
  .sidebar-content {
    display: flex;
    flex-direction: column;
  }
  
  .sidebar-content h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
  }
  
  .sidebar-content ul {
    list-style-type: none;
    padding: 0;
  }
  
  .sidebar-content ul li {
    margin-bottom: 10px;
  }
  
  .sidebar-content ul li a {
    text-decoration: none;
    color: #333;
    font-size: 1rem;
  }
  
  .sidebar-content ul li a:hover {
    color: #007bff;
  }
  /* Sidebar Content */
.right-sidebar {
  font-family: 'Arial', sans-serif;
  color: #333;
}

/* Add hover effect on links */
.right-sidebar a:hover {
  color: #0056b3;
  text-decoration: underline;
}

.nav-link-icon {
  cursor: pointer; /* Make the cursor a hand (pointer) when hovered */
  display: flex;
  align-items: center;
}

.nav-link-icon:hover {
  color: #007bff; /* Optionally change the color on hover */
}

.nav-link-inner--text {
  margin-left: 10px; /* Add space between icon and text */
}

details {
  background: rebeccapurple;
  color: #ddd;
  width: 300px;
  border-radius: 4px;
  margin-bottom: 5px;
}
summary {
  padding: 10px;
}
summary:focus {
  outline: none;
}
details ul {
  background: #ddd;
  margin: 0;
  color: black;
  padding: 10px;
  border-radius: 0 0 4px 4px;
}

details li {
  margin-left: 30px;
  margin-top: 10px;
}