import { createContext, useContext, useState, useEffect } from "react";
import PropTypes from "prop-types";

const SpeechContext = createContext();
export const useSpeech = () => useContext(SpeechContext);

export const SpeechProvider = ({ children }) => {

  const [isSpeechOn, setIsSpeechOn] = useState(() => {
    return localStorage.getItem("isSpeechOn") === "true";
  });

  const [language, setLanguage] = useState(() => {
    return localStorage.getItem("ttsLang") || "en-IN";
  });

  const speak = (text) => {
    if (!isSpeechOn || !text) return;

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = language;
    window.speechSynthesis.cancel(); // stop old speech
    window.speechSynthesis.speak(utterance);
  };

  const toggleSpeech = () => {
    setIsSpeechOn((prev) => !prev);
    window.speechSynthesis.cancel();
  };

  useEffect(() => {
    localStorage.setItem("isSpeechOn", isSpeechOn);
  }, [isSpeechOn]);

  useEffect(() => {
    localStorage.setItem("ttsLang", language);
  }, [language]);

  return (
    <SpeechContext.Provider value={{ isSpeechOn, toggleSpeech, speak, language, setLanguage }}>
      {children}
    </SpeechContext.Provider>
  );
};

SpeechProvider.propTypes = {
  children: PropTypes.node.isRequired,
};
