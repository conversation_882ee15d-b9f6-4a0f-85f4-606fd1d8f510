import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Spinner,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from "reactstrap";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
const ProfileUpdateInstitute = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const [formData, setFormData] = useState({
    university: "",
    divison: "",
    establishYear: "",
    district: "",
    vidhansabha: "",
    regDate: "",
    collegeType: "",
    isLead: "",
    collegeUrl: "",
    address: "",
    lat: "",
    long: "",
    area: "",
    isTribal: "false",
    degreeTypes: "",
    principalType: "",
    isPrincipalPosted: false,
  });

  // console.log(formData.principalType, "formData.isPrincipalPosted");

  const [errors, setErrors] = useState({});
  const [previewModal, setPreviewModal] = useState(false);
  const formatFieldValue = (key, value) => {
    if (key === "collegeType") {
      return value === "1" ? "Government" : "Private";
    }
    if (key === "isLead") {
      return value === "1" ? "Yes" : "No"; // Convert isLead value to Yes/No
    }
    if (key === "regDate") {
      return new Date(value).toLocaleDateString();
    }
    if (key === "isTribal") {
      return value ? "Yes" : "No";
    }
    return value || "Not provided";
  };
  const validateFields = () => {
    const newErrors = {};

    Object.keys(formData).forEach((key) => {
      if (!formData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };
  const togglePreviewModal = () => setPreviewModal(!previewModal);

  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };
  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [id, endPoint, token]); // Include value and token as dependencies if they can change

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    setFormData({ ...formData, [name]: value });

    setErrors({ ...errors, [name]: "" });
    if (name === "collegeEmail") {
      if (!validateEmail(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          collegeEmail: "Please enter a valid email address.",
        }));
      }
    }
    // setFormData((prevData) => ({
    //   ...prevData,
    //   [name]: parseInt(value, 10), // Ensure value is stored as a number
    // }));
    if (name === "isTribal") {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value === "true", // Convert "true"/"false" strings to boolean
      }));
    }

    if (name === "isPrincipalPosted") {
      setFormData((prevData) => ({
        ...prevData,
        [name]: value === "true",
      }));
    }
  };

  useEffect(() => {
    const setPrincipalType = () => {
      setFormData((prevData) => ({
        ...prevData,
        principalType: "",
      }));
    };
    if (formData.isPrincipalPosted === false) {
      setPrincipalType();
    }
  }, [formData.isPrincipalPosted]);

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching division data:", error);
        alert("Failed to load division data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const handledivisonChange = async (e) => {
    const { value } = e.target;

    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setDistrict(response.data);
      } else {
        alert("Failed to fetch districts.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert("An error occurred. Please try again later.");
    }

    // Correct state update
    setFormData((prevData) => ({
      ...prevData,
      divison: value,
    }));
  };

  // useEffect(() => {
  //   const handleDistrictChange = async (e) => {
  //     try {
  //       const response = await axios.get(
  //         `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
  //         {
  //           headers: {
  //             "Content-Type": "application/json",
  //             Authorization: `Bearer ${token}`,
  //           },
  //         }
  //       );
  //       if (response.status === 200) {
  //         setVidhansabha(response.data);
  //       } else {
  //         alert("Failed to fetch vidhansabha data.");
  //       }
  //     } catch (error) {
  //       console.error("Error fetching vidhansabha:", error);
  //       alert("An error occurred. Please try again later.");
  //     }
  //   };
  //   handleDistrictChange();
  // }, [endPoint, token]);

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    validateFields();
    // Create an array of the field values to validate
    const valuesToValidate = [
      formData.name,
      formData.university,
      formData.collegeEmail,
      formData.aisheCode,
      formData.collegeType,
      formData.isLead,
      formData.isPrincipalPosted,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      SwalMessageAlert(
        "Please fill out all fields before submitting.",
        "warning"
      );
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      SwalMessageAlert(
        "All fields are filled. You can proceed with submission.",
        "success"
      );
    }

    try {
      const body = {
        name: formData.name,
        university: formData.university,
        collegeEmail: formData.collegeEmail,
        contactPerson: formData.contactPerson,
        contactNumber: Number(formData.contactNumber),
        divison: String(formData.divison),
        establishYear:
          formData.establishYear === undefined
            ? ""
            : String(formData.establishYear),
        district: String(formData.district),
        vidhansabha: String(formData.vidhansabha),
        aisheCode: formData.aisheCode,
        collegeType: String(formData.collegeType),
        isLead: String(formData.isLead),
        regDate: formData.regDate,
        address: formData.address,
        collegeUrl: formData.collegeUrl,
        lat: formData.lat === undefined ? "" : String(formData.lat),
        long: formData.long === undefined ? "" : String(formData.long),
        area: formData.area === undefined ? "" : String(formData.area),
        isTribal: String(formData.isTribal),
        degreeTypes: String(formData.degreeTypes),
        principalType: String(formData.principalType),
        isPrincipalPosted: String(formData.isPrincipalPosted),
      };
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/college-profile/update/${id}`,
          { ...body },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          //  SwalMessageAlert(response.data.msg, "success");
          SwalMessageAlert("Institute Updated Successfully.", "success");
          setTimeout(() => {
            window.location.replace("admin/dashboard");
          }, 2000);
          setFormData({
            // ...formData,
            name: "",
            university: "",
            collegeEmail: "",
            contactPerson: "",
            contactNumber: "",
            divison: "",
            establishYear: "",
            district: "",
            vidhansabha: "",
            aisheCode: "",
            regDate: "",
            collegeType: "",
            isLead: "",
            collegeUrl: "",
            address: "",
            lat: "",
            long: "",
            area: "",
            isTribal: "",
            degreeTypes: "",
            principalType: "",
            isPrincipalPosted: false,
          });
          // navigate("admin/university");

          
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      } else {
        SwalMessageAlert("Adding for College failed.", "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert("An error occurred. Please try again later.", "error");

      // navigate("admin/university");
    }
  };
  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.divison}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.divison) fetchDistrict();
  }, [formData.divison, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.vidhansabha) fetchVidhansabha();
  }, [formData.vidhansabha, endPoint, token]);

  const updateCollegeStatus = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/college/update-status/${id}`,
        {}, // Pass empty object if no data needs to be sent in the body
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        // Check the status code to ensure success
        window.location.replace("admin/institute");
      } else {
        alert("Failed to fetch College data. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  useEffect(() => {
    const getCollege = async () => {
      try {
        const response = await axios
          .get(`${endPoint}/api/college/get-college/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          })
          .then(async (response) => {
            if (response.status === 200) {
              const data = response.data;
              let formattedDate;
              if (data.regDate) {
                formattedDate = new Date(data.regDate)
                  .toISOString()
                  .split("T")[0];
              } else {
                formattedDate = "";
              }
              setFormData({
                ...data,
                // ...formData,
                regDate: formattedDate,
                divison: data.divison || "",
                vidhansabha: data.vidhansabha || "",
                district: data.district || "",
                isLead: data.isLead,
                isPrincipalPosted:
                  data.isPrincipalPosted !== true
                    ? false
                    : data.isPrincipalPosted,
                principalType:
                  data.principalType === undefined
                    ? formData.principalType
                    : data.principalType,
              });

              // console.log(data.principalType,"Value of Charge Type 3");

              try {
                const response = await axios.get(
                  `${endPoint}/api/district/getVidhansabha-district-wise/${data.district}`,
                  {
                    headers: {
                      "Content-Type": "application/json",
                      'web-url': window.location.href,
                      Authorization: `Bearer ${token}`,
                    },
                  }
                );
                if (response.status === 200) {
                  setVidhansabha(response.data);
                } else {
                  alert("Failed to fetch vidhansabha data.");
                }
              } catch (error) {
                console.error("Error fetching vidhansabha:", error);
                alert("An error occurred. Please try again later.");
              }
            }
          })
          .catch((err) => {
            handleApiError(err);
          });
      } catch (error) {
        handleApiError(err);
      }
    };
    getCollege(); // Call the function inside useEffect
  }, [id, endPoint, token]); // Dependencies

  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Update Institute Profile</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <b>
                      Last Update Date : {formatDate(formData.updatedAt)} &nbsp;
                    </b>
                    {formData.status === false ? (
                      <button
                        className="btn btn-warning btn-sm"
                        onClick={() => updateCollegeStatus(id)}
                      >
                        <Spinner
                          size="sm"
                          color="white"
                          style={{ marginRight: "8px" }}
                        />
                        Verify
                      </button>
                    ) : (
                      <span className="btn btn-success btn-sm">Verified</span>
                    )}
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form>
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-aisheCode"
                          >
                            AISHE Code
                          </label>
                          <Input
                            name="aisheCode"
                            id="input-aisheCode"
                            autoComplete="pope"
                            placeholder="AISHE CODE"
                            type="text"
                            value={formData.aisheCode}
                            onChange={handleInputChange}
                            readOnly
                          />
                          {errors.aisheCode && (
                            <small style={{ color: "red" }}>
                              {errors.aisheCode}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            Institute Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="Institute Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            readOnly
                          />
                          {errors.name && (
                            <p style={{ color: "red" }}>{errors.name}</p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-collegeEmail"
                          >
                            Institute Email
                          </label>
                          <Input
                            name="collegeEmail"
                            id="input-collegeEmail"
                            placeholder="College Email"
                            type="email"
                            autoComplete="pope"
                            value={formData.collegeEmail}
                            onChange={handleInputChange}
                            readOnly
                          />
                          {errors.collegeEmail && (
                            <p style={{ color: "red" }}>
                              {errors.collegeEmail}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityType"
                          >
                            Is Lead College
                          </label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead"
                                value={1}
                                checked={formData.isLead === 1}
                                onChange={handleInputChange}
                                readOnly
                              />
                              <Label check>Yes</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isLead"
                                id="input-lead"
                                value={0}
                                checked={formData.isLead === 0}
                                onChange={handleInputChange}
                                readOnly
                              />

                              <Label check>No</Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactPerson"
                          >
                            Contact Person
                          </label>
                          <Input
                            name="contactPerson"
                            id="input-contactPerson"
                            placeholder="Contact Person"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactPerson}
                            onChange={handleInputChange}
                            readOnly
                          />
                          {errors.contactPerson && (
                            <p style={{ color: "red" }}>
                              {errors.contactPerson}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactNumber"
                          >
                            Contact Number
                          </label>
                          <Input
                            name="contactNumber"
                            id="input-contactNumber"
                            placeholder="Contact Number"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactNumber}
                            onChange={handleInputChange}
                            readOnly
                          />
                          {errors.contactNumber && (
                            <p style={{ color: "red" }}>
                              {errors.contactNumber}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-division"
                          >
                            Division
                          </label>
                          <Input
                            name="divison"
                            id="input-division"
                            type="select"
                            value={formData.divison}
                            onChange={handledivisonChange}
                            readOnly
                          >
                            <option value="" disabled>
                              Select Division
                            </option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.divisionCode}
                                  selected={
                                    Number(type.divisionCode) ===
                                    Number(formData.divison)
                                  }
                                >
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.divison && (
                            <small style={{ color: "red" }}>
                              {errors.divison}
                            </small>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            District
                          </label>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            readOnly
                          >
                            <option value="" disabled>
                              Select Division
                            </option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.LGDCode}
                                  selected={
                                    Number(type.LGDCode) ===
                                    Number(formData.district)
                                  }
                                >
                                  {type.districtNameEng}
                                </option>
                              ))}
                            {/* Add more districts as needed */}
                          </Input>
                          {errors.district && (
                            <small style={{ color: "red" }}>
                              {errors.district}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-vidhansabha"
                          >
                            Vidhan Sabha
                          </label>
                          <Input
                            name="vidhansabha"
                            id="input-vidhansabha"
                            type="select"
                            value={formData.vidhansabha}
                            onChange={handleInputChange}
                          >
                            <option value="" disabled>
                              Select Vidhansabha
                            </option>
                            {vidhansabha &&
                              vidhansabha.length > 0 &&
                              vidhansabha.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.ConstituencyNumber}
                                  selected={
                                    Number(type.ConstituencyNumber) ===
                                    Number(formData.vidhansabha)
                                  }
                                >
                                  {type.ConstituencyName}
                                </option>
                              ))}
                          </Input>
                          {errors.vidhansabha && (
                            <small style={{ color: "red" }}>
                              {errors.vidhansabha}
                            </small>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-university"
                          >
                            Choose University
                          </label>
                          <Input
                            name="university"
                            id="input-university"
                            placeholder="Choose University"
                            type="select"
                            value={formData.university}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                          >
                            <option value="">Select University</option>
                            {university &&
                              university.length > 0 &&
                              university.map((type, index) => (
                                <option key={index} value={type._id}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                          {errors.university && (
                            <p style={{ color: "red" }}>{errors.university}</p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-establishYear"
                          >
                            Establishment Year
                          </label>
                          <Input
                            name="establishYear"
                            id="input-establishYear"
                            placeholder="Establishment Year"
                            type="number"
                            autoComplete="pope"
                            value={formData.establishYear}
                            onChange={handleInputChange}
                          />
                          {errors.establishYear && (
                            <small style={{ color: "red" }}>
                              {errors.establishYear}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-regDate"
                          >
                            Registration Date
                          </label>
                          <Input
                            name="regDate"
                            id="input-regDate"
                            placeholder="Registration Date"
                            type="date"
                            autoComplete="pope"
                            value={formData.regDate}
                            onChange={handleInputChange}
                          />
                          {errors.regDate && (
                            <small style={{ color: "red" }}>
                              {errors.regDate}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityType"
                          >
                            College Type
                          </label>
                          <Input
                            name="collegeType"
                            id="input-collegeType"
                            type="select"
                            value={formData.collegeType}
                            onChange={handleInputChange}
                          >
                            <option value="">Select College Type</option>
                            <option value="1">Govt.</option>
                            <option value="2">Private</option>
                            {/* Add more university types if needed */}
                          </Input>
                          {errors.collegeType && (
                            <p style={{ color: "red" }}>{errors.collegeType}</p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-isTribal"
                          >
                            Is Tribal Institute
                          </label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isTribal"
                                id="input-tribal-yes"
                                value="true"
                                checked={formData.isTribal === true}
                                onChange={handleInputChange}
                              />
                              <Label check>Yes</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="isTribal"
                                id="input-tribal-no"
                                value="false"
                                checked={formData.isTribal === false}
                                onChange={handleInputChange}
                              />
                              <Label check>No</Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-degreeTypes"
                          >
                            Degree Types
                          </label>
                          <Input
                            type="select"
                            name="degreeTypes"
                            id="input-degreeTypes"
                            value={formData.degreeTypes}
                            onChange={handleInputChange}
                          >
                            <option value="">Select College Type</option>
                            <option value="UG">Under Graduate</option>
                            <option value="PG">Post Graduate</option>
                            {/* Add more options as needed */}
                          </Input>
                          {errors.degreeTypes && (
                            <small style={{ color: "red" }}>
                              {errors.degreeTypes}
                            </small>
                          )}
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-collegeUrl"
                          >
                            College URL
                          </label>
                          <Input
                            name="collegeUrl"
                            id="input-collegeUrl"
                            autoComplete="pope"
                            placeholder="College URL"
                            type="url"
                            value={formData.collegeUrl}
                            onChange={handleInputChange}
                          />
                          {errors.collegeUrl && (
                            <small style={{ color: "red" }}>
                              {errors.collegeUrl}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="4">
                        <Row>
                          <Col lg="4">
                            <FormGroup>
                              <label
                                className="form-control-label"
                                htmlFor="input-lat"
                              >
                                Latitude
                              </label>
                              <Input
                                name="lat"
                                id="input-lat"
                                placeholder="Latitude"
                                type="text"
                                autoComplete="pope"
                                value={formData.lat}
                                onChange={handleInputChange}
                              />
                              {errors.lat && (
                                <small style={{ color: "red" }}>
                                  {errors.lat}
                                </small>
                              )}
                            </FormGroup>
                          </Col>
                          <Col lg="4">
                            <FormGroup>
                              <label
                                className="form-control-label"
                                htmlFor="input-long"
                              >
                                Longitude
                              </label>
                              <Input
                                name="long"
                                id="input-long"
                                placeholder="Longitude"
                                type="text"
                                autoComplete="pope"
                                value={formData.long}
                                onChange={handleInputChange}
                              />
                              {errors.long && (
                                <small style={{ color: "red" }}>
                                  {errors.long}
                                </small>
                              )}
                            </FormGroup>
                          </Col>
                          <Col lg="4">
                            <FormGroup>
                              <label
                                className="form-control-label"
                                htmlFor="input-area"
                              >
                                Area (in meter)
                              </label>
                              <Input
                                name="area"
                                id="input-area"
                                placeholder="Area"
                                type="text"
                                autoComplete="pope"
                                value={formData.area}
                                onChange={handleInputChange}
                              />
                              {errors.area && (
                                <small style={{ color: "red" }}>
                                  {errors.area}
                                </small>
                              )}
                            </FormGroup>
                          </Col>
                        </Row>
                      </Col>

                      <Col lg="8">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-address"
                          >
                            Address
                          </label>
                          <Input
                            name="address"
                            id="input-address"
                            placeholder="Address"
                            // type="text"
                            as="textarea"
                            value={formData.address}
                            onChange={handleInputChange}
                          />
                          {errors.address && (
                            <small style={{ color: "red" }}>
                              {errors.address}
                            </small>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Row>
                          <Col>
                            <h3 className="font-weight-600">
                              Principal Details
                            </h3>
                          </Col>{" "}
                          <br />
                        </Row>
                        <Row>
                          <Col lg="3">
                            <FormGroup>
                              <label
                                className="form-control-label"
                                htmlFor="input-universityType"
                              >
                                Is Principal Posted
                              </label>
                              <div
                                className="row"
                                style={{ justifyContent: "space-evenly" }}
                              >
                                <FormGroup check>
                                  <Input
                                    type="radio"
                                    name="isPrincipalPosted"
                                    id="input-lead"
                                    value="true"
                                    checked={
                                      formData.isPrincipalPosted === true
                                    }
                                    onChange={handleInputChange}
                                    readOnly
                                  />
                                  <Label check>Yes</Label>
                                </FormGroup>
                                <FormGroup check>
                                  <Input
                                    type="radio"
                                    name="isPrincipalPosted"
                                    id="input-lead"
                                    value="false"
                                    checked={
                                      formData.isPrincipalPosted === false
                                    }
                                    onChange={handleInputChange}
                                    readOnly
                                  />

                                  <Label check>No</Label>
                                </FormGroup>
                              </div>
                            </FormGroup>
                          </Col>
                          {formData.isPrincipalPosted === true && (
                            <>
                              <Col lg="3">
                                <FormGroup>
                                  <label
                                    className="form-control-label"
                                    htmlFor="input-principalType"
                                  >
                                    Charge Type
                                  </label>
                                  <Input
                                    type="select"
                                    name="principalType"
                                    id="input-principalType"
                                    value={formData.principalType}
                                    onChange={handleInputChange}
                                  >
                                    <option value="">Select Charge Type</option>
                                    <option value="Regular">Regular</option>
                                    <option value="In-charge">In-charge</option>
                                    <option value="Additional Charge">
                                      Additional Charge
                                    </option>
                                    {/* Add more options as needed */}
                                  </Input>
                                  {errors.principalType && (
                                    <small style={{ color: "red" }}>
                                      {errors.principalType}
                                    </small>
                                  )}
                                </FormGroup>
                              </Col>

                              <Col md="3">
                                <div className="preview-item mb-3">
                                  <label className="form-control-label">
                                    {" "}
                                    Principal Name:
                                  </label>
                                  <input
                                    type="text"
                                    value={formData.prinicipalName}
                                    disabled
                                    className="form-control"
                                  />
                                </div>
                              </Col>
                              <Col md="3">
                                <div className="preview-item mb-3">
                                  <label className="form-control-label">
                                    Principal No:
                                  </label>
                                  <input
                                    type="text"
                                    value={formData.principalNo}
                                    disabled
                                    className="form-control"
                                  />
                                </div>
                              </Col>
                            </>
                          )}
                        </Row>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={togglePreviewModal}>
                      Preview & Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* ------- preview modal---------- */}

        <Modal
          isOpen={previewModal}
          toggle={togglePreviewModal}
          style={{
            maxWidth: "800px",
            width: "90%",
            borderRadius: "10px",
            boxShadow: "0 4px 8px rgba(0, 0, 0, 0.2)",
          }}
        >
          <ModalHeader toggle={togglePreviewModal}>
            <h2>Preview of Institute Entry</h2>
            <h5 className="text-danger mt-2">
              Note: Please check all fields carefully before submitting
            </h5>
          </ModalHeader>
          <ModalBody style={{}}>
            <div>
              {/* Institute Name and University */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label
                      className="form-control-label"
                      htmlFor="input-address"
                    >
                      Institute Name :
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Choose University :
                    </label>
                    <input
                      type="text"
                      value={
                        university.find(
                          (type) =>
                            String(type._id) === String(formData.university)
                        )?.name || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Institute Email:
                    </label>
                    <input
                      type="text"
                      value={formData.collegeEmail}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Contact Info and Establishment Year */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Person:
                    </label>
                    <input
                      type="text"
                      value={formData.contactPerson}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Contact Number:
                    </label>
                    <input
                      type="text"
                      value={formData.contactNumber}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Establishment Year:
                    </label>
                    <input
                      type="text"
                      value={formData.establishYear}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Registration Date and Institute Type */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Registration Date:
                    </label>
                    <input
                      type="text"
                      value={formData.regDate}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Institute Type:
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue(
                        "collegeType",
                        formData.collegeType
                      )}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">Division:</label>

                    <input
                      type="text"
                      value={
                        division.find(
                          (type) =>
                            String(type.divisionCode) ===
                            String(formData.divison)
                        )?.name || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Division, District, and Vidhan Sabha */}
              <Row className="mb-4">
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">District:</label>
                    <input
                      type="text"
                      value={
                        district.find(
                          (type) =>
                            String(type.LGDCode) === String(formData.district)
                        )?.districtNameEng || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">Vidhan Sabha:</label>
                    <input
                      type="text"
                      value={
                        vidhansabha.find(
                          (type) =>
                            String(type.ConstituencyNumber) ===
                            String(formData.vidhansabha)
                        )?.ConstituencyName || "N/A"
                      }
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>

                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">
                      Is Lead Institute:
                    </label>
                    <input
                      type="text"
                      value={formatFieldValue("isLead", formData.isLead)}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>

              {/* Vidhan Sabha, Is Lead Institute, AISHE Code, Address */}
              <Row className="mb-4">
                <Col lg="4">
                  <FormGroup>
                    <label
                      className="form-control-label"
                      htmlFor="input-isTribal"
                    >
                      Is Tribal Institute
                    </label>
                    <div
                      className="row"
                      style={{ justifyContent: "space-evenly" }}
                    >
                      <FormGroup check>
                        <Input
                          type="radio"
                          name="isTribal"
                          id="input-tribal-yes"
                          value="true"
                          checked={formatFieldValue(
                            formData.isTribal === "true"
                          )}
                          disabled
                        />
                        <Label check>Yes</Label>
                      </FormGroup>
                      <FormGroup check>
                        <Input
                          type="radio"
                          name="isTribal"
                          id="input-tribal-no"
                          value="false"
                          checked={formatFieldValue(
                            formData.isTribal === "false"
                          )}
                          disabled
                        />
                        <Label check>No</Label>
                      </FormGroup>
                    </div>
                  </FormGroup>
                </Col>

                <Col lg="4">
                  <FormGroup>
                    <label
                      className="form-control-label"
                      htmlFor="input-degreeTypes"
                    >
                      Degree Types
                    </label>
                    <Input
                      type="select"
                      name="degreeTypes"
                      id="input-degreeTypes"
                      value={formData.degreeTypes}
                      disabled
                    >
                      <option value="">Select College Type</option>
                      <option value="UG">Under Graduate</option>
                      <option value="PG">Post Graduate</option>
                      {/* Add more options as needed */}
                    </Input>
                  </FormGroup>
                </Col>

                <Col lg="4">
                  <FormGroup>
                    <label className="form-control-label" htmlFor="input-lat">
                      Latitude
                    </label>
                    <Input
                      name="lat"
                      id="input-lat"
                      placeholder="Latitude"
                      type="text"
                      autoComplete="pope"
                      value={formData.lat}
                      disabled
                    />
                  </FormGroup>
                </Col>
              </Row>
              <Row>
                <Col lg="4">
                  <FormGroup>
                    <label className="form-control-label" htmlFor="input-long">
                      Longitude
                    </label>
                    <Input
                      name="long"
                      id="input-long"
                      placeholder="Longitude"
                      type="text"
                      autoComplete="pope"
                      value={formData.long}
                      disabled
                    />
                  </FormGroup>
                </Col>
                <Col lg="4">
                  <FormGroup>
                    <label className="form-control-label" htmlFor="input-area">
                      Area (in meter)
                    </label>
                    <Input
                      name="area"
                      id="input-area"
                      placeholder="Area"
                      type="text"
                      autoComplete="pope"
                      value={formData.area}
                      disabled
                    />
                  </FormGroup>
                </Col>
                <Col md="4">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">AISHE Code:</label>
                    <input
                      type="text"
                      value={formData.aisheCode}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              {/* AISHE Code and Address */}
              <Row className="mb-4">
                <Col md="6">
                  <div className="preview-item mb-3">
                    <label className="form-control-label">Address:</label>
                    <input
                      type="text"
                      value={formData.address}
                      disabled
                      className="form-control"
                    />
                  </div>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col>
                  <h3>Principal Details</h3>
                </Col>
              </Row>
              <Row className="mb-4">
                <Col lg="3">
                  <FormGroup>
                    <label
                      className="form-control-label"
                      htmlFor="input-universityType"
                    >
                      Is Principal Posted
                    </label>
                    <div
                      className="row"
                      style={{ justifyContent: "space-evenly" }}
                    >
                      <FormGroup check>
                        <Input
                          type="radio"
                          name="isPrincipalPosted"
                          id="input-lead"
                          value="true"
                          checked={formData.isPrincipalPosted === true}
                          onChange={handleInputChange}
                          readOnly
                          disabled
                        />
                        <Label check>Yes</Label>
                      </FormGroup>
                      <FormGroup check>
                        <Input
                          type="radio"
                          name="isPrincipalPosted"
                          id="input-lead"
                          value="false"
                          checked={formData.isPrincipalPosted === false}
                          onChange={handleInputChange}
                          readOnly
                          disabled
                        />

                        <Label check>No</Label>
                      </FormGroup>
                    </div>
                  </FormGroup>
                </Col>
                {formData.isPrincipalPosted === true && (
                  <>
                    <Col lg="3">
                      <FormGroup>
                        <label
                          className="form-control-label"
                          htmlFor="input-principalType"
                        >
                          Charge Type
                        </label>
                        <Input
                          type="select"
                          name="principalType"
                          id="input-principalType"
                          value={formData.principalType}
                          disabled
                          onChange={handleInputChange}
                        >
                          <option value="">Select Charge Type</option>
                          <option value="Regular">Regular</option>
                          <option value="In-charge">In-charge</option>
                          <option value="Additional Charge">
                            Additional Charge
                          </option>
                          {/* Add more options as needed */}
                        </Input>
                        {errors.principalType && (
                          <small style={{ color: "red" }}>
                            {errors.principalType}
                          </small>
                        )}
                      </FormGroup>
                    </Col>

                    <Col md="6">
                      <div className="preview-item mb-3">
                        <label className="form-control-label">
                         Principal Name:
                        </label>
                        <input
                          type="text"
                          value={formData.prinicipalName}
                          disabled
                          className="form-control"
                        />
                      </div>
                    </Col>
                    <Col md="6">
                      <div className="preview-item mb-3">
                        <label className="form-control-label">
                          Principal No:
                        </label>
                        <input
                          type="text"
                          value={formData.principalNo}
                          disabled
                          className="form-control"
                        />
                      </div>
                    </Col>
                  </>
                )}
              </Row>
            </div>
          </ModalBody>
          <ModalFooter style={{ backgroundColor: "#f1f3f5" }}>
            <Button
              color="secondary"
              onClick={togglePreviewModal}
              style={{ borderRadius: "5px" }}
            >
              Cancel
            </Button>
            <Button
              color="primary"
              onClick={handleSubmit}
              style={{
                borderRadius: "5px",
                backgroundColor: "#007bff",
                border: "none",
              }}
            >
              Submit
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default ProfileUpdateInstitute;
