import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Label,
  Spinner,
  FormFeedback,
} from "reactstrap";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams } from "react-router-dom";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx"
import formatDate from "../../utils/formateDate.jsx";
const UniversityUpdate = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [formData, setFormData] = useState({
    name: "",
    universityEmail: "",
    contactPerson: "",
    contactNumber: "",
    divison: "",
    establishYear: "",
    district: "",
    vidhansabha: "",
    registerationNumber: "",
    universityType: "",
    uniRegDate: "",
    address: "",
    universityUrl: "",
  });

  const { id } = useParams();
  const [errorMessage, setErrorMessage] = useState("");
  const [errors, setErrors] = useState({
    contactNumber: "",
    universityEmail: "",
  });
  const validateContactNumber = (number) => {
    const regex = /^[0-9]{10}$/; // Change as per your requirements (e.g., length, format)
    return regex.test(number);
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email format validation
    return regex.test(email);
  };

  // Fetch university data on component mount or when 'id' changes
  useEffect(() => {
    const getuniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-university/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          const formattedDate = new Date(data.uniRegDate)
            .toISOString()
            .split("T")[0]; // Convert to YYYY-MM-DD
          setFormData({
            ...data,
            uniRegDate: formattedDate,
            divison: data.divison || "",
            vidhansabha: data.vidhansabha || "",
            district: data.district || "",
            status: data.status,
          });
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getuniversity(); // Call the function inside useEffect
  }, [id, endPoint, token]); // Dependencies

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: "",
    }));
    if (name === "contactNumber") {
      if (!validateContactNumber(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          contactNumber: "Please enter a valid 10-digit contact number.",
        }));
      }
    }
    if (name === "universityEmail") {
      if (!validateEmail(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          universityEmail: "Please enter a valid email address.",
        }));
      }
    }
  };

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/division/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const handledivisonChange = async (e) => {
    const { value } = e.target;

    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setDistrict(response.data);
      } else {
        alert("Failed to fetch districts.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert("An error occurred. Please try again later.");
    }

    // Correct state update
    setFormData((prevData) => ({
      ...prevData,
      divison: value,
    }));
  };

  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({ ...formData, district: value });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
      } else {
        alert("Failed to fetch vidhansabha data.");
      }
    } catch (error) {
      console.error("Error fetching vidhansabha:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  const handleKeyPress = (e) => {
    const key = e.key;
    const isNumber = /^[0-9]$/.test(key);
    if (!isNumber && key !== "Backspace" && key !== "Tab") {
      e.preventDefault(); // Prevents the input of non-numeric characters
    }
  };
  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();
    if (String(formData.contactNumber).length !== 10) {
      SwalMessageAlert("Please enter a valid 10-digit phone number!", "error");
      return;
    }
    const valuesToValidate = [
      formData.name,
      formData.universityEmail,
      formData.contactPerson,
      formData.contactNumber,
      formData.divison,
      formData.establishYear,
      formData.district,
      formData.vidhansabha,
      formData.registerationNumber,
      formData.universityType,
      formData.uniRegDate,
      formData.address,
      formData.universityUrl,
    ];
    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    // Condition for empty fields
    if (hasEmptyFields) {
      SwalMessageAlert("Please fill out all fields before submitting.", "error");
      return; // Prevent form submission
    }

    if (allFieldsFilled) {
      SwalMessageAlert("All fields are filled. You can proceed with submission.", "success");
    }

    try {
      const body = {
        name: formData.name,
        universityEmail: formData.universityEmail,
        contactPerson: formData.contactPerson,
        contactNumber: String(formData.contactNumber),
        divison: String(formData.divison),
        establishYear: String(formData.establishYear),
        district: String(formData.district),
        vidhansabha: String(formData.vidhansabha),
        registerationNumber: formData.registerationNumber,
        universityType: String(formData.universityType),
        uniRegDate: formData.uniRegDate,
        address: formData.address,
        universityUrl: formData.universityUrl,
      };
      const response = await axios.put(
        `${endPoint}/api/university/update/${id}`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        window.location.replace("admin/university");
        setFormData({
          name: "",
          universityEmail: "",
          contactPerson: "",
          contactNumber: "",
          division: "",
          establishYear: "",
          district: "",
          vidhansabha: "",
          registrationNumber: "",
          uniRegDate: "",
          universityType: "",
          universityUrl: "",
          address: "",
        });
      } else {
        alert("Adding for University failed.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.divison}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.divison) fetchDistrict();
  }, [formData.divison, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.vidhansabha) fetchVidhansabha();
  }, [formData.vidhansabha, endPoint, token]);

  const updateUniversityStatus = async (id) => {
    try {
      const response = await axios.put(
        `${endPoint}/api/university/update-status/${id}`,
        {}, // Pass empty object if no data needs to be sent in the body
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        // Check the status code to ensure success
        window.location.replace("admin/university");
      } else {
        alert("Failed to fetch University data. Please try again.");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">UPDATE UNIVERSITY</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <b>
                      Last Update Date : {formatDate(formData.updatedAt)} &nbsp;
                    </b>
                    {formData.status === false ? (
                      <button
                        className="btn btn-warning btn-sm"
                        onClick={() => updateUniversityStatus(id)}
                      >
                        <Spinner
                          size="sm"
                          color="white"
                          style={{ marginRight: "8px" }}
                        />
                        Verify
                      </button>
                    ) : (
                      <span className="btn btn-success btn-sm">Verified</span>
                    )}
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            University Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="University Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                          />
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityEmail"
                          >
                            University Email
                          </label>
                          <Input
                            name="universityEmail"
                            id="input-universityEmail"
                            placeholder="University Email"
                            type="email"
                            autoComplete="pope"
                            value={formData.universityEmail}
                            onChange={handleInputChange}
                            required
                            invalid={!!errors.universityEmail}
                          />
                          {errors.universityEmail && (
                            <FormFeedback>
                              {errors.universityEmail}
                            </FormFeedback>
                          )}
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactPerson"
                          >
                            Contact Person
                          </label>
                          <Input
                            name="contactPerson"
                            id="input-contactPerson"
                            placeholder="Contact Person"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactPerson}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-contactNumber"
                          >
                            Contact Number
                          </label>
                          <Input
                            name="contactNumber"
                            id="input-contactNumber"
                            placeholder="Contact Number"
                            type="text"
                            autoComplete="pope"
                            value={formData.contactNumber}
                            onChange={handleInputChange}
                            required
                            invalid={!!errors.contactNumber}
                            onKeyPress={handleKeyPress} // Attach key press event
                          />
                          {errors.contactNumber && (
                            <FormFeedback>{errors.contactNumber}</FormFeedback>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-divison"
                          >
                            Divison
                          </label>
                          <Input
                            name="divison"
                            id="input-divison"
                            type="select"
                            value={formData.divison}
                            onChange={handledivisonChange}
                            required
                          >
                            <option value="">Select Divison</option>
                            {division &&
                              division.length > 0 &&
                              division.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.divisionCode}
                                  selected={
                                    Number(type.divisionCode) ===
                                    Number(formData.divison)
                                  }
                                >
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-district"
                          >
                            District
                          </label>
                          <Input
                            name="district"
                            id="input-district"
                            type="select"
                            value={formData.district}
                            onChange={handleDistrictChange}
                            required
                          >
                            <option value="">Select District</option>
                            {district &&
                              district.length > 0 &&
                              district.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.LGDCode}
                                  selected={
                                    Number(type.LGDCode) ===
                                    Number(formData.district)
                                  }
                                >
                                  {type.districtNameEng}
                                </option>
                              ))}
                            {/* Add more districts as needed */}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-vidhansabha"
                          >
                            Vidhan Sabha
                          </label>
                          <Input
                            name="vidhansabha"
                            id="input-vidhansabha"
                            type="select"
                            value={formData.vidhansabha}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="" disabled>
                              Select Vidhansabha
                            </option>
                            {vidhansabha &&
                              vidhansabha.length > 0 &&
                              vidhansabha.map((type, index) => (
                                <option
                                  key={index}
                                  value={type.ConstituencyNumber}
                                  selected={
                                    Number(type.ConstituencyNumber) ===
                                    Number(formData.vidhansabha)
                                  }
                                >
                                  {type.ConstituencyName}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-establishYear"
                          >
                            Establishment Year
                          </label>
                          <Input
                            name="establishYear"
                            id="input-establishYear"
                            placeholder="Establishment Year"
                            type="number"
                            autoComplete="pope"
                            value={formData.establishYear}
                            required
                            onChange={handleInputChange}
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-registrationNumber"
                          >
                            Registration Number
                          </label>
                          <Input
                            name="registerationNumber"
                            id="input-registrationNumber"
                            autoComplete="pope"
                            placeholder="Registration Number"
                            type="text"
                            value={formData.registerationNumber}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-uniRegDate"
                          >
                            Registration Date
                          </label>
                          <Input
                            name="uniRegDate"
                            id="input-uniRegDate"
                            placeholder="Registration Date"
                            type="date"
                            autoComplete="pope"
                            value={formData.uniRegDate}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="3">
                        <FormGroup>
                          <Label className="Label">University Type</Label>
                          <div
                            className="row"
                            style={{ justifyContent: "space-evenly" }}
                          >
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="universityType"
                                id="input-universityType"
                                value="1"
                                checked={formData.universityType === 1}
                                onChange={handleInputChange}
                              />
                              <Label check>Govt</Label>
                            </FormGroup>
                            <FormGroup check>
                              <Input
                                type="radio"
                                name="universityType"
                                id="input-universityType"
                                value="0"
                                checked={formData.universityType === 0}
                                onChange={handleInputChange}
                              />
                              <Label check>Private</Label>
                            </FormGroup>
                          </div>
                        </FormGroup>
                      </Col>
                      {/* <Col lg="3">
                        <FormGroup>
                          <label className="form-control-label" htmlFor="input-universityType">
                            University Type
                          </label>
                          <Input
                            name="universityType"
                            id="input-universityType"
                            type="select"
                            value={formData.universityType}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select University Type</option>
                            <option value="1">Govt.</option>
                            <option value="2">Private</option>
                            {/* Add more university types if needed */}
                      {/* </Input> */}

                      {/* </FormGroup>
                      // </Col> */}
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-universityUrl"
                          >
                            University URL
                          </label>
                          <Input
                            name="universityUrl"
                            id="input-universityUrl"
                            autoComplete="pope"
                            placeholder="University URL"
                            type="url"
                            value={formData.universityUrl}
                            required
                            onChange={handleInputChange}
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-address"
                          >
                            Address
                          </label>
                          <Input
                            name="address"
                            id="input-address"
                            placeholder="Address"
                            // type="text"
                            as="textarea"
                            value={formData.address}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UniversityUpdate;
