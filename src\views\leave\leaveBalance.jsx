import { useState, useEffect } from "react";
import <PERSON>wal from "sweetalert2";
import axios from "axios";
import {
    <PERSON>,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    CardTitle,
    CardFooter,
    Form,
    FormGroup,
    Label,
    Input,
    Button,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import { useParams } from "react-router-dom";


const LeaveBalanceEntry = () => {
    // State for card values - initial dummy data
    const token = sessionStorage.getItem('authToken');
    const endPoint = import.meta.env.VITE_API_URL;
    const collegeId = sessionStorage.getItem("id")
    const { id } = useParams();

    const [college, setcollege] = useState([]);
    const [isTribal, setIsTribal] = useState(false);


    useEffect(() => {
        const getCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-single-college/${collegeId}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data;
                    setIsTribal(data.isTribal);
                    // console.log(data.isTribal,"data.isTribal");
                    
                }
            } catch (error) {
                console.error("Error fetching college data:", error);
                alert("Failed to load college data.");
            }
        };

        getCollege(); // Call the function inside useEffect
    }, [id, endPoint, token]); // Dependencies


    const [currentBalance, setCurrentBalance] = useState([]);
    const currentYear = new Date().getFullYear();

    // console.log(currentBalance, "Getting Current balance");
    // console.log(currentBalance[0]?.restrictedHoliday, "Getting Current balance Optional holiday    ");


    const [formData, setFormData] = useState({
        restrictedHoliday: '',
        casualLeave: '',
        earnedLeave: '',
        halfPayLeave: '',
        tribalCasualLeave: "",
        tribalEarnedLeave: "",
    });

    useEffect(() => {
        if (currentBalance && currentBalance.length > 0) {
            // Set form data based on the first object in currentData
            const { restrictedHoliday, casualLeave, earnedLeave, halfPayLeave, tribalCasualLeave, tribalEarnedLeave, } = currentBalance[0];

            setFormData({
                restrictedHoliday: restrictedHoliday || '',
                casualLeave: casualLeave || '',
                earnedLeave: earnedLeave || '',
                halfPayLeave: halfPayLeave || '',
                tribalCasualLeave: tribalCasualLeave || "",
                tribalEarnedLeave: tribalEarnedLeave || "",

            });
        }
    }, [currentBalance]);

    // Handle form input changes
    const handleChange = (e, maxLimit = Infinity) => {
        const { name, value } = e.target;

        // Allow update if value is empty or within the max limit
        if (value === "" || parseInt(value, 10) <= maxLimit) {
            setFormData({ ...formData, [name]: value });
        } else {
            Swal.fire({
                icon: 'warning',
                title: 'Invalid Input',
                text: `${name} cannot be greater than ${maxLimit}`,
                confirmButtonText: 'OK',
            });
        }
    };


    useEffect(() => {
        const fetchApplications = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/leave/leaveBalance/${id}`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                if (response.status === 200) {
                    setCurrentBalance(response.data);
                } else {
                    alert("Data Not Fetched.");

                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
                // navigate('/auth/Register');
            }

        };
        fetchApplications();
    }, [endPoint, token]);

    // // console.log("Current Balance ",currentBalance);


    const handleSubmit = async (e) => {
        e.target.disabled = true;
        setTimeout(() => {
            e.target.disabled = false;
          }, 5000);
          
        e.preventDefault();
        try {
            let body = {
                restrictedHoliday: String(formData.restrictedHoliday),
                casualLeave: String(formData.casualLeave),
                earnedLeave: String(formData.earnedLeave),
                halfPayLeave: String(formData.halfPayLeave),
                tribalCasualLeave: String(formData.tribalCasualLeave),
                tribalEarnedLeave: String(formData.tribalEarnedLeave),
            };

            // Use empId as a URL parameter
            const response = await axios.put(`${endPoint}/api/leave/update_leave_balance/${id}`, body, {
                headers: {
                    'Content-Type': 'application/json',
                    'web-url': window.location.href,
                    "Authorization": `Bearer ${token}`
                }
            });

            if (response.status === 200) { // Assuming 200 status for successful update
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: "Leave Balance Updated Successfully.",
                });
            }
            setTimeout(function () {
                window.location.reload();
            }, 3000);
        } catch (error) {
            if (error.response) {
                const { status, data } = error.response;

                let errorMsg = data.message || "An error occurred. Please try again.";
                if (status === 400) {
                    errorMsg = data.msg || errorMsg;
                }

                Swal.fire({
                    icon: 'error',
                    title: 'Submission Error',
                    text: errorMsg,
                });
            } else {
                console.error("An error occurred while submitting the form:", error);
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: "An error occurred. Please try again later.",
                });
            }
        }
    };



    return (
        <>
            <Header />
            <Container className="mt--7" fluid>

                <Row>

                    <Col lg="6" xl="3">
                        <Card className="card-stats mb-4 mb-xl-0">
                            <CardBody>
                                <Row>
                                    <div className="col">
                                        <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                            Optional Holiday
                                        </CardTitle>
                                        <span className="h2 font-weight-bold mb-0">
                                            {currentBalance[0]?.restrictedHoliday}
                                        </span>
                                    </div>
                                    <Col className="col-auto">
                                        <div className="icon icon-shape rounded-circle shadow">
                                            <i className="fas fa-calendar-alt" />
                                        </div>
                                    </Col>
                                </Row>
                            </CardBody>
                            <CardFooter style={{ backgroundColor: '#ffcccc' }} className="text-muted">
                                Optional Holiday {currentYear}
                            </CardFooter>
                        </Card>
                    </Col>

                    <Col lg="6" xl="3">
                        <Card className="card-stats mb-4 mb-xl-0">
                            <CardBody>
                                <Row>
                                    <div className="col">
                                        <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                            Casual Leave
                                        </CardTitle>
                                        <span className="h2 font-weight-bold mb-0">
                                            {currentBalance[0]?.casualLeave}
                                        </span>
                                    </div>
                                    <Col className="col-auto">
                                        <div className="icon icon-shape rounded-circle shadow">
                                            <i className="fas fa-plane" />
                                        </div>
                                    </Col>
                                </Row>
                            </CardBody>
                            <CardFooter style={{ backgroundColor: '#fff3cd' }} className="text-muted">
                                Casual Leaves {currentYear}
                            </CardFooter>
                        </Card>
                    </Col>

                    <Col lg="6" xl="3">
                        <Card className="card-stats mb-4 mb-xl-0">
                            <CardBody>
                                <Row>
                                    <div className="col">
                                        <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                            Earned Leave
                                        </CardTitle>
                                        <span className="h2 font-weight-bold mb-0">
                                            {currentBalance[0]?.earnedLeave}
                                        </span>
                                    </div>
                                    <Col className="col-auto">
                                        <div className="icon icon-shape rounded-circle shadow">
                                            <i className="fas fa-check-circle" />
                                        </div>
                                    </Col>
                                </Row>
                            </CardBody>
                            <CardFooter style={{ backgroundColor: '#d4edda' }} className="text-muted">
                                Earned Leaves (All Time)
                            </CardFooter>
                        </Card>
                    </Col>

                    <Col lg="6" xl="3">
                        <Card className="card-stats mb-4 mb-xl-0">
                            <CardBody>
                                <Row>
                                    <div className="col">
                                        <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                            Half Pay Leave
                                        </CardTitle>
                                        <span className="h2 font-weight-bold mb-0">
                                            {currentBalance[0]?.halfPayLeave}
                                        </span>
                                    </div>
                                    <Col className="col-auto">
                                        <div className="icon icon-shape rounded-circle shadow">
                                            <i className="fas fa-clock" />
                                        </div>
                                    </Col>
                                </Row>
                            </CardBody>
                            <CardFooter style={{ backgroundColor: '#cce5ff' }} className="text-muted">
                                Half Pay Leaves (All Time)
                            </CardFooter>
                        </Card>
                    </Col>
                </Row><br />


                {isTribal && isTribal === true &&
                    <Row>
                        <Col lg="6" xl="3">
                            <Card className="card-stats mb-4 mb-xl-0">
                                <CardBody>
                                    <Row>
                                        <div className="col">
                                            <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                                Tribal Earned Leave
                                            </CardTitle>
                                            <span className="h2 font-weight-bold mb-0">
                                                {currentBalance[0]?.tribalEarnedLeave}
                                            </span>
                                        </div>
                                        <Col className="col-auto">
                                            <div className="icon icon-shape rounded-circle shadow">
                                                <i className="fas fa-mountain" />
                                            </div>
                                        </Col>
                                    </Row>
                                </CardBody>
                                <CardFooter style={{ backgroundColor: '#d4edda' }} className="text-muted">
                                    Tribal Earned Leaves (All Time)
                                </CardFooter>
                            </Card>
                        </Col>

                        <Col lg="6" xl="3">
                            <Card className="card-stats mb-4 mb-xl-0">
                                <CardBody>
                                    <Row>
                                        <div className="col">
                                            <CardTitle tag="h5" className="text-uppercase text-muted mb-0">
                                                Casual Leave
                                            </CardTitle>
                                            <span className="h2 font-weight-bold mb-0">
                                                {currentBalance[0]?.tribalCasualLeave}
                                            </span>
                                        </div>
                                        <Col className="col-auto">
                                            <div className="icon icon-shape rounded-circle shadow">
                                                <i className="fas fa-tree" />
                                            </div>
                                        </Col>
                                    </Row>
                                </CardBody>
                                <CardFooter style={{ backgroundColor: '#fff3cd' }} className="text-muted">
                                  Tribal Casual Leaves {currentYear}
                                </CardFooter>
                            </Card>
                        </Col>
                    </Row>}


                <Row className="mt-4">
                    <Col md="1">
                    </Col>
                    <Col md="10">
                        <Card>
                            <CardHeader>
                                <h3>Leave Balance ( <span style={{ color: "blue" }}>{currentBalance[0]?.employeeName}</span> ) <span style={{ color: "red" }}> ( {currentBalance[0]?.empId})</span></h3>
                            </CardHeader>
                            <CardBody className="border">
                                <Row className="mt--3 mb-3">
                                    <span style={{ fontWeight: "bolder" }}>Leave Used In Year</span>
                                </Row>
                                <Row  >
                                    <Col >
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "darkGreen", fontWeight: "bolder" }}>
                                            Optional Holiday
                                        </Row>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                            {3 - formData.restrictedHoliday}
                                        </Row>
                                    </Col>
                                    <Col>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "purple", fontWeight: "bolder" }}>
                                            Casual Leave
                                        </Row>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                            {13 - formData.casualLeave}
                                        </Row>
                                    </Col>
                                    <Col>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "darkOrange", fontWeight: "bolder" }}>
                                            Earned Leave
                                        </Row>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                            0
                                        </Row>
                                    </Col>
                                    <Col>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "blue", fontWeight: "bolder" }}>
                                            Half Pay Leave
                                        </Row>
                                        <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                            0
                                        </Row>
                                    </Col>

                                    {isTribal && isTribal === true &&
                                        <>
                                            <Col>
                                                <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "green", fontWeight: "bolder" }}>
                                                    Tribal Earned Leave
                                                </Row>
                                                <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                                    0
                                                </Row>
                                            </Col>
                                            <Col>
                                                <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center", color: "black", fontWeight: "bolder" }}>
                                                    Tribal Casual Leave
                                                </Row>
                                                <Row style={{ justifyContent: "center", alignItems: "center", textJustify: "center" }}>
                                                    0
                                                </Row>
                                            </Col>
                                        </>}
                                </Row>
                            </CardBody>
                            <CardBody>
                                <Form >
                                    <Row className="mt--4 mb-3" style={{ fontWeight: "bolder", }}>
                                        Entry Current Balance
                                    </Row>
                                    <Row>
                                        <Col >
                                            <FormGroup>
                                                <Label for="restrictedHoliday">Optional Holiday</Label>
                                                <Input
                                                    type="number"
                                                    name="restrictedHoliday"
                                                    id="restrictedHoliday"
                                                    value={formData.restrictedHoliday}
                                                    onChange={(e) => handleChange(e, 3)} // Max limit for restrictedHoliday
                                                    min="0"
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col >
                                            <FormGroup>
                                                <Label for="casualLeave">Casual Leave</Label>
                                                <Input
                                                    type="number"
                                                    name="casualLeave"
                                                    id="casualLeave"
                                                    value={formData.casualLeave}
                                                    onChange={(e) => handleChange(e, 13)} // Max limit for casualLeave
                                                    min="0"
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col>
                                            <FormGroup>
                                                <Label for="earnedLeave">Earned Leave</Label>
                                                <Input
                                                    type="number"
                                                    name="earnedLeave"
                                                    id="earnedLeave"
                                                    value={formData.earnedLeave}
                                                    onChange={(e) => handleChange(e, 300)} // Max limit for earnedLeave
                                                    min="0"
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col >
                                            <FormGroup>
                                                <Label for="halfPayLeave">Half Pay Leave {college.isTribal}</Label>
                                                <Input
                                                    type="number"
                                                    name="halfPayLeave"
                                                    id="halfPayLeave"
                                                    value={formData.halfPayLeave}
                                                    onChange={handleChange}
                                                    min="0"
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        {isTribal && isTribal === true &&
                                            <>
                                                <Col >
                                                    <FormGroup>
                                                        <Label for="tribalEarnedLeave">Tribal Earned Leave </Label>
                                                        <Input
                                                            type="number"
                                                            name="tribalEarnedLeave"
                                                            id="tribalEarnedLeave"
                                                            value={formData.tribalEarnedLeave}
                                                            onChange={(e) => handleChange(e, 300)} // Max limit for earnedLeave
                                                            min="0"
                                                            required
                                                        />
                                                    </FormGroup>
                                                </Col>
                                                <Col>
                                                    <FormGroup>
                                                        <Label for="tribalCasualLeave">Tribal Casual Leave</Label>
                                                        <Input
                                                            type="number"
                                                            name="tribalCasualLeave"
                                                            id="tribalCasualLeave"
                                                            value={formData.tribalCasualLeave}
                                                            onChange={handleChange}
                                                            min="0"
                                                            required
                                                        />
                                                    </FormGroup>
                                                </Col>
                                            </>}

                                    </Row>
                                    <Button color="primary" onClick={handleSubmit}>
                                        Submit
                                    </Button>
                                </Form>

                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default LeaveBalanceEntry;