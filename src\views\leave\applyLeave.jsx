import { useState, useEffect } from "react";
import moment from 'moment';
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardBody,
  FormGroup,
  Container,
  Row,
  Spinner,
  Col,
  Form,
  Input,
  Label,
  Table,
  Badge,
  Modal,
  ModalHeader,
  Modal<PERSON>ody,
  ModalFooter,
} from "reactstrap";
// import { Document, Page, Text, View, pdf } from "@react-pdf/renderer";

import { BsDownload, BsEye } from "react-icons/bs"; // Bootstrap Icons
import Swal from "sweetalert2";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx"
import { Link, useNavigate } from "react-router-dom";


const ApplyLeave = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const empId = sessionStorage.getItem("id");
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [appliedLeave, setAppliedLeave] = useState([]);
  const [holiday, setHoliday] = useState([]);
  const [isTribal, setIsTribal] = useState(false);


  const navigate = useNavigate();
  const [dayGap, setDayGap] = useState(0);
  const [file, setFile] = useState(null);
  const filteredLeave = appliedLeave.filter(
    (leave) =>
      leave.leaveStatus === 1 ||
      leave.leaveStatus === 2 ||
      (leave.leaveStatus === 2 &&
        (leave.designation === "PG Principal" ||
          leave.designation === "UG Principal"))
  );
  const [leaveBalance, setLeaveBalance] = useState([]);
  const [newLeaveBalance, setNewLeaveBalance] = useState([]);
  const [maxLeave, setMaxLeave] = useState(0);
  const [rules, setRules] = useState([]);


  // // console.log(leaveBalance, "leaveBalance of User");


  const [pendingJoining, setPendingJoining] = useState([]);


  const [user, setUser] = useState("");

  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);


  const [activeLeavePeriod, setActiveLeavePeriod] = useState([]);


  // Applied Leaves and Pending Leavs




  // Leave Rule List
  useEffect(() => {
    const fetchLeaveRules = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/leave/rule/list`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setRules(response.data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchLeaveRules();
  }, [token, endPoint]);

  // Employee Details Single
  useEffect(() => {
    const fetchEmployeeData = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/get-employee/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setUser(data);


        } else {
          alert("Data Not Fetched.");

        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");

      }
    };
    fetchEmployeeData();
  }, [token, endPoint, empId]);


  // Leave Balance Single
  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/leaveBalance/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setIsTribal(data[0]?.isTribal);
          setLeaveBalance(data);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchApplications();
  }, [empId, token, endPoint]);

  // Get all Holiday 
  useEffect(() => {
    const fetchHoliday = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/holiday/get-all-holiday`,
          {
            headers: {
              'Content-Type': 'application/json',
              'web-url': window.location.href,
              "Authorization": `Bearer ${token}`
            }
          });
        if (response.status === 200) {
          const govtHoliday = response.data.filter(item => item.type === 'Govt')
          // const restrictedHoliday = response.data.filter(item => item.type === 'Restricted')
          setHoliday(govtHoliday);
          // setRestrictedHoliday(restrictedHoliday);
        } else {
          alert("Data Not Fetched.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    }
    fetchHoliday();
  }, [token, endPoint]);


  // Setting Modal for show step wise Status
  const [modal, setModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  // code for modal toggle
  const toggle = (id) => {
    // console.log("getting Leave Status",id);

    const status = id.leaveStatus
    setModal(!modal);
    setSelectedStatus(status);
  };
  const statusCode = selectedStatus;


  useEffect(() => {
    if (statusCode >= 1 && statusCode <= 3) {
      setCurrentStep(statusCode - 1); // Convert status code to zero-based index
    }
  }, [statusCode]);
  const steps = [
    { label: "Employee", icon: "fa-user" },
    { label: "Office", icon: "fa-solid fa-briefcase" },
    { label: "Directorate", icon: "fa-solid fa-user-shield" },
  ];

  // Defining Form Data Here
  const [formData, setFormData] = useState({
    applicantId: "",
    applicationType: "",
    reason: "",
    leaveType: "",
    maxLeave: "",
    fromDate: "",
    tillDate: "",
    dayCount: "",
    permission: "0",
    stationAddress: "",
    remarkByUser: "",
    uploadFile: file,
  });

  // Handle Change for All form data
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };


  useEffect(() => {
    const fetchAppliedLeave = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/leave/applied_Leaves/${empId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const data = response.data;

          const joiningPending = data.filter(
            (leave) =>
              leave.leaveStatus === 3 &&
              leave.isJoined === 0 &&
              (leave.leaveType === "Earned Leave" ||
                leave.leaveType === "Medical Leave" ||
                leave.leaveType === "Half Pay Leave")
          );
          setPendingJoining(joiningPending);
          
          setAppliedLeave(data);
          const today = new Date().toISOString().split("T")[0];


          const activeLeavePeriod = data.filter((leave) =>
            (leave.leaveStatus === 3 || leave.leaveStatus === 2 || leave.leaveStatus === 1) && leave.fromDate > today );

          // console.log(activeLeavePeriod, "Log active Leave Period");
          // setActiveLeavePeriod(activeLeavePeriod);
          // Check leave period availability after setting activeLeavePeriod
          const { fromDate, tillDate } = formData;
          if (fromDate && tillDate) {
            // console.log(fromDate, tillDate, "Getting From Date and Till Date");

            // Convert incoming string dates to Date objects


            const isLeavePeriodAvailable = () => {
              const from = new Date(fromDate);
              const till = new Date(tillDate);
              return !activeLeavePeriod.some(leave => {
                if ([1, 2, 3].includes(leave.leaveStatus)) {
                  const leaveFrom = new Date(leave.fromDate);
                  const leaveTill = new Date(leave.tillDate);

                  return (from <= leaveTill && till >= leaveFrom);
                }
                return false;
              });
            };

            if (isLeavePeriodAvailable()) {
              // console.log("Leave can be applied for this period.", fromDate, tillDate);
            } else {
              // console.log("Leave cannot be applied for this period due to overlapping with existing leaves.", fromDate, tillDate);
              setFormData((prevData) => ({
                ...prevData,
                fromDate: '',
                tillDate: '',
                dayCount: 0,
              }));
              SwalMessageAlert("Your leave request cannot be processed for this period due to existing leaves for same Duration.", "error");
            }
          }
        }

       

      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchAppliedLeave();
  }, [empId, endPoint, token,  formData.fromDate, formData.tillDate]);
  const [message, setMessage] = useState({
    message: "",
    color: "",
  });
  const maxSizeInBytes = 0.1 * 1024 * 1024; // 2 MB

  // Handle Change for File data
  const handleFileChange = (e) => {
    const getFile = e.target.files[0];
    if (getFile) {
      setFile(getFile);
      if (getFile.size > maxSizeInBytes) {
        setMessage({
          message: 'File size exceeds the limit of 100 KB.',
          color: 'red',
        });
        e.target.value = ''; // Clear the input
      } else {
        setMessage({
          message: 'File is within the size limit.',
          color: 'green',
        });
      }
    }
  };


  // Reson Handled Here
  const [selectedReason, setSelectedReason] = useState("");
  const [manualReason, setManualReason] = useState("");
  const [showManualInput, setShowManualInput] = useState(false);

  // handled Reason Change here
  const handleReasonChange = (e) => {
    const value = e.target.value;
    setSelectedReason(value);
    setFormData((prevData) => ({
      ...prevData,
      remarkByUser: e.target.value,
    }));

    if (value === "Other") {
      setShowManualInput(true);
    } else {
      setShowManualInput(false);
      setManualReason("");
      setFormData((prevData) => ({
        ...prevData,
        reason: value,
      }));
    }
  };

  // handled Manual Reason Change here
  const handleManualReasonChange = (e) => {
    const value = e.target.value;
    setManualReason(value);
    setFormData((prevData) => ({
      ...prevData,
      reason: manualReason,
    }));
  };

  // handle leave Type change here
  const handleLeaveTypeChange = (e) => {
    const value = e.target.value;
    setFormData((prevData) => ({
      ...prevData,
      leaveType: value,
      fromDate: '',
      tillDate: '',
      dayCount: 0,
    }));
    setDayGap(0);


  };

  useEffect(() => {
    if (leaveBalance.length > 0) {
      const newCL = leaveBalance[0]?.casualLeave;
      const newEL = leaveBalance[0]?.earnedLeave;
      const newRH = leaveBalance[0]?.restrictedHoliday;
      const newHPL = leaveBalance[0]?.halfPayLeave;
      const newTCL = leaveBalance[0]?.tribalCasualLeave;
      const newTEL = leaveBalance[0]?.tribalEarnedLeave;



      switch (formData.leaveType) {
        case "1":
          setMaxLeave(newCL);
          break;
        case "2":
          setMaxLeave(newEL);
          break;
        case "3":
          setMaxLeave(newRH > 0 ? newRH : 0);
          break;
        case "4":
          setMaxLeave(newHPL / 2);
          break;
        case "5":
          setMaxLeave(newHPL);
          break;
        case "12":
          setMaxLeave(newTCL);
          break;
        case "13":
          setMaxLeave(newTEL);
          break;
        default:
          setMaxLeave("");
      }
    }
  }, [formData.leaveType, leaveBalance]);




  const handleDateChange = (e) => {
    const { name, value } = e.target;
    const today = new Date().toISOString().split("T")[0];

    // Check for backdates
    // // console.log(typeof formData.leaveType,formData.leaveType,"Getting Leave Type");
    // // console.log(value,"Getting Value");
    // // console.log(today,"Getting today");

    // const check = value < today ;
    // const check2 = formData.leaveType !== "1" || formData.leaveType !== "12"

    // // console.log("Checking ",check,check2);



    if ((formData.leaveType !== "1" && formData.leaveType !== "12") && value < today) {
      SwalMessageAlert("Backdates are Not Allowed. Please select a valid date.", "warning");
      return;
    }

    setFormData((prevData) => {
      let updatedData = { ...prevData, [name]: value };

      if (name === "fromDate") {
        updatedData = {
          ...updatedData,
          tillDate: "",
          dayCount: 0,
        };
        setDayGap(0);
      }

      // Recalculate day count if both dates are set
      if (updatedData.fromDate && updatedData.tillDate) {
        const fromDate = new Date(updatedData.fromDate);
        const tillDate = new Date(updatedData.tillDate);
        const dayCount = (tillDate - fromDate) / (1000 * 3600 * 24) + 1;
        updatedData.dayCount = dayCount > 0 ? dayCount : 0;
      }

      return updatedData;
    });

    // Perform holiday calculations only when valid date range exists
    const updatedData = { ...formData, [name]: value };
    if (name === "fromDate" || (updatedData.fromDate && updatedData.tillDate)) {
      const start = moment.utc(updatedData.fromDate);
      const end = moment.utc(updatedData.tillDate);

      // console.log("Start Date (UTC):", start.format());
      // console.log("End Date (UTC):", end.format());

      let totalHolidays = 0;
      const holidayDates = holiday.map((holiday) => moment.utc(holiday.date));

      // console.log("Holiday Dates (UTC):", holidayDates.map(date => date.format("YYYY-MM-DD")));

      if (updatedData.fromDate && updatedData.tillDate) {
        let current = start.clone();

        while (current.isSameOrBefore(end, "day")) {
          const isSunday = current.day() === 0;
          const isSaturday = current.day() === 6;

          const isHoliday = holidayDates.some((holidayDate) => holidayDate.isSame(current, "day"));

          // console.log(`Comparing ${current.format("YYYY-MM-DD")} with holidays: ${isHoliday}`);

          if (user.workType === "TEACHING") {
            if (isSunday || isHoliday) totalHolidays++;
          } else if (user.workType === "NON TEACHING") {
            if (isSunday || isSaturday || isHoliday) totalHolidays++;
          }

          current.add(1, "day");
        }
      }

      // console.log("Total Holidays:", totalHolidays);
      setDayGap(totalHolidays);
    }
  };



  useEffect(() => {
    // // console.log("hitted Handle Change");
    // Calculate new leave balances based on the updated formData
    let newCL = leaveBalance[0]?.casualLeave;
    let newEL = leaveBalance[0]?.earnedLeave;
    let newRH = leaveBalance[0]?.restrictedHoliday;
    let newHPL = leaveBalance[0]?.halfPayLeave;
    let newTCL = leaveBalance[0]?.tribalCasualLeave;
    let newTEL = leaveBalance[0]?.tribalEarnedLeave;




    // Update the appropriate leave balance based on leaveType
    if (formData.leaveType === "1") {
      newCL -= formData.dayCount;

    } else if (formData.leaveType === "2") {
      newEL -= formData.dayCount;
    } else if (formData.leaveType === "3") {
      newRH -= formData.dayCount;
    } else if (formData.leaveType === "4") {
      newHPL -= formData.dayCount * 2;
    } else if (formData.leaveType === "5") {
      newHPL -= formData.dayCount;
    } else if (formData.leaveType === "12") {
      newTCL -= formData.dayCount;
    } else if (formData.leaveType === "13") {
      newTEL -= formData.dayCount;
    } else {
      // // console.log("Direct enterd Here");
    }

    const newLeaveBalance = [{ CL: newCL, EL: newEL, RH: newRH, HPL: newHPL, TCL: newTCL, TEL: newTEL }];

    setNewLeaveBalance(newLeaveBalance);
  }, [leaveBalance, formData.dayCount]);

  useEffect(() => {
    if (formData.dayCount && dayGap >= 0 && (formData.leaveType === '1' || formData.leaveType === '3' || formData.leaveType === '12')) {
      setFormData(prevData => ({
        ...prevData,
        dayCount: Math.max(0, prevData.dayCount - dayGap),
      }));
    }
  }, [dayGap, formData.tillDate]);

  useEffect(() => {
    if (formData.dayCount && maxLeave) {
      const dayCount = parseInt(formData.dayCount, 10);
      const maxLeaveLimit = parseInt(maxLeave, 10);

      if (formData.leaveType === '1' || formData.leaveType === '3' || formData.leaveType === '12') {
        if (dayCount > maxLeaveLimit + dayGap) {
          showLeaveLimitWarning("Casual Leave Date is More than Max Limit.");
        }
      } else if (dayCount > maxLeaveLimit) {
        showLeaveLimitWarning("Selected Date is More than Max Limit.");
      }
    }

    if (formData.fromDate && formData.tillDate && formData.fromDate > formData.tillDate) {
      Swal.fire({
        icon: "warning",
        title: "Warning",
        text: "Till Date can't be earlier than From Date.",
        position: "top",
      });
      resetInvalidDates();
    }
  }, [formData.dayCount, formData.maxLeave, dayGap]);

  const showLeaveLimitWarning = (message) => {
    Swal.fire({
      icon: "warning",
      title: "Warning",
      text: message,
      position: "top",
    });

    setFormData(prevData => ({
      ...prevData,
      dayCount: "",
      tillDate: "",
    }));
  };

  const resetInvalidDates = () => {
    setFormData(prevData => ({
      ...prevData,
      dayCount: "",
      tillDate: "",
    }));
  };






  // handle cancel
  const handleCancel = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure you want to cancel?",
      text: "You won't be able to revert this!",
      icon: "warning",
      input: "text", // Adds a text input field
      inputPlaceholder: "Enter reason for cancellation...",
      inputAttributes: {
        "aria-label": "Reason for cancellation",
      },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, cancel it!",
      cancelButtonText: "No, keep it",
      preConfirm: (reason) => {
        if (!reason) {
          Swal.showValidationMessage("Reason for cancellation is required");
        }
        return reason;
      },
    });

    if (result.isConfirmed) {
      const reason = result.value; // Capture the reason entered by the user

      try {
        const response = await axios.put(
          `${endPoint}/api/leave/apply_cancel/${id}`,
          { reason },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          Swal.fire(
            "Applied for Cancel!",
            "Your request has been sent with your reason.",
            "success"
          );
          setTimeout(function () {
            window.location.reload();
          }, 3000);
        }
      } catch (error) {
        Swal.fire("Error!", "There was an error updating your status.", error);
      }
    }
  };


  const [isDisabled, setIsDisabled] = useState(false);




  // handle Submit
  const handleSubmit = async () => {
    try {
      // let body = {
      //   applicantId: String(empId),
      //   applicationType: String(formData.applicationType),
      //   reason: formData.reason,
      //   leaveType: String(formData.leaveType),
      //   fromDate: String(formData.fromDate),
      //   tillDate: String(formData.tillDate),
      //   dayCount: String(formData.dayCount),
      //   permission: String(formData.permission),
      //   stationAddress: formData.stationAddress,
      //   remarkByUser: formData.remarkByUser,
      // };
      setIsDisabled(true);


      const body = new FormData();
      body.append("applicantId", String(empId));
      body.append("applicationType", String(formData.applicationType));
      body.append("reason", formData.reason);
      body.append("leaveType", String(formData.leaveType));
      body.append("fromDate", String(formData.fromDate));
      body.append("tillDate", String(formData.tillDate));
      body.append("dayCount", String(formData.dayCount));
      body.append("permission", String(formData.permission));
      body.append("stationAddress", formData.stationAddress);
      body.append("remarkByUser", formData.remarkByUser);

      if (file) {
        body.append("uploadFile", file);
      }

      // console.log("Form submitted:", formData);

      const response = await axios.post(
        `${endPoint}/api/leave/apply_leave`,
        body,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 201) {
        setFormData({
          applicationType: "",
          reason: "",
          manualReason: "",
          leaveType: "",
          maxLeave: "",
          fromDate: "",
          tillDate: "",
          dayCount: "",
          permission: "",
          stationAddress: "",
          remarkByUser: "",
        });
        SwalMessageAlert("Apply for Leave is Successful.", "success");
        setShowManualInput(false);
        const timer = setTimeout(() => {
          navigate(`/admin/Preview/${response.data._id}`);
        }, 3000);
        return () => clearTimeout(timer);
      } else {
        SwalMessageAlert("Apply for Leave failed.", "error");
      }
    } catch (error) {
      if (error.response) {
        const backendMessage = error.response.data.error;
        SwalMessageAlert(`${backendMessage}`, "warning");
      }
    }
  };

  const mandatory = {
    color: "red",
    fontSize: "14px",
    fontWeight: "400",
    paddingLeft: "5px",
  };



  // useEffect(() => {
  //   if (filteredLeave.length >= 1 || pendingJoining.length >= 1) {
  //     Swal.fire({
  //       title: `Hey ${firstName} !`,
  //       text: `Your previous application is pending for ${filteredLeave.length >= 1
  //         ? "Approval"
  //         : pendingJoining.length >= 1
  //           ? "Joining"
  //           : ""
  //         }. You can apply after its completion.`,
  //       iconHtml:
  //         '<i class="fas fa-grin-beam" style="font-size: 50px; color: #ffcc00;"></i>',
  //       confirmButtonText: "Got it!",
  //       backdrop: true,
  //       allowOutsideClick: false,
  //     });
  //   }
  // }, [filteredLeave.length]);  

  // // console.log(dayGap, "Getting Leave Gaps");

  // handling remark here 
  useEffect(() => {
    // Update remarkByUser if it's empty
    setFormData((prevData) => ({
      ...prevData,
      remarkByUser: prevData.remarkByUser === '' ? prevData.reason : prevData.remarkByUser,
    }));
  }, [formData.reason]);

  // useEffect(() => {
  //   // Update remarkByUser if it's empty
  //   setFormData((prevData) => ({
  //     ...prevData,
  //     tillDate: '',
  //   }));
  //   setMaxLeave(0)
  //   setDayGap(0)
  // }, [formData.fromDate]);

  const handleReset = () => {
    // console.log("Atleast clicking");

    setFormData({
      applicationType: "",
      reason: '',
      manualReason: "",
      leaveType: "",
      maxLeave: "",
      fromDate: "",
      tillDate: "",
      dayCount: "",
      permission: "",
      stationAddress: "",
      remarkByUser: "",
    });

    setSelectedReason('');
    setDayGap(0);
    setFile(null)
    // Reset day gap or other related states
  };



  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        {newLeaveBalance && (
          <Card
            className="bg-secondary mb-3"
            style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
          >
            <CardHeader className="bg-white border-1">
              <Row>
                <Col>
                  <h1>
                    Leave Balance{" "}
                    {leaveBalance[0]?.isUpdated === 0 && (
                      <span style={{ color: "red", fontSize: "15px" }}>
                        *( Your Leave Balance is not updated plz contact to your
                        Higher Authority, You Can not apply Now )
                      </span>
                    )}
                    {/* {pendingJoining.length >= 1 && (
                      <span style={{ color: "red", fontSize: "15px" }}>
                        *
                        ( Your previous application is pending for Joining. You
                        can apply after its completion. )
                      </span>
                    )}*/}
                    {filteredLeave.length >= 3 && (
                      <span style={{ color: "red", fontSize: "15px" }}>
                        *( Your previous application is pending for Approval. You
                        can apply after its completion. )
                      </span>
                    )}
                  </h1>
                </Col>
              </Row>
            </CardHeader>
            <CardBody className="bg-white d-flex">
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#28a745",
                      color: "white",
                      width: "100%",
                    }}
                  >
                    CL - {newLeaveBalance[0]?.CL}
                  </Badge>
                </h1>
              </Col>
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#007bff",
                      color: "white",
                      width: "100%",
                    }}
                  >
                    OL - {newLeaveBalance[0]?.RH}
                  </Badge>
                </h1>
              </Col>
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#ffc107",
                      color: "black",
                      width: "100%",
                    }}
                  >
                    EL - {newLeaveBalance[0]?.EL}
                  </Badge>
                </h1>
              </Col>
              <Col>
                <h1>
                  <Badge
                    style={{
                      backgroundColor: "#17a2b8",
                      color: "white",
                      width: "100%",
                    }}
                  >
                    HPL - {newLeaveBalance[0]?.HPL}
                  </Badge>
                </h1>
              </Col>
              {isTribal && isTribal === true && <>
                <Col>
                  <h1>
                    <Badge
                      style={{
                        backgroundColor: "#0d5348",
                        color: "white",
                        width: "100%",
                      }}
                    >
                      TCL - {newLeaveBalance[0]?.TCL}
                    </Badge>
                  </h1>
                </Col>
                <Col>
                  <h1>
                    <Badge
                      style={{
                        backgroundColor: "#623b0b",
                        color: "white",
                        width: "100%",
                      }}
                    >
                      TEL - {newLeaveBalance[0]?.TEL}
                    </Badge>
                  </h1>
                </Col>  </>}
            </CardBody>
          </Card>
        )}
        {leaveBalance[0]?.isUpdated === 1 && !(filteredLeave.length >= 3) &&
          (
            <Row>
              <Col>
                <Card
                  className="bg-secondary"
                  style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
                >
                  <CardHeader className="bg-white border-0">
                    <Row className="align-items-center">
                      <Col xs="8">
                        <h3 className="mb-0">APPLY LEAVE / आवेदन करे  </h3>
                      </Col>
                      <Col className="text-right" xs="4">
                        {/* <Button color="primary" size="sm">
                          Settings
                        </Button> */}
                      </Col>
                    </Row>
                  </CardHeader>
                  <CardBody>
                    <Form id="LeaveForm" onSubmit={handleSubmit}>
                      <Row>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Application Type(आवेदन के प्रकार)
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              className="text-"
                              type="select"
                              name="applicationType"
                              value={formData.applicationType}
                              onChange={handleChange}
                            >
                              <option value="">--चयन करें--</option>
                              <option value="1">
                                एकल अवकाश (Single Leave)
                              </option>
                              <option value="2">
                                एकाधिक अवकाश (Multiple Leave)
                              </option>
                            </Input>
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Reason(कारण)<span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="select"
                              name="reason"
                              value={selectedReason}
                              onChange={handleReasonChange}
                            >
                              <option value="">--चयन करें--</option>
                              <option value="व्यक्तिगत कारण">
                                व्यक्तिगत कारण (Personal Reason)
                              </option>
                              <option value="स्वास्थ्य कारण">
                                स्वास्थ्य कारण (Health Reason)
                              </option>
                              <option value="परिवारिक कार्यक्रम">
                                परिवारिक कार्यक्रम (Family Event)
                              </option>
                              <option value="शादी में सम्मिलित होना">
                                शादी में सम्मिलित होना (Attending a Wedding)
                              </option>
                              <option value="स्वास्थ्य परीक्षण">
                                स्वास्थ्य परीक्षण (Medical Checkup)
                              </option>
                              <option value="अत्यावश्यक कार्य">
                                अत्यावश्यक कार्य (Emergency Work)
                              </option>
                              <option value="बच्चों की देखभाल">
                                बच्चों की देखभाल (Childcare)
                              </option>
                              <option value="शोकसभा में सम्मिलित होना">
                                शोकसभा में सम्मिलित होना (Attending a Funeral)
                              </option>
                              <option value="यात्रा">यात्रा (Travel)</option>
                              <option value="सामाजिक कार्य">
                                सामाजिक कार्य (Social Work)
                              </option>
                              <option value="शैक्षणिक कारण">
                                शैक्षणिक कारण (Educational Reason)
                              </option>
                              <option value="प्राकृतिक आपदा">
                                प्राकृतिक आपदा (Natural Calamity)
                              </option>
                              <option value="अस्पताल में भर्ती">
                                अस्पताल में भर्ती (Hospitalization)
                              </option>
                              <option value="विश्राम की आवश्यकता">
                                विश्राम की आवश्यकता (Need for Rest)
                              </option>
                              <option value="माता-पिता की देखभाल">
                                माता-पिता की देखभाल (Parental Care)
                              </option>
                              <option value="निजी कार्य">
                                निजी कार्य (Personal Work)
                              </option>
                              <option value="कार्यालय के बाहर की बैठक">
                                कार्यालय के बाहर की बैठक (Meeting Outside the
                                Office)
                              </option>
                              <option value="मानसिक स्वास्थ्य">
                                मानसिक स्वास्थ्य (Mental Health)
                              </option>
                              <option value="धार्मिक कारण">
                                धार्मिक कारण (Religious Reason)
                              </option>
                              <option value="बैंक या अन्य प्रशासनिक कार्य">
                                बैंक या अन्य प्रशासनिक कार्य (Bank or Other
                                Administrative Work)
                              </option>
                              <option value="Other">अन्य (Other)</option>
                            </Input>
                            {showManualInput && (
                              <Input
                                type="text"
                                className="formInput mt-2"
                                placeholder="कारण दर्ज करें"
                                value={manualReason}
                                onChange={handleManualReasonChange}
                              />
                            )}
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Leave Type (अवकाश का प्रकार){" "}
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="select"
                              name="leaveType"
                              value={formData.leaveType}
                              onChange={handleLeaveTypeChange}
                            >
                              <option value="">--चयन करें--</option>
                              <option value="1">
                                आकास्मिक अवकाश (Casual Leave)
                              </option>
                              <option value="2">
                                अर्जित अवकाश (Earned Leave)
                              </option>
                              <option value="3">
                                ऐच्छिक अवकाश (Optional Holiday)
                              </option>
                              <option value="4">
                                चिकित्सा अवकाश (Medical Leave)
                              </option>
                              <option value="5">
                                अर्धवैतनिक अवकाश (Half Pay Leave)
                              </option>
                              <option value="6">
                                प्रसूति अवकाश (Maternity Leave)
                              </option>
                              <option value="7">
                                दत्तक ग्रहण अवकाश (Adoption Leave)
                              </option>
                              <option value="8">
                                पितृत्व अवकाश (Paternity Leave)
                              </option>
                              <option value="9">
                                संतान पालन अवकाश (Child Care Leave)
                              </option>
                              <option value="10">
                                अदेय अवकाश (No Pay Leave)
                              </option>
                              <option value="11">
                                असाधारण अवकाश (Extraordinary Leave)
                              </option>
                              {isTribal && isTribal === true && <>
                                <option value="12">
                                  अनुसूचित आकस्मिक अवकाश (Tribal Casual Leave)
                                </option>
                                <option value="13">
                                  अनुसूचित अर्जित अवकाश (Tribal Earned Leave)
                                </option> </>}
                            </Input>
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Maximum Leave (अधिकतम अवकाश)
                            </Label>
                            <Input
                              type="text"
                              name="maxLeave"
                              value={maxLeave}
                              onChange={handleChange}
                              placeholder="अधिकतम अवकाश"
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              From Date (दिनांक से )
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="date"
                              name="fromDate"
                              value={formData.fromDate}
                              onChange={handleDateChange} // Use the new handler
                            />
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Till Date (दिनांक तक ){" "}
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="date"
                              name="tillDate"
                              value={formData.tillDate}
                              onChange={handleDateChange} // Use the new handler
                            />
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              दिनों की संख्या (Day Count){" "}
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="text"
                              name="dayCount"
                              value={formData.dayCount}
                              // onChange={handleBalanceChange}
                              readOnly // Make this input read-only
                              placeholder="दिनों की संख्या"
                            />
                          </FormGroup>
                        </Col>
                        <Col lg="3">
                          <FormGroup>
                            <Label className="Label">
                              Permission for Station Leave / मुख्यालय छोड़ने की
                              अनुमति?
                            </Label>
                            <div
                              className="row"
                              style={{ justifyContent: "space-evenly" }}
                            >
                              <FormGroup check>
                                <Input
                                  type="radio"
                                  name="permission"
                                  value="1"
                                  checked={formData.permission === "1"}
                                  onChange={handleChange}
                                />
                                <Label check>हाँ</Label>
                              </FormGroup>
                              <FormGroup check>
                                <Input
                                  type="radio"
                                  name="permission"
                                  value="0"
                                  checked={formData.permission === "0"}
                                  onChange={handleChange}
                                />
                                <Label check>नहीं</Label>
                              </FormGroup>
                            </div>
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col lg="6">
                          <FormGroup>
                            <Label className="Label">
                              Address During Leave (अवकाश के दौरान पता )
                              <span style={mandatory}>*</span>
                            </Label>
                            <Input
                              type="textarea"
                              name="stationAddress"
                              value={formData.stationAddress}
                              onChange={handleChange}
                              placeholder="अवकाश का पता"
                            />
                          </FormGroup>
                        </Col>
                        <Col lg="6">
                          <FormGroup>
                            <Label className="Label">Remark (रिमार्क )</Label>
                            <span style={mandatory}>*</span>
                            <Input
                              type="textarea"
                              name="remarkByUser"
                              placeholder="Remark"
                              value={formData.remarkByUser}
                              onChange={(e) =>
                                setFormData((prevData) => ({
                                  ...prevData,
                                  remarkByUser: e.target.value,
                                }))
                              }
                            />
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col lg="6">
                          <FormGroup>
                            <Label className="Label" > <b style={{ color: message.color }}>{message.message}</b><br />
                              Upload Document (आवेदन की प्रति अपलोड करें){" "}
                              <span style={mandatory}>
                                (.Pdf फाइल 100 kb तक)
                              </span>
                            </Label>
                            {/* <Input
                              type="file"
                              name="uploadFile"
                              onChange={handleFileChange}
                              placeholder="Upload leave copy"
                            /> */}
                            <input type="file" style={{ fontWeight: "bolder" }} onChange={handleFileChange} name="uploadFile" className="form-control" id="inputGroupFile01" accept=".pdf"></input>
                          </FormGroup>

                        </Col>

                      </Row>
                      <Row>
                        <Col>
                          <Button onClick={togglePreviewModal} className="btn-sm" color="primary">
                            Preview & Submit
                          </Button>
                        </Col>
                        <Col className="d-flex justify-content-end">
                          <Button onClick={handleReset} className="btn-sm" color="danger">
                            Reset
                          </Button>
                        </Col>
                      </Row>
                    </Form>
                  </CardBody>
                </Card>
              </Col>
            </Row>
          )}

        {leaveBalance[0]?.isUpdated === 1 && (
          <Row className="mt-5">
            <Col>
              <Card
                className="p-1"
                style={{ boxShadow: "0 4px 8px rgba(0, 0, 0, 0.9)" }}
              >
                <CardHeader className="border-3">
                  <h3 className="mb-0">Edit Leave / सुधार करें</h3>
                </CardHeader>
                <Modal isOpen={modal} toggle={toggle}>
                  <ModalHeader toggle={toggle}>Status</ModalHeader>
                  <ModalBody>
                    <div className="main">
                      <ul
                        style={{
                          display: "flex",
                          justifyContent: "space-around",
                        }}
                      >
                        {steps.map((step, index) => (
                          <li
                            key={index}
                            style={{
                              listStyle: "none",
                              display: "flex",
                              flexDirection: "column",
                              alignItems: "center",
                            }}
                          >
                            <i
                              className={`icons awesome fa-solid ${step.icon}`}
                              style={{ fontSize: "25px", color: "#1b761b" }}
                            ></i>
                            <div
                              className={`step ${index === currentStep ? "active" : ""
                                }`}
                              style={{
                                height: "30px",
                                width: "30px",
                                borderRadius: "50%",
                                backgroundColor:
                                  index <= currentStep ? "#1b761b" : "#d7d7c3",
                                margin: "16px 0 10px",
                                display: "grid",
                                placeItems: "center",
                                color: "ghostwhite",
                                position: "relative",
                                cursor: "default", // Change cursor to default since clicking is disabled
                              }}
                            >
                              <p
                                style={{
                                  fontSize: "18px",
                                  display:
                                    index <= currentStep ? "none" : "block",
                                }}
                              >
                                {index + 1}
                              </p>
                              <i
                                className="awesome fa-solid fa-check"
                                style={{
                                  display:
                                    index <= currentStep ? "flex" : "none",
                                }}
                              ></i>
                            </div>
                            <p
                              className="label"
                              style={{
                                fontFamily: "sans-serif",
                                letterSpacing: "1px",
                                fontSize: "14px",
                                fontWeight: "bold",
                                color: "#1b761b",
                              }}
                            >
                              {step.label}
                            </p>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <Button color="secondary" onClick={toggle}>
                      Close
                    </Button>
                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                  </ModalFooter>
                </Modal>
                <Table className="align-items-center table-flush" responsive>
                  <thead className="thead-light">
                    <tr>
                      <th>Sno. <br /> / क्र.</th>
                      <th>
                        <strong>Edit / Cancel / View / Download </strong>
                      </th>

                      <th>
                        Status / <br /> स्थिति
                      </th>
                      <th>
                        Movement <br /> (गतिविधि)
                      </th>
                      <th >

                        Application ID  <br />(आवेदन  क्रमांक / दिनांक)
                      </th>
                      <th>
                        Leave  Type  (अवकाश का प्रकार)  <br /> / Reason  (कारण)
                      </th>
                      <th >
                        Date Between <br /> (दिनांक से - तक)
                      </th>

                    </tr>
                  </thead>
                  <tbody>
                    {filteredLeave.map((item, index) => (
                      <tr key={item.id}>
                        <td><strong>{index + 1}</strong></td>
                        <td>

                          <Link to={`/admin/update-leave-apply/${item._id}`}>
                            <Button
                              className="btn-sm mr-2"
                              style={{
                                backgroundColor: "#ffb800",
                                color: "white",
                              }}
                              disabled={
                                item.isPrincipalAction === 1 ||
                                item.isDirectorAction === 1
                              }
                            >
                              Edit
                            </Button>
                          </Link>
                          <Button
                            className="btn-sm mr-2"
                            style={{
                              backgroundColor: "#ff4e67",
                              color: "white",
                            }}
                            onClick={() => handleCancel(item._id)}
                          >
                            Cancel
                          </Button>
                          <Link to={`/admin/Preview/${item._id}`}>
                            <Button className="btn-sm m-2 " style={{ backgroundColor: "yellow" }}>
                              <BsDownload size={18} />
                              <BsEye size={18} />
                            </Button>
                          </Link>
                          <a href={`https://heonline.cg.nic.in/${item.uploadFile}`} download>
                            <Button className="btn mr-2 m-2 btn-sm btn-primary">

                              {item.uploadFile ? <BsDownload size={18} /> : "No File"}
                            </Button>
                          </a>
                        </td>
                        <td>
                          <td>
                            <h2>
                              <Badge
                                className="badge-sm"
                                onClick={
                                  item.leaveStatus === 1 || item.leaveStatus === 2
                                    ? () => toggle(item)
                                    : null
                                }
                                style={{
                                  fontWeight: "bolder",
                                  color: "white",
                                  backgroundColor:
                                    item.leaveStatus === 3
                                      ? "green" // Approved background color
                                      : item.leaveStatus === 4
                                        ? "red" // Rejected background color
                                        : item.leaveStatus === 1
                                          ? "#f3c70c" // Pending background color
                                          : item.leaveStatus === 5
                                            ? "blue"
                                            : item.leaveStatus === 2
                                              ? "#ff8a00"
                                              : item.leaveStatus === 6
                                                ? "#a5945a"
                                                : "lavender",
                                }}
                              >
                                {item.leaveStatus === 3 ? (
                                  "Approved"
                                ) : item.leaveStatus === 4 ? (
                                  "Rejected"
                                ) : item.leaveStatus === 6 ? (
                                  "Applied for Cancel"
                                ) : item.leaveStatus === 1 ? (
                                  <>
                                    <Spinner
                                      size="sm"
                                      color="white"
                                      style={{ marginRight: "8px" }}
                                    />
                                    Pending
                                  </>
                                ) : item.leaveStatus === 2 ? (
                                  item.designation === "UG Principal" || item.designation === "PG Principal" ? (
                                    "Pending"
                                  ) : (
                                    "Forwarded"
                                  )
                                ) : item.leaveStatus === 5 ? (
                                  "Cancelled"
                                ) : (
                                  "Error"
                                )}
                              </Badge>
                            </h2>
                          </td>
                        </td>
                        <td><Button
                          className="btn-sm mr-2"
                          color="success"
                          onClick={() => toggle(item.leaveStatus)}
                        >
                          Movement
                        </Button></td>
                        <td><span style={{ fontWeight: "bolder" }}>{item.applicationId} </span> <br />({formatDate(item.appliedDate)})</td>
                        <td><span style={{ fontWeight: "bolder" }}>{item.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : item.leaveType} </span><br /> ({item.reason})</td>
                        <td><strong>({formatDate(item.fromDate)}) to  ({formatDate(item.tillDate)}) <br /><span style={{ fontWeight: "bolder", color: "red" }}>{item.dayCount} Days</span></strong></td>
                      </tr>
                    ))}
                  </tbody>
                </Table>
                <CardFooter className="py-4"></CardFooter>
              </Card>
              <Modal
                isOpen={previewModal}
                toggle={togglePreviewModal}
                style={{
                  maxWidth: "1000px", // Adjust modal width here
                  width: "90%", // Set responsive width
                }}
              >
                <ModalHeader toggle={togglePreviewModal}>
                  <h2>Preview</h2>
                  <br />
                  <h4 className="text-danger">
                    Note - Check Carefully before Submit
                  </h4>
                </ModalHeader>
                <ModalBody>
                  <Col>
                    <Row>
                      <Col xs={3}>
                        <p>
                          <strong>Application Type:</strong><br /><input type="text" value={formData.applicationType === "1"
                            ? "एकल अवकाश (Single Leave)"
                            : formData.applicationType === "2"
                              ? "एकाधिक अवकाश (Multiple Leave)" : "N/A"
                          } disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Reason:</strong><br /> <input type="text" value={formData.reason} disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Leave Type:</strong><br /> <input type="text" value={formData.leaveType === "1"
                            ? "आकास्मिक अवकाश (Casual Leave)"
                            : formData.leaveType === "2"
                              ? "अर्जित अवकाश (Earned Leave)"
                              : formData.leaveType === "3"
                                ? "ऐच्छिक अवकाश (Optional Holiday)"
                                : formData.leaveType === "4"
                                  ? "चिकित्सा अवकाश (Medical Leave)"
                                  : formData.leaveType === "5"
                                    ? "अर्धवैतनिक अवकाश (Half Pay Leave)"
                                    : formData.leaveType === "6"
                                      ? "प्रसूति अवकाश (Maternity Leave)"
                                      : formData.leaveType === "7"
                                        ? "दत्तक ग्रहण अवकाश (Adoption Leave)"
                                        : formData.leaveType === "8"
                                          ? "पितृत्व अवकाश (Paternity Leave)"
                                          : formData.leaveType === "9"
                                            ? "संतान पालन अवकाश (Child Care Leave)"
                                            : formData.leaveType === "10"
                                              ? "अदेय अवकाश (No Pay Leave)"
                                              : formData.leaveType === "11"
                                                ? "असाधारण अवकाश (Extraordinary Leave)"

                                                : isTribal && isTribal === true && formData.leaveType === "12"
                                                  ? "अनुसूचित आकस्मिक अवकाश (Tribal Casual Leave)"
                                                  : isTribal && isTribal === true && formData.leaveType === "13"
                                                    ? "अनुसूचित अर्जित अवकाश (Tribal Earned Leave)"
                                                    : "N/A"
                          } disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Maximum Leave:</strong><br /> <input type="text" value={maxLeave} disabled />
                        </p>
                      </Col>
                    </Row>
                    <Row>

                      <Col xs={3}>
                        <p>
                          <strong>From Date:</strong><br /> <input type="text" value={formatDate(formData.fromDate)} disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Till Date:</strong><br /> <input type="text" value={formatDate(formData.tillDate)} disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Day Count:</strong><br /> <input type="text" value={formData.dayCount} disabled />
                        </p>
                      </Col>
                      <Col xs={3}>
                        <p>
                          <strong>Permission For Station Leave:</strong><br /> <input type="text" value={formData.permission === "1" ? "हाँ (Yes)" : "नहीं (No)"
                          } disabled />
                        </p>
                      </Col>
                    </Row>
                    <Row>

                      <Col xs={6}>
                        <p>
                          <strong>Address During Leave:</strong><br /> <input type="text" style={{ width: "100%" }} value={formData.stationAddress} disabled />
                        </p>
                      </Col>
                      <Col xs={6}>
                        <p>
                          <strong>Remark:</strong><br /> <input type="text" style={{ width: "100%" }} value={formData.remarkByUser} disabled />
                        </p>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <p>
                          <strong>Uploded Document:</strong><br /> <input type="text" value={file ? file.name : "No file uploaded"} disabled />
                        </p>
                      </Col>
                    </Row>
                  </Col>
                </ModalBody>
                <ModalFooter>
                  <Button color="secondary" onClick={togglePreviewModal}>
                    Cancel
                  </Button>
                  <Button color="primary" disabled={isDisabled} onClick={handleSubmit}>
                    {isDisabled ? <>Processing <Spinner
                      size="sm"
                      color="white"
                      style={{ marginRight: "-8px" }}
                    /></> : "Confirm Submit"}
                  </Button>
                </ModalFooter>
              </Modal>
            </Col>
          </Row>
        )}
      </Container>
    </>
  );
};

export default ApplyLeave;
