import { useState, useEffect } from "react";
import axios from "axios";
import { Form, FormGroup, Label, Input, Button, Container, Row, Col, CardBody, Card, CardHeader } from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import  Swal  from 'sweetalert2';
import { useParams,  useNavigate } from "react-router-dom";

const LeaveRuleUpdate = () => {
    const { id } = useParams(); // Get the ID from the URL

    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const navigate = useNavigate();

    // State to manage form data
    const [formData, setFormData] = useState({
        empClass: "",
        type: "",
        maxCL: 0,
        maxEL: 0,
        maxRH: 0,
        maxMedicalLeave: 0,
        maxHPL: 0,
        maxMaternityLeave: 0,
        maxAdoptionLeave: 0,
        maxPaternityLeave: 0,
        maxCCL: 0,
        maxNPL: 0,
        maxEOL: 0,
        pAuthMaxCL: 0,
        pAuthMaxEL: 0,
        pAuthMaxRH: 0,
        pAuthMaxMedicalLeave: 0,
        pAuthMaxHPL: 0,
        pAuthNPL: 0,
        pAuthCCL: 0,
        pAuthECL: 0,
        isVerified: 0,
        yealyCL: 0,
        yealyEL: 0,
        yealyRH: 0,
        yealyHPL: 0,
        
    });

    const [classData, setClass] = useState([]);
    useEffect(() => {
        const fetchClass = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/class/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    setClass(response.data);
                } else {
                    alert("Class Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchClass();
    }, [token]);


    useEffect(() => {
        const fetchLeaveRule = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/leave/single-rule/${id}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
                if (response.status === 200) {
                    setFormData(response.data);
                } else {
                    alert("Data Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the leave rule:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchLeaveRule();
    }, [id, token, endPoint]);

    // Handler to update formData based on input changes
    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({
            ...formData,
            [name]: value,
        });
    };

    // Handler to submit the form data
    const handleSubmit = async (e) => {
        e.target.disabled = true;
        setTimeout(() => {
            e.target.disabled = false;
          }, 5000);
          
        e.preventDefault();

        const body = {
            empClass: String(formData.empClass),
            type: String(formData.type),
            maxCL: String(formData.maxCL),
            maxEL: String(formData.maxEL),
            maxRH: String(formData.maxRH),
            maxMedicalLeave: String(formData.maxMedicalLeave),
            maxHPL: String(formData.maxHPL),
            maxMaternityLeave: String(formData.maxMaternityLeave),
            maxAdoptionLeave: String(formData.maxAdoptionLeave),
            maxPaternityLeave: String(formData.maxPaternityLeave),
            maxCCL: String(formData.maxCCL),
            maxNPL: String(formData.maxNPL),
            maxEOL: String(formData.maxEOL),
            pAuthMaxCL: String(formData.pAuthMaxCL),
            pAuthMaxEL: String(formData.pAuthMaxEL),
            pAuthMaxRH: String(formData.pAuthMaxRH),
            pAuthMaxMedicalLeave: String(formData.pAuthMaxMedicalLeave),
            pAuthMaxHPL: String(formData.pAuthMaxHPL),
            pAuthNPL: String(formData.pAuthNPL),
            pAuthCCL: String(formData.pAuthCCL),
            pAuthECL: String(formData.pAuthECL),
            yealyCL: String(formData.yealyCL),
            yealyEL: String(formData.yealyEL),
            yealyRH: String(formData.yealyRH),
            yealyHPL:String(formData.yealyHPL),
            isVerfied:String(1),
        };


        Swal.fire({
            title: "Verify Data",
            text: "Please verify the data before submitting.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Verify",
            cancelButtonText: "Cancel",
          }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    const response = await axios.put(`${endPoint}/api/leave/update-rule/${id}`, body, {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                    if (response.status === 200) {
                        // alert("Leave rule updated successfully.");
                        SwalMessageAlert("Leave rule updated successfully.","success")
                        
                        navigate("/admin/leaveRules")
                    } else {
                        SwalMessageAlert("Failed to update leave rule.","error");
                    }
                } catch (error) {
                    if (error.response) {
                        
                        const backendMessage = error.response.data.error ;
                            SwalMessageAlert(`${backendMessage}`, "error"); // For other types of errors
                        }
                  
                }
            } else {
              // console.log("Submission canceled.");
            }
          });

        
        
    };

    return (
        <>
            <Header />

            <Container className="mt--7" fluid>
                <Row>
                    <Col>
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">LEAVE RULE UPDATE</h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Form >
                                    <hr />
                                    <h2 className="text-primary mt--4 mb--4">
                                        Select Category <span className="text-danger">*</span>
                                    </h2>
                                    <hr />
                                    {/* Row 1 */}
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="empClass">Employee Class</Label>
                                                <Input type="select" name="empClass" id="empClass" value={formData.empClass} onChange={handleChange} required>
                                                    <option value="">Select Class</option>
                                                    {classData &&
                                                        classData.length > 0 &&
                                                        classData.map((type, index) => (
                                                            <option key={index} value={type._id}>
                                                                {type.className}
                                                            </option>
                                                        ))}
                                                </Input>
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="type">Work Type</Label>
                                                <Input
                                                    type="select"
                                                    name="type"
                                                    id="type"
                                                    value={formData.type}
                                                    onChange={handleChange}
                                                    required
                                                >
                                                    <option value="">Select Type</option>
                                                    <option value="TEACHING">Teaching</option>
                                                    <option value="NON TEACHING">Non-Teaching</option>
                                                </Input>
                                            </FormGroup>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <h2 className="text-primary mt--4 mb--4">Max In All Time</h2>
                                    <hr />
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxCL">Max Casual Leave (CL)</Label>
                                                <Input type="text" name="maxCL" value={formData.maxCL} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxEL">Max Earned Leave (EL)</Label>
                                                <Input type="text" name="maxEL" value={formData.maxEL} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxRH">Max Optional Holiday (OL)</Label>
                                                <Input type="text" name="maxRH" value={formData.maxRH} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                    </Row>

                                    {/* Row 2 */}
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxMedicalLeave">Max Medical Leave</Label>
                                                <Input type="text" name="maxMedicalLeave" value={formData.maxMedicalLeave} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxHPL">Max Half-Pay Leave (HPL)</Label>
                                                <Input type="text" name="maxHPL" value={formData.maxHPL} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxMaternityLeave">Max Maternity Leave</Label>
                                                <Input type="text" name="maxMaternityLeave" value={formData.maxMaternityLeave} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxAdoptionLeave">Max Adoption Leave</Label>
                                                <Input type="text" name="maxAdoptionLeave" value={formData.maxAdoptionLeave} onChange={handleChange} required />
                                            </FormGroup>
                                        </Col>
                                    </Row>

                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxPaternityLeave">Max Paternity Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="maxPaternityLeave"
                                                    value={formData.maxPaternityLeave}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxCCL">Max Child Care Leave (CCL)</Label>
                                                <Input
                                                    type="text"
                                                    name="maxCCL"
                                                    value={formData.maxCCL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxNPL">Max No-Pay Leave (NPL)</Label>
                                                <Input
                                                    type="text"
                                                    name="maxNPL"
                                                    value={formData.maxNPL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="maxEOL">Max Extraordinary Leave (EOL)</Label>
                                                <Input
                                                    type="text"
                                                    name="maxEOL"
                                                    value={formData.maxEOL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <h2 className="text-primary mt--4 mb--4">
                                        Principal Authorized
                                    </h2>
                                    <hr />

                                    {/* Row 4 */}
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthMaxCL">Principal Authorized Max CL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthMaxCL"
                                                    value={formData.pAuthMaxCL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthMaxEL">Principal Authorized Max EL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthMaxEL"
                                                    value={formData.pAuthMaxEL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthMaxRH">Principal Authorized Max OL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthMaxRH"
                                                    value={formData.pAuthMaxRH}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthMaxMedicalLeave">Principal Authorized Max Medical Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthMaxMedicalLeave"
                                                    value={formData.pAuthMaxMedicalLeave}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                    </Row>

                                    {/* Row 5 */}
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthMaxHPL">Principal Authorized Max HPL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthMaxHPL"
                                                    value={formData.pAuthMaxHPL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthNPL">Principal Authorized NPL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthNPL"
                                                    value={formData.pAuthNPL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthCCL">Principal Authorized CCL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthCCL"
                                                    value={formData.pAuthCCL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="pAuthECL">Principal Authorized EOL</Label>
                                                <Input
                                                    type="text"
                                                    name="pAuthECL"
                                                    value={formData.pAuthECL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                    </Row>

                                    <hr />
                                    <h2 className="text-primary mt--4 mb--4">
                                        Senctioned yearly
                                    </h2>
                                    <hr />
                                    <Row>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="yealyCL">Yearly Casual Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="yealyCL"
                                                    value={formData.yealyCL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="yealyEL">Yearly Earned Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="yealyEL"
                                                    value={formData.yealyEL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="yealyRH">Yearly Optional Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="yealyRH"
                                                    value={formData.yealyRH}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                        <Col md={3}>
                                            <FormGroup>
                                                <Label for="yealyHPL">Yearly Half Pay Leave</Label>
                                                <Input
                                                    type="text"
                                                    name="yealyHPL"
                                                    value={formData.yealyHPL}
                                                    onChange={handleChange}
                                                    required
                                                />
                                            </FormGroup>
                                        </Col>
                                    </Row>


                                    <Button onClick={handleSubmit} color="primary">
                                        Update
                                    </Button>
                                </Form>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default LeaveRuleUpdate;