import loadable from "@loadable/component";
export const Dashboard = loadable(() => import("../views/Admin/Dashboard.jsx"));
export const HolidayDetails = loadable(() =>
  import("../views/Admin/HolidayDetails.jsx")
);
export const InstituteEntry = loadable(() =>
  import("../views/Admin/InstituteEntry.jsx")
);
export const SubjectEntry = loadable(() =>
  import("../views/Admin/SubjectEntry.jsx")
);
export const SubjectEntryForSeat = loadable(() =>
  import("../views/Admin/SubjectEntryForSeat.jsx")
);
export const University = loadable(() =>
  import("../views/Admin/addUniversity.jsx")
);
export const Director = loadable(() => import("../views/Admin/director.jsx"));
export const LeaveDashboard = loadable(() =>
  import("../views/leave/LeaveDashboard.jsx")
);
export const ApplyLeave = loadable(() =>
  import("../views/leave/applyLeave.jsx")
);
export const LeaveList = loadable(() => import("../views/leave/LeaveList.jsx"));
export const JoiningList = loadable(() => import("../views/leave/joining.jsx"));
export const LeaveActionByPrincipal = loadable(() =>
  import("../views/leave/LeaveActionByPrincipal.jsx")
);
export const ActionsReportAtPrincipal = loadable(() =>
  import("../views/leave/ActionsReportAtPrincipal.jsx")
);
export const UniversityList = loadable(() =>
  import("../views/Admin/universityList.jsx")
);
export const InstituteList = loadable(() =>
  import("../views/Admin/instituteList.jsx")
);
export const EmployeeAdd = loadable(() =>
  import("../views/Admin/addEmployee.jsx")
);
// export const EmployeeListOld = loadable(() =>
//   import("../views/Admin/employeeListOld.jsx")
// );
export const EmployeeList = loadable(() =>
  import("../views/Admin/employeeList.jsx")
);
export const LeaveActionAtDirector = loadable(() =>
  import("../views/leave/LeaveActionByDirector.jsx")
);
export const ActionsReportAtDirector = loadable(() =>
  import("../views/leave/ActionReportAtDirecotorate.jsx")
);
export const LeaveBalanceList = loadable(() =>
  import("../views/leave/LeaveBalanceList.jsx")
);
export const LeaveBalanceListPrincipal = loadable(() =>
  import("../views/leave/LeaveListofPrincipals.jsx")
);
export const AddDesignation = loadable(() =>
  import("../views/Admin/addDesignation.jsx")
);
export const DesignationList = loadable(() =>
  import("../views/Admin/designationList.jsx")
);
export const Division = loadable(() =>
  import("../views/Admin/addDivision.jsx")
);
export const District = loadable(() =>
  import("../views/Admin/addDistrict.jsx")
);
export const AddVidhansabha = loadable(() =>
  import("../views/Admin/addVidhansabha.jsx")
);
export const LeaveRuleRegistration = loadable(() =>
  import("../views/leave/LeaveRuleRegistration.jsx")
);
export const EmployeeCode = loadable(() =>
  import("../views/Admin/employeeCode.jsx")
);
export const DeletedEmployeeList = loadable(() =>
  import("../views/Admin/deletedEmployees")
);
export const IPRForm = loadable(() => import("../views/leave/IPRForm.jsx"));
export const IPRList = loadable(() => import("../views/leave/IPRList.jsx"));
export const IPRDateForm = loadable(() =>
  import("../views/Admin/iprDateForm.jsx")
);
export const AttendanceList = loadable(() =>
  import("../views/Attendance/attendanceList.jsx")
);
export const AttendanceTodayList = loadable(() =>
  import("../views/Attendance/todayAttendanceList.jsx")
);
export const GenerateAttendanceReport = loadable(() =>
  import("../views/Attendance/generateAttendanceReport.jsx")
);
export const GenerateEmmployeeReport = loadable(() =>
  import("../views/Report/generateEmployeeReport.jsx")
);
export const IprListAtSection = loadable(() =>
  import("../views/leave/IprListAtSection.jsx")
);
export const ErrorLogs = loadable(() =>
  import("../views/ErrorLogs/ErrorLogUploader.jsx")
);
export const DirectorEmpList = loadable(() =>
  import("../views/Admin/DirectorEmpList.jsx")
);
export const ACRMapping = loadable(() => import("../views/ACR/acrMapping.jsx"));
export const ACRForm = loadable(() => import("../views/ACR/acrForm.jsx"));
export const ACRDateForm = loadable(() => import("../views/ACR/acrDateForm.jsx"));
export const IPRACR = loadable(() => import("../views/Admin/iprAcr.jsx"));
export const ACRFormList = loadable(() => import("../views/ACR/acrFormList.jsx"));
export const DirectorateACRMapping = loadable(() => import("../views/ACR/directorateEmployeeACR/directorateEmpACRMapping.jsx"));
export const ACRFormDirectorList = loadable(() => import("../views/ACR/acrFormDirectorList.jsx"));
export const SeatExtensionList = loadable(()=> import("../views/Admin/seatExtensionList.jsx"))
export const PrincipalList = loadable(() => import("../views/Admin/principalList.jsx"));

export const JanBhagiDariCourse = loadable(() => import("../views/NavinCourese/NavinCourse.jsx"));

export const GenerateEmployeeReportCount = loadable(() => import("../views/Report/generateEmployeeReportCollegeWiseCount.jsx"));
export const NewCourseCollegeReport = loadable(() => import("../views/NavinCourese/navinCourseCollegeReport.jsx"));
export const NewCourseAllReport = loadable(() => import("../views/NavinCourese/NewCourseAllReport.jsx"));
export const NewCourseAllReportGovt = loadable(() => import("../views/NavinCourese/NewCourseAllReportGovt.jsx"));
export const GuestFacultyForm = loadable(() => import("../views/Admin/guestLecture.jsx"));
export const GuestFacultyList = loadable(() => import("../views/Admin/guestFacultyList.jsx"));
export const AllGuestFacultyList = loadable(() => import("../views/Admin/allGuestFacultyList.jsx"));
export const EmployeeTransfer = loadable(()=> import("../views/Admin/employeeTransfer.jsx"));
export const TransferedEmpList = loadable(()=> import("../views/Admin/transferedEmpList.jsx"));
export const IncomingTransfer = loadable(()=> import("../views/Admin/incomingTransfer.jsx"));
export const OutgoingTransfer = loadable(()=> import("../views/Admin/outgoingTransfer.jsx"));
export const TransferedEmpListByCollege = loadable(()=> import("../views/Admin/TransferedEmpListByCollege.jsx"));
export const PrincipalChargeList = loadable(() => import("../views/Admin/PrincipalChargeList.jsx"));
export const CollegeChargeList = loadable(() => import("../views/Admin/CollegeChargeList.jsx"));
export const AllGuestFacultyReportCount = loadable(()=> import("../views/Admin/allGuestFacultyReportCount.jsx"));
export const AddCourse = loadable(() => import("../views/Admin/addCourse.jsx"));
export const TrashGuestFaculty =loadable(() => import("../views/Admin/trashGuestFaculty.jsx"));
export const PHD_Form_set =loadable(() => import("../views/Admin/PHD_Form_set.jsx"));

export const NewPhdRequestApplication =loadable(() => import("../views/PhdRequestForm/newPhdRequestApplication.jsx")); //Rx
export const PhdRequestListReport =loadable(() => import("../views/PhdRequestForm/reportPhdRequestListApplications.jsx")); //Rx


// Components/DynamicBreadcrumb 
import { Link, useLocation } from "react-router-dom";

const DynamicBreadcrumb = () => {
  const location = useLocation();
  const pathnames = location.pathname.split("/").filter((x) => x);

  return (
    <ol className="breadcrumb">
      {/* <li className="breadcrumb-item">
        <Link className="fw-bold" to="/">Dashboard</Link>
      </li> */}
      {pathnames.map((name, index) => {
        const routeTo = "/" + pathnames.slice(0, index + 1).join("/");
        const isLast = index === pathnames.length - 1;
        const displayName = decodeURIComponent(name.charAt(0).toUpperCase() + name.slice(1));

        return isLast ? (
          <li key={index} className="breadcrumb-item active fw-bold" aria-current="page">
            {displayName}
          </li>
        ) : (
          <li key={index} className="breadcrumb-item fw-bold">
            <Link className="fw-bold" to={routeTo}>{displayName} Dashboard</Link>
          </li>
        );
      })}
    </ol>
  );
};

export default DynamicBreadcrumb;

