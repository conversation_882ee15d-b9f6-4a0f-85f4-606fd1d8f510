import { useState, useEffect } from "react";
import {

    <PERSON>,
    Col,
    Button,
    Mo<PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>dal<PERSON>ooter,
    <PERSON>,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams, useNavigate } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";

const PrintActionLetter = () => {
    const token = sessionStorage.getItem('authToken');
    const [data, setData] = useState([]);
    const [sectionData, setSectionData] = useState([]);
    const endPoint = import.meta.env.VITE_API_URL;
    const { id } = useParams();
    const [clgInfo, setClgInfo] = useState([]);
    const [user, setUser] = useState([]);
    const [showPreview, setShowPreview] = useState(false);
    const navigate = useNavigate();
    const [currentBalance, setCurrentBalance] = useState([]);


    useEffect(() => {
        const getApplication = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/leave/getSingleLeave/${id}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data; // Convert to YYYY-MM-DD
                    setData(data);
                    handlePreview();
                }
            } catch (error) {
                console.error("Error fetching Application data:", error);
                alert("Failed to fetch Application data.");
            }
        };

        getApplication(); // Call the function inside useEffect
    }, [id, endPoint, token]); // Dependencies


    useEffect(() => {
        if (data && data.applicantId) {
            const fetchEmployeeData = async () => {
                try {
                    // // console.log(data,"Getting USer in Effect");

                    const response = await axios.get(
                        `${endPoint}/api/employee/get-employee/${data.applicantId}`,
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        const data = response.data;
                        setUser(data);

                    } else {
                        alert("Data Not Fetched.");
                        // navigate('/auth/Register');
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                    // navigate('/auth/Register');
                }
            };
            fetchEmployeeData();
        }
    }, [token, endPoint, data]);


    // // console.log(user,"Getting USer");
    useEffect(() => {
        if (data && data.applicantId) {

            // console.log(data, "Data in effect balance");

            const fetchApplications = async () => {
                try {
                    const response = await axios.get(`${endPoint}/api/leave/leaveBalance/${data.applicantId}`,
                        {
                            headers: {
                                'Content-Type': 'application/json',
                                'web-url': window.location.href,
                                "Authorization": `Bearer ${token}`
                            }
                        });
                    if (response.status === 200) {
                        setCurrentBalance(response.data);
                    } else {
                        alert("Data Not Fetched.");

                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                    // navigate('/auth/Register');
                }

            };
            fetchApplications();
        }
    }, [endPoint, token, data]);


    useEffect(() => {

        if (data && data.fileAtSection) {

            // console.log(data, "Data in effect balance");

            const fetchSection = async () => {
                try {
                    const response = await axios.get(
                        `${endPoint}/api/section/getSingle/${data.fileAtSection}`,
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        setSectionData(response.data);
                    } else {
                        alert("Failed to fetch designation details.");
                    }
                } catch (error) {
                    console.error("An error occurred while fetching the data:", error);
                    alert("An error occurred. Please try again later.");
                }
            };
            fetchSection();
        }
    }, [endPoint, token, data]);


    useEffect(() => {
        const fetchCollegeInfo = async () => {

            if (user && user.college) { // Check if user and college are defined

                try {
                    const response = await axios.get(
                        `${endPoint}/api/college/get-college/${user.college}`,
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        setClgInfo(response.data);
                    } else {
                        alert("College Not Found.");
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                }
            }
        };
        fetchCollegeInfo();
    }, [user]);

    // console.log(currentBalance[0]?.casualLeave, "Getting Casual leave");


    const handlePreview = () => {
        setShowPreview(true);
    };

    const handlePrint = () => {
        const printWindow = window.open('', '_blank');
        const printContent = document.getElementById('print-container').innerHTML;
        const printDocument = printWindow.document;

        printDocument.write(`
           <html>
        <head>
          <title>Print Form</title>
          <style>
            @page { size: A4; margin: 20px; }
            body { font-family: Arial, sans-serif; }
            .print-container {
              width: 21cm;
              min-height: 29.7cm;
              background: #fff;
            }
            .form-field {
              margin-bottom: 15px;
            }
            .form-field label {
              display: block;
              margin-bottom: 5px;
              font-weight: bold;
            }
            .form-field input {
              width: 100%;
              padding: 8px;
              box-sizing: border-box;
            }
            h1 {
              font-size: 24px;
              margin-bottom: 20px;
            }
            p {
              font-size: 16px;
              margin-bottom: 10px;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            ${printContent}
          </div>
        </body>
      </html>
        `);
        printDocument.close();
        printWindow.print();
    };

    const currentDateTime = new Date().toLocaleString('en-IN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true, // Use 12-hour clock
    });

    return (
        <>
            <Header />
            <Modal isOpen={showPreview} toggle={() => setShowPreview(false)} centered size="lg">
                <ModalHeader >
                    Print Preview
                </ModalHeader>
                <ModalBody>
                    <div id="print-container" >
                        <Row >
                            <Col style={{}} >
                                <span >Print Date : {currentDateTime}</span>
                            </Col>
                            <Col style={{ paddingLeft: "70%" }} >
                                <p className="text-right" style={{ fontWeight: "bolder" }}>
                                    <span >आवेदन क्रमांक : {data.applicationId}</span>
                                </p>
                            </Col>
                        </Row>
                        <hr />
                        <div style={{ fontSize: "18px", textAlign: "center", marginBottom: "2px", paddingBottom: "0px", fontWeight: "bold", display: "block" }}>
                            <span> <strong>उच्च शिक्षा विभाग</strong></span> <br />
                            <span> <strong>उच्च शिक्षा संचालनालय</strong></span> <br />
                            <span> <strong>इन्द्रावती भवन अटल नगर, नवा रायपुर, छ.ग.</strong></span>
                        </div><br /><br /><br />

                        {/* <Row  className="mb-7">
                             <Col md={4} style={{ fontSize: "13px", textAlign: "center", marginTop: "0px", paddingTop: "0px", marginBottom: "2px", paddingBottom: "0px" }} >
                                <h5>Application Id - <span style={{ color: "blue" }}>{data.applicationId}</span></h5>
                            </Col>
                            <Col md={2}>
                            </Col>
                            <Col md={4}>
                                <h5 style={{ fontSize: "13px", textAlign: "center", marginTop: "0px", paddingTop: "0px", marginBottom: "2px", paddingBottom: "0px" }}>प्रिन्ट दिनांक/Print Date :{new Date().toLocaleDateString()}</h5>
                            </Col> 
                        </Row> */}
                        <Row >
                            <Col style={{ paddingLeft: "30px" }}>

                                <span> प्रति,</span><br />
                                <span style={{ marginLeft: "50px" }}> {data.applicantName}</span><br />
                                <Col style={{ marginLeft: "32px", width: "400px" }} ><span >{clgInfo.address}</span></Col>
                                <br /><br />
                                {(data.actionByCommissioner) === 3 ? <span style={{ color: "green", fontWeight: "bolder" }}> विषय - स्वीकृति की सुचना </span> : data.actionByCommissioner === 4 ? <span style={{ color: "red", fontWeight: "bolder" }}> विषय - अस्वीकृति की सुचना </span> : ''}

                            </Col>
                        </Row>
                        <br />
                        <Row>
                            <Col style={{ paddingLeft: "30px" }} >
                                <p  >
                                    <strong> CL: {currentBalance[0]?.casualLeave}</strong><strong> EL: {currentBalance[0]?.earnedLeave}</strong>  <strong> OL: {currentBalance[0]?.restrictedHoliday}</strong>
                                    <strong> HPL: {currentBalance[0]?.halfPayLeave}</strong>
                                </p>
                            </Col>
                        </Row>

                        <Row className="mt-3">
                            <Col style={{ paddingLeft: "30px" }}>
                                <span >अवकाश का कारण : <span style={{ fontWeight: "bolder" }}> {data.reason} </span></span>
                            </Col>
                        </Row>
                        <Row>
                            <Col>
                                <br /><br />
                                <table className='table-uneven' style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead>
                                        <tr style={{ borderTop: '1px solid black', padding: '10px', textAlign: 'left' }}>
                                            <th >क्रमांक</th>
                                            <th >अवकाश का प्रकार</th>
                                            <th >दिनांक से </th>
                                            <th >दिनांक तक</th>
                                            <th >अवकाश की अवधि</th>
                                        </tr>
                                    </thead>
                                    <tbody >
                                        <tr style={{ borderTop: '1px solid black', padding: '10px', textAlign: 'left' }}>
                                            <td>{1}</td>
                                            <td>{data.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : data.leaveType}</td>
                                            <td>{formatDate(data.fromDate)}</td>
                                            <td>{formatDate(data.tillDate)}</td>
                                            <td>{data.dayCount} Days</td>
                                        </tr>
                                    </tbody>
                                </table>
                                <br />
                                <br />
                                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                                    <thead style={{ borderTop: '1px solid black', padding: '10px', textAlign: 'left' }}>
                                        <tr style={{ borderBottom: '1px solid black' }}>
                                            <th>पूर्ववर्ती छुट्टियाँ </th>
                                            <td>
                                                {formatDate(new Date(data.fromDate).setDate(new Date(data.fromDate).getDate() - 1))}
                                            </td>
                                        </tr>
                                        <tr style={{ borderBottom: '1px solid black' }}>
                                            <th>पश्चातवर्ती छुट्टियाँ </th>
                                            <td>{formatDate(new Date(data.tillDate).setDate(new Date(data.tillDate).getDate() + 1))}</td>
                                        </tr>
                                        <tr style={{ borderBottom: '1px solid black' }}>
                                            <th>स्टेशन से बाहर जाने की अनुमति </th>
                                            <td>{data.permission === 0 ? "नहीं " : "हाँ "}</td>
                                        </tr >
                                        <tr style={{ borderBottom: '1px solid black' }}>
                                            <th>अवकाश के दौरान पता </th>
                                            <td>{data.stationAddress}</td>
                                        </tr>
                                        <tr style={{ borderBottom: '1px solid black' }}>
                                            <th>रिमार्क</th>
                                            <td>{data.remarkByUser}</td>
                                        </tr>
                                        <tr style={{ marginTop: "20px" }}>
                                            <th>नोट : आयुक्त महोदय द्वारा अनुमोदित </th>

                                        </tr>
                                    </thead>
                                </table>
                                <br /><br />
                                <Row className="d-flex">
                                    <Col style={{ paddingLeft: "70%" }} >
                                        <br /><br />
                                        {/* <span> <br />
                                            भवदीय
                                        </span> <br />
                                        <span> {data.applicantName} </span>
                                          */}
                                        <br />
                                        <span> शाखा : {sectionData.sectionName} </span><br />

                                        <span>कार्यवाही की तिथि  <br />  {formatDate(data.actionByDirectorDate)} </span>

                                    </Col>
                                </Row>
                            </Col>
                        </Row>
                    </div>

                </ModalBody>
                <ModalFooter>
                    <Button color="secondary" onClick={() => { setShowPreview(false); navigate(-1); }}>
                        Close
                    </Button>
                    <Button color="primary" onClick={() => { handlePrint(); navigate(-1); }}>
                        Print
                    </Button>
                </ModalFooter>
            </Modal >
        </>
    );
};

export default PrintActionLetter;