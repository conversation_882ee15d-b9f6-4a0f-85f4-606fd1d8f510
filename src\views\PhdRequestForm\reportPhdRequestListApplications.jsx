import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON>er,
  Container,
  Col,
  Row,
  Table,
  Modal,
  <PERSON>dal<PERSON>eader,
  <PERSON>dal<PERSON>ody,
  CardBody,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>ner,
  Badge,
  FormGroup,
  Label,
  Input,
} from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";

const PhdApplicationReport = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;

  const [phdData, setPhdData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [fromDate, setFromDate] = useState("");
  const [toDate, setToDate] = useState(new Date().toISOString().split("T")[0]); // Today's date
  const [viewModal, setViewModal] = useState(false);
  const [selectedApplication, setSelectedApplication] = useState(null);

  // Get today's date for max date restriction
  const today = new Date().toISOString().split("T")[0];

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };

  // Fetch PhD applications data
  const fetchPhdApplications = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (fromDate.trim()) params.append("fromDate", fromDate);
      if (toDate.trim()) params.append("toDate", toDate);
      if (searchText.trim()) params.append("search", searchText.trim());

      const response = await axios.get(
        `${endPoint}/api/phd-application/report?${params.toString()}`,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setPhdData(response.data.data || []);
      } else {
        SwalMessageAlert("No Data Found", "error");
      }
    } catch (error) {
      handleApiError(error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPhdApplications();
  }, [fromDate, toDate, searchText]);

  // View application details
  const handleViewDetails = (application) => {
    setSelectedApplication(application);
    setViewModal(true);
  };

  // Export Pdf + Excel
  const handlePrint = () => {
    try {
      if (phdData.length === 0) {
        SwalMessageAlert("कोई डेटा उपलब्ध नहीं है।", "warning");
        return;
      }

      let rowsHTML = "";
      phdData.forEach((app, index) => {
        rowsHTML += `
        <tr>
          <td>${index + 1}</td>
          <td>${app.applicationNo}</td>
          <td>${formatDate(app.submissionDate)}</td>
          <td>${app.empCode}</td>
          <td>${app.district || ""}</td>
          <td>${app.empName || ""}, ${app.designation || ""}<br>${
          app.college || ""
        }</td>
          <td>${formatDate(app.appointmentDate) || ""}</td>
          <td>${app.service5yr === "yes" ? "हाँ" : "नहीं"}</td>
          <td>${app.probationCompleted === "yes" ? "हाँ" : "नहीं"}</td>
          <td>${app.mphilDone === "yes" ? "हाँ" : "नहीं"}</td>
          <td>${app.courseWorkDone === "yes" ? "हाँ" : "नहीं"}</td>
          <td>${app.university || ""}</td>
          <td>${app.courseWorkComplete === "yes" ? "पूर्ण" : "नहीं"}</td>
          <td>${app.leaveDuration || ""} दिवस</td>
          <td>${app.remark || ""}</td>
        </tr>
      `;
      });

      let printHTML = `
      <html>
        <head>
          <title>पी.एच.डी. आवेदन सूची</title>
          <style>
            @page {
              size: A4 landscape;
              margin: 15px;
            }
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 0;
              font-size: 10px;
            }
            .print-container {
              width: 100%;
              padding: 15px;
              box-sizing: border-box;
              background: #fff;
            }
            .header {
              text-align: center;
              margin-bottom: 10px;
            }
            .header h2, .header h3 {
              margin: 4px 0;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-top: 10px;
              table-layout: fixed;
            }
            th, td {
              border: 1px solid #000;
              padding: 4px;
              text-align: center;
              vertical-align: middle;
              font-size: 9px;
              word-wrap: break-word;
            }
            th {
              background-color: #f0f0f0;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            <div class="header">
              <h2>पी.एच.डी. अनुमति हेतु प्राप्त आवेदन पत्रों का विवरण</h2>
              <h3>(ऐसे आवेदक जिन्होंने पाँच वर्ष की सेवा अवधि, परिवीक्षा अवधि, एम.फिल अर्हता अथवा 6 माह का कोर्सवर्क पूर्ण कर लिया हो की सूची)</h3>
            </div>
            <table>
              <thead>
                <tr>
                  <th>क्र.</th>
                  <th>आवेदन संख्या</th>
                  <th>आवेदन दिनांक</th>
                  <th>कर्मचारी कोड</th>
                  <th>जिला</th>
                  <th>आवेदक का नाम, पदनाम एवं महाविद्यालय का नाम</th>
                  <th>प्रथम नियुक्ति तिथि</th>
                  <th>05 वर्ष सेवा पूर्ण किया है / नहीं</th>
                  <th>एम.फिल किया है / नहीं</th>
                  <th>परिवीक्षा अवधि पूर्ण किया है / नहीं</th>
                  <th>कोर्स वर्क पूर्ण किया है / नहीं</th>
                  <th>जिस संस्था में पीएचडी कर रहे हैं उसका पूरा नाम</th>
                  <th>कोर्स वर्क पूर्ण हुआ है या नहीं</th>
                  <th>सम्बंधित के अवकाश खाते में शेष अर्जित अवकाश</th>
                  <th>प्राचार्य का टिप्पणी / अनुशंसा</th>
                  <th>रिमार्क</th>
                </tr>
              </thead>
              <tbody>
                ${rowsHTML}
              </tbody>
            </table>
          </div>
        </body>
      </html>
    `;

      const printWindow = window.open("", "_blank");
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.print();
    } catch (error) {
      SwalMessageAlert(`Print Error: ${error.message}`, "error");
    }
  };

  const exportToExcel = async () => {
    try {
      if (phdData.length === 0) {
        SwalMessageAlert("कोई डेटा उपलब्ध नहीं है।", "warning");
        return;
      }

      let tableHTML = `
        <table border="1">
          <thead>
            <tr>
              <th>क्र.</th>
              <th>आवेदन संख्या</th>
              <th>आवेदन दिनांक</th>
              <th>कर्मचारी कोड</th>
              <th>नाम</th>
              <th>पदनाम</th>
              <th>महाविद्यालय</th>
              <th>जिला</th>
              <th>विधानसभा</th>
              <th>मोबाइल</th>
              <th>ईमेल</th>
              <th>पीएचडी नामांकित</th>
              <th>कोर्स वर्क पूर्ण</th>
              <th>एम.फिल पूर्ण</th>
              <th>विश्वविद्यालय</th>
              <th>शोध निर्देशक</th>
              <th>शोध केंद्र</th>
              <th>पंजीकरण संख्या</th>
              <th>जन्म तिथि</th>
              <th>नियुक्ति तिथि</th>
            </tr>
          </thead>
          <tbody>
      `;

      phdData.forEach((app, index) => {
        tableHTML += `
          <tr>
            <td>${index + 1}</td>
            <td>${app.applicationNo}</td>
            <td>${formatDate(app.submissionDate)}</td>
            <td>${app.empCode}</td>
            <td>${app.empName}</td>
            <td>${app.designation}</td>
            <td>${app.college}</td>
            <td>${app.district}</td>
            <td>${app.vidhansabha}</td>
            <td>${app.contact}</td>
            <td>${app.email}</td>
            <td>${app.isPhdEnrolled === "yes" ? "हाँ" : "नहीं"}</td>
            <td>${app.courseWorkDone === "yes" ? "हाँ" : "नहीं"}</td>
            <td>${app.mphilDone === "yes" ? "हाँ" : "नहीं"}</td>
            <td>${app.university}</td>
            <td>${app.sodhNirdeshak}</td>
            <td>${app.sodhKendra}</td>
            <td>${app.registrationNo}</td>
            <td>${app.janmTithi ? formatDate(app.janmTithi) : "N/A"}</td>
            <td>${app.niyuktiTithi ? formatDate(app.niyuktiTithi) : "N/A"}</td>
          </tr>
        `;
      });

      tableHTML += "</tbody></table>";

      const excelFileContent = `
        <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
          <head><!--[if gte mso 9]><xml>
            <x:ExcelWorkbook>
              <x:ExcelWorksheets>
                <x:ExcelWorksheet>
                  <x:Name>PhD Applications</x:Name>
                  <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                </x:ExcelWorksheet>
              </x:ExcelWorksheets>
            </x:ExcelWorkbook>
          </xml><![endif]--></head>
          <body>${tableHTML}</body>
        </html>
      `;

      const utf8BOM = "\uFEFF";
      const blob = new Blob([utf8BOM + excelFileContent], {
        type: "application/vnd.ms-excel;charset=utf-8;",
      });
      const date = new Date().toLocaleDateString();
      const downloadLink = document.createElement("a");
      downloadLink.href = URL.createObjectURL(blob);
      const filename =
        fromDate && toDate
          ? `PhD_Applications_${fromDate}_to_${toDate}_${date}.xls`
          : `PhD_Applications_All_${date}.xls`;
      downloadLink.download = filename;
      downloadLink.click();
    } catch (error) {
      SwalMessageAlert(`Export Error: ${error.message}`, "error");
    }
  };

  const handlePrintIndividual = (id) => {
    try {
      const printContent = phdData.find((data) => data._id === id);
      if (!printContent) {
        SwalMessageAlert("No data found for this application.", "error");
        return;
      }

      let printHTML = `
        <html>
          <head>
            <title>पीएचडी आवेदन रिपोर्ट - ${printContent.applicationNo}</title>
            <style>
              @page {
                size: A4 portrait;
                margin: 20px;
              }
              body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 0;
                font-size: 12px;
              }
              .print-container {
                width: 100%;
                background: #fff;
                padding: 20px;
                box-sizing: border-box;
              }
              .header {
                text-align: center;
                margin-bottom: 20px;
              }
              .header h1 {
                font-size: 16px;
                margin: 5px 0;
                font-weight: bold;
              }
              .data-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 20px;
              }
              .data-table th,
              .data-table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
                vertical-align: top;
                font-size: 11px;
              }
              .data-table th {
                background-color: #f0f0f0;
                font-weight: bold;
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              <div class="header">
                <h1>पी.एच.डी अनुदान हेतु आवेदन पत्र का विवरण</h1>
              </div>
              <table class="data-table">
                <tr>
                  <th>आवेदन संख्या</th>
                  <td>${printContent.applicationNo}</td>
                </tr>
                <tr>
                  <th>कर्मचारी कोड</th>
                  <td>${printContent.empCode}</td>
                </tr>
                <tr>
                  <th>नाम</th>
                  <td>${printContent.empName}</td>
                </tr>
                <tr>
                  <th>पदनाम</th>
                  <td>${printContent.designation}</td>
                </tr>
                <tr>
                  <th>महाविद्यालय</th>
                  <td>${printContent.college}</td>
                </tr>
                <tr>
                  <th>जिला</th>
                  <td>${printContent.district}</td>
                </tr>
                <tr>
                  <th>विधानसभा</th>
                  <td>${printContent.vidhansabha}</td>
                </tr>
                <tr>
                  <th>मोबाइल</th>
                  <td>${printContent.contact}</td>
                </tr>
                <tr>
                  <th>ईमेल</th>
                  <td>${printContent.email}</td>
                </tr>
                <tr>
                  <th>पीएचडी नामांकित</th>
                  <td>${
                    printContent.isPhdEnrolled === "yes" ? "हाँ" : "नहीं"
                  }</td>
                </tr>
                <tr>
                  <th>कोर्स वर्क पूर्ण</th>
                  <td>${
                    printContent.courseWorkDone === "yes" ? "हाँ" : "नहीं"
                  }</td>
                </tr>
                <tr>
                  <th>एम.फिल पूर्ण</th>
                  <td>${printContent.mphilDone === "yes" ? "हाँ" : "नहीं"}</td>
                </tr>
                <tr>
                  <th>विश्वविद्यालय</th>
                  <td>${printContent.university}</td>
                </tr>
                <tr>
                  <th>शोध निर्देशक</th>
                  <td>${printContent.sodhNirdeshak}</td>
                </tr>
                <tr>
                  <th>शोध केंद्र</th>
                  <td>${printContent.sodhKendra}</td>
                </tr>
                <tr>
                  <th>पंजीकरण संख्या</th>
                  <td>${printContent.registrationNo}</td>
                </tr>
                <tr>
                  <th>जन्म तिथि</th>
                  <td>${
                    printContent.janmTithi
                      ? formatDate(printContent.janmTithi)
                      : "N/A"
                  }</td>
                </tr>
                <tr>
                  <th>नियुक्ति तिथि</th>
                  <td>${
                    printContent.niyuktiTithi
                      ? formatDate(printContent.niyuktiTithi)
                      : "N/A"
                  }</td>
                </tr>
                <tr>
                  <th>आवेदन दिनांक</th>
                  <td>${formatDate(printContent.submissionDate)}</td>
                </tr>
              </table>
            </div>
          </body>
        </html>
      `;

      const printWindow = window.open("", "_blank");
      printWindow.document.write(printHTML);
      printWindow.document.close();
      printWindow.print();
    } catch (error) {
      SwalMessageAlert(`Print Error: ${error.message}`, "error");
    }
  };

  const columns = [
    {
      name: "Action",
      cell: (row) => (
        <div className="d-flex flex-column justify-content-center gap-2">
          <Button
            color="info"
            size="sm"
            className="w-100 d-flex   justify-content-center px-2 py-1 shadow-sm"
            onClick={() => handleViewDetails(row)}
            title="विवरण देखें"
          >
            <i className="fa fa-eye me-1"></i>
            <span className="d-none d-md-inline">विवरण</span>
          </Button>
          <br />
          <Button
            color="primary"
            size="sm"
            className="w-100 d-flex   justify-content-center px-2 py-1 shadow-sm"
            onClick={() => handlePrintIndividual(row._id)}
            title="प्रिंट करें"
          >
            <i className="fa fa-print me-1"></i>
            <span className="d-none d-md-inline">प्रिंट</span>
          </Button>
        </div>
      ),
      wrap: true,
      width: "110px",
    },
    {
      name: "आवेदन विवरण",
      cell: (row) => (
        <div className="p-2 bg-white rounded shadow-sm hover:shadow-md transition-all">
          <div className="mb-2 text-primary">
            <strong>आवेदन संख्या:</strong>
            <span className="ml-2">{row.applicationNo}</span>
          </div>
          <div className="mb-2">
            <strong className="text-gray-700">कर्मचारी कोड:</strong>
            <span className="ml-2">{row.empCode}</span>
          </div>
          <div className="mb-2">
            <strong className="text-gray-700">मोबाइल:</strong>
            <span className="ml-2">{row.contact}</span>
          </div>
          <div className="mb-2">
            <strong className="text-gray-700">आवेदन दिनांक:</strong>
            <span className="ml-2">{formatDate(row.submissionDate)}</span>
          </div>
          {/* <div className="mb-2">
               <strong className="text-gray-700">आवेदन स्थिति:</strong>
                <Badge color="success" className="px-2 py-1 rounded-pill mx-2">
            {row.status}
          </Badge>
            </div> */}
        </div>
      ),
      wrap: true,
      width: "220px",
    },
    {
      name: "कर्मचारी विवरण",
      cell: (row) => (
        <div className="p-2 bg-white rounded shadow-sm hover:shadow-md transition-all">
          <div className="mb-2">
            <strong className="text-primary"> कर्मचारी/आवेदक नाम:</strong>
            <span className="ml-2 font-semibold">{row.empName}</span>
          </div>
          <div className="mb-2">
            <strong className="text-gray-700">पदनाम:</strong>
            <span className="ml-2">{row.designation}</span>
          </div>
          <div className="mb-2 text-nowrap">
            <strong className="text-gray-700 ">कार्यरत महाविद्यालय:</strong>
            <span className="ml-2 ">{row.college}</span>
          </div>
          <div className="mb-2">
            <strong className="text-gray-700">जिला:</strong>
            <span className="ml-2">{row.district}</span>
          </div>
        </div>
      ),
      wrap: true,
      width: "450px",
    },

    {
      name: "पीएचडी विवरण",
      cell: (row) => (
        <div className="p-2 bg-white rounded shadow-sm hover:shadow-md transition-all text-sm">
          <div className="mb-2">
            <strong className="text-primary">पीएचडी नामांकित:</strong>
            <Badge
              color={row.isPhdEnrolled === "yes" ? "success" : "danger"}
              className="ms-2"
            >
              {row.isPhdEnrolled === "yes" ? "हाँ" : "नहीं"}
            </Badge>
          </div>
          <div className="mb-2">
            <strong className="text-dark">कोर्स वर्क पूर्ण:</strong>
            <Badge
              color={row.courseWorkDone === "yes" ? "success" : "danger"}
              className="ms-2"
            >
              {row.courseWorkDone === "yes" ? "हाँ" : "नहीं"}
            </Badge>
          </div>
          <div className="mb-2">
            <strong className="text-dark">एम.फिल पूर्ण:</strong>
            <Badge
              color={row.mphilDone === "yes" ? "success" : "danger"}
              className="ms-2"
            >
              {row.mphilDone === "yes" ? "हाँ" : "नहीं"}
            </Badge>
          </div>
        </div>
      ),
      wrap: true,
      width: "230px",
    },
    {
      name: "PhD शोध केंद्र की जानकारी",
      cell: (row) => (
        <div className="p-2 bg-white rounded shadow-sm hover:shadow-md transition-all text-sm">
          <div className="mb-2">
            <strong>शोध निर्देशक:</strong>
            <span className="ms-2">{row.sodhNirdeshak || "N/A"}</span>
          </div>
          <div className="mb-2">
            <strong>शोध केंद्र:</strong>
            <span className="ms-2">{row.sodhKendra || "N/A"}</span>
          </div>
          <div className="mb-2">
            <strong>विश्वविद्यालय:</strong>
            <span className="ms-2">{row.university || "N/A"}</span>
          </div>
          <div className="mb-2">
            <strong>पंजीकरण संख्या:</strong>
            <span className="ms-2">{row.registrationNo || "N/A"}</span>
          </div>
        </div>
      ),
      wrap: true,
      width: "250px",
    },
  ];

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <div style={{ display: "none" }} id="print-container"></div>
        <Card className="shadow">
          <CardHeader>
            <div className="d-flex flex-column flex-md-row flex-wrap justify-content-between align-items-start align-items-md-center gap-3">
              <h3 className="mb-0 text-danger"> फाइनल पी.एच.डी आवेदन रिपोर्ट</h3>

              <div className="d-flex flex-column flex-sm-row flex-wrap align-items-start align-items-sm-end gap-3">
                {/* From Date */}
                <FormGroup className="mb-0 mx-3">
                  <Label for="fromDate" className="mb-1">
                   कब से दिनांक:
                  </Label>
                  <Input
                    type="date"
                    id="fromDate"
                    value={fromDate}
                    max={today}
                    onChange={(e) => {
                      const selectedDate = e.target.value;
                      if (toDate && selectedDate > toDate) {
                        setToDate(selectedDate);
                      }
                      setFromDate(selectedDate);
                    }}
                    bsSize="sm"
                  />
                </FormGroup>

                {/* To Date */}
                <FormGroup className="mb-0 mx-3">
                  <Label for="toDate" className="mb-1">
                   कब तक दिनांक:
                  </Label>
                  <Input
                    type="date"
                    id="toDate"
                    value={toDate}
                    min={fromDate || undefined}
                    max={today}
                    onChange={(e) => setToDate(e.target.value)}
                    bsSize="sm"
                  />
                </FormGroup>

                {/* Search Field */}
                <FormGroup className="mb-0 mx-3" style={{ minWidth: "200px" }}>
                  <Label className="mb-1 d-none d-sm-block">आवेदन नाम, कोड, आवेदन संख्या से खोजें:</Label>
                  <Input
                    type="text"
                    placeholder="नाम, कोड, आवेदन संख्या..."
                    value={searchText}
                    onChange={(e) => setSearchText(e.target.value)}
                    bsSize="sm"
                    style={{
                      borderRadius: "20px",
                      paddingLeft: "1rem",
                      paddingRight: "1rem",
                    }}
                  />
                </FormGroup>
              </div>
            </div>
          </CardHeader>

          <CardBody>
            {/* Export/Print Buttons */}
            <div className="mb-3 d-flex flex-wrap justify-content-end gap-2">
              <Button
                onClick={handlePrint}
                color="warning"
                size="sm"
                className="d-flex align-items-center"
              >
                <i className="fa fa-print me-2"></i>
                <span>प्रिंट सूची</span>
              </Button>
              <Button
                onClick={exportToExcel}
                color="success"
                size="sm"
                className="d-flex align-items-center"
              >
                <i className="fa fa-file-excel me-2"></i>
                <span>एक्सेल फ़ाइल</span>
              </Button>
            </div>

            {/* Loading Spinner */}
            {loading ? (
              <div className="text-center py-5">
                <Spinner color="primary" size="lg" />
                <p className="mt-3 text-muted">डेटा लोड हो रहा है...</p>
              </div>
            ) : (
              <div className="table-responsive">
                <DataTable
                  columns={columns}
                  data={phdData}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  responsive
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa",
                        fontWeight: "bold",
                        padding: "1rem",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff",
                        "&:nth-of-type(odd)": {
                          backgroundColor: "#f9fafb",
                        },
                        "&:hover": {
                          backgroundColor: "#f3f4f6",
                          transition: "all 0.2s",
                        },
                      },
                    },
                    cells: {
                      style: {
                        padding: "1rem",
                      },
                    },
                  }}
                />
              </div>
            )}
          </CardBody>
        </Card>

<Modal isOpen={viewModal} toggle={() => setViewModal(false)} size="xl">
  <ModalHeader
    toggle={() => setViewModal(false)}
    className="bg-primary text-white"
  >
  
   <h5 className="text-white">  <i className="fa fa-file-text me-2"></i> पी.एच.डी हेतु आवेदन पत्र - {selectedApplication?.applicationNo}</h5>
  </ModalHeader>

  <ModalBody>
    {selectedApplication && (
      <div className="p-2">
        <Row className="gy-4">
          {/* व्यक्तिगत विवरण */}
          <Col md="6">
            <div className="border rounded p-3 h-100">
              <h5 className="border-bottom pb-2 mb-3 fw-bold">
                व्यक्तिगत विवरण
              </h5>
              <Table bordered hover size="sm" responsive>
                <tbody>
                  <tr><td className="bg-light"><strong>नाम:</strong></td><td>{selectedApplication.empName}</td></tr>
                  <tr><td className="bg-light"><strong>कर्मचारी कोड:</strong></td><td>{selectedApplication.empCode}</td></tr>
                  <tr><td className="bg-light"><strong>पदनाम:</strong></td><td>{selectedApplication.designation}</td></tr>
                  <tr><td className="bg-light"><strong>महाविद्यालय:</strong></td><td>{selectedApplication.college}</td></tr>
                  <tr><td className="bg-light"><strong>जिला:</strong></td><td>{selectedApplication.district}</td></tr>
                  <tr><td className="bg-light"><strong>मोबाइल:</strong></td><td>{selectedApplication.contact}</td></tr>
                  <tr><td className="bg-light"><strong>ईमेल:</strong></td><td>{selectedApplication.email}</td></tr>
                </tbody>
              </Table>
            </div>
          </Col>

          {/* पीएचडी विवरण */}
          <Col md="6">
            <div className="border rounded p-3 h-100">
              <h5 className="border-bottom pb-2 mb-3 fw-bold">
                पीएचडी विवरण
              </h5>
              <Table bordered hover size="sm" responsive>
                <tbody>
                  <tr><td className="bg-light"><strong>पीएचडी नामांकित:</strong></td>
                    <td><Badge color={selectedApplication.isPhdEnrolled === "yes" ? "success" : "warning"}>{selectedApplication.isPhdEnrolled === "yes" ? "हाँ" : "नहीं"}</Badge></td></tr>
                  <tr><td className="bg-light"><strong>कोर्स वर्क पूर्ण:</strong></td>
                    <td><Badge color={selectedApplication.courseWorkDone === "yes" ? "success" : "warning"}>{selectedApplication.courseWorkDone === "yes" ? "हाँ" : "नहीं"}</Badge></td></tr>
                  <tr><td className="bg-light"><strong>एम.फिल पूर्ण:</strong></td>
                    <td><Badge color={selectedApplication.mphilDone === "yes" ? "success" : "warning"}>{selectedApplication.mphilDone === "yes" ? "हाँ" : "नहीं"}</Badge></td></tr>
                  <tr><td className="bg-light"><strong>विश्वविद्यालय:</strong></td><td>{selectedApplication.university}</td></tr>
                  <tr><td className="bg-light"><strong>शोध निर्देशक:</strong></td><td>{selectedApplication.sodhNirdeshak}</td></tr>
                  <tr><td className="bg-light"><strong>शोध केंद्र:</strong></td><td>{selectedApplication.sodhKendra}</td></tr>
                  <tr><td className="bg-light"><strong>पंजीकरण संख्या:</strong></td><td>{selectedApplication.registrationNo}</td></tr>
                </tbody>
              </Table>
            </div>
          </Col>
        </Row>

        {/* दस्तावेज़ */}
        <Row className="mt-4">
          <Col>
            <div className="border rounded p-3">
              <h5 className="border-bottom pb-2 mb-3 fw-bold">
                अपलोडेड दस्तावेज़
              </h5>
              <div className="d-flex flex-wrap gap-2">
                {selectedApplication.courseWorkCertificate && (
                  <Button
                    color="info"
                    size="sm"
                    className="d-flex align-items-center"
                    onClick={() =>
                      window.open(
                        `${endPoint}/uploads${selectedApplication.courseWorkCertificate}`,
                        "_blank"
                      )
                    }
                  >
                    <i className="fa fa-file-pdf me-2"></i>
                    कोर्स वर्क प्रमाणपत्र
                  </Button>
                )}
                {selectedApplication.serviceBook && (
                  <Button
                    color="info"
                    size="sm"
                    className="d-flex align-items-center"
                    onClick={() =>
                      window.open(
                        `${endPoint}/uploads${selectedApplication.serviceBook}`,
                        "_blank"
                      )
                    }
                  >
                    <i className="fa fa-file-pdf me-2"></i>
                    सेवा पुस्तक
                  </Button>
                )}
                {selectedApplication.satyaapitPatra && (
                  <Button
                    color="info"
                    size="sm"
                    className="d-flex align-items-center"
                    onClick={() =>
                      window.open(
                        `${endPoint}/uploads${selectedApplication.satyaapitPatra}`,
                        "_blank"
                      )
                    }
                  >
                    <i className="fa fa-file-pdf me-2"></i>
                    सत्यापित पत्र
                  </Button>
                )}
                {selectedApplication.mphilCertificate && (
                  <Button
                    color="info"
                    size="sm"
                    className="d-flex align-items-center"
                    onClick={() =>
                      window.open(
                        `${endPoint}/uploads${selectedApplication.mphilCertificate}`,
                        "_blank"
                      )
                    }
                  >
                    <i className="fa fa-file-pdf me-2"></i>
                    एम.फिल प्रमाणपत्र
                  </Button>
                )}
              </div>
            </div>
          </Col>
        </Row>
      </div>
    )}
  </ModalBody>

  <ModalFooter>
    <Button color="secondary" onClick={() => setViewModal(false)}>
      <i className="fa fa-times me-2"></i>
      बंद करें
    </Button>
  </ModalFooter>
</Modal>

      
      </Container>
    </>
  );
};

export default PhdApplicationReport;
