import { useEffect, useState } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    CardHeader,
    CardBody,
    FormGroup,
    Form,
    InputGroupText,
    Input,
    Container,
    Row,
    Col,
    Label,
    Modal,
    ModalHeader,
    ModalBody,
    ModalFooter,
    InputGroup,
    InputGroupAddon
} from "reactstrap";



import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import { MultiSelect } from "react-multi-select-component";
import Swal from "sweetalert2";

const Director = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const [section, setSection] = useState([]);
    const inputStyle = {
        color: "gray", // Set the desired gray color
        borderColor: "#ccc", // Optional: Set border color for better visibility
    };

    const [selectedSection, setSelectedSection] = useState([]);


    const [formData, setFormData] = useState({
        name: '',
        email: '',
        designation: '',
        postingLocation: '',
        section: [],
        empCode: "",
        contact: "",
        divison: "",
        district: "",
        vidhansabha: "",
        classData: "",
        address: "",
        workType: "",
        currentSalary: "",
        nextIncrementDate: "",
        gender: "",
        title: "",
        onDeputation: "false",
        deputedDesignation: "",
        mainPostingLocation: "",
        employeeType: "",
    });
    const [isModalOpen, setModalOpen] = useState(false);
    const toggleModal = () => setModalOpen(!isModalOpen);
    const [district, setDistrict] = useState([]);
    const [vidhansabha, setVidhansabha] = useState([]);
    const [classData, setClassData] = useState([]);
    const [division, setDivision] = useState([]);
    const [college, setCollege] = useState([]);




    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    const data = response.data;
                    setDivision(data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };
        const fetchClassData = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/class/getAll`, {
                    headers: { 
                        
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}` },
                });
                if (response.status === 200) {
                    setClassData(response.data);
                } else {
                    SwalMessageAlert("No Class Data Found", "error");
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };
        fetchClassData();
        getDivision();
    }, [endPoint, token]); // Dependencies


    const handleDivisionChange = async (e) => {
        const { value } = e.target;
        try {
            const response = await axios.get(
                `${endPoint}/api/district/get-division-district/${value}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            if (response.status === 200) {
                setDistrict(response.data);
                // navigate("admin/Dashboard");
            } else {
                alert("Login failed. Please check your credentials and try again.");
                // navigate('/auth/Register');
            }
        } catch (error) {
            console.error("An error occurred while submitting the form:", error);
            alert("An error occurred. Please try again later.");
            // navigate('/auth/Register');
        }
        setFormData({
            ...formData,
            divison: value,
        });
    };

    const handleDistrictChange = async (e) => {
        const { value } = e.target;
        setFormData({
            ...formData,
            district: value,
        });
        try {
            const response = await axios.get(
                `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            if (response.status === 200) {
                setVidhansabha(response.data);
                // navigate("admin/Dashboard");
            } else {
                alert("Login failed. Please check your credentials and try again.");
                // navigate('/auth/Register');
            }
        } catch (error) {
            console.error("An error occurred while submitting the form:", error);
            alert("An error occurred. Please try again later.");
            // navigate('/auth/Register');
        }
    };

    const [designationData, setDesignationData] = useState([]);
    const handleClassInputChange = async (e) => {
        const { value } = e.target;
        try {
            const response = await axios.get(
                `${endPoint}/api/degisnation-class-wise/${value}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            if (response.status === 200) {
                setDesignationData(response.data);
            } else {
                SwalMessageAlert("Designation Not Found", "error");
                // navigate('/auth/Register');
            }
        } catch (error) {
            console.error("An error occurred while submitting the form:", error);
            alert("An error occurred. Please try again later.");
            // navigate('/auth/Register');
        }
        setFormData({
            ...formData,
            classData: value,
        });
    };

    useEffect(() => {
        const fetchCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-all-college`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    setCollege(response.data);
                } else {
                    alert("Failed to fetch College data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchCollege();
    }, [endPoint, token]);



    useEffect(() => {
        const fetchSection = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/section/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });

                if (response.status === 200) {
                    const verifiedSection = response.data.filter(
                        (section) => section.isVerified === true
                    );

                    setSection(verifiedSection);
                } else {
                    alert("Failed to fetch Section. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchSection();
    }, [endPoint, token]);

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });


        if (name === "onDeputation") {
            if (value === "false") {
                setFormData({
                    ...formData,
                    [name]: value,
                    deputedDesignation: "", // Set to null
                    mainPostingLocation: "", // Set to null
                });
            }
        }
    }


    const handleSubmit = async (e) => {
        e.preventDefault();
        // Create an array of the field values to validate
        const valuesToValidate = [
            formData.name,
            formData.email,
            formData.designation,
            formData.postingLocation,
            formData.postingLocation,
            formData.section,
            formData.empCode,
            formData.contact,
            formData.divison,
            formData.district,
            formData.vidhansabha,
            formData.classData,
            formData.address,
            formData.workType,
            formData.currentSalary,
            formData.nextIncrementDate,
            formData.gender,
            formData.title,
            formData.onDeputation,

        ];

        const hasEmptyFields = valuesToValidate.some(value => value === null || value === '' || value === undefined);
        const allFieldsFilled = valuesToValidate.every(value => value !== null && value !== '' && value !== undefined);

        // Condition for empty fields
        if (hasEmptyFields) {
            SwalMessageAlert("Please fill out all fields before submitting.", "warning");
            return; // Prevent form submission
        }

        // Condition for filled fields (you can implement additional logic here)
        if (allFieldsFilled) {
            alert("All fields are filled. You can proceed with submission.");
        }

        try {
            const body = {
                name: formData.name,
                email: formData.email,
                designation: formData.designation,
                mobile: formData.mobile,
                section: selectedSection.map((item) => item.value),
                postingLocation: String(formData.postingLocation),
                empCode: String(formData.empCode),
                contact: String(formData.contact),
                divison: String(formData.divison),
                district: String(formData.district),
                vidhansabha: String(formData.vidhansabha),
                classData: String(formData.classData),
                address: String(formData.address),
                workType: String(formData.workType),
                currentSalary: String(formData.currentSalary),
                nextIncrementDate: String(formData.nextIncrementDate),
                gender: String(formData.gender),
                title: String(formData.title),
                onDeputation: String(formData.onDeputation),
                deputedDesignation: String(formData.deputedDesignation),
                mainPostingLocation: String(formData.mainPostingLocation),
                employeeType: String(formData.employeeType),
            }

            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                e.target.disabled = true;
                setTimeout(() => {
                    e.target.disabled = false;
                  }, 5000);
                  

                const response = await axios.post(`${endPoint}/api/director/add`, { ...body }, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
                if (response.status === 200) {
                    setFormData({
                        name: '',
                        email: '',
                        designation: '',
                        postingLocation: '',
                        section: [],
                        empCode: "",
                        contact: "",
                        divison: "",
                        district: "",
                        vidhansabha: "",
                        class: "",
                        address: "",
                        workType: "",
                        currentSalary: "",
                        nextIncrementDate: "",
                        gender: "",
                        title: "",
                        onDeputation: "",
                        deputedDesignation: "",
                        mainPostingLocation: "",
                        employeeType: "",
                    });
                    SwalMessageAlert("Added successfully.", "success");
                    setTimeout(() => window.location.reload(), 2000);
                }
            } else {
                SwalMessageAlert("Director/Secratory Already Exists.", "error");
            }

        } catch (error) {
            console.error("An error occurred while submitting the form:", error);
            alert("message Director/Secratory Already Exists.");
        }

    };


    // Handle Pagination


    const options = section.map((type) => ({
        value: type._id,
        label: type.sectionName,
    }));





    return (
        <>
            <Header />


            {/* Page content */}
            <Container className="mt--7" fluid>
                <Row>
                    <Col >
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">Directorate</h3>
                                    </Col>

                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Form >
                                    <div className="pl-lg-4">
                                        <Row>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="empCode" className="form-control-label">
                                                        Employee Code
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-hat-3" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="empCode"
                                                            name="empCode"
                                                            placeholder="Employee Code"
                                                            type="text"
                                                            maxLength={12}
                                                            style={inputStyle}
                                                            value={formData.empCode || ""}
                                                            onChange={handleInputChange}
                                                        />

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="contact" className="form-control-label">
                                                        Contact
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-mobile-button" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="contact"
                                                            name="contact"
                                                            placeholder="Contact"
                                                            type="tel"
                                                            style={inputStyle}
                                                            value={formData.contact || ""}
                                                            maxLength={10}
                                                            onChange={(e) => {
                                                                const value = e.target.value;

                                                                // Check if the input is empty
                                                                if (value === "") {
                                                                    handleInputChange(e);
                                                                    return;
                                                                }

                                                                // Regex to match numbers starting with 6, 7, 8, or 9
                                                                const validStartRegex = /^[6-9]/;

                                                                if (validStartRegex.test(value)) {
                                                                    // If valid, pass 'true' as the second argument to handleInputChange
                                                                    handleInputChange(e);
                                                                } else {
                                                                    // Show alert if the input starts with invalid numbers
                                                                    // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                                                    SwalMessageAlert(
                                                                        " Mobile number must start with digits 6, 7, 8, or 9.",
                                                                        "warning"
                                                                    );

                                                                }
                                                            }}
                                                        />

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>

                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="title" className="form-control-label">
                                                        Title
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="fas fa-user"></i>
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="title"
                                                            name="title"
                                                            type="select" // Change type to "select"
                                                            value={formData.title || ""}
                                                            style={{ ...inputStyle, padding: "10px" }} // Adjust inputStyle as needed
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">-- Select Title --</option>
                                                            <option value="Mr.">Mr.</option>
                                                            <option value="Mrs.">Mrs.</option>
                                                            <option value="Ms.">Ms.</option>
                                                            <option value="Miss">Miss</option>
                                                            <option value="Dr.">Dr.</option>
                                                            <option value="Prof.">Prof.</option>
                                                            <option value="Hon.">Hon.</option>
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="name" className="form-control-label">
                                                        Name
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-hat-3" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="name"
                                                            name="name"
                                                            placeholder="Name"
                                                            type="text"
                                                            style={inputStyle}
                                                            value={formData.name || ""}
                                                            onChange={handleInputChange}
                                                        />

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>

                                        </Row>
                                        <Row>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="email" className="form-control-label">
                                                        Email
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-email-83" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="email"
                                                            placeholder="Email"
                                                            name="email"
                                                            type="email"
                                                            style={inputStyle}
                                                            autoComplete="new-email"
                                                            onChange={handleInputChange}
                                                        />

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="gender" className="form-control-label">
                                                        Gender
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="fas fa-male" style={{ marginRight: '5px' }}></i>
                                                                <i className="fas fa-female"></i>
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="gender"
                                                            name="gender"
                                                            type="select" // Change type to "select"
                                                            value={formData.gender || ""}
                                                            style={inputStyle}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">-- Select Gender --</option>
                                                            <option value="Male">Male</option>
                                                            <option value="Female">Female</option>
                                                            <option value="Other">Other</option>
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="currentSalary" className="form-control-label">
                                                        Current Salary
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-money-coins" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="currentSalary"
                                                            name="currentSalary"
                                                            placeholder="Current Salary"
                                                            type="text"
                                                            style={inputStyle}
                                                            value={formData.currentSalary || ""}
                                                            onChange={handleInputChange}
                                                            maxLength={6}
                                                        />
                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="nextIncrementDate" className="form-control-label">
                                                        Next Increment Date
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-calendar-grid-58" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="nextIncrementDate"
                                                            name="nextIncrementDate"
                                                            placeholder="Next Increment Date"
                                                            type="date"
                                                            style={inputStyle}
                                                            value={formData.nextIncrementDate || ""}
                                                            min={new Date().toISOString().split('T')[0]}
                                                            onChange={handleInputChange}
                                                        />
                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>



                                        </Row>

                                        <Row>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="division" className="form-control-label">
                                                        Select Division
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-big" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            name="divison"
                                                            id="input-division"
                                                            type="select"
                                                            value={formData.divison}
                                                            onChange={handleDivisionChange}
                                                            style={inputStyle}
                                                        >
                                                            <option value="">Select Division</option>
                                                            {division &&
                                                                division.length > 0 &&
                                                                division.map((type, index) => (
                                                                    <option key={index} value={type.divisionCode}>
                                                                        {type.name}
                                                                    </option>
                                                                ))}
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="district" className="form-control-label">
                                                        Select District
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-big" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            name="district"
                                                            id="input-district"
                                                            type="select"
                                                            value={formData.district}
                                                            onChange={handleDistrictChange}
                                                            style={inputStyle}
                                                        >
                                                            <option value="">Select District</option>
                                                            {district &&
                                                                district.length > 0 &&
                                                                district.map((type, index) => (
                                                                    <option key={index} value={type.LGDCode}>
                                                                        {type.districtNameEng}
                                                                    </option>
                                                                ))}
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>

                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="vidhanSabha" className="form-control-label">
                                                        Select Vidhan Sabha
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-big" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            name="vidhansabha"
                                                            id="input-vidhansabha"
                                                            type="select"
                                                            value={formData.vidhansabha}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">Select Vidhan Sabha</option>
                                                            {vidhansabha &&
                                                                vidhansabha.length > 0 &&
                                                                vidhansabha.map((type, index) => (
                                                                    <option
                                                                        key={index}
                                                                        value={type.ConstituencyNumber}
                                                                    >
                                                                        {type.ConstituencyName}
                                                                    </option>
                                                                ))}
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="vidhanSabha" className="form-control-label">
                                                        Work Type
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-big" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            name="workType"
                                                            id="input-workType"
                                                            type="select"
                                                            value={formData.workType}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">Select Work Type</option>
                                                            <option value="TEACHING">TEACHING</option>
                                                            <option value="NON TEACHING">NON TEACHING</option>
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col lg="3">
                                                <FormGroup>
                                                    <label className="form-control-label" htmlFor="input-Section">
                                                        Section
                                                    </label>
                                                    <MultiSelect
                                                        options={options} // Pass the mapped options
                                                        value={selectedSection} // Current selected values
                                                        onChange={setSelectedSection} // Function to update selected values
                                                        labelledBy="Select" // Label for accessibility
                                                    />

                                                </FormGroup>
                                            </Col>
                                            <Col lg='3'>

                                                <div style={{ marginTop: "20px" }}>
                                                    <strong>Selected Sections:</strong>
                                                    <ul>
                                                        {selectedSection.map((item) => (
                                                            <li key={item.value}>{item.label}</li>
                                                        ))}
                                                    </ul>
                                                </div>

                                            </Col>



                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="class" className="form-control-label">
                                                        Select Class
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-book-bookmark" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="class"
                                                            type="select"
                                                            name="classData"
                                                            style={inputStyle}
                                                            onChange={handleClassInputChange}
                                                        >
                                                            <option value="">Select Class</option>
                                                            {classData &&
                                                                classData.length > 0 &&
                                                                classData.map((type, index) => (
                                                                    <option key={index} value={type._id}>
                                                                        {type.className}
                                                                    </option>
                                                                ))}
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="designation" className="form-control-label">
                                                        Main Designation
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-briefcase-24" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="designation"
                                                            type="select"
                                                            name="designation"
                                                            style={inputStyle}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">Select Designation</option>
                                                            {designationData &&
                                                                designationData.length > 0 &&
                                                                designationData.map((type, index) => (
                                                                    <option key={index} value={type._id}>
                                                                        {type.designation}
                                                                    </option>
                                                                ))}
                                                        </Input>

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                        </Row>
                                        <Row>
                                            <Col xs="3">
                                                <FormGroup>
                                                    <Label for="onDeputation" className="form-control-label">
                                                        On Deputation
                                                    </Label>
                                                    <div>
                                                        <Label style={{ paddingLeft: "50px", paddingRight: "50px" }} check>
                                                            <Input
                                                                type="radio"
                                                                name="onDeputation"
                                                                value="true"
                                                                onChange={handleInputChange}

                                                                checked={formData.onDeputation === "true"}
                                                            />
                                                            Yes
                                                        </Label>
                                                        <Label check>
                                                            <Input
                                                                type="radio"
                                                                name="onDeputation"
                                                                value="false"
                                                                onChange={handleInputChange}

                                                                checked={formData.onDeputation === "false"}
                                                            />
                                                            No
                                                        </Label>
                                                    </div>
                                                </FormGroup>
                                            </Col>

                                            {/* Deputed Designation Field */}
                                            <Col xs="3">
                                                <FormGroup>
                                                    <Label for="deputedDesignation" className="form-control-label">
                                                        Deputed Designation
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-briefcase-24" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="deputedDesignation"
                                                            type="select"
                                                            name="deputedDesignation"
                                                            style={{ maxWidth: "100%" }}
                                                            disabled={formData.onDeputation === "false"}
                                                            value={formData.deputedDesignation}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">Select Designation</option>
                                                            {designationData &&
                                                                designationData.length > 0 &&
                                                                designationData.map((type, index) => (
                                                                    <option key={index} value={type._id}>
                                                                        {type.designation}
                                                                    </option>
                                                                ))}
                                                        </Input>
                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>

                                            {/* Main Posting Location Field */}
                                            <Col xs="3">
                                                <FormGroup>
                                                    <Label for="mainPostingLocation" className="form-control-label">
                                                        Main Posting Location
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-big" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="mainPostingLocation"
                                                            type="select"
                                                            name="mainPostingLocation"
                                                            style={inputStyle}
                                                            disabled={formData.onDeputation === "false"}
                                                            value={formData.mainPostingLocation || ""}
                                                            onChange={handleInputChange}
                                                        >
                                                            <option value="">Select Location</option>
                                                            {college &&
                                                                college.length > 0 &&
                                                                college.map((type, index) => (
                                                                    <option key={index} value={type._id}>
                                                                        {type.name}
                                                                    </option>
                                                                ))}
                                                        </Input>
                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>

                                            {/* Employee Type Field */}
                                            <Col xs="3">
                                                <FormGroup>
                                                    <Label for="employeeType" className="form-control-label">
                                                        Employee Type
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-briefcase-24" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="employeeType"
                                                            type="select"
                                                            name="employeeType"
                                                            style={{ maxWidth: "100%" }}
                                                            onChange={handleInputChange}
                                                            value={formData.employeeType}
                                                        >
                                                            <option value="">Select Employee Type</option>
                                                            <option value="DIRECTORATE">Directorate</option>
                                                            <option value="COMMISSIONER">Commissioner</option>
                                                        </Input>
                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                        </Row>
                                        <Row>

                                            <Col xs="3">
                                                <FormGroup>
                                                    <label className="form-control-label" htmlFor="input-postingLocation">
                                                        Posting Location
                                                    </label>
                                                    <Input
                                                        name="postingLocation"
                                                        id="input-postingLocation"
                                                        type="textarea"
                                                        value={formData.postingLocation}
                                                        onChange={handleInputChange}
                                                        required
                                                    >

                                                    </Input>

                                                </FormGroup>
                                            </Col>

                                            <Col xs="3"  >
                                                <FormGroup>
                                                    <Label for="address" className="form-control-label">
                                                        Address
                                                    </Label>
                                                    <InputGroup className="input-group-alternative mb-3">
                                                        <InputGroupAddon addonType="prepend">
                                                            <InputGroupText>
                                                                <i className="ni ni-map-pin" />
                                                            </InputGroupText>
                                                        </InputGroupAddon>
                                                        <Input
                                                            id="address"
                                                            placeholder="Address"
                                                            name="address"
                                                            type="textarea"
                                                            style={inputStyle}
                                                            onChange={handleInputChange}
                                                        />

                                                    </InputGroup>
                                                </FormGroup>
                                            </Col>
                                        </Row>

                                        <Button color="primary" onClick={toggleModal}>
                                            Preveiw & Submit
                                        </Button>
                                    </div>
                                </Form>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
                <Modal style={{
                    maxWidth: "1000px",
                    width: "90%",
                }} isOpen={isModalOpen} toggle={toggleModal}>
                    <ModalHeader toggle={toggleModal}><h3> Preview</h3><br />
                        <h5 className="text-danger">
                            Note - Check Carefully before Submit
                        </h5></ModalHeader>
                    <ModalBody>

                        <Row>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="empCode" className="form-control-label">
                                        Employee Code
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="empCode"
                                            name="empCode"
                                            placeholder="Employee Code"
                                            type="text"
                                            maxLength={12}
                                            style={inputStyle}
                                            value={formData.empCode || ""}
                                            onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="contact" className="form-control-label">
                                        Contact
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-mobile-button" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="contact"
                                            name="contact"
                                            placeholder="Contact"
                                            type="tel"
                                            style={inputStyle}
                                            value={formData.contact || ""}
                                            maxLength={10}
                                            disabled
                                            onChange={(e) => {
                                                const value = e.target.value;

                                                // Check if the input is empty
                                                if (value === "") {
                                                    handleInputChange(e);
                                                    return;
                                                }

                                                // Regex to match numbers starting with 6, 7, 8, or 9
                                                const validStartRegex = /^[6-9]/;

                                                if (validStartRegex.test(value)) {
                                                    // If valid, pass 'true' as the second argument to handleInputChange
                                                    handleInputChange(e);
                                                } else {
                                                    // Show alert if the input starts with invalid numbers
                                                    // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                                    SwalMessageAlert(
                                                        " Mobile number must start with digits 6, 7, 8, or 9.",
                                                        "warning"
                                                    );

                                                }
                                            }}
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="title" className="form-control-label">
                                        Title
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="title"
                                            name="title"
                                            placeholder="Mr. / Mrs. "
                                            type="text"
                                            maxLength={12}
                                            style={inputStyle}
                                            value={formData.title || ""}
                                            onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="name" className="form-control-label">
                                        Name
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="name"
                                            name="name"
                                            placeholder="Name"
                                            type="text"
                                            disabled
                                            style={inputStyle}
                                            value={formData.name || ""}
                                            onChange={handleInputChange}
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="gender" className="form-control-label">
                                        Gender
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="gender"
                                            name="gender"
                                            placeholder="Male / Female. "
                                            type="text"
                                            maxLength={12}
                                            style={inputStyle}
                                            value={formData.gender || ""}
                                            onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="email" className="form-control-label">
                                        Email
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-email-83" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="email"
                                            placeholder="Email"
                                            name="email"
                                            value={formData.email}
                                            type="email"
                                            style={inputStyle}
                                            autoComplete="new-email"
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="nextIncrementDate" className="form-control-label">
                                        Next Increment Date
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="nextIncrementDate"
                                            name="nextIncrementDate"
                                            placeholder="Date"
                                            type="nextIncrementDate"
                                            maxLength={12}
                                            style={inputStyle}
                                            value={formData.nextIncrementDate || ""}
                                            onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>

                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="currentSalary" className="form-control-label">
                                        Basic Salary
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-hat-3" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="currentSalary"
                                            name="currentSalary"
                                            placeholder="Date"
                                            type="currentSalary"
                                            maxLength={12}
                                            style={inputStyle}
                                            value={formData.currentSalary || ""}
                                            onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>



                        </Row>

                        <Row>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="division" className="form-control-label">
                                        Select Division
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-big" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            name="divison"
                                            id="input-division"
                                            type="select"
                                            disabled
                                            value={formData.divison}
                                            style={inputStyle}
                                        >
                                            <option value="">Select Division</option>
                                            {division &&
                                                division.length > 0 &&
                                                division.map((type, index) => (
                                                    <option key={index} value={type.divisionCode}>
                                                        {type.name}
                                                    </option>
                                                ))}
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="district" className="form-control-label">
                                        Select District
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-big" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            name="district"
                                            id="input-district"
                                            type="select"
                                            disabled
                                            value={formData.district}
                                            // onChange={handleDistrictChange}
                                            style={inputStyle}
                                        >
                                            <option value="">Select District</option>
                                            {district &&
                                                district.length > 0 &&
                                                district.map((type, index) => (
                                                    <option key={index} value={type.LGDCode}>
                                                        {type.districtNameEng}
                                                    </option>
                                                ))}
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>

                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="vidhanSabha" className="form-control-label">
                                        Select Vidhan Sabha
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-big" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            name="vidhansabha"
                                            id="input-vidhansabha"
                                            type="select"
                                            value={formData.vidhansabha}
                                            // onChange={handleInputChange}
                                            disabled
                                        >
                                            <option value="">Select Vidhan Sabha</option>
                                            {vidhansabha &&
                                                vidhansabha.length > 0 &&
                                                vidhansabha.map((type, index) => (
                                                    <option
                                                        key={index}
                                                        value={type.ConstituencyNumber}
                                                    >
                                                        {type.ConstituencyName}
                                                    </option>
                                                ))}
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="vidhanSabha" className="form-control-label">
                                        Work Type
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-big" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            name="workType"
                                            id="input-workType"
                                            type="select"
                                            value={formData.workType}
                                            disabled
                                        // onChange={handleInputChange}
                                        >
                                            <option value="">Select Work Type</option>
                                            <option value="TEACHING">TEACHING</option>
                                            <option value="NON TEACHING">NON TEACHING</option>
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                        </Row>
                        <Row>
                            <Col lg="3">
                                <FormGroup>
                                    <label className="form-control-label" htmlFor="input-Section">
                                        Section
                                    </label>
                                    <MultiSelect
                                        disabled
                                        options={options} // Pass the mapped options
                                        value={selectedSection} // Current selected values
                                        // onChange={setSelectedSection} // Function to update selected values
                                        labelledBy="Select" // Label for accessibility
                                    />

                                </FormGroup>
                            </Col>
                            <Col lg='3'>

                                <div style={{ marginTop: "20px" }}>
                                    <strong>Selected Sections:</strong>
                                    <ul>
                                        {selectedSection.map((item) => (
                                            <li key={item.value}>{item.label}</li>
                                        ))}
                                    </ul>
                                </div>

                            </Col>




                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="class" className="form-control-label">
                                        Select Class
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-book-bookmark" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="class"
                                            type="select"
                                            name="classData"
                                            value={formData.classData}
                                            style={inputStyle}
                                            // onChange={handleClassInputChange}
                                            disabled
                                        >
                                            <option value="">Select Class</option>
                                            {classData &&
                                                classData.length > 0 &&
                                                classData.map((type, index) => (
                                                    <option key={index} value={type._id}>
                                                        {type.className}
                                                    </option>
                                                ))}
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="designation" className="form-control-label">
                                        Designation
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-briefcase-24" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="designation"
                                            type="select"
                                            name="designation"
                                            style={inputStyle}
                                            value={formData.designation}
                                            // onChange={handleInputChange}
                                            disabled
                                        >
                                            <option value="">Select Designation</option>
                                            {designationData &&
                                                designationData.length > 0 &&
                                                designationData.map((type, index) => (
                                                    <option key={index} value={type._id}>
                                                        {type.designation}
                                                    </option>
                                                ))}
                                        </Input>

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                        </Row>
                        <Row>
                            <Col xs="3">
                                <FormGroup>
                                    <Label for="onDeputation" className="form-control-label">
                                        On Deputation
                                    </Label>
                                    <div>
                                        <Label style={{ paddingLeft: "50px", paddingRight: "50px" }} check>
                                            <Input
                                                type="radio"
                                                name="onDeputation"
                                                value="true"
                                                onChange={handleInputChange}
                                                disabled
                                                checked={formData.onDeputation === "true"}
                                            />
                                            Yes
                                        </Label>
                                        <Label check>
                                            <Input
                                                type="radio"
                                                name="onDeputation"
                                                value="false"
                                                disabled

                                                onChange={handleInputChange}

                                                checked={formData.onDeputation === "false"}
                                            />
                                            No
                                        </Label>
                                    </div>
                                </FormGroup>
                            </Col>

                            {/* Deputed Designation Field */}
                            <Col xs="3">
                                <FormGroup>
                                    <Label for="deputedDesignation" className="form-control-label">
                                        Deputed Designation
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-briefcase-24" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="deputedDesignation"
                                            type="select"
                                            name="deputedDesignation"
                                            style={{ maxWidth: "100%" }}

                                            value={formData.deputedDesignation}
                                            disabled

                                            onChange={handleInputChange}
                                        >
                                            <option value="">Select Designation</option>
                                            {designationData &&
                                                designationData.length > 0 &&
                                                designationData.map((type, index) => (
                                                    <option key={index} value={type._id}>
                                                        {type.designation}
                                                    </option>
                                                ))}
                                        </Input>
                                    </InputGroup>
                                </FormGroup>
                            </Col>

                            {/* Main Posting Location Field */}
                            <Col xs="3">
                                <FormGroup>
                                    <Label for="mainPostingLocation" className="form-control-label">
                                        Main Posting Location
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-big" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="mainPostingLocation"
                                            type="select"
                                            name="mainPostingLocation"
                                            style={inputStyle}
                                            disabled
                                            value={formData.mainPostingLocation || ""}
                                            onChange={handleInputChange}
                                        >
                                            <option value="">Select Location</option>
                                            {college &&
                                                college.length > 0 &&
                                                college.map((type, index) => (
                                                    <option key={index} value={type._id}>
                                                        {type.name}
                                                    </option>
                                                ))}
                                        </Input>
                                    </InputGroup>
                                </FormGroup>
                            </Col>

                            {/* Employee Type Field */}
                            <Col xs="3">
                                <FormGroup>
                                    <Label for="employeeType" className="form-control-label">
                                        Employee Type
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-briefcase-24" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="employeeType"
                                            type="select"
                                            name="employeeType"
                                            style={{ maxWidth: "100%" }}
                                            disabled
                                            onChange={handleInputChange}
                                        >
                                            <option value="">Select Employee Type</option>
                                            <option value="DIRECTORATE">Directorate</option>
                                            <option value="COMMISSIONER">Commissioner</option>
                                        </Input>
                                    </InputGroup>
                                </FormGroup>
                            </Col>
                        </Row>
                        <Row>
                            <Col xs="3">
                                <FormGroup>
                                    <label className="form-control-label" htmlFor="input-postingLocation">
                                        Posting Location
                                    </label>
                                    <Input
                                        name="postingLocation"
                                        id="input-postingLocation"
                                        type="textarea"
                                        value={formData.postingLocation}
                                        // onChange={handleInputChange}
                                        disabled
                                        required
                                    >

                                    </Input>

                                </FormGroup>
                            </Col>
                            <Col xs="3"  >
                                <FormGroup>
                                    <Label for="address" className="form-control-label">
                                        Address
                                    </Label>
                                    <InputGroup className="input-group-alternative mb-3">
                                        <InputGroupAddon addonType="prepend">
                                            <InputGroupText>
                                                <i className="ni ni-map-pin" />
                                            </InputGroupText>
                                        </InputGroupAddon>
                                        <Input
                                            id="address"
                                            placeholder="Address"
                                            name="address"
                                            type="textarea"
                                            style={inputStyle}
                                            value={formData.address}
                                            // onChange={handleInputChange}
                                            disabled
                                        />

                                    </InputGroup>
                                </FormGroup>
                            </Col>
                        </Row>
                    </ModalBody>
                    <ModalFooter>
                        <Button color="primary" onClick={handleSubmit}>
                            Submit
                        </Button>
                        <Button color="secondary" onClick={toggleModal}>
                            Cancel
                        </Button>
                    </ModalFooter>
                </Modal>
            </Container>
        </>
    );
};

export default Director;
