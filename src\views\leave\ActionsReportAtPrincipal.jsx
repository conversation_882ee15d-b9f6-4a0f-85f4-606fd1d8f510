import { useState, useEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ooter,
    Container,
    Row,
    Col,
    Input,
    Table,
    Badge,
    Pagination,
    PaginationItem,
    PaginationLink,
    Modal, ModalHeader, ModalBody, ModalFooter,

} from "reactstrap";
import { BsDownload, BsEye, } from 'react-icons/bs';
import Header from "../../components/Headers/Header.jsx";
import { Spinner } from "reactstrap";
import { Link } from 'react-router-dom';
import Swal from 'sweetalert2';
import axios from "axios";
import formatDate from "../../utils/formateDate.jsx";

const ActionsReport = () => {


    const token = sessionStorage.getItem("authToken");
    const endPoint = import.meta.env.VITE_API_URL;
    const ClgId = sessionStorage.getItem("id");


    //#region  For Pegination
    const [currentPage, setCurrentPage] = useState(1);

    const [applications, setApplications] = useState([]);
    const [filteredApplications, setFilteredApplications] = useState([]);

    const [itemsPerPage, setRecordsPerPage] = useState(5); // Default value

    const handleChange = (event) => {
        const value = Number(event.target.value);
        setRecordsPerPage(value);
    };



    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredApplications.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(applications.length / itemsPerPage);
    const [selectedStatus, setSelectedStatus] = useState(null);
    const [modal, setModal] = useState(false);
    const [currentStep, setCurrentStep] = useState(0);

    const [searchTerm, setSearchTerm] = useState('');



    useEffect(() => {
        const fetchApplications = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/leave/employees_leave/${ClgId}`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                if (response.status === 200) {
                    const data = response.data.reverse();
                    // // console.log(data, "Getting Data here ActionsReport");
                    const isPrincipalAction = data.filter(
                        (leave) => leave.isPrincipalAction === 1
                    );

                    setApplications(isPrincipalAction);

                    // window.location.replace("admin/Dashboard");
                } else {
                    alert("Data Not Fetched.");
                    // navigate('/auth/Register');
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchApplications();
    }, [ClgId, endPoint, token]);


    useEffect(() => {
        const filteredItems = applications.filter(item => {
            return Object.keys(item).some(key =>
                String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
            );
        });
        setFilteredApplications(filteredItems);
        setCurrentPage(1); // Reset to first page on filter change
    }, [searchTerm, applications]);


    const handleFilterChange = (Id) => {
        const status = parseInt(Id);
        // console.log(status);

        setSelectedStatus(status);
        if (status) {
            const filtered = applications.filter((app) => app.leaveStatus === status);
            setFilteredApplications(filtered);
        } else {
            setFilteredApplications(applications); // Reset to all applications if no filter
        }
        setCurrentPage(1); // Reset to first page on filter change
    };


    const getStatusCount = (status) => {
        return applications.filter(app => app.leaveStatus === status).length;
    };



    const [movement, setMovement] = useState('');
    const [checkMovement, setCheckMovement] = useState(false);
    const [applicationForMovement, setApplicationForMovement] = useState([]);

    const toggleMovement = async (item) => {
        // console.log("Getting Application ID", item);
        setApplicationForMovement(item)

        try {
            const response = await axios.get(`${endPoint}/api/leave/movement/${item._id}`,
                {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
            if (response.status === 200) {
                const data = response.data.reverse();
                // console.log(data, "Getting Data");
                setMovement(data)


            } else {
                alert("Data Not Fetched.");
            }
        } catch (error) {
            console.error("An error occurred while Getting Data:", error);
            alert("An error occurred. Please try again later.");
        }

        // setApplicationForMovement(item);
        setCheckMovement(!checkMovement);
    }




    const handlePageChange = (pageNumber) => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber);
        }
    };

    const toggle = (status) => {
        setModal(!modal);
        setSelectedStatus(status);
    };

    const statusCode = selectedStatus;

    useEffect(() => {
        if (statusCode >= 1 && statusCode <= 3) {
            setCurrentStep(statusCode - 1); // Convert status code to zero-based index
        }
    }, [statusCode]);

    const handleShowDetails = (remark1, remark2, remark3) => {
        // console.log("Getting Users Remark,", remark1);
        // console.log("Getting Principle Remark,", remark2);
        // console.log("Getting Director Remark,", remark3);

        Swal.fire({
            html: `<div style="text-align: left; max-height: 300px; overflow-y: auto;"><h3><b>Remark By User</h3><p> ${remark1}<p>
           </br> <h3><b>Remark By Principal</b></h3><p> ${remark2}<p>
           </br> <h3><b>Remark By Directorate</b></h3><p> ${remark3}<p></div>`,
            showCloseButton: true,
            confirmButtonText: 'Close',
        });
    };


    const steps = [
        { label: "Employee", icon: "fa-user" },
        { label: "Office", icon: "fa-solid fa-briefcase" },
        { label: "Directorate", icon: "fa-solid fa-user-shield" }
    ];

    const statuses = [
        { valueOf: "", label: `All (${applications.length})`, color: "blue" },
        { valueOf: "1", label: `Pending (${getStatusCount(1)})`, color: "orange" },
        { valueOf: "2", label: `Forwarded (${getStatusCount(2)})`, color: "gray" },
        { valueOf: "3", label: `Approved (${getStatusCount(3)})`, color: "darkgreen" },
        { valueOf: "4", label: `Rejected (${getStatusCount(4)})`, color: "red" },
        { valueOf: "5", label: `Cancelled (${getStatusCount(5)})`, color: "skyblue" },
        { valueOf: "6", label: `Applied for Cancel (${getStatusCount(6)})`, color: "orange" },
    ];


    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Row>
                    <div className="col">
                        <Card className="shadow">
                            <CardHeader className="border-0">
                                <h3 className="mb-0">Action Reports / कार्यवाही सूची</h3>
                                <Row className="mt-3">
                                    <Col>
                                        {statuses.map((status) => (
                                            <button className="btn-sm"
                                                key={status.valueOf}
                                                onClick={() => handleFilterChange((status.valueOf))}
                                                style={{
                                                    backgroundColor: status.color,
                                                    color: "white",
                                                    border: "none",
                                                    borderRadius: "5px",
                                                    padding: "10px",
                                                    margin: "5px",
                                                    cursor: "pointer",
                                                    fontWeight: "bolder",
                                                }}
                                            >
                                                {status.label}
                                            </button>
                                        ))}
                                    </Col>
                                    <Col md={3}>
                                        <Input
                                            type="text"
                                            placeholder="Search..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            style={{ marginBottom: '10px' }}
                                        />
                                    </Col>
                                </Row>
                            </CardHeader>

                            <Table className="align-items-center table-flush" responsive>
                                <thead className="thead-light">
                                    <tr>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Sno. <br />/ क्र.</th>
                                        {/* <th>ApplicantId.<br />(आवेदक आई.डी.)</th> */}

                                        <th style={{ color: "black", fontWeight: "bolder" }}>File Movement / Remark <br /> / View / Download <br /> Action Letter</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Status <br />स्थिति </th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Application <br /> Details <br /> (आवेदन जानकारी )</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Leave Details <br /> (अवकाश जानकारी)</th>
                                        <th style={{ color: "black", fontWeight: "bolder" }}>Date <br /> Between <br /> (दिनांक से - तक)</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    {currentItems.map((application, index) => (
                                        <tr key={application.id}>
                                            <td><strong>{indexOfFirstItem + index + 1}</strong></td>
                                            {/* <td>{application.applicantId}</td> */}

                                            <td> <button
                                                className="btn-sm m-2 btn-info"
                                                onClick={() => toggleMovement(application)}
                                            >
                                                Movement
                                            </button>

                                                <button
                                                    className="btn btn-sm m-2 btn-primary"
                                                    onClick={() => handleShowDetails(application.remarkByUser, application.remarkByPrincipal, application.remarkByDirector)}>
                                                    Remark
                                                </button>

                                                <br />

                                                <Link to={`/admin/Preview/${application._id}`}>
                                                    <Button className="btn-sm m-2"
                                                        style={{ backgroundColor: "yellow" }}
                                                    ><BsDownload size={18} /><BsEye size={18} /></Button>
                                                </Link>

                                                <a  href={`https://heonline.cg.nic.in/${application.uploadFile}`}download>
                                                    <Button className="btn btn-sm m-2 btn-primary">
                                                        
                                                        {application.uploadFile ? <BsDownload size={18} /> : "No File"}
                                                    </Button>
                                                </a>

                                                <br />
                                                {
                                                    application.recieptGenrated === true &&
                                                    < Link to={`/admin/Genrated/${application._id}`}>
                                                        <Button className=" btn btn-sm m-2" style={{ backgroundColor: "orange" }}>
                                                            <BsDownload size={18} />
                                                            <BsEye size={18} />
                                                        </Button>
                                                    </Link>
                                                }

                                            </td>
                                            <td>
                                                <h2>
                                                    <Badge

                                                        className="badge-sm"
                                                        onClick={application.leaveStatus === 1 || application.leaveStatus === 2 ? () => toggle(application.leaveStatus) : null}
                                                        style={{
                                                            fontWeight: 'bolder',
                                                            color: 'white',
                                                            backgroundColor:
                                                                application.leaveStatus === 3
                                                                    ? 'green'  // Approved background color
                                                                    : application.leaveStatus === 4
                                                                        ? 'red'   // Rejected background color
                                                                        : (application.leaveStatus === 1)
                                                                            ? '#f3c70c'  // Pending background color

                                                                            : application.leaveStatus === 5
                                                                                ? 'blue'
                                                                                : (application.leaveStatus === 2)
                                                                                    ? '#ff8a00'
                                                                                    : (application.leaveStatus === 6)
                                                                                        ? '#ff8a00'
                                                                                        : 'lavender'
                                                        }}
                                                    >
                                                        {application.leaveStatus === 3
                                                            ? 'Approved'
                                                            : application.leaveStatus === 4
                                                                ? 'Rejected'
                                                                : application.leaveStatus === 6
                                                                    ? 'approve Cancel'
                                                                    : (application.leaveStatus === 1)
                                                                        ? (
                                                                            <>
                                                                                <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                                Pending
                                                                            </>
                                                                        )
                                                                        : (application.leaveStatus === 2)
                                                                            ? (
                                                                                <>
                                                                                    {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                    Forwarded
                                                                                </>
                                                                            )
                                                                            : application.leaveStatus === 5
                                                                                ? 'Cancelled'
                                                                                : 'Error'}
                                                    </Badge>

                                                </h2>
                                            </td>


                                            <td> <strong> Name : </strong> {application.applicantName} <br /> <strong> Application ID : </strong>{application.applicationId} <br /><strong>Applied Date :</strong>{formatDate(application.appliedDate)} <br /><strong>Emp Code :</strong> {application.empCode}</td>
                                            <td><strong>Type : </strong>{application.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : application.leaveType} <br /><strong>Reason :</strong>{application.reason}</td>
                                            <td><strong>({formatDate(application.fromDate)}) <br /> to <br /> ({formatDate(application.tillDate)}) <br /> <span style={{ fontWeight: "bolder", color: "red" }}>{application.dayCount} Days</span></strong></td>

                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <Modal style={{ maxWidth: "1000px", minWidth: "400px" }} isOpen={checkMovement} toggle={toggleMovement}>
                                <ModalHeader toggle={toggleMovement}><h3>File Movement Of ({applicationForMovement.applicationId}) </h3></ModalHeader>
                                <ModalBody>

                                    <Table className="align-items-center table-flush" responsive bordered>
                                        <thead className="thead-light">
                                            <tr>
                                                <th>Sno./ क्र.</th>
                                                <th>Action/ <br />Sent By</th>
                                                <th>Section Name</th>
                                                <th>Action Date</th>
                                                <th>Day Taken</th>
                                                <th>Currently With</th>
                                                <th>Status</th>
                                                <th>Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {movement && movement.length > 0 ? movement.map((item, index) =>
                                            (
                                                <tr key={item._id}>
                                                    <td className="text-center">{index + 1}</td>
                                                    <td className="text-left">{item.directorName}</td>
                                                    <td className="text-left">{item.sectionName}</td>
                                                    <td className="text-center">{formatDate(item.actionDate)}</td>
                                                    <td className="text-center">{item.dayTaken}</td>
                                                    <td className="text-center"><strong style={{ fontWeight: "bolder", fontSize: "14px" }}>
                                                        {item.sendToDirector}
                                                    </strong>
                                                        {index === 0 && (
                                                            <i
                                                                className="fa fa-check fa-xl"
                                                                style={{ marginLeft: "10px", color: "green" }}
                                                                aria-hidden="true"
                                                            ></i>
                                                        )}
                                                    </td>
                                                    <td>
                                                        <h2>
                                                            <Badge

                                                                className="badge-sm"
                                                                onClick={item.status === 1 || item.status === 2 ? () => toggle(item.status) : null}
                                                                style={{
                                                                    fontWeight: 'bolder',
                                                                    color: 'white',
                                                                    backgroundColor:
                                                                        item.status === 3 && item.isRecieptGenrated === true
                                                                            ? 'green'  // Approved background color
                                                                            : item.status === 3 && item.isRecieptGenrated === false
                                                                                ? '#ff8a00'
                                                                                : item.status === 4 && item.isRecieptGenrated === true
                                                                                    ? 'red'   // Rejected background color
                                                                                    : item.status === 4 && item.isRecieptGenrated === false
                                                                                        ? '#ff8a00'
                                                                                        : (item.status === 1)
                                                                                            ? '#f3c70c'  // Pending background color
                                                                                            : (item.status === 6)
                                                                                                ? '#ff8a00'
                                                                                                : item.status === 5
                                                                                                    ? 'blue'
                                                                                                    : (item.status === 2)
                                                                                                        ? '#ff8a00'
                                                                                                        : 'lavender'
                                                                }}
                                                            >
                                                                {item.status === 3 && item.isRecieptGenrated === true
                                                                    ? 'Approved'
                                                                    : item.status === 3 && item.isRecieptGenrated === false
                                                                        ? 'Actioned by Director'
                                                                        : item.status === 4
                                                                            ? 'Rejected'
                                                                            : item.status === 4 && item.isRecieptGenrated === false
                                                                                ? 'Actioned by Director'
                                                                                : item.status === 6
                                                                                    ? 'approve Cancel'
                                                                                    : (item.status === 1)
                                                                                        ? (
                                                                                            <>
                                                                                                <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />
                                                                                                Pending at principal
                                                                                            </>
                                                                                        )
                                                                                        : (item.status === 2)
                                                                                            ? (
                                                                                                <>
                                                                                                    {/* <Spinner size="sm" color="white" style={{ marginRight: '8px' }} /> */}
                                                                                                    Forwarded
                                                                                                </>
                                                                                            )
                                                                                            : item.status === 5
                                                                                                ? 'Cancelled'
                                                                                                : 'Error'}
                                                            </Badge>

                                                        </h2>
                                                    </td>
                                                    <td className="text-left">{item.remarksFileMovment}</td>
                                                </tr>
                                            )) : <tr>
                                                <td colSpan="6" className="text-center">No Data Found</td>
                                            </tr>}
                                        </tbody>
                                    </Table>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggleMovement}>
                                        Close
                                    </Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <Modal isOpen={modal} toggle={toggle}>
                                <ModalHeader toggle={toggle}>Status</ModalHeader>
                                <ModalBody>
                                    <div className="main">
                                        <ul style={{ display: 'flex', justifyContent: 'space-around' }}>
                                            {steps.map((step, index) => (
                                                <li key={index} style={{ listStyle: 'none', display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                                                    <i className={`icons awesome fa-solid ${step.icon}`} style={{ fontSize: '25px', color: '#1b761b' }}></i>
                                                    <div
                                                        className={`step ${index === currentStep ? 'active' : ''}`}
                                                        style={{
                                                            height: '30px',
                                                            width: '30px',
                                                            borderRadius: '50%',
                                                            backgroundColor: index <= currentStep ? '#1b761b' : '#d7d7c3',
                                                            margin: '16px 0 10px',
                                                            display: 'grid',
                                                            placeItems: 'center',
                                                            color: 'ghostwhite',
                                                            position: 'relative',
                                                            cursor: 'default' // Change cursor to default since clicking is disabled
                                                        }}
                                                    >
                                                        <p style={{ fontSize: '18px', display: index <= currentStep ? 'none' : 'block' }}>{index + 1}</p>
                                                        <i className="awesome fa-solid fa-check" style={{ display: index <= currentStep ? 'flex' : 'none' }}></i>
                                                    </div>
                                                    <p className="label" style={{ fontFamily: 'sans-serif', letterSpacing: '1px', fontSize: '14px', fontWeight: 'bold', color: '#1b761b' }}>{step.label}</p>
                                                </li>
                                            ))}
                                        </ul>
                                    </div>
                                </ModalBody>
                                <ModalFooter>
                                    <Button color="secondary" onClick={toggle}>Close</Button>
                                    {/* <Button color="primary" disabled={currentStep >= steps.length - 1}>Next</Button> */}
                                </ModalFooter>
                            </Modal>
                            <CardFooter className="py-4">
                                <div>
                                    <label htmlFor="recordsPerPage">No. of Records Per Page: </label>
                                    <select
                                        id="recordsPerPage"
                                        value={itemsPerPage}
                                        onChange={handleChange}
                                    >
                                        <option value={5}>5</option>
                                        <option value={10}>10</option>
                                        <option value={20}>20</option>
                                        <option value={50}>50</option>
                                    </select>
                                </div>
                                <nav aria-label="...">
                                    <Pagination className="pagination justify-content-end mb-0">
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem key={i} className={currentPage === i + 1 ? 'active' : ''}>
                                                <PaginationLink
                                                    href="#"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(i + 1);
                                                    }}
                                                >
                                                    {i + 1}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                    </Pagination>
                                </nav>
                            </CardFooter>
                        </Card>
                    </div>
                </Row>
            </Container>
        </>
    );
};

export default ActionsReport;