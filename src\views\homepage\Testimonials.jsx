import React from 'react';
import { Container } from 'react-bootstrap';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay, Pagination } from 'swiper/modules';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';

import './testimonial.css';
import './custom.css';
import img from "/radhakrishanan.jpg";

const Testimonial = () => {
  const testimonials = [
    {
      name: " Dr. <PERSON>. ",
      text: "The end product of education should be a free, creative man who can battle against historical circumstances and adversities of nature!",
      image: "/radhakrishanan.jpg"
    },
    {
      name: "Demo Name",
      text: "Very happy to find this institute! After graduation, we were very worried about our children. What will he do as he does not have any work experience? But when he joined this ABC institute, he became so professional!",
      image: "/testomonial_1.jpeg"
    }
    // Add more testimonials as needed
  ];

  return (
    <Container className='my-5'>
      <h2 className="text-center text-warning">Our Testimonials</h2>
      {/* <h5 className="text-center text-info"><strong >What parents Say About Us?</strong></h5> */}

      <Swiper
        modules={[Autoplay, Pagination]}
        spaceBetween={50}
        slidesPerView={1}
        loop={true}
        autoplay={{
          delay: 3000,
          disableOnInteraction: false,
          pauseOnMouseEnter: true
        }}
        pagination={{
          clickable: true,
          dynamicBullets: true
        }}
        className="testimonial-slider"
      >
        {testimonials.map((testimonial, index) => (
          <SwiperSlide key={index}>
            <div className="testimonial-card">
              <div className="testimonial-content">
                <div className="testimonial-image">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="rounded-circle"
                    style={{ width: '150px', height: '150px', objectFit: 'cover' }}
                  />
                </div>
                <div className="testimonial-text">
                  <h4>{testimonial.name}</h4>
                  <p className="text-muted">{testimonial.position}</p>
                  <p>"{testimonial.text}"</p>
                </div>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
    </Container>
  );
};

export default Testimonial;