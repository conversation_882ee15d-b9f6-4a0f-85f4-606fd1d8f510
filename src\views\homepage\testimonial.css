.testimonial-slider {
  padding: 2rem 0;
}

.testimonial-card {
  padding: 2rem;
  text-align: center;
}

.testimonial-content {
  max-width: 800px;
  margin: 0 auto;
}


.testimonial-image {
  margin-bottom: 1.5rem;
}

.testimonial-text {
  margin-top: 1rem;
}

.testimonial-text p {
  font-size: 1.1rem;
  line-height: 1.6;
}

/* Swiper pagination styles */
.swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  background: #999;
  opacity: 0.5;
}

.swiper-pagination-bullet-active {
  opacity: 1;
  background: #000;
}
.testimonial-card {
  background-color: #e6ebf0; /* Change to your desired color */
  /* padding: 20px; Add some padding for aesthetics */
  border-radius: 10px; /* Optional: for rounded corners */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Optional: for a subtle shadow effect */
}