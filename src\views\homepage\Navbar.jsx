// Navbar.jsx
import React from 'react';
import { Navbar, Nav, NavItem, NavLink } from 'reactstrap';
//import logo from './assets/logo.png'; // Add your logo image here

const MyNavbar = () => {
  return (
    <Navbar color="dark" dark expand="md" className="px-4">
      <img src={logo} alt="Logo" style={{ width: '50px', height: '50px' }} />
      <h2 className="text-white mx-3">HAMAR Chhattisgarh</h2>
      <Nav className="ms-auto" navbar>
        <NavItem>
          <NavLink href="#">Home</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Introduction</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Government</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Online Services</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Gallery</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Chhattisgarh Tourism</NavLink>
        </NavItem>
        <NavItem>
          <NavLink href="#">Contact Us</NavLink>
        </NavItem>
      </Nav>
    </Navbar>
  );
};

export default MyNavbar;
