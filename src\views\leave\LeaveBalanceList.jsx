import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Container,
  Col,
  Input,
  PaginationLink,
  PaginationItem,
  Pagination,
  Badge,
  Row,
  Table,
  
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";

const LeaveBalanceList = () => {
  const clgId = sessionStorage.getItem("id");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const [balance, setBalance] = useState([]);
  const [filteredBalance, setFilteredBalance] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [codeFilter, setCodeFilter] = useState("");
  const [nameFilter, setNameFilter] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setRecordsPerPage] = useState(5); // Default value
const [searchInput, setSearchInput] = useState("");

  const [isTribal, setIsTribal] = useState(false);

  const handleChange = (event) => {
    const value = Number(event.target.value);
    setRecordsPerPage(value);
  };
    useEffect(() => {
        const getCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-single-college/${clgId}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data;
                    setIsTribal(data.isTribal);
                    // console.log(data.isTribal, "data.isTribal");

                }
            } catch (error) {
                console.error("Error fetching college data:", error);
                alert("Failed to load college data.");
            }
        };

        getCollege(); // Call the function inside useEffect
    }, [clgId, endPoint, token]); // Dependencies


    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredBalance.slice(
        indexOfFirstItem,
        indexOfLastItem
    );
    const totalPages = Math.ceil(filteredBalance.length / itemsPerPage);

    const handlePageChange = (pageNumber) => {
        if (pageNumber >= 1 && pageNumber <= totalPages) {
            setCurrentPage(pageNumber);
    }
  };



    useEffect(() => {
        const fetchBalance = async () => {
            try {
                      const params = {};
       
            if (codeFilter.trim() !== "") {
              params.empCode = codeFilter.trim();
            }
          if (nameFilter.trim() !== "") {
              params.name = nameFilter.trim();
            }
                const response = await axios.get(`${endPoint}/api/leave/BalanceUsers/${clgId}`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        },
                       params, 
                    });
                if (response.status === 200) {

                    const data = response.data.reverse()
                    console.log("");
                    
                    setBalance(data);
                    setFilteredBalance(data);
                } else {
                    alert("Data Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        fetchBalance();
    }, [endPoint, token, clgId , codeFilter,nameFilter]);



  useEffect(() => {
    const filteredItems = balance.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilteredBalance(filteredItems);
  }, [searchTerm, balance]);

  const pendingForUpdateBalance = balance.filter(
    (item) => item.isUpdated === 0
  );

 const [empName, setEmpName] = useState([]); // holds all data
  const [filteredEmpList, setFilteredEmpList] = useState([]);
  

useEffect(() => {
  const empData = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/empName`, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        } 
      });

      if (response.status === 200) {
        setEmpName(response.data);
      } else {
        alert("Data Not Fetched.");
      }
    } catch (error) {
      console.error("An error occurred while Getting Data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  empData();
}, [endPoint, token, clgId]);


  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <div className="col">
            <Card className="shadow mb-5">
              <CardHeader className="border-0">
                {" "}
                <Row className="mt-3 d-flex">
                  <Col xl={4}>
                    <h3 className="mb-0">
                      Update Leave Balance/ अवकाश खाता सुधारे
                    </h3>
                  </Col>
                  <Col xl={4}>
                    <h3 className="text-danger">
                      {" "}
                      <Badge
                        style={{
                          color: "white",
                          backgroundColor: "blue",
                          fontSize: "15px",
                          fontWeight: "bold",
                        }}
                      >
                        Pending - {pendingForUpdateBalance.length}
                      </Badge>{" "}
                    </h3>
                  </Col>
                </Row>
                <Row className="mt-3">
                  <Col md={3}>
                    {/* <Input
                      type="text"
                      placeholder="Search by Employee Name / Code"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    /> */}
                   <Input
                      type="text"
                      placeholder="Search by Employee Code"
                      value={codeFilter}
                      onChange={(e) => setCodeFilter(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    /> 
                  </Col>
                  <Col md={3}>
                   <Input
                      type="text"
                      placeholder="Search by Employee Name "
                      value={nameFilter}
                      onChange={(e) => setNameFilter(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    /> 
                  </Col>
                </Row>
              </CardHeader>

              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                    <th>
                      Sno. <br />/ क्र.
                    </th>
                    <th>
                      Update <br /> (अपडेट करें)
                    </th>
                    <th>
                      Employee Code.
                      <br />
                      (कर्मचारी कोड.) <br /> Name / <br /> नाम
                    </th>
                    <th>
                      Leave Type / Balance <br /> (अवकाश प्रकार / शेष )
                    </th>
                    <th>
                      Last Updated date
                      <br /> (अतिम अपडेट दिनांक)
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {currentItems &&
                    currentItems.length > 0 &&
                    currentItems.map((balance, index) => (
                      <tr key={balance._id}>
                        <td>{indexOfFirstItem + index + 1}</td>
                        <td>
                          <Link
                            to={`/admin/addLeaveBalanceByPrincipal/${balance.employee}`}
                          >
                            <Button
                              className="btn-sm"
                              style={{
                                backgroundColor: "darkorange",
                                color: "white",
                              }}
                            >
                              Edit
                            </Button>
                          </Link>
                        </td>
                        <td>
                          <span style={{ fontWeight: "bolder" }}>
                            {balance.empId? balance.empId: Array.isArray(empName)? empName.find((emp) => emp._id === balance.employee)?.empCode || "" : ""}
                            <br />
                            
                          {(Array.isArray(empName) && empName.find(cls => cls._id === balance.employee)?.name) || ""}
 
                          </span>
                        </td>
                        <td>
                          <span
                            style={{ fontWeight: "bolder", color: "green" }}
                          >
                            Optional Leave - {balance.restrictedHoliday}
                          </span>
                          <br />
                          <span
                            style={{ fontWeight: "bolder", color: "purple" }}
                          >
                            Casual Leave - {balance.casualLeave}
                          </span>
                          <br />
                          <span
                            style={{ fontWeight: "bolder", color: "orange" }}
                          >
                            Earned Leave - {balance.earnedLeave}
                          </span>
                          <br />
                          <span
                            style={{ fontWeight: "bolder", color: "skyblue" }}
                          >
                            Half Pay Leave - {balance.halfPayLeave}
                          </span>
                          {isTribal && isTribal === true && (
                            <>
                              <br />
                              <span
                                style={{ fontWeight: "bolder", color: "green" }}
                              >
                                Tribal Earned Leave -{" "}
                                {balance.tribalEarnedLeave}
                              </span>
                              <br />
                              <span
                                style={{ fontWeight: "bolder", color: "black" }}
                              >
                                Tribal Casual Leave -{" "}
                                {balance.tribalCasualLeave}
                              </span>
                            </>
                          )}
                        </td>
                        {balance.lastUpdated ? (
                          <td>
                            {formatDate(balance.lastUpdated)} (
                            {new Date(balance.lastUpdated).toLocaleTimeString()}
                            )
                          </td>
                        ) : (
                          <td>
                            {" "}
                            <Badge
                              style={{
                                backgroundColor: "red",
                                color: "white",
                                fontWeight: "bolder",
                                fontSize: "11px",
                              }}
                            >
                              Not Updated{" "}
                            </Badge>
                          </td>
                        )}
                      </tr>
                    ))}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <div>
                  <label htmlFor="recordsPerPage">
                    {" "}
                    <span style={{ fontWeight: "bold" }}>
                      {" "}
                      No. of Records Per Page:{" "}
                    </span>
                  </label>
                  <select
                    id="recordsPerPage"
                    value={itemsPerPage}
                    onChange={handleChange}
                  >
                    <option value={5}>5</option>
                    <option value={10}>10</option>
                    <option value={20}>20</option>
                    <option value={50}>50</option>
                  </select>
                </div>
                <nav aria-label="...">
                  <Pagination className="pagination justify-content-end mb-0">
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem
                        key={i}
                        className={currentPage === i + 1 ? "active" : ""}
                      >
                        <PaginationLink
                          href="#pablo"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(i + 1);
                          }}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </div>
        </Row>
      </Container>
    </>
  );
};
export default LeaveBalanceList;
