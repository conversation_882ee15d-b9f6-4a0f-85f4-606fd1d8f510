import React, { useState, useEffect } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  Card,
  CardHeader,
  CardBody,
  Input,
  Button,
  Container,
  Row,
  Col,
} from "reactstrap";
import {
  FormControl,
} from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const SubjectEntry = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [subjects, setSubjects] = useState([]);
  const [newSubject, setNewSubject] = useState("");
  const [editingSubject, setEditingSubject] = useState(null);
  const [updatedSubjectName, setUpdatedSubjectName] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;
  useEffect(() => {
    fetchSubjects();
  }, []);

  const fetchSubjects = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/get-subjects`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        const data = response.data;
        setSubjects(data);
      } else {
        SwalMessageAlert("Failed Load Subject!", "error");
      }
    } catch (error) {
      SwalMessageAlert(`Failed Load Subject! ${error}`, "error");
    }
  };

  const handleAddSubject = async () => {
    try {
      // const newId = subjects.length + 1;
      const body = { subjectName: newSubject };
      const response = await axios.post(`${endPoint}/api/subject/add`, body, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        SwalMessageAlert("Subject Added Successfully", "success");
        window.location.reload();
      } else {
        SwalMessageAlert(`${response.data.msg}`, "error");
      }
    } catch (error) {
      SwalMessageAlert(`Subject Add Failed! ${error}`, "error");
    }
  };

  const handleEditSubject = (subject) => {
    setEditingSubject(subject._id);
    setUpdatedSubjectName(subject.subjectName);
  };

  const handleUpdateSubject = async (id) => {
    try {
      const body = { subjectName: updatedSubjectName }; // Prepare the updated subject data
      const response = await axios.put(
        `${endPoint}/api/subject/update/${id}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Subject Updated Successfully", "success");
        setEditingSubject(null); // Exit edit mode
        fetchSubjects(); // Refresh the data
      } else {
        SwalMessageAlert(`${response.data.msg}`, "error");
      }
    } catch (error) {
      SwalMessageAlert(`Subject Update Failed! ${error}`, "error");
    }
  };

  const handleCancelEdit = () => {
    setEditingSubject(null);
    setUpdatedSubjectName("");
  };

  const columns = [
    {
      name: "Sr. No.",
      selector: "id",
      cell: (row, index) => (currentPage - 1) * rowsPerPage + index + 1,
    },
    {
      name: "Subject Name",
      cell: (row) =>
        editingSubject === row._id ? (
          <Input
            type="text"
            value={updatedSubjectName}
            onChange={(e) => setUpdatedSubjectName(e.target.value)} // Update the state
          />
        ) : (
          row.subjectName
        ),
      sortable: true,
      width: "50%",
    },
    {
      name: "Operations",
      cell: (row) =>
        editingSubject === row._id ? (
          <>
            <Button
              color="success"
              onClick={() => handleUpdateSubject(row._id)}
              size="sm"
              className="me-2"
            >
              Update
            </Button>
            <Button color="danger" onClick={handleCancelEdit} size="sm">
              Cancel
            </Button>
          </>
        ) : (
          <Button
            color="primary"
            onClick={() => handleEditSubject(row)}
            size="sm"
          >
            Edit
          </Button>
        ),
      width: "40%",
    },
  ];
  const [filterText, setFilterText] = useState("");
  const filteredData = subjects.filter((item) => {
    const filterTextLower = filterText.toLowerCase();
    return (
      item.subjectName &&
      item.subjectName.toLowerCase().includes(filterTextLower)
    );
  });
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col className="order-xl-1" xl="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">
                      Master Subject Entry/Edit For Seat Vridhi
                    </h3>
                    <div className="mt-4">
                      <Input
                        type="text"
                        value={newSubject}
                        onChange={(e) => setNewSubject(e.target.value)}
                        placeholder="Enter new subject"
                        className="mb-2"
                      />
                      <Button
                        className="btn btn-sm"
                        color="primary"
                        onClick={handleAddSubject}
                        disabled={!newSubject.trim()}
                      >
                        Add New Subject
                      </Button>
                    </div>
                  </Col>
                  <FormControl
                    type="text"
                    placeholder="Search Subject..."
                    className="ml-auto"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </Row>
              </CardHeader>
              <CardBody>
                <DataTable
                  title="Subjects"
                  columns={columns}
                  data={filteredData}
                  highlightOnHover
                  responsive
                  pagination
                  paginationPerPage={rowsPerPage}
                  onChangePage={(page) => setCurrentPage(page)}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default SubjectEntry;
