import { useState, useEffect } from "react";
import {
  Row,
  Col,
  Button,
  <PERSON>,
  <PERSON><PERSON>ead<PERSON>,
  CardHeader,
  Table,
  CardBody,
  ModalBody,
  ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import logo from "../../assets/img/brand/CGGov.png";

import axios from "axios";
import { useParams, useNavigate } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import "../../assets/css/empProfile.css";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

const PrintCollegeProfile = () => {
  const token = sessionStorage.getItem("authToken");
  const name = sessionStorage.getItem("name");

  const endPoint = import.meta.env.VITE_API_URL;
  const { id } = useParams();
  //  const id = sessionStorage.getItem("id");
  //  console.log(id ,"collegeid")
  const [user, setUser] = useState([]);
  const [showPreview, setShowPreview] = useState(false);
  const navigate = useNavigate();
  const [data, setInstitueProfile] = useState([]);
  const [designationData, setDesignationData] = useState([]);
  const [district, setDistrict] = useState([]);
  const [classData, setClassData] = useState([]);
  const [districtAll, setDistrictAll] = useState([]);

  const [vidhansabha, setVidhansabha] = useState([]);

  const [division, setDivision] = useState([]);
  const [loading, setLoading] = useState(false);
  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]);

  const fetchData = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-institute-profile?clgId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setInstitueProfile(data);
        handlePreview();
      } else {

        SwalMessageAlert("PROFILE NOT COMPLETED", "warning");
        navigate("/admin/institute-list");
      }
    } catch (error) {
      console.error("Error fetching Employee data:", error);
      alert("Failed to load Employee data.");
    }
  };
  useEffect(() => {

    fetchData();

  }, [id]);


  const [getAllBlock, setGetAllBlock] = useState([]);
  useEffect(() => {
    const getBlock = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-dist-wise-block/${data?.basicDetails?.district}`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setGetAllBlock(data);
        }
      } catch (error) {
        console.error("Error fetching block data:", error);
        alert("Failed to load block data.");
      }
    };
    if (
      data &&
      data.basicDetails &&
      data.basicDetails.district
    ) {
      getBlock();
    }

  }, [data, endPoint, token]);

  const validBlocks = Array.isArray(getAllBlock)
    ? getAllBlock.filter(b => b.BlockNameEng && typeof b.BlockNameEng === "string")
    : [];

  const fetchVidhansabha = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${Number(
          data?.basicDetails?.district
        )}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
      } else {
        alert("Failed to fetch district data.");
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
    }
  };
  useEffect(() => {
    if (
      data &&
      data.basicDetails &&
      data.basicDetails.district
    ) {
      fetchVidhansabha();
    }
  }, [endPoint, token, data]);

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
          SwalMessageAlert;
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchDesignation();
    fetchClassData();
    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]);

  useEffect(() => {
    const getDistrictAll = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrictAll(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 1.");
      }
    };
    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data 2.");
      }
    };

    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
    getDistrict();
    getDistrictAll();
    getDistrictAllStateWise();
  }, [endPoint, token]);
  const [stateWiseDistrict, setstateWiseDistrict] = useState([]);

  const getDistrictAllStateWise = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/get-alldistrict-state`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        setstateWiseDistrict(data);
      }
    } catch (error) {
      console.error("Error fetching district data:", error);
      alert("Failed to load district data 3.");
    }
  };


  const setupDetails = data?.setupDetails?.setupDetails || [];
  const formatBoolean = (value) => {
    if (value === "true") return "Yes";
    if (value === "false") return "No";
    return value;
  };
  const handlePreview = () => {
    setShowPreview(true);
  };

  const handlePrint = () => {
    const printWindow = window.open("", "_blank");
    const printContent = document.getElementById("print-container").innerHTML;
    const printDocument = printWindow.document;

    printDocument.write(`
            <html>
              <head>
                <title>Print Form</title>
                <style>
                  @page { 
                    size: A4; 
                    margin: 20px; 
                  }
                  body { 
                    font-family: Arial, sans-serif; 
                    position: relative; 
                  }
                  .print-container {
                    width: 100%;
                    min-height: 29.7cm;
                    background: #fff;
                    position: relative;
                    padding-bottom: 50px; /* Space for footer */
                  }
                  .form-field {
                    margin-bottom: 15px;
                  }
                  .form-field label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                  }
                  .form-field input {
                    width: 100%;
                    padding: 8px;
                    box-sizing: border-box;
                  }
                  h1 {
                    font-size: 24px;
                    margin-bottom: 20px;
                  }
                  p {
                    font-size: 16px;
                    margin-bottom: 10px;
                  }
                  .header, .footer {
                    position: fixed;
                    left: 0;
                    right: 0;
                    text-align: center;
                    font-size: 12px;
                  }
                  .header {
                    top: 0;
                  }
                  .footer {
                    bottom: 0;
                  }
                  .page-number {
                    display: inline-block;
                  }
                </style>
              </head>
              <body>
                <div class="header">
                  <h1>Header Title</h1>
                </div>
                <div class="print-container">
                  ${printContent}
                </div>
                <div class="footer" style="position: fixed; top:28cm; left: 0; right: 0; bottom: 0; text-align: center; padding: 10px;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
        <div class="page-number" style="flex: 1; text-align: center;">Page 1 of 3</div>
        <h4 style="margin: 0; text-align: right;">Printed at : ${name}</h4>
    </div>
</div>
               <script>
              // Function to update page numbers
              function updatePageNumbers() {
                const totalPages = Math.ceil(document.body.scrollHeight / window.innerHeight);
                const pageNumberElements = document.querySelectorAll('.page-number');
                pageNumberElements.forEach((el, index) => {
                  el.innerHTML = "Page " + (index + 1) + " of " + totalPages;
                });
              }
              window.onload = function() {
                updatePageNumbers();
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
              </body>
            </html>
        `);
    printDocument.close();
  };

  // const currentDateTime = new Date().toLocaleString('en-IN', {
  //     day: '2-digit',
  //     month: '2-digit',
  //     year: 'numeric',
  //     hour: '2-digit',
  //     minute: '2-digit',
  //     second: '2-digit',
  //     hour12: true, // Use 12-hour clock
  // });
  const cellStyle = {
    border: '1px solid black',
    padding: '10px',
    fontSize: '15px',
  };
  const labelStyle = {
    ...cellStyle,
    fontWeight: 'bold',
  };

  const valueStyle = {
    ...cellStyle,
    color: 'darkblue',
  };
  const headerStyle = {
    backgroundColor: "lightyellow",
    fontWeight: "bold",
    textAlign: "center",
    padding: "2px",
    border: "1px solid #ddd"
  };
  return (
    <>
      <Header />
      <Modal
        isOpen={showPreview}
        toggle={() => setShowPreview(false)}
        backdrop="static"
        style={{ maxWidth: "70%" }}
      >
        <ModalHeader style={{ height: "20px", paddingTop: "5px" }}>
          <Row style={{ display: "flex" }}>
            <Col lg={10} style={{ width: 1002 }}>
              {" "}
              Print Preview
            </Col>
            <Col lg={2}>
              <Button
                style={{ paddingTop: "12px" }}
                close
                onClick={() => {
                  handlePreview();
                  navigate("/admin/dashboard");
                }}
              />
            </Col>
          </Row>
        </ModalHeader>
        <ModalBody style={{ marginTop: "0px" }}>
          <div id="print-container">
            <CardHeader>
              <Row
                style={{
                  textAlign: "center", // Center horizontally
                  display: "block", // Use flexbox for alignment
                }}
              >
                <Col
                  style={{
                    textAlign: "left",
                    display: "flex", // Center horizontally
                  }}
                >
                  {logo && (
                    <img
                      alt="logo"
                      height="50px"
                      className="navbar-brand-img"
                      style={{ marginRight: "20px" }}
                      src={logo}
                    />
                  )}
                  <span
                    style={{
                      textAlign: "left",
                      fontSize: "16px",
                      color: "darkblue",
                      fontWeight: "bolder",
                      display: "block", // Ensure block-level behavior
                    }}
                  >
                    DEPARTMENT OF  HIGHER EDUCATION
                  </span>
                </Col>
              </Row>
              <br></br>
              <br></br>
            </CardHeader>


            <Row>
              <div className="profile-table">
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <tbody>
                    {/* === Section: Basic Details === */}

                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                      <td colspan="12" style={labelStyle} className="text-center">Basic Details (Part I)</td>
                    </tr>

                    {/* Row 1 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>College Name</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.name || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Is College Sanction Letter Uploaded</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.isClgFile === "true" ? "Yes" : "No"}</td>
                      <td colSpan="2" style={labelStyle}>University</td>
                      <td colSpan="2" style={valueStyle}>
                        {university.find((type) => String(type._id) === String(data?.basicDetails?.university))?.name || "N/A"}
                      </td>

                    </tr>

                    {/* Row 2 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>AISHE Code</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.aisheCode || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Principal Name</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.contactPerson || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Principal Mobile No</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.contactNumber || "N/A"}</td>

                    </tr>

                    {/* Row 3 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Is Lead College</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.isLead === 1 ? "Yes" : "No"}</td>
                      <td colSpan="2" style={labelStyle}>College Mail ID</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.collegeEmail || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Establishment Year</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.establishYear || "N/A"}</td>

                    </tr>

                    {/* Row 4 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Is Establishment File Uploaded</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.isEstablishFile === "true" ? "Yes" : "No"}</td>
                      <td colSpan="2" style={labelStyle}>Government E-mail ID 1</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.govtEmail1 || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Government E-mail ID 2</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.govtEmail2 || "N/A"}</td>

                    </tr>

                    {/* Row 5 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Telephone No.</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.telephone || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Division</td>
                      <td colSpan="2" style={valueStyle}>
                        {division.find((type) => Number(type.divisionCode) === Number(data?.basicDetails?.divison))?.name || "N/A"}
                      </td>
                      <td colSpan="2" style={labelStyle}>District</td>
                      <td colSpan="2" style={valueStyle}>
                        {district.find((type) => Number(type.LGDCode) === Number(data?.basicDetails?.district))?.districtNameEng || "N/A"}
                      </td>
                    </tr>

                    {/* Row 6 */}
                    <tr>

                      <td colSpan="2" style={labelStyle}>Vidhansabha Name</td>
                      <td colSpan="2" style={valueStyle}>
                        {vidhansabha.find((type) => Number(type.ConstituencyNumber) === Number(data?.basicDetails?.vidhansabha))?.ConstituencyName || "N/A"}
                      </td>
                      <td colSpan="2" style={labelStyle}>Vidhansabha Number</td>
                      <td colSpan="2" style={valueStyle}>{data?.basicDetails?.ConstituencyNumber || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>GPS Coordinates</td>
                      <td colSpan="2" style={valueStyle}>
                        {data?.basicDetails?.lat && data?.basicDetails?.long ? `Lat: ${data?.basicDetails?.lat}, Long: ${data?.basicDetails?.long}` : "N/A"}
                      </td>
                    </tr>

                    {/* Row 7 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Address</td>
                      <td colSpan="10" style={valueStyle}>{data?.basicDetails?.address || "N/A"}</td>
                    </tr>

                    {/* Row 8 */}


                    {/* === Section: More Details === */}

                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                      <td colSpan="12" style={labelStyle} className="text-center">Basic Details (Part II)</td>
                    </tr>

                    {/* Row 1 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Registration Number</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.registrationNumber || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>Area Type</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.areaType || "N/A"}</td>
                      <td colSpan="2" style={labelStyle}>College Website</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.collegeUrl || "N/A"}</td>
                    </tr>

                    {/* Row 2 */}
                    <tr>
                      <td colSpan="4" style={labelStyle}>DDO Code</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.ddoCode || "N/A"}</td>

                      <td colSpan="4" style={labelStyle}>Block</td>
                      <td colSpan="2" style={valueStyle}>
                        {(validBlocks?.find(b => String(b._id) === String(data?.moreDetails?.block))?.BlockNameEng) || "N/A"}
                      </td>
                    </tr>

                    {/* Row 3 */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>College Type</td>
                      <td colSpan="2" style={valueStyle}>
                        {data?.moreDetails?.collegeType === "1" ? "Govt." :
                          data?.moreDetails?.collegeType === "2" ? "Private" : "N/A"}
                      </td>
                      <td colSpan="2" style={labelStyle}>Degree Types</td>
                      <td colSpan="2" style={valueStyle}>
                        {data?.moreDetails?.degreeTypes === "UG" ? "Under Graduate" :
                          data?.moreDetails?.degreeTypes === "PG" ? "Post Graduate" : "N/A"}
                      </td>
                      <td colSpan="2" style={labelStyle}>Demography</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.demography || "N/A"}</td>
                    </tr>

                    {/* Row 4 - College Features/Flags */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>College Features</td>
                      <td colSpan="10" style={valueStyle}>
                        {[

                          { id: "isAutonomous", label: "Autonomous College" },
                          { id: "isModelCollege", label: "Model College" },
                          { id: "englishMedium", label: "English Medium" },
                          { id: "sangeetCollege", label: "Sangeet College" },
                        ].map((item, index) => (
                          <span key={item.id} className="mr-3">
                            <span style={{ fontWeight: "bold" }}>{item.label}: </span>
                            <span style={{ color: data?.moreDetails?.[item.id] === "true" ? "green" : "red" }}>
                              {data?.moreDetails?.[item.id] === "true" ? "true" : "false"}
                            </span>
                            {index < 3 ? " | " : ""}
                          </span>
                        ))}
                      </td>
                    </tr>

                    {/* Row 5 - Dates */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Taken Over</td>
                      <td colSpan="2" style={valueStyle}>{data?.moreDetails?.takenOver === "true" ? "Yes" : "No"}</td>
                      {data?.moreDetails?.takenOver === "true" && (

                        <>
                          <td colSpan="2" style={labelStyle}>Taken Over Date</td>
                          <td colSpan="2" style={valueStyle}>{formatDate(data?.moreDetails?.takenOverDate)}</td>
                          <td colSpan="2" style={labelStyle}>Is Taken Over File Upload</td>
                          <td colSpan="2" style={valueStyle}>{data?.moreDetails?.isTakenOverFile === "true" ? "Yes" : "No"}</td>
                        </>)}

                    </tr>

                    {/* Row 6 - Course Information */}
                    <tr>
                      {data?.moreDetails?.UGC2FRegistered === "true" && (

                        <>
                          <td colSpan="2" style={labelStyle}>UGC 2F Date</td>
                          <td colSpan="2" style={valueStyle}>{formatDate(data?.moreDetails?.ugc2FDate)}</td>
                          <td colSpan="2" style={labelStyle}>Is 12B Registred File Upload</td>
                          <td colSpan="2" style={valueStyle}>{data?.moreDetails?.isRegistered12B === "true" ? "Yes" : "No"}</td>
                        </>)}

                      {data?.moreDetails?.isRegistered12B === "true" && (
                        <>
                          <td colSpan="2" style={labelStyle}>UGC 12B Date</td>
                          <td colSpan="2" style={valueStyle}>{formatDate(data?.moreDetails?.ugc12BDate)}</td>
                        </>
                      )}


                    </tr>
                    <tr>

                      <td colSpan="4" style={labelStyle}>Is UGC 2F Registred File Upload</td>
                      <td colSpan="8" style={valueStyle}>{data?.moreDetails?.is2FUgcFile === "true" ? "Yes" : "No"}</td>
                    </tr>

                    {/* === Section: NAAC Details === */}
                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                      <td colSpan="12" style={labelStyle}>NAAC Details</td>
                    </tr>
                    {/* Row 2 - NAAC Details */}

                    <tr>
                      <td colSpan="4" style={labelStyle}>Eligible for NAAC</td>
                      <td colSpan="8" style={valueStyle}>
                        <span className={data?.naacDetails?.eligibleForNAAC === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                          {data?.naacDetails?.eligibleForNAAC === "true" ? "Yes" : "No"}
                        </span>
                      </td>
                    </tr>
                    {data?.naacDetails?.eligibleForNAAC === "true" && (
                      <>
                        <tr>
                          <td colSpan="4" style={labelStyle}>NAAC Evaluated</td>
                          <td colSpan="8" style={valueStyle}>
                            <span className={data?.naacDetails?.naacEvaluated === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                              {data?.naacDetails?.naacEvaluated === "true" ? "Yes" : "No"}
                            </span>
                          </td>
                        </tr>
                        {data?.naacDetails?.naacEvaluated === "true" && (
                          <>
                            <tr>
                              <td colSpan="2" style={labelStyle}>NAAC Year</td>
                              <td colSpan="1" style={valueStyle}>{data?.naacDetails?.naacYear || "N/A"}</td>
                              <td colSpan="1" style={labelStyle}>Grade</td>
                              <td colSpan="2" style={valueStyle}>{data?.naacDetails?.naacGrade || "N/A"}</td>
                              <td colSpan="4" style={labelStyle}>Is Grade of Latest Cycle File Upload</td>
                              <td colSpan="2" style={valueStyle}>{data?.naacDetails?.isGradeCycleFile === "true" ? "Yes" : "No"}</td>
                            </tr>
                          </>
                        )}
                      </>
                    )}


                    {/* === Section: Janbhagidari Details === */}
                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                      <td colSpan="12" style={labelStyle}>Janbhagidari Details</td>
                    </tr>
                    <tr>
                      <td colSpan="3" style={labelStyle}>Janbhagidari Samiti Registered</td>
                      <td colSpan="1" style={valueStyle}>
                        <span className={data?.janbhagidariDetails?.isJBRegistered === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                          {data?.janbhagidariDetails?.isJBRegistered === "true" ? "Yes" : "No"}
                        </span>
                      </td>

                      {data?.janbhagidariDetails?.isJBRegistered === "true" && (
                        <>
                          <td colSpan="2" style={labelStyle}>Registration Date</td>
                          <td colSpan="2" style={valueStyle}>{data?.janbhagidariDetails?.jbRegistrationDate || "N/A"}</td>
                          <td colSpan="2" style={labelStyle}>President Name</td>
                          <td colSpan="2" style={valueStyle}>{data?.janbhagidariDetails?.jbPresidentName || "N/A"}</td>
                        </>
                      )}
                    </tr>

                    {/* Row 4 - Janbhagidari Fund Info */}
                    {data?.janbhagidariDetails?.isJBRegistered === "true" && (
                      <>
                        <tr>
                          <td colSpan="12" style={headerStyle} className="text-center">Janbhagidari Fund Info</td>
                        </tr>
                        <tr>
                          <td colSpan="1" style={labelStyle}>Fund Year</td>
                          <td colSpan="2" style={valueStyle}>{data?.janbhagidariDetails?.jbFundYear || "N/A"}</td>
                          <td colSpan="1" style={labelStyle}>Income</td>
                          <td colSpan="2" style={valueStyle}>₹ {data?.janbhagidariDetails?.jbFundIncome || "0"}</td>
                          <td colSpan="1" style={labelStyle}>Expenditure</td>
                          <td colSpan="2" style={valueStyle}>₹ {data?.janbhagidariDetails?.jbFundExpenditure || "0"}</td>
                          <td colSpan="1" style={labelStyle}>Balance</td>
                          <td colSpan="2" style={valueStyle}>₹ {data?.janbhagidariDetails?.jbFundBalance || "0"}</td>
                        </tr>
                        <tr>
                          <td colSpan="2" style={labelStyle}>Audit Completed</td>
                          <td colSpan="2" style={valueStyle}>
                            <span className={data?.janbhagidariDetails?.jbFundAuditCompleted === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                              {data?.janbhagidariDetails?.jbFundAuditCompleted === "true" ? "Yes" : "No"}
                            </span>
                          </td>
                          <td colSpan="2" style={labelStyle}>Is Audit File Upload ?</td>
                          <td colSpan="2" style={valueStyle}>{data?.janbhagidariDetails?.isCaAuditFile === "true" ? "Yes" : "No"}</td>

                          <td colSpan="2" style={labelStyle}>Remark</td>
                          <td colSpan="2" style={valueStyle}>{data?.janbhagidariDetails?.jbFundRemark || "N/A"}</td>
                        </tr>

                      </>

                    )}

                    {/* === Section: NSS and NCC Details === */}
                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                      <td colSpan="12" style={labelStyle}>NSS / NCC Details</td>
                    </tr>
                    <tr>
                      <td colSpan="12" style={headerStyle} className="text-center">NSS Details</td>
                    </tr>
                    <tr>
                      <td colSpan="2" style={labelStyle}>NSS Available</td>
                      <td colSpan="1" style={valueStyle}>
                        <span className={data?.nssAndNccDetails?.nssAvailable === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                          {data?.nssAndNccDetails?.nssAvailable === "true" ? "Yes" : "No"}
                        </span>
                      </td>

                      {data?.nssAndNccDetails?.nssAvailable === "true" && (
                        <>
                          <td colSpan="2" style={labelStyle}>Establishment Date</td>
                          <td colSpan="1" style={valueStyle}>{data?.nssAndNccDetails?.nssEstablishmentDate || "N/A"}</td>
                          <td colSpan="2" style={labelStyle}>In-Charge Name</td>
                          <td colSpan="1" style={valueStyle}>{data?.nssAndNccDetails?.nssInChargeName || "N/A"}</td>
                          <td colSpan="1" style={labelStyle}>Total Count</td>
                          <td colSpan="2" style={valueStyle}>
                            Boys: {data?.nssAndNccDetails?.nssBoysCount || "0"},
                            Girls: {data?.nssAndNccDetails?.nssGirlsCount || "0"},
                            Total: {data?.nssAndNccDetails?.nssTotalCount || "0"}
                          </td>
                        </>
                      )}
                    </tr>
                    {data?.nssAndNccDetails?.nccAvailable === "true" && (
                      <>
                        <tr>
                          <td colSpan="3" style={labelStyle}>Is File Upload(Establishment) </td>
                          <td colSpan="9" style={valueStyle}>{data?.nssAndNccDetails?.isNssEstablishFile === "true" ? "Yes" : "No"}</td>

                        </tr>
                      </>
                    )}
                    {/* Row 6 - NCC Details */}
                    <tr>
                      <td colSpan="12" style={headerStyle} className="text-center">NCC Details</td>
                    </tr>
                    <tr>
                      <td colSpan="2" style={labelStyle}>NCC Available</td>
                      <td colSpan="1" style={valueStyle}>
                        <span className={data?.nssAndNccDetails?.nccAvailable === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                          {data?.nssAndNccDetails?.nccAvailable === "true" ? "Yes" : "No"}
                        </span>
                      </td>

                      {data?.nssAndNccDetails?.nccAvailable === "true" && (
                        <>
                          <td colSpan="1" style={labelStyle}>Establishment Date</td>
                          <td colSpan="2" style={valueStyle}>{data?.nssAndNccDetails?.nccEstablishmentDate || "N/A"}</td>
                          <td colSpan="2" style={labelStyle}>In-Charge Name</td>
                          <td colSpan="4" style={valueStyle}>{data?.nssAndNccDetails?.nccInChargeName || "N/A"}</td>


                        </>
                      )}
                    </tr>
                    {data?.nssAndNccDetails?.nccAvailable === "true" && (
                      <>

                        <tr>
                          <td colSpan="1" style={labelStyle}>Total Count</td>
                          <td colSpan="4" style={valueStyle}>
                            Boys: {data?.nssAndNccDetails?.nccBoysCount || "0"},
                            Girls: {data?.nssAndNccDetails?.nccGirlsCount || "0"},
                            Total: {data?.nssAndNccDetails?.nccTotalCount || "0"}
                          </td>
                          <td colSpan="3" style={labelStyle}>Is File Upload(Establishment) </td>
                          <td colSpan="4" style={valueStyle}>{data?.nssAndNccDetails?.isNccEstablishFile === "true" ? "Yes" : "No"}</td>

                        </tr>
                      </>
                    )}
                    {/* === Section: Infrastructure === */}
                    <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                      <td colSpan="12" style={labelStyle}>Infrastructure Details</td>
                    </tr>


                    {/* Basic Infrastructure */}
                    <tr>
                      <td colSpan="2" style={labelStyle}>Is Land Alloted</td>
                      <td colSpan="1" style={valueStyle}>{data?.infrastructureDetails?.isLandAlloted === "true" ? "Yes" : "No"}</td>

                      {data?.infrastructureDetails?.isLandAlloted === "true" && (
                        <>
                          <td colSpan="1" style={labelStyle}>Land Area (sq.ft)</td>
                          <td colSpan="1" style={valueStyle}>{data?.infrastructureDetails?.landAreaSqFt}</td>
                          <td colSpan="2" style={labelStyle}>Building Constructed</td>
                          <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.buildingConstructed)}</td>
                          <td colSpan="2" style={labelStyle}>Is Land Area File Upload</td>
                          <td colSpan="1" style={valueStyle}>{data?.infrastructureDetails?.isLandAreaFile === "true" ? "Yes" : "No"}</td>

                        </>
                      )}
                    </tr>
                    <tr >

                      <td colSpan="2" style={labelStyle}>Number of Teaching Rooms</td>
                      <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.teachingRooms}</td>
                      <td colSpan="2" style={labelStyle}>Number of Labs</td>
                      <td colSpan="1" style={valueStyle}>{data?.infrastructureDetails?.labs}</td>
                      <td colSpan="2" style={labelStyle}>Girls Common Room</td>
                      <td colSpan="1" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.girlsCommonRoom)}</td>
                      <td colSpan="1" style={labelStyle}>Library</td>
                      <td colSpan="1" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.library)}</td>
                    </tr>

                    <tr>




                    </tr>
                    {data?.infrastructureDetails?.library === "true" && (
                      <tr>

                        <td colSpan="2" style={labelStyle}>No. of Physical Books</td>
                        <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.physicalBooksNo}</td>
                        <td colSpan="2" style={labelStyle}>No. of Digital Books</td>
                        <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.digitalBookNo}</td>
                        <td colSpan="2" style={labelStyle}>No. of Journals</td>
                        <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.journalsNo}</td>
                      </tr>


                    )}
                    <tr>
                      <td colSpan="4" style={labelStyle}>Reading Room</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.readingRoom)}</td>
                      <td colSpan="4" style={labelStyle}>journalsNo Room</td>
                      <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.journalsNo}</td>
                    </tr>

                    <tr>
                      <td colSpan="4" style={labelStyle}>Sports Ground</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.sportsGround)}</td>
                      <td colSpan="4" style={labelStyle}>Auditorium</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.auditorium)}</td>
                    </tr>

                    {/* Smart Classes */}
                    <tr>
                      <td colSpan="4" style={labelStyle}>Smart Class</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.smartClass)}</td>
                      {data?.infrastructureDetails?.smartClass === "true" ? (
                        <>
                          <td colSpan="4" style={labelStyle}>Number of Smart Classes</td>
                          <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.numberOfSmartClass}</td>
                        </>
                      ) : (
                        <td colSpan="2" style={valueStyle}></td>
                      )}
                    </tr>

                    {/* Hostel Details */}
                    {/* <tr>
                                        <td colSpan="4" style={headerStyle}>HOSTEL FACILITIES</td>
                                             </tr> */}

                    <tr>
                      <td colSpan="4" style={labelStyle}>Hostel Available</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.hostelAvailable)}</td>

                    </tr>

                    {data?.infrastructureDetails?.hostelAvailable === "true" && (
                      <>
                        <tr>
                          <td colSpan="4" style={labelStyle}>Boys Hostel Count</td>
                          <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.boysHostelCount}</td>
                          <td colSpan="4" style={labelStyle}>Girls Hostel Count</td>
                          <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.girlsHostelCount}</td>
                        </tr>

                        {/* Boys Hostels Details */}

                        {data?.infrastructureDetails?.boysHostelCount > 0 && (
                          <>
                            <tr>
                              <td colSpan="12" style={headerStyle}>Boys Hostels Details</td>
                            </tr>
                            {data?.infrastructureDetails?.boysHostels.map((hostel, index) => (
                              <tr key={`boys-hostel-${index}`}>
                                <td colSpan="2" style={labelStyle}>Hostel {index + 1} Name</td>
                                <td colSpan="2" style={valueStyle}>{hostel.name}</td>
                                <td colSpan="2" style={labelStyle}>Seats Available</td>
                                <td colSpan="2" style={valueStyle}>{hostel.seats}</td>
                                <td colSpan="2" style={labelStyle}>Hostel Category</td>
                                <td colSpan="2" style={valueStyle}>{hostel.hostalCategory}</td>
                              </tr>
                            ))}
                          </>
                        )}

                        {/* Girls Hostels Details */}
                        {data?.infrastructureDetails?.girlsHostelCount > 0 && (
                          <>
                            <tr>
                              <td colSpan="12" style={headerStyle}>Girls Hostels Details</td>
                            </tr>
                            {data?.infrastructureDetails?.girlsHostels.map((hostel, index) => (
                              <tr key={`girls-hostel-${index}`}>
                                <td colSpan="2" style={labelStyle}>Hostel {index + 1} Name</td>
                                <td colSpan="2" style={valueStyle}>{hostel.name}</td>
                                <td colSpan="2" style={labelStyle}>Seats Available</td>
                                <td colSpan="2" style={valueStyle}>{hostel.seats}</td>
                                <td colSpan="2" style={labelStyle}>Hostel Category</td>
                                <td colSpan="2" style={valueStyle}>{hostel.hostalCategory}</td>
                              </tr>
                            ))}
                          </>
                        )}
                      </>
                    )}

                    {/* Other Facilities */}
                    <tr>
                      <td colSpan="12" style={headerStyle}>OTHER FACILITIES</td>
                    </tr>
                    <tr>
                      <td colSpan="4" style={labelStyle}>Biometric Attendance</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.biometricAttendance)}</td>
                      <td colSpan="4" style={labelStyle}>Wi-Fi</td>
                      <td colSpan="2" style={valueStyle}>{data?.infrastructureDetails?.wifi}</td>
                    </tr>


                    <tr>
                      <td colSpan="4" style={labelStyle}>Staff Toilet</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.staffToilet)}</td>
                      <td colSpan="4" style={labelStyle}>Boys Toilet</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.boysToilet)}</td>
                    </tr>
                    <tr>
                      <td colSpan="4" style={labelStyle}>Girls Toilet</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.girlsToilet)}</td>
                      <td colSpan="4" style={labelStyle}>Ramp Available</td>
                      <td colSpan="2" style={valueStyle}>{formatBoolean(data?.infrastructureDetails?.rampAvailable)}</td>
                    </tr>
                    {/* === Section: NSP Registration === */}
                    <tr style={{
                      border: '3px solid black',
                      fontSize: "14px",
                      textAlign: "center",
                      padding: "10px",
                      backgroundColor: "lightgray",
                      width: "100%",
                      verticalAlign: "middle",
                    }} className="text-center">
                      <td colSpan="12" style={{ ...labelStyle, width: "16.66%" }}>NSP Registration Details</td>
                    </tr>
                    <tr style={{
                      border: '1px solid black',
                      fontSize: "14px",
                      textAlign: "left",
                    }}>
                      <td colspan="4" style={labelStyle}>Registered on NSP</td>
                      <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.nspRegistered === "true" ? "Yes" : "No"}</td>
                      <td colspan="4" style={labelStyle}>NSP Institute ID</td>
                      <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.nspInstituteId || "N/A"}</td>
                    </tr>
                    {data?.nspRegistrationDetails?.nspRegistered === "true" && (
                      <>
                        <tr style={{
                          border: '1px solid black',
                          fontSize: "14px",
                          textAlign: "left",
                        }}>
                          <td colspan="4" style={labelStyle}>Nodal Officer Name</td>
                          <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.nodalOfficerName || "N/A"}</td>
                          <td colspan="4" style={labelStyle}>Nodal Officer Mobile</td>
                          <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.nodalOfficerMobile || "N/A"}</td>
                        </tr>
                        <tr>
                          <td colspan="4" style={labelStyle}>Nodal Officer Bio-Auth</td>
                          <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.nodalOfficerBioAuth === "true" ? "Yes" : "No"}</td>
                          <td colspan="4" style={labelStyle}>Principal Bio-Auth</td>
                          <td colspan="2" style={valueStyle}>{data?.nspRegistrationDetails?.principalBioAuth === "true" ? "Yes" : "No"}</td>
                        </tr>
                      </>
                    )}

                    {/* === Section: Setup === */}

                    <tr style={{
                      border: '3px solid black',
                      fontSize: "14px",
                      textAlign: "center",
                      padding: "10px",
                      backgroundColor: "lightgray",
                      width: "100%",
                      verticalAlign: "middle",
                    }} className="text-center">
                      <td colSpan="12" style={{ ...labelStyle, width: "16.66%" }}>Setup Details</td>
                    </tr>
                  </tbody>
                </table>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr style={{
                      border: '1px solid black',
                      fontSize: "14px",
                      textAlign: "center",
                      padding: "10px",
                      backgroundColor: "lightgray",
                      width: "100%",
                      verticalAlign: "middle",
                    }}>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Class</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Designation</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Sanctioned <br /> Posts</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Working</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Sanction <br /> Order No</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Sanction <br /> Date</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Has File</th>
                      <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", fontSize: "14px", border: '1px solid black' }}>Uploaded <br /> File name</th>

                    </tr>
                  </thead>
                  <tbody>
                    {setupDetails.map((item, index) => (

                      <tr key={index} tyle={{
                        border: '1px solid black',
                        fontSize: "14px",
                        textAlign: "left",
                      }}>
                        <td style={valueStyle} >
                          {classData.find(cls => cls._id === item.class)?.className || ""}

                        </td>
                        <td style={valueStyle}>
                          {designationData.find((type) => String(type._id) === String(item.designationsList))?.designation || "N/A"}

                        </td>
                        <td style={valueStyle}>{item.sanctionedPosts}</td>
                        <td style={valueStyle}>{item.working}</td>
                        <td style={valueStyle}>{item.sanctionOrderNo}</td>
                        <td style={valueStyle}>{formatDate(item.sanctionDate)}</td>
                        <td style={valueStyle}>{item.isSanctionOrderFile === "true" ? "Yes" : "No"}</td>
                        <td style={valueStyle}>{item.SanctionOrderFileName}</td>

                      </tr>

                    ))}
                  </tbody>
                </table>
              </div>
            </Row>
          </div>
        </ModalBody>
        <ModalFooter>
          <Button
            color="secondary"
            onClick={() => {
              handlePreview();
              navigate("/admin/dashboard");
            }}
          >
            Close
          </Button>
          <Button
            color="primary"
            onClick={() => {
              handlePrint();
              navigate(-1);
            }}
          >
            Print
          </Button>
        </ModalFooter>
      </Modal>
    </>
  );
};

export default PrintCollegeProfile;
