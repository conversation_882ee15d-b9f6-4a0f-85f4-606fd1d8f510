import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    FormGroup,
    Col,
    Label,
    Button,
    Input,
    Table
} from "reactstrap";
import Swal from "sweetalert2";
import axios from "axios";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";

const ACRFormUGandPGPart4 = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);

    const [formData, setFormData] = useState({
        applicationId: employee[0].applicationId,
        employeeId: employee[0].employeeId,
        employeeName: employee[0].employeeName,
        collegeName: employee[0].collegeName,
        collegeId: employee[0].collegeId,
        shreniKaran2: "",
        remark2: "",
    });
    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
    };
    const handleFinalSubmit = async (e) => {
        // Validation function to check for required fields

        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {
                shreniKaran2: "",
                remark2: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";
                    }
                }
            }
            return newErrors;
        };

        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }
        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });
            if (result.isConfirmed) {
                e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert(response.data.msg, "success");
                    setTimeout(() => window.location.reload(), 5000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };


    return (
        <>
            <Container className="mt-0" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-0">छत्तीसगढ़ शासन, उच्च शिक्षा विभाग, मंत्रालय, रायपुर <br /> वार्षिक गोपनीय मूल्यांकन पत्रक (महाविद्यालयीन प्राचार्य के लिए)</h2>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Card className="shadow pb-4">

                                                <CardBody>
                                                    <div className="mb-4">
                                                        <Row>
                                                            <Col>
                                                                <h3>
                                                                    <u>क्रमांक 1 से 5 तक कार्यालय द्वारा भरा जावेगा</u>
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col>
                                                                <Row className="mb-3 mt-4">
                                                                    <Col md="3" className="mt--4">
                                                                        <Label>
                                                                            {" "}
                                                                            <strong>1.</strong> वर्ष {year} के 31 मार्च <br /> को
                                                                            समाप्त अवधि के लिए
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="text"
                                                                            className="form-control"
                                                                            name="samaptAvadhi"
                                                                            value={employee[0].basicDetails[0].samaptAvadhi}
                                                                            onChange={handleInputChange}
                                                                        />
                                                                        {errors.samaptAvadhi && (
                                                                            <small className="text-danger">
                                                                                {errors.samaptAvadhi}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                    <Col md="3">
                                                                        <Label>
                                                                            <strong>2.</strong> पूरा नाम श्री/श्रीमती / कुमारी
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="text"
                                                                            name="employeeName"
                                                                            value={employee[0].basicDetails[0].employeeName}

                                                                            className="form-control"
                                                                        />
                                                                        {errors.employeeName && (
                                                                            <small className="text-danger">
                                                                                {errors.employeeName}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                    <Col md="3">
                                                                        <Label>
                                                                            <strong>3.</strong> जन्म तिथि
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="date"
                                                                            name="dateofBirth"
                                                                            value={employee[0].basicDetails[0].dateofBirth}
                                                                            className="form-control"
                                                                            onChange={handleInputChange}
                                                                        />
                                                                        {errors.dateofBirth && (
                                                                            <small className="text-danger">
                                                                                {errors.dateofBirth}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                    <Col md="3">
                                                                        <Label>
                                                                            <strong>4.</strong> प्रतिवेदित अवधि में धारित पद :
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="text"
                                                                            name="designation"
                                                                            value={employee[0].basicDetails[0].dharitPad}
                                                                            className="form-control"
                                                                        />
                                                                    </Col>
                                                                </Row>
                                                                <Row className="mb-3 mt-5">
                                                                    <Col md="3">
                                                                        <Label>
                                                                            <strong>5.(a)</strong> संस्था का नाम :
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="text"
                                                                            name="collegeName"
                                                                            value={employee[0].basicDetails[0].collegeName} // Use employee data here

                                                                            className="form-control"
                                                                            onChange={handleInputChange}
                                                                        />
                                                                        {errors.collegeName && (
                                                                            <small className="text-danger">
                                                                                {errors.collegeName}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                    <Col md="3" className="mt--4">
                                                                        <Label>
                                                                            <strong>5.(b)</strong> वर्तमान संस्था / महाविद्यालय
                                                                            में कब से कार्यरत है
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="date"
                                                                            className="form-control"
                                                                            name='wartmanSansthaWorkingDate'
                                                                            value={employee[0].basicDetails[0].wartmanSansthaWorkingDate}
                                                                            onChange={handleInputChange}
                                                                        />
                                                                        {errors.wartmanSansthaWorkingDate && (
                                                                            <small className="text-danger">
                                                                                {errors.wartmanSansthaWorkingDate}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                </Row>
                                                            </Col>
                                                        </Row>
                                                        <hr />
                                                        <Row className="mt-3 mb-3">
                                                            <Col>
                                                                <h3>
                                                                    <u>
                                                                        {" "}
                                                                        क्रमांक 6 से 22 तक प्रतिवेदित प्राधिकारी / प्राचार्य
                                                                        द्वारा भरा जायेगा
                                                                    </u>
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col>
                                                                <Row className="mb-3">
                                                                    <Col md="3">
                                                                        <Label>
                                                                            <strong> 6.</strong> वार्षिक संपत्ति प्रतिवेदन
                                                                            प्रस्तुत करने का दिनांक :
                                                                        </Label>
                                                                        <Input readOnly
                                                                            type="date"
                                                                            className="form-control"
                                                                            name="varshikSampattiSubmitDate"
                                                                            value={employee[0].basicDetails[0].varshikSampattiSubmitDate}
                                                                            onChange={handleInputChange}
                                                                        />
                                                                        {errors.varshikSampattiSubmitDate && (
                                                                            <small className="text-danger">
                                                                                {errors.varshikSampattiSubmitDate}
                                                                            </small>
                                                                        )}
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>7.</strong> प्राचार्य के अधीन कार्यरत
                                                                                    शैक्षणिक एवं अशैक्षणिक स्टॉप की संख्या
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    {" "}
                                                                                    (एक). राजपत्रित :
                                                                                </Label>
                                                                                <Input readOnly
                                                                                    type="number"
                                                                                    className="form-control"
                                                                                    name="rajpatritStaff"
                                                                                    value={employee[0].basicDetails[0].rajpatritStaff}
                                                                                    onChange={handleInputChange}
                                                                                />
                                                                                {errors.rajpatritStaff && (
                                                                                    <small className="text-danger">
                                                                                        {errors.rajpatritStaff}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो ). अराजपत्रित :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="arajpatritStaff"
                                                                                    value={employee[0].basicDetails[0].arajpatritStaff}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.arajpatritStaff && (
                                                                                    <small className="text-danger">
                                                                                        {errors.arajpatritStaff}
                                                                                    </small>
                                                                                )}

                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>8.</strong> प्राचार्य द्वारा प्रतिवेदक
                                                                                    अधिकारी के रूप में लिखे जाने वाले वार्षिक गोपनीय
                                                                                    मूल्यांकन पत्रक कितने शैक्षणिक अधिकारियों एवं
                                                                                    गैर शैक्षणिक कर्मचारियों के संबंध में लिखे जाकर
                                                                                    समीक्षक प्राधिकारी को भेजे जा चुके हैं।
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) शैक्षणिक स्टॉफ के लिए :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="sekshnikStaffKeLiye"
                                                                                    value={employee[0].basicDetails[0].sekshnikStaffKeLiye}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.sekshnikStaffKeLiye && (
                                                                                    <small className="text-danger">
                                                                                        {errors.sekshnikStaffKeLiye}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) गैर शैक्षणिक राजपत्रित अधिकारियों के लिए :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="gairsekshnikStaffKeLiye"
                                                                                    value={employee[0].basicDetails[0].gairsekshnikStaffKeLiye}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.gairsekshnikStaffKeLiye && (
                                                                                    <small className="text-danger">
                                                                                        {errors.gairsekshnikStaffKeLiye}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (तीन) गैर शैक्षणिक अराजपत्रित स्टॉफ के लिए :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="gairsekshnikArajpatrit"
                                                                                    value={employee[0].basicDetails[0].gairsekshnikArajpatrit}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.gairsekshnikArajpatrit && (
                                                                                    <small className="text-danger">
                                                                                        {errors.gairsekshnikArajpatrit}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col md="4">
                                                                                <Label>
                                                                                    {" "}
                                                                                    <strong>9. </strong> वार्षिक गोपनीय मूल्यांकन
                                                                                    पत्रक के मास्टर रजिस्टर में किस-किस वर्ष की
                                                                                    प्रविष्टियों की जा चुकी हैं :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="varshikGopniyaMulyankanYears"
                                                                                    value={employee[0].basicDetails[0].varshikGopniyaMulyankanYears}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.varshikGopniyaMulyankanYears && (
                                                                                    <small className="text-danger">
                                                                                        {errors.varshikGopniyaMulyankanYears}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label>
                                                                                    {" "}
                                                                                    <strong>10. </strong> क्या प्राध्यापकों व सहायक
                                                                                    प्राध्यापकों से उपस्थिति रजिस्टर पूर्ण करवाकर
                                                                                    जमा कर लिये गये हैं:
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="pradhyapakSahayakAttendance"
                                                                                    value={employee[0].basicDetails[0].pradhyapakSahayakAttendance}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.pradhyapakSahayakAttendance && (
                                                                                    <small className="text-danger">
                                                                                        {errors.pradhyapakSahayakAttendance}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label>
                                                                                    {" "}
                                                                                    <strong>11. </strong>संस्था में अध्यापन कार्य
                                                                                    दिवस कितनी रही :
                                                                                </Label>
                                                                                <Input readOnly
                                                                                    type="text"
                                                                                    className="form-control mt-4"
                                                                                    name="sansthaAdhyapanKaryaDiwas"
                                                                                    value={employee[0].basicDetails[0].sansthaAdhyapanKaryaDiwas}
                                                                                    onChange={handleInputChange}
                                                                                />
                                                                                {errors.sansthaAdhyapanKaryaDiwas && (
                                                                                    <small className="text-danger">
                                                                                        {errors.sansthaAdhyapanKaryaDiwas}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>12.</strong> वर्ष में कितने छात्रों को
                                                                                    विशेष कमजोर चिन्हित कर उनको विशेष कोचिंग दी गई :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) सामान्य श्रेणी :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="viseshKamjorSamanyaShreni"
                                                                                    value={employee[0].basicDetails[0].viseshKamjorSamanyaShreni}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.viseshKamjorSamanyaShreni && (
                                                                                    <small className="text-danger">
                                                                                        {errors.viseshKamjorSamanyaShreni}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) अनुसूचित जाति श्रेणी :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="viseshKamjorAnusuchitJati"
                                                                                    value={employee[0].basicDetails[0].viseshKamjorAnusuchitJati}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.viseshKamjorAnusuchitJati && (
                                                                                    <small className="text-danger">
                                                                                        {errors.viseshKamjorAnusuchitJati}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (तीन) अनुसूचित जनजाति श्रेणी :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="viseshKamjorAnusuchitJanJati"
                                                                                    value={employee[0].basicDetails[0].viseshKamjorAnusuchitJanJati}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.viseshKamjorAnusuchitJanJati && (
                                                                                    <small className="text-danger">
                                                                                        {errors.viseshKamjorAnusuchitJanJati}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (चार) पिछडी जाति श्रेणी :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="viseshKamjorPichadiJati"
                                                                                    value={employee[0].basicDetails[0].viseshKamjorPichadiJati}
                                                                                    onChange={handleInputChange} />
                                                                            </Col>
                                                                            {errors.viseshKamjorPichadiJati && (
                                                                                <small className="text-danger">
                                                                                    {errors.viseshKamjorPichadiJati}
                                                                                </small>
                                                                            )}
                                                                        </Row>
                                                                        <Row className="mt-2">
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (पांच) अल्प संख्यक श्रेणी :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="viseshKamjorAlpShankhyak"
                                                                                    value={employee[0].basicDetails[0].viseshKamjorAlpShankhyak}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.viseshKamjorAlpShankhyak && (
                                                                                    <small className="text-danger">
                                                                                        {errors.viseshKamjorAlpShankhyak}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>13.</strong> संस्था को वर्ष के दौरान
                                                                                    विश्वविद्यालय अनुदान आयोग से प्राप्त सहायता में
                                                                                    कितनी राशि उपयोग की गई :{" "}
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label>(एक) राशि उपयोग की गई :</Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="AnudanRashiUpyogKiGai"
                                                                                    value={employee[0].basicDetails[0].AnudanRashiUpyogKiGai}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.AnudanRashiUpyogKiGai && (
                                                                                    <small className="text-danger">
                                                                                        {errors.AnudanRashiUpyogKiGai}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label>
                                                                                    (दो) कुल उपयोग की गई राशि का प्रतिशत :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    name="upYogitRashiPratishat"
                                                                                    className="form-control"
                                                                                    value={employee[0].basicDetails[0].upYogitRashiPratishat}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.upYogitRashiPratishat && (
                                                                                    <small className="text-danger">
                                                                                        {errors.upYogitRashiPratishat}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>14.</strong> संस्था का जनभागीदारी समिति
                                                                                    की कितनी बैठक आयोजित की गई एवं कितनी-कितनी
                                                                                    अतिरिक्त राशि जनभागीदारी समिति द्वारा संस्था को
                                                                                    प्राप्त हुई :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) बैठकों की संख्या :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="janBhagidariBaithakoKiSankhya"
                                                                                    value={employee[0].basicDetails[0].janBhagidariBaithakoKiSankhya}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.janBhagidariBaithakoKiSankhya && (
                                                                                    <small className="text-danger">
                                                                                        {errors.janBhagidariBaithakoKiSankhya}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) एकत्रित राशि :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="janBhagidariekatritRashi"
                                                                                    value={employee[0].basicDetails[0].janBhagidariekatritRashi}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.janBhagidariekatritRashi && (
                                                                                    <small className="text-danger">
                                                                                        {errors.janBhagidariekatritRashi}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="4">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (तीन) उपयोग की गई राशि :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="janBhagiDariUpyogKiGaiRashi"
                                                                                    value={employee[0].basicDetails[0].janBhagiDariUpyogKiGaiRashi}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.janBhagiDariUpyogKiGaiRashi && (
                                                                                    <small className="text-danger">
                                                                                        {errors.janBhagiDariUpyogKiGaiRashi}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col md="4">
                                                                                <Label>
                                                                                    {" "}
                                                                                    <strong>15. </strong> संबंधित आडिट आपत्तियों की
                                                                                    संख्या :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="auditApptiyonKiSankhya"
                                                                                    value={employee[0].basicDetails[0].auditApptiyonKiSankhya}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.auditApptiyonKiSankhya && (
                                                                                    <small className="text-danger">
                                                                                        {errors.auditApptiyonKiSankhya}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>16.</strong>पेंशन प्रकरणों की संख्या जो
                                                                                    तैयार कर पेंशन स्वीकृतकर्ता को भेजे गये और शेष
                                                                                    पेंशन प्रकरणों की संख्या :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) कितने प्रकरण भेजे गये :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="kitnePensionPrakarBhejeGai"
                                                                                    value={employee[0].basicDetails[0].kitnePensionPrakarBhejeGai}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.kitnePensionPrakarBhejeGai && (
                                                                                    <small className="text-danger">
                                                                                        {errors.kitnePensionPrakarBhejeGai}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) कितने पेंशन प्रकरण अनिर्णीत शेष हैं :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    name="anirnitPensionPrakaran"
                                                                                    className="form-control"
                                                                                    value={employee[0].basicDetails[0].anirnitPensionPrakaran}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.anirnitPensionPrakaran && (
                                                                                    <small className="text-danger">
                                                                                        {errors.anirnitPensionPrakaran}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>17.</strong>कितने अनुशासिक कार्यवाही के
                                                                                    मामले में जॉच की गई व कितने शेष हैं :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) वर्ष प्रारंभ होने से पूर्व चले आ रहे प्रकरण
                                                                                    :
                                                                                </Label>
                                                                                <Input readOnly type="number"
                                                                                    className="form-control"
                                                                                    name="purvPrakaran"
                                                                                    value={employee[0].basicDetails[0].purvPrakaran}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.purvPrakaran && (
                                                                                    <small className="text-danger">
                                                                                        {errors.purvPrakaran}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) वर्ष के दौरान संस्थित प्रकरण :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    value={employee[0].basicDetails[0].sansthitPrakran}
                                                                                    name="sansthitPrakran"
                                                                                    onChange={handleInputChange} />
                                                                                {errors.sansthitPrakran && (
                                                                                    <small className="text-danger">
                                                                                        {errors.sansthitPrakran}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col className="mt--4" md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (तीन) वर्ष के दौरान कार्यवाही समाप्त वाले प्रकरण
                                                                                    :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="karyaWahiPrakaran"
                                                                                    value={employee[0].basicDetails[0].karyaWahiPrakaran}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.karyaWahiPrakaran && (
                                                                                    <small className="text-danger">
                                                                                        {errors.karyaWahiPrakaran}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (चार) वर्ष के अन्त में शेष प्रकरण :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="sheshPrakaran"
                                                                                    value={employee[0].basicDetails[0].sheshPrakaran}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.sheshPrakaran && (
                                                                                    <small className="text-danger">
                                                                                        {errors.sheshPrakaran}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>18. </strong> वर्ष की अवधि में प्राचार्य
                                                                                    द्वारा स्वयं कुल कितने शिक्षण कार्य किये गये
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) स्नातक स्तर की कक्षाओं के पीरियड :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="ugperiods"
                                                                                    value={employee[0].basicDetails[0].ugperiods}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.ugperiods && (
                                                                                    <small className="text-danger">
                                                                                        {errors.ugperiods}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) स्नातकोत्तर स्तर की कक्षाओं के पीरियेड :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="pgperiods"
                                                                                    value={employee[0].basicDetails[0].pgperiods}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.pgperiods && (
                                                                                    <small className="text-danger">
                                                                                        {errors.pgperiods}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>19. </strong> महाविद्यालय में अध्ययनरत
                                                                                    छात्रों की कुल संख्या :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) स्नातक स्तर पर :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="studentNougStarPar"
                                                                                    value={employee[0].basicDetails[0].studentNougStarPar}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.studentNougStarPar && (
                                                                                    <small className="text-danger">
                                                                                        {errors.studentNougStarPar}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) स्नातकोत्तर स्तर पर :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="pgperistudentNoPgStarParods"
                                                                                    value={employee[0].basicDetails[0].pgperistudentNoPgStarParods}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.pgperistudentNoPgStarParods && (
                                                                                    <small className="text-danger">
                                                                                        {errors.pgperistudentNoPgStarParods}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col>
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>20.</strong>विगत शैक्षणिक वर्ष में
                                                                                    संस्था के छात्रों के परीक्षाफल का विश्लेषण :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (एक) स्नातक प्रथम वर्ष परीक्षा :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="ugFirstExamResult"
                                                                                    value={employee[0].basicDetails[0].ugFirstExamResult}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.ugFirstExamResult && (
                                                                                    <small className="text-danger">
                                                                                        {errors.ugFirstExamResult}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (दो) स्नातक द्वितीय वर्ष परीक्षा :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="ugSecondExamResult"
                                                                                    value={employee[0].basicDetails[0].ugSecondExamResult}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.ugSecondExamResult && (
                                                                                    <small className="text-danger">
                                                                                        {errors.ugSecondExamResult}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (तीन) स्नातक तृतीय वर्ष परीक्षा :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="ugThirdExamResult"
                                                                                    value={employee[0].basicDetails[0].ugThirdExamResult}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.ugThirdExamResult && (
                                                                                    <small className="text-danger">
                                                                                        {errors.ugThirdExamResult}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (धार) स्नातकोत्तर पूर्वार्द्ध परीक्षा :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="pgFirstHalfExamResult"
                                                                                    value={employee[0].basicDetails[0].pgFirstHalfExamResult}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.pgFirstHalfExamResult && (
                                                                                    <small className="text-danger">
                                                                                        {errors.pgFirstHalfExamResult}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                        <Row className="mt-2">
                                                                            <Col md="3">
                                                                                <Label style={{ fontSize: "15px" }}>
                                                                                    (पांच) स्नातकोत्तर उत्तरार्द्ध परीक्षा :
                                                                                </Label>
                                                                                <Input readOnly type="text"
                                                                                    className="form-control"
                                                                                    name="pgSecondHalfExamResult"
                                                                                    value={employee[0].basicDetails[0].pgSecondHalfExamResult}
                                                                                    onChange={handleInputChange} />
                                                                                {errors.pgSecondHalfExamResult && (
                                                                                    <small className="text-danger">
                                                                                        {errors.pgSecondHalfExamResult}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-flex mt-3 mb-3">
                                                                    <Col md="12">
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>21. </strong> संस्था के छात्रों द्वारा
                                                                                    वर्ष के दौरान कोई विशिष्ट उपलब्धि प्राप्त की गई
                                                                                    थी तो उसका संक्षिप्त विवरण :
                                                                                </Label>
                                                                                <Input type="textarea"
                                                                                    className="form-control"
                                                                                    name="visisthUplabdhi"
                                                                                    value={employee[0].basicDetails[0].visisthUplabdhi}
                                                                                    onChange={handleInputChange}
                                                                                    readOnly
                                                                                />
                                                                                {errors.visisthUplabdhi && (
                                                                                    <small className="text-danger">
                                                                                        {errors.visisthUplabdhi}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-flex mt-3 mb-3">
                                                                    <Col md="12">
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>22. </strong> संस्था के छात्रों का यदि
                                                                                    किसी महत्वपूर्ण शासकीय सेवा या अशासकीय संगठन में
                                                                                    किसी उच्च पद पर चयन हुआ हो तो उसका उल्लेख करें :
                                                                                </Label>
                                                                                <Input type="textarea" className="form-control"
                                                                                    name="mahatvapurnaSaskiyaSevaChayan"
                                                                                    value={employee[0].basicDetails[0].mahatvapurnaSaskiyaSevaChayan}
                                                                                    onChange={handleInputChange}
                                                                                    readOnly
                                                                                />
                                                                                {errors.mahatvapurnaSaskiyaSevaChayan && (
                                                                                    <small className="text-danger">
                                                                                        {errors.mahatvapurnaSaskiyaSevaChayan}
                                                                                    </small>
                                                                                )}
                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                                <Row className="d-block mt-3 mb-3">
                                                                    <Col md="12">
                                                                        <Row>
                                                                            <Col>
                                                                                <Label>
                                                                                    <strong>23. </strong>
                                                                                    वर्ष के दौरान आपके द्वारा पढ़ी गई सबसे अच्छी तीन पुस्तकों के नाम लेखक व प्रकाशक का नाम :
                                                                                </Label>
                                                                            </Col>
                                                                        </Row>
                                                                        <Row>
                                                                            <Col lg="12">
                                                                                {employee[0].basicDetails[0].books.map((book, index) => (
                                                                                    <Row key={index}>
                                                                                        <Col>
                                                                                            <FormGroup>
                                                                                                <Label style={{ fontWeight: "700" }} for={`bookName${index}`}>
                                                                                                    पुस्तक {index + 1}
                                                                                                </Label>
                                                                                                <Input readOnly
                                                                                                    type="text"
                                                                                                    id={`bookName${index}`}
                                                                                                    value={book.name}
                                                                                                    onChange={(e) => handleChange(index, "name", e.target.value)}
                                                                                                    required
                                                                                                />
                                                                                            </FormGroup>
                                                                                        </Col>
                                                                                        <Col>
                                                                                            <FormGroup>
                                                                                                <Label style={{ fontWeight: "700" }} for={`bookWriter${index}`}>
                                                                                                    लेखक
                                                                                                </Label>
                                                                                                <Input readOnly
                                                                                                    type="text"
                                                                                                    id={`bookWriter${index}`}
                                                                                                    value={book.writer}
                                                                                                    onChange={(e) => handleChange(index, "writer", e.target.value)}
                                                                                                    required
                                                                                                />
                                                                                            </FormGroup>
                                                                                        </Col>
                                                                                        <Col>
                                                                                            <FormGroup>
                                                                                                <Label style={{ fontWeight: "700" }} for={`bookPublisher${index}`}>
                                                                                                    प्रकाशक
                                                                                                </Label>
                                                                                                <Input readOnly
                                                                                                    type="text"
                                                                                                    id={`bookPublisher${index}`}
                                                                                                    value={book.publisher}
                                                                                                    onChange={(e) => handleChange(index, "publisher", e.target.value)}
                                                                                                    required
                                                                                                />
                                                                                            </FormGroup>
                                                                                        </Col>
                                                                                    </Row>
                                                                                ))}
                                                                                {/* Disable the button if there are already 3 books */}

                                                                            </Col>
                                                                        </Row>
                                                                    </Col>
                                                                </Row>
                                                            </Col>
                                                        </Row>
                                                    </div>
                                                </CardBody>
                                            </Card>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row className="mt-3 mb-3 text-center">
                                        <Col>
                                            <h2>
                                                <u> प्रतिवेदक प्राधिकारी द्वारा मूल्यांकन
                                                </u>
                                            </h2>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col>
                                            <Row className="d-block mt-3 mb-3">
                                                <Col>
                                                    <Label>
                                                        <strong> 01.</strong> क्या आप स्व मूल्यांकन में उल्लेखित किसी उत्तर से असहमत है
                                                        या त्रुटिपूर्ण समझते है । यदि हों, तो विवरण देते हुए अपना अभिमत दें :

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        maxLength={500}
                                                        name="swamulyankanSahmati"
                                                        readOnly
                                                        value={employee[0].levelDetails[0].data.swamulyankanSahmati}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.swamulyankanSahmati && (
                                                        <small className="text-danger">
                                                            {errors.swamulyankanSahmati}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 02.</strong> प्रतिवेदित प्राचार्य द्वारा संस्था को जिस
                                                        प्रकार संचालित किया गया उसका वर्गीकरण करें और अभिमत का
                                                        आधार भी बताये ।
                                                    </Label>
                                                    <Input
                                                        type="select"
                                                        id="ratingSelect"
                                                        value={employee[0].levelDetails[0].data.abhimatAadhar}
                                                        name="abhimatAadhar" readOnly
                                                        onChange={handleInputChange}
                                                        className="form-control"
                                                        required
                                                    >
                                                        <option value="">चुनें </option>
                                                        <option value="अत्यंत प्रशंसनीय">अत्यंत प्रशंसनीय</option>
                                                        <option value="संतोषजनक अच्छा">संतोषजनक अच्छा</option>
                                                        <option value="अपर्याप्त">अपर्याप्त</option>
                                                    </Input>
                                                    {errors.abhimatAadhar && (
                                                        <small className="text-danger">
                                                            {errors.abhimatAadhar}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 03.</strong>प्राचार्य द्वारा प्रशासनिक कार्यों एवं उत्तर दायित्वों को
                                                        समय पर पूर्ण करने में रूचिः
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        name="dayitwaPurnSuchi"
                                                        maxLength={150}
                                                        readOnly
                                                        value={employee[0].levelDetails[0].data.dayitwaPurnSuchi}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.dayitwaPurnSuchi && (
                                                        <small className="text-danger">
                                                            {errors.dayitwaPurnSuchi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 04.</strong>प्राचार्य द्वारा अधीनस्थ शैक्षणिक स्टाफ पर नियन्त्रण एवं
                                                        पर्यवेक्षण का मूल्यांकनः
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        maxLength={150}
                                                        readOnly
                                                        name="teachingStaffNiyantranMulyankan"
                                                        value={employee[0].levelDetails[0].data.teachingStaffNiyantranMulyankan}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.teachingStaffNiyantranMulyankan && (
                                                        <small className="text-danger">
                                                            {errors.teachingStaffNiyantranMulyankan}
                                                        </small>
                                                    )}
                                                </Col>

                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 05.</strong>प्राचार्य द्वारा संस्था के अशैक्षणिक स्टाफ पर
                                                        नियन्त्रण एवं पर्यवेक्षण का मूल्यांकनः
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        maxLength={150}
                                                        readOnly
                                                        name="nonTeachingNiyantranMulyankan"
                                                        value={employee[0].levelDetails[0].data.nonTeachingNiyantranMulyankan}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.nonTeachingNiyantranMulyankan && (
                                                        <small className="text-danger">
                                                            {errors.nonTeachingNiyantranMulyankan}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 06.</strong>प्राचार्य द्वारा जनभागीदारी समिति के माध्यम से प्राप्त
                                                        जनसहयोग एवं संस्था के हित में प्राप्त उपलब्धियों का मूल्यांकनः

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="janBhagidariUplabdhiMulyankan"
                                                        value={employee[0].levelDetails[0].data.janBhagidariUplabdhiMulyankan}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.janBhagidariUplabdhiMulyankan && (
                                                        <small className="text-danger">
                                                            {errors.janBhagidariUplabdhiMulyankan}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 07.</strong>प्राचार्य द्वारा संस्था के छात्रों की शैक्षणिक उपलब्धियों
                                                        को उच्च स्तर प्राप्त करने के लिए किये गये प्रायासो का मूल्यांकन :
                                                    </Label>

                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="sansthaChhatraUchhStarMulyankarn"
                                                        value={employee[0].levelDetails[0].data.sansthaChhatraUchhStarMulyankarn}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.sansthaChhatraUchhStarMulyankarn && (
                                                        <small className="text-danger">
                                                            {errors.sansthaChhatraUchhStarMulyankarn}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 08.</strong> प्राचार्य द्वारा संस्था के विशेष कमजोर छात्रों
                                                        के लिये की गई व्यवस्था का मूल्यांकन और उनके
                                                        प्रयासो के फलस्वरूप प्राप्त नतीजों का मूल्यांकन :

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="visheshKamjorChhatraVyavastha"
                                                        value={employee[0].levelDetails[0].data.visheshKamjorChhatraVyavastha}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.visheshKamjorChhatraVyavastha && (
                                                        <small className="text-danger">
                                                            {errors.visheshKamjorChhatraVyavastha}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 09.</strong>प्राचार्य के अधीनस्थ कर्मचारियों की समस्याओं के
                                                        निराकरण हेतु संवेदनशीलता का मूल्यांकन

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="samasyaNirakaranSanvedanSeelta"
                                                        value={employee[0].levelDetails[0].data.samasyaNirakaranSanvedanSeelta}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.samasyaNirakaranSanvedanSeelta && (
                                                        <small className="text-danger">
                                                            {errors.samasyaNirakaranSanvedanSeelta}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 10.</strong>छात्रों की समस्याओं एवं कठिनायों को हल करने
                                                        की दिशा में प्राचार्य द्वारा प्रदर्शित संवेदनशीलता :
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="chhatraSamasyaSanvedanSeelta"
                                                        value={employee[0].levelDetails[0].data.chhatraSamasyaSanvedanSeelta}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.chhatraSamasyaSanvedanSeelta && (
                                                        <small className="text-danger">
                                                            {errors.chhatraSamasyaSanvedanSeelta}
                                                        </small>
                                                    )}
                                                </Col>

                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 11.</strong> प्राचार्य द्वारा अन्य अधिकारियों, जिला प्रशासन
                                                        एवं उच्चर अधिकारियों को दिये गये एवं
                                                        उनसे प्राप्त सहयोग का मूल्यांकन :
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        maxLength={150}
                                                        className="form-control"
                                                        readOnly
                                                        name="adhikariSahyogMulyankan"
                                                        value={employee[0].levelDetails[0].data.adhikariSahyogMulyankan}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.adhikariSahyogMulyankan && (
                                                        <small className="text-danger">
                                                            {errors.adhikariSahyogMulyankan}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="6">
                                                    <Label>
                                                        <strong> 12.</strong>प्राचार्य की योजना बद्ध रूप से कार्य करने की
                                                        क्षमता एवं समस्याओं का पूर्वानुमान लगाकर
                                                        तैयारी करने की योग्यता का मूल्यांकन :
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        maxLength={150}
                                                        readOnly
                                                        name="purwanumanMulyankan"
                                                        value={employee[0].levelDetails[0].data.purwanumanMulyankan}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.purwanumanMulyankan && (
                                                        <small className="text-danger">
                                                            {errors.purwanumanMulyankan}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col lg="4">
                                                    <Label>
                                                        <strong> 13.</strong> वर्गीकरण :
                                                    </Label>
                                                    <Input
                                                        type="select"
                                                        id="ratingSelect"
                                                        className="form-control"
                                                        name="remark"
                                                        readOnly
                                                        value={employee[0].levelDetails[0].data.remark}
                                                        onChange={handleInputChange}
                                                        required
                                                    >
                                                        <option value="">चुनें </option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा ">बहुत अच्छा </option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="संतोषप्रद">संतोषप्रद</option>
                                                        <option value="अपर्याप्त">अपर्याप्त</option>
                                                    </Input>
                                                    {errors.remark && (
                                                        <small className="text-danger">
                                                            {errors.remark}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col lg="12">
                                                    <Label>
                                                        <strong> 14.</strong> विवरणात्मक टीप :
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        className="form-control"
                                                        maxLength={500}
                                                        readOnly
                                                        name="vivranatmakTeep"
                                                        value={employee[0].levelDetails[0].data.vivranatmakTeep}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.vivranatmakTeep && (
                                                        <small className="text-danger">
                                                            {errors.vivranatmakTeep}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <hr />
                                            <Row className="mb-3">
                                                <Col>
                                                    <h2 style={{ textAlign: "center" }}>
                                                        <u> समीक्षक अधिकारी की टिप्पणी </u>
                                                    </h2>
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="3">
                                                    <Label>श्रेणीकरण</Label>
                                                    <Input
                                                        name="shreniKaran1"
                                                        type="select"
                                                        id="recordsPerPage"
                                                        readOnly
                                                        value={employee[0].levelDetails[1].data.shreniKaran1}
                                                        onChange={handleInputChange}

                                                    >
                                                        <option value="">चुनें</option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="साधारण">साधारण</option>
                                                        <option value="घटिया">घटिया</option>
                                                    </Input>
                                                    {errors.shreniKaran1 && (
                                                        <small className="text-danger">
                                                            {errors.shreniKaran1}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="d-flex mt-3 mb-3">
                                                <Col md="12">
                                                    <Label>रिमार्क</Label>
                                                    <Input
                                                        name="remark1"
                                                        type="textarea"
                                                        maxLength={500}
                                                        readOnly
                                                        style={{ height: "100px" }}
                                                        value={employee[0].levelDetails[1].data.remark1}
                                                        onChange={handleInputChange}

                                                    />
                                                    {errors.remark1 && (
                                                        <small className="text-danger">
                                                            {errors.remark1}
                                                        </small>)}
                                                </Col>
                                            </Row>
                                            <hr />
                                            <Row className="mb-3">
                                                <Col>
                                                    <h2 style={{ textAlign: "center" }}>
                                                        <u>

                                                            स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                    </h2>
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="3">
                                                    <Label>श्रेणीकरण</Label>
                                                    <Input
                                                        name="shreniKaran2"
                                                        type="select"
                                                        id="recordsPerPage"
                                                        value={formData.shreniKaran2}
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="">चुनें</option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="साधारण">साधारण</option>
                                                        <option value="घटिया">घटिया</option>
                                                    </Input>
                                                    {errors.shreniKaran2 && (
                                                        <small className="text-danger">
                                                            {errors.shreniKaran2}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="12">
                                                    <Label>रिमार्क</Label>
                                                    <Input
                                                        name="remark2"
                                                        type="textarea"
                                                        value={formData.remark2}
                                                        maxLength={500}
                                                        style={{ height: "100px" }}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.remark2 && (
                                                        <small className="text-danger">{errors.remark2}</small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Button color="success" onClick={handleFinalSubmit}>
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRFormUGandPGPart4.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRFormUGandPGPart4;
