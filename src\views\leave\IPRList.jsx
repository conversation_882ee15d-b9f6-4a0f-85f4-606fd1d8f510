import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  Con<PERSON>er,
  <PERSON>,
  Row,
  <PERSON>,
  Modal,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ge,
  Input
} from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import Swal from "sweetalert2";
import { Link } from "react-router-dom";



const IPRList = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const userId = sessionStorage.getItem("id");
  const userType = sessionStorage.getItem("userType");
  const [filterText, setFilterText] = useState("");
  const [lastIprFiled, setLastIprFiled] = useState([]);

  const [singleIprFiled, setSingleIprFiled] = useState({
    employeeName: "",
    currentDesignation: "",
    officeAddress: "",
    currentSalary: "",
    nextIncrementDate: "",
    property: [
      {
        location: "",
        details: "",
        price: "",
        ownerName: "",
        ownerRelation: "",
        purchaseType: "",
        purchaseDate: "",
        sellerName: "",
        sellerDetails: "",
        incomeFromProperty: "",
        remarks: "",
      },
    ],
  });
  const [previewIPRModal, setPreviewIPRModal] = useState(false);
  const togglePreviewIPRModal = () => setPreviewIPRModal(!previewIPRModal);
  const [user, setUser] = useState("");
  useEffect(() => {
    if (userType === "Employee") {
      const fetchEmployeeData = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/employee/get-employee-bycode/${userId}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = response.data[0];
            setUser(data);
          } else {
            SwalMessageAlert("Data Not Fetched.", "error");
          }
        } catch (error) {
          console.error("An error occurred while Getting Data:", error);
          alert("An error occurred. Please try again later.");
        }
      };
      fetchEmployeeData();
    }
    if (userType === "Institute") {
      const fetchLastIprFiledInstituteWise = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/ipr/getAll/${userId}?userType=Institute`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          if (response.status === 200) {
            const data = Array.isArray(response.data)
              ? response.data
              : [response.data];
            setLastIprFiled(data);
          } else {
            SwalMessageAlert("Failed to fetch IPR data.", "error");
            setLastIprFiled([]);
          }
        } catch (error) {
          handleApiError(error);
          setLastIprFiled([]);
        }
      };
      fetchLastIprFiledInstituteWise();
    }
  }, [token, endPoint, userId]);
  useEffect(() => {
    if (user) {
      setSingleIprFiled((prevData) => ({
        ...prevData,
        currentDesignation: user["designationDetails"].designation,
        officeAddress: user["collegeDetails"].address,
        currentSalary: user.currentSalary,
        nextIncrementDate: user.nextIncrementDate,
      }));
    }
  }, [user]);



  const fetchLastIprFiled = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/ipr/getAll/${userId}?userType=Employee`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = Array.isArray(response.data)
          ? response.data
          : [response.data];
        setLastIprFiled(data);
      } else {
        SwalMessageAlert("Failed to fetch IPR data.", "error");
        setLastIprFiled([]);
      }
    } catch (error) {
      handleApiError(error);
      setLastIprFiled([]);
    }
  };

  useEffect(() => {
    if (userType === "Employee") {
      fetchLastIprFiled();
    }
  }, []);

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };

  const handleForward = async (item) => {


    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, Forward',
      cancelButtonText: 'Not Now!',

    }).then(async (result) => {
      if (result.isConfirmed) {

        try {
          // console.log("Forwarding the IPR...");

          const response = await axios.put(
            `${endPoint}/api/ipr/forward-ipr/${item}`,
            { isForwarded: true },
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            // console.log("Response:", response);
            SwalMessageAlert('IPR Forwarded Successfully.', 'success');
            setTimeout(function () {
              window.location.reload();
            }, 3000);
          } else {
            console.error("Error: Unexpected status", response.status);
            SwalMessageAlert('Failed to forward the IPR.', 'error');
          }
        } catch (error) {
          console.error("API Error:", error); // Log the error for debugging
          handleApiError(error); // Custom error handler
        }
      }
    });
  };

  const columns = [
    {
      name: "View",
      cell: (lastIprFiled) => (
        <>
          <button
            className="btn btn-primary btn-sm"
            title="View Details"
            onClick={handleViewIPR(lastIprFiled._id)}
          >
            <i className="fa fa-eye"></i>
          </button>
          < Link to={`/admin/PreviewIPR/${lastIprFiled._id}`}>
            <Button className=" btn btn-sm m-2" style={{ backgroundColor: "orange" }}>
              <i className="fas fa-print"></i>
            </Button>
          </Link>

          {/* <button
                className="btn btn-warning btn-sm"
                title="Edit Details"
                style={{ display: lastIprFiled.iprFinalSubmit ? "none" : "" }}
                onClick={handleEditIPR(lastIprFiled._id)}
              >
                Edit <i className="fa fa-edit"></i>
              </button> */}
        </>
      ),
    },
    {
      name: "Name",
      cell: (lastIprFiled) => lastIprFiled.employeeName,
    },
    {
      name: "Application Id",
      cell: (lastIprFiled) => lastIprFiled.applicationId,
    },
    {
      name: "Draft Status",
      cell: (lastIprFiled) =>
        !lastIprFiled.iprDraft ? (
          <>
            {" "}
            <button className="btn btn-danger btn-sm" title="Edit Details">
              No Draft
            </button>
          </>
        ) : (
          <>
            {" "}
            <button className="btn btn-success btn-sm" title="Edit Details">
              Drafted
            </button>
          </>
        ),
    },
    {
      name: "Date Of Draft",
      cell: (lastIprFiled) => formatDate(lastIprFiled.createdAt),
    },
    {
      name: "Final Submit Status",
      cell: (lastIprFiled) =>
        !lastIprFiled.iprFinalSubmit ? (
          <>
            {" "}
            <button className="btn btn-danger btn-sm" title="Edit Details">
              Not Submit
            </button>
          </>
        ) : (
          <>
            {" "}
            <button className="btn btn-success btn-sm" title="Edit Details">
              Submited
            </button>
          </>
        ),
    },
    {
      name: "Date Of Final Submit",
      cell: (lastIprFiled) =>
        lastIprFiled.iprFinalSubmitDate
          ? formatDate(lastIprFiled.iprFinalSubmitDate)
          : "No Date",
    },
    {
      name: "Accepted By Institute",
      cell: (lastIprFiled) =>
        !lastIprFiled.iprAccepted ? (
          <>
            {" "}
            <button className="btn btn-danger btn-sm" title="Edit Details">
              Not Accepted
            </button>
          </>
        ) : (
          <>
            {" "}
            <button className="btn btn-success btn-sm" title="Edit Details">
              Accepted
            </button>
          </>
        ),
    },
    ...(userType === "Institute"
      ? [
        {
          name: "Action",
          cell: (lastIprFiled) =>
            !lastIprFiled.iprFinalSubmit ? (
              <>
                {" "}
                <Badge
                  className="bg-warning text-white"
                  style={{ fontSize: "10px" }}
                  title="Waiting For Final Submit"
                >
                  <Spinner
                    size="sm"
                    color="white"
                    style={{ marginRight: "8px" }}
                  />
                  Waiting Final Submit
                </Badge>
              </>
            ) : !lastIprFiled.iprAccepted ? (
              <>
                {" "}
                <button
                  className="btn btn-primary btn-sm"
                  onClick={verifyStatus(lastIprFiled._id)}
                  title="Edit Details"
                >
                  Verify And Accept
                </button>
              </>
            ) :(<>
             <button
                  className="btn btn-success btn-sm"
                  title="Forward"
                >
                  <span className="fa fa-check"></span>
                </button>
            </>)
            // : lastIprFiled.isForwarded === false ? (
            //   <>

            //     <button
            //       className="btn btn-warning btn-sm"
            //       title="Forward"
            //       onClick={() => handleForward(lastIprFiled._id)}
            //     >
            //       Forward
            //     </button>
            //   </>
            // ) : (
            //   <>

            //     <button
            //       className="btn btn-primary btn-sm"
            //       title="Forward"
            //     // onClick={() => handleForward(lastIprFiled._id)}
            //     >
            //       Forwarded
            //     </button>
            //   </>
            // )
        },
      ]
      : []),
  ];
  const verifyStatus = (id) => async () => {
    try {
      const response = await axios.get(`${endPoint}/api/ipr/accept-ipr/${id}`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      // console.log(response);
      if (response.status === 200) {
        SwalMessageAlert("IPR Accepted Successfully", "success");
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      } else {
        SwalMessageAlert("IPR Accept Failed", "error");
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };
  const filteredData = lastIprFiled.filter((item) => {
    const filterTextLower = filterText.toLowerCase();
    return (
      item.applicationId &&
      item.applicationId.toLowerCase().includes(filterTextLower) ||
       item.employeeName &&
      item.employeeName.toLowerCase().includes(filterTextLower)
    );
  });
  const [vivranDate, setvivranDate] = useState(null);
  const handleViewIPR = (id) => async () => {
    try {
      const response = await axios.get(`${endPoint}/api/ipr/get-single/${id}`, {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      if (response.status === 200) {
        const data = Array.isArray(response.data)
          ? response.data
          : [response.data];
        const body = {
          employeeName: data[0].employeeName || "",
          currentDesignation: data[0].currentDesignation || "",
          officeAddress: data[0].officeAddress || "",
          currentSalary: data[0].currentSalary || "",
          nextIncrementDate: data[0].nextIncrementDate || "",
          property: data[0].properties,
          applicationId: data[0].applicationId || "",
        };
        setSingleIprFiled(body);
        const applicationIdArray = body.applicationId.split("/");
        const vivranDate = Number(applicationIdArray[2]) - 1;
        setvivranDate(vivranDate);
      } else {
        SwalMessageAlert("Data Not Fetched.", "error");
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
    setPreviewIPRModal(true);
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <h3 className="mb-0">IPR List</h3>
              <Input
                              type="text"
                              placeholder="Search Name OR Application ID..."
                              className="ml-auto"
                              value={filterText}
                              onChange={(e) => setFilterText(e.target.value)}
                              style={{ width: "350px", borderRadius: "30px" }}
                            />
          </CardHeader>

          <CardBody>
            <DataTable
              columns={columns}
              data={filteredData}
              pagination
              paginationPerPage={10}
              highlightOnHover
              striped
              customStyles={{
                header: {
                  style: { backgroundColor: "#f8f9fa", fontWeight: "bold" },
                },
                rows: {
                  style: {
                    backgroundColor: "#fff",
                    borderBottom: "1px solid #ddd",
                  },
                  onHoverStyle: { backgroundColor: "#ffff99" },
                },
              }}
            />
          </CardBody>
        </Card>

        <Modal
          isOpen={previewIPRModal}
          toggle={togglePreviewIPRModal}
          style={{
            maxWidth: "1200px", // Adjust modal width
            width: "90%", // Responsive width
            textAlign: "center", // Center all text content
          }}
        >
          <ModalHeader
            style={{
              justifyContent: "center",
              textAlign: "center",
              display: "flex", // Ensure centering
              marginLeft: "140px", // Stack items vertically
            }}
            toggle={togglePreviewIPRModal}
          >
            <CardHeader>
              <Row
                style={{
                  justifyContent: "center", // Center horizontally
                  display: "flex", // Use flexbox for alignment
                }}
              >
                <Col></Col>
                <Col className="text-center">
                  <span
                    style={{
                      fontSize: "24px",
                      fontWeight: "bolder",
                      display: "block", // Ensure block-level behavior
                    }}
                  >
                    प्रपत्र - एक
                  </span>
                </Col>
                <Col></Col>
              </Row>
              <Row
                style={{
                  justifyContent: "center",
                  display: "flex",
                  marginTop: "10px", // Add some spacing between rows
                }}
              >
                <Col md="12" className="text-center">
                  <span
                    style={{
                      fontSize: "24px",
                      fontWeight: "bolder",
                      display: "block",
                    }}
                  >
                    'शासकीय सेवक का अचल सम्पत्ति का वार्षिक विवरण : - 31.12.
                    {vivranDate}
                    की स्थिति में'
                  </span>
                </Col>
              </Row>
            </CardHeader>
          </ModalHeader>
          <ModalBody>
            <table
              className="table"
              style={{ lineHeight: "5px", textAlign: "left" }}
            >
              <tbody>
                <tr>
                  <td>1.अधिकारी / कर्मचारी का पूरा नाम :</td>
                  <td>{singleIprFiled.employeeName}</td>
                </tr>
                <tr>
                  <td>2.वर्तमान पद :</td>
                  <td>{singleIprFiled.currentDesignation}</td>
                </tr>
                <tr>
                  <td>3.वर्तमान वेतन : </td>
                  <td>{singleIprFiled.currentSalary}</td>
                </tr>
                <tr>
                  <td>4.कार्यालय / महाविद्यालय का नाम / पता :</td>
                  <td>{singleIprFiled.officeAddress}</td>
                </tr>
                <tr>
                  <td>5.अगली वेतन वृद्धि की तारीख :</td>
                  <td>
                    {singleIprFiled.nextIncrementDate
                      ? formatDate(singleIprFiled.nextIncrementDate)
                      : ""}
                  </td>
                </tr>
              </tbody>
            </table>
            <Row>
              <Table striped responsive bordered hover>
                <thead>
                  <tr
                    style={{
                      fontSize: "12px",
                      textAlign: "center",
                      padding: "0px",
                      verticalAlign: "middle",
                    }}
                  >
                    <th style={{ padding: "0", margin: "0" }}>क्र.</th>
                    <th style={{ padding: "0", margin: "0" }}>
                      संभाग, जिला तथा ग्राम का नाम जिसमे संपत्ति स्थित हो
                      :
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      संपत्ति का नाम तथा <br /> ब्यौरा गृह तथा <br /> भूमि अन्य
                      भवन .
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      वर्तमान मूल्य{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      यदि स्वयं के नाम <br /> पर न हों तो बताइये <br /> कि वह
                      जिसके <br /> नाम पर धारित है{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      और उनका <br />
                      शासकीय कर्मचारी <br /> से क्या सम्बन्ध है{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      उसे किस प्रकार <br /> अर्जित किया गया ,<br /> पट्टी बंधक
                      ,विरासत भेंट
                      <br /> या अन्य किसी प्रकार से{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      अर्जन वर्ष {" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      जिससे अर्जन की <br />
                      गयी उसका नाम{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      जिससे अर्जन की <br /> गयी उसका ब्यौरा{" "}
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>
                      संपत्ति से <br />
                      वार्षिक आय
                    </th>
                    <th style={{ padding: "0", margin: "0" }}>अभियुक्ति </th>
                  </tr>
                </thead>
                <tbody>
                  {singleIprFiled.property &&
                    singleIprFiled.property.length > 0 ? (
                    singleIprFiled.property.map((detail, index) => (
                      <tr key={index}>
                        {/* {location: '', details: '', price: '', ownerName: '', ownerRelation: '', purchaseType: '', purchaseDate: '', sellerName: '', sellerDetails: "", incomeFromProperty: '', remarks: '' } */}
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {index + 1}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.location}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.details}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.price}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.ownerName}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.ownerRelation}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.purchaseType}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.purchaseDate}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.sellerName}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.sellerDetails}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.incomeFromProperty}
                        </td>
                        <td
                          style={{
                            fontSize: "14px",
                            backgroundColor: "white",
                          }}
                        >
                          {detail.remarks}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan="12">निरंक </td>
                    </tr>
                  )}
                </tbody>
              </Table>
            </Row>
          </ModalBody>
          <ModalFooter>


            <Button color="secondary" onClick={togglePreviewIPRModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default IPRList;
