import React, { useEffect, useState } from "react";
import { Route, Routes, Link } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Row,
  Col,
  Label
} from "reactstrap";
import { FaUser } from 'react-icons/fa';
import axios from "axios";

const Register = () => {

  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem('authToken')
  const inputStyle = {
    color: "gray", // Set the desired gray color
    borderColor: "#ccc" // Optional: Set border color for better visibility
  };
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    contact: '',
    divison: '',
    district: '',
    vidhansabha: '',
    college: '',
    designation: '',
    class: '',
    address: '',
  });

  const [university, setUniversity] = useState([]);


  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/university/get-all-university`, {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch University data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();


    // Optionally add dependencies in the dependency array
  }, []); // Include value and token as dependencies if they can change


  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const division = [
    { value: 1, name: "Bastar" },
    { value: 2, name: "Bilaspur" },
    { value: 3, name: "Durg" },
    { value: 4, name: "Raipur" },
    { value: 5, name: "Sarguja" },
  ]


  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.value === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };


  const handleDivisionChange = async (e) => {
    const { name, value } = e.target;
    try {
      const response = await axios.get(`${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
      if (response.status === 200) {
        setDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      divison: value,
    });
  }


  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    setFormData({
      ...formData,
      district: value,
    });
    try {
      const response = await axios.get(`${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
      if (response.status === 200) {
        setVidhansabha(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
  }
  const [college, setCollege] = useState([]);

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchCollege();

    // Optionally add dependencies in the dependency array
  }, []); // Include value and token as dependencies if they can change

  return (
    <>
      <Col lg="12" md="8">
        <Card className="bg-white shadow border-0">
          <CardHeader className="bg-transparent pb--5 d-block">
            <div className="text-center  mb-3">
              <FaUser style={{ height: '40px', width: '40px', color: '#003ca7', textAlign: "center" }} />
            </div>
            <div className="text-center ">
              <h2 style={{ color: "#005cff", marginTop: "10px", fontFamily: "Cambria, Cochin, Georgia, Times, 'Times New Roman', serif" }}>EMPLOYEE SIGNUP</h2>
            </div>

          </CardHeader>
          <CardBody className="">

            <Form role="form">
              <Row>
                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="name" className="form-control-label">Name</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-hat-3" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="name" name="name" placeholder="Name" type="text" style={inputStyle} />
                    </InputGroup>
                  </FormGroup>
                </Col>

                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="email" className="form-control-label">Email</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-email-83" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input
                        id="email"
                        placeholder="Email"
                        name="email"
                        type="email"
                        style={inputStyle}
                        autoComplete="new-email"
                      />
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>

              <Row>
                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="contact" className="form-control-label">Contact</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-mobile-button" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="contact" name="contact" placeholder="Contact" type="tel" style={inputStyle} />
                    </InputGroup>
                  </FormGroup>
                </Col>

                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="division" className="form-control-label">Select Division</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-map-big" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input
                        name="divison"
                        id="input-division"
                        type="select"
                        value={formData.divison}
                        onChange={handleDivisionChange}
                        required
                        style={inputStyle}
                      >
                        <option value="">Select Division</option>
                        {division && division.length > 0 && division.map((type, index) => (
                          <option key={index} value={type.value}>
                            {type.name}
                          </option>
                        ))}
                      </Input>
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>

              <Row>
                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="district" className="form-control-label">Select District</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-map-big" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input
                        name="district"
                        id="input-district"
                        type="select"
                        value={formData.district}
                        onChange={handleDistrictChange}
                        required
                        style={inputStyle}
                      >
                        <option value="">Select District</option>
                        {district && district.length > 0 && district.map((type, index) => (
                          <option key={index} value={type.LGDCode}>
                            {type.districtNameEng}
                          </option>
                        ))}
                      </Input>
                    </InputGroup>
                  </FormGroup>
                </Col>

                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="vidhanSabha" className="form-control-label">Select Vidhan Sabha</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-map-big" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input
                        name="vidhansabha"
                        id="input-vidhansabha"
                        type="select"
                        value={formData.vidhansabha}
                        onChange={handleInputChange}
                        required
                      >
                        <option value="">Select Vidhan Sabha</option>
                        {vidhansabha && vidhansabha.length > 0 && vidhansabha.map((type, index) => (
                          <option key={index} value={type.ConstituencyNumber}>
                            {type.ConstituencyName}
                          </option>
                        ))}
                      </Input>
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>

              <Row>
                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="college" className="form-control-label">Select College</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-building" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="college" name="college" type="select" style={inputStyle}>
                        <option value="">Select College</option>
                        {college && college.length > 0 && college.map((type, index) => (
                          <option key={index} value={type._id}>
                            {type.name}
                          </option>
                        ))}
                      </Input>
                    </InputGroup>
                  </FormGroup>
                </Col>

                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="designation" className="form-control-label">Designation</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-briefcase-24" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="designation" name="designation" placeholder="Designation" style={inputStyle} type="text" />
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>

              <Row>
                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="class" className="form-control-label">Select Class</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-book-bookmark" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="class" type="select" name="class" style={inputStyle}>
                        <option value="">Select Class</option>
                        <option value="1">Class 1</option>
                        <option value="2">Class 2</option>
                        <option value="3">Class 3</option>
                        <option value="4">Class 4</option>
                      </Input>
                    </InputGroup>
                  </FormGroup>
                </Col>

                <Col lg="6" md="12">
                  <FormGroup>
                    <Label for="address" className="form-control-label">Address</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend">
                        <InputGroupText>
                          <i className="ni ni-map-pin" />
                        </InputGroupText>
                      </InputGroupAddon>
                      <Input id="address" placeholder="Address" name="address" type="text" style={inputStyle} />
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>

              {/* <Row>
                  <Col lg="12">
                    <FormGroup>
                      <Label for="profileUpload" className="form-control-label">Upload Profile</Label>
                      <InputGroup className="input-group-alternative mb-3">
                        <InputGroupAddon addonType="prepend"></InputGroupAddon>
                        <Input id="profileUpload" type="file" accept="image/*" style={inputStyle} />
                      </InputGroup>
                    </FormGroup>
                  </Col>
                </Row> */}

              {/* <Row className="my-4">
                  <Col xs="12">
                    <div className="custom-control custom-control-alternative custom-checkbox">
                      <input
                        className="custom-control-input"
                        id="customCheckRegister"
                        type="checkbox"
                      />
                      <label
                        className="custom-control-label"
                        htmlFor="customCheckRegister"
                      >
                        <span className="text-muted">
                          I agree with the{" "}
                          <a href="#" onClick={(e) => e.preventDefault()}>
                            Privacy Policy
                          </a>
                        </span>
                      </label>
                    </div>
                  </Col>
                </Row> */}

              <div className="text-center">
                <Button className="mt-4" color="primary" type="button">
                  Create Account
                </Button>
              </div>
            </Form>

          </CardBody>
        </Card>
      </Col>
    </>
  );
};

export default Register;
