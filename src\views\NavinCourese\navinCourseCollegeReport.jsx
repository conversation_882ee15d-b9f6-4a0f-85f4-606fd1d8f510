import { useEffect, useState } from "react";
import {
    Card,
    CardHeader,
    CardBody,
    Table,
    Container,
    Row,
    Input,
    Col,
    Modal,
    ModalHeader,
    ModalBody,
    Form,
    Label,
    ModalFooter,
    <PERSON><PERSON>,
    Badge,
} from "reactstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { FaDownload } from "react-icons/fa";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import { Link } from "react-router-dom";


const NewCourseCollegeReport = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const userType = sessionStorage.getItem("type");
    const collegeId = sessionStorage.getItem("id");
    const [application, setApplication] = useState({})
    const [data, setData] = useState("");
    const [previewModal, setPreviewModal] = useState(false);
    const togglePreviewModal = () => setPreviewModal(!previewModal);
    const [designations, setDesignations] = useState([]);
    useEffect(() => {
        const fetchDesignations = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/designation/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });

                if (response.status === 200) {
                    const verifiedDesignations = response.data.filter(
                        (designation) => designation.isVerified === 1
                    );

                    setDesignations(verifiedDesignations);
                } else {
                    alert("Failed to fetch designations. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchDesignations();
    }, [endPoint, token]);


    const [university, setUniversity] = useState([]);
    useEffect(() => {
        const fetchUniversity = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/university/get-all-university`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    setUniversity(response.data);
                } else {
                    alert("Failed to fetch College data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        // Call the function
        fetchUniversity();

        // Optionally add dependencies in the dependency array
    }, [endPoint, token]);

    const handleModelClick = async (id) => {
        try {
            setPreviewModal(true);
            const response = await axios.get(
                `${endPoint}/api/newCourse/getById/${id}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            if (response.status === 200) {
                setApplication(response.data);

            } else {
                alert("Failed to fetch Employee data. Please try again.");
            }
        } catch (error) {
            console.error("An error occurred while fetching the data:", error);
            alert("An error occurred. Please try again later.");
        }
    };

    // Call the function





    useEffect(() => {
        const newCourseApplicationList = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/newCourse/getByCollege/${collegeId}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    setData(response.data);
                    // console.log(response.data, "Getting Response");

                    // console.log(response.data, "response.data Getting Data");

                } else {
                    alert("Failed to fetch Employee data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        // Call the function
        newCourseApplicationList();

        // Optionally add dependencies in the dependency array
    }, [endPoint, token]); // Include value and token as dependencies if they can change





    const exportToExcel = async () => {
        try {
            if (data.length > 0) {  // Check if data is available
                let tableHTML = `
                  <table border="1">
                    <thead>
                      <tr>
                        <th>S.No</th>
                        <th>Application No</th>
                        <th>Sanchalan Type</th>
                        <th>Applied Date</th>
                        <th>Course Name</th>
                        <th>Class Name</th>
                        <th>Subject Name</th>
                      </tr>
                    </thead>
                    <tbody>
                `;

                data.forEach((college, index) => {
                    tableHTML += `            
                      <tr>
                        <td>${index + 1}</td>
                        <td>${college.applicationNo}</td>
                        <td>${college.navinVishay[0]?.type}</td>
                        <td>${formatDate(college.createdAt)}</td>
                        <td>${college.navinVishay.map(item => item.course).join(', ')}</td>
                        <td>${college.navinVishay.map(item => item.className).join(',')}</td>

                        <td>${college.navinVishay.map(item => item.subject).join(',')}</td>
                      </tr>
                    `;
                });

                tableHTML += "</tbody></table>";

                const excelFileContent = `
                  <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
                    <head><!--[if gte mso 9]><xml>
                      <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                          <x:ExcelWorksheet>
                            <x:Name>Employees</x:Name>
                            <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                          </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                      </x:ExcelWorkbook>
                    </xml><![endif]--></head>
                    <body>${tableHTML}</body>
                  </html>
                `;

                const blob = new Blob([excelFileContent], { type: "application/vnd.ms-excel;charset=utf-8;" });
                const date = new Date().toLocaleDateString();
                const downloadLink = document.createElement("a");
                downloadLink.href = URL.createObjectURL(blob);
                downloadLink.download = `New_Course_${date}.xls`;
                downloadLink.click();
            } else {
                SwalMessageAlert("No data available for export.", "warning");
            }
        } catch (error) {
            SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
        }
    };




    const columns = [

        {
            name: "Application No",
            selector: (row) => row.applicationNo,
            sortable: true,
        },
        {
            name: "Applied Date",
            selector: (row) => formatDate(row.createdAt),
            sortable: true,
        },
        {
            name: "Course Type",
            selector: (row) => row.courseType,
            sortable: true,
        },
        {
            name: "Sanchalan Type",
            selector: (row) => (
                row.navinVishay[0]?.type === "janBhagidari" ? "जनभागीदारी" :
                    row.navinVishay[0]?.type === "government" ? "शासकीय" :
                        row.navinVishay[0]?.type === "selfFinance" ? "स्व-वित्तीय" :
                            ""
            ),
            sortable: true,
        },
        {
            name: "Courses/Class Name /Subject",
            selector: (row) => (
                <div>
                    {row.navinVishay.map((item, index) => (
                        <span key={index}>
                            ({index + 1}){" "} {item.course} / {item.className} / {item.subject} <br />
                        </span>
                    ))}
                </div>
            ),
            sortable: false, // Optional: Sorting might not work with JSX
        },

        {
            name: "View / Print",
            width: "100px",
            cell: (row) => (<>
                <Button className="btn-sm btn-warning" onClick={() => handleModelClick(row._id)}>
                    View
                </Button>

                < Link to={`/admin/newCourse-print/${row._id}`}>
                    <Button className=" btn btn-sm mr-2" style={{ backgroundColor: "orange" }}>
                        <i className="fas fa-print"></i>
                    </Button>
                </Link>
            </>
            ),
            ignoreRowClick: true, // Prevent row click from triggering
            allowOverflow: true, // Allow overflow for the button
            button: true, // Optional: if your table library supports this
        },
        {
            name: "Edit",
            width: "100px",
            cell: (row) => (<>
                {row.finalized === true && <Badge style={{ color: "white", textTransform: "none", backgroundColor: "blue", fontSize: "12px", cursor: "auto", padding: "8px" }} title="Edit Details">
                    Finalized
                </Badge>}
                {row.finalized === false && <>
                    {(row.navinVishay[0]?.type === "janBhagidari" || row.navinVishay[0]?.type === "selfFinance") &&
                        <Link to={`/admin/update-course-janbhagidari/${row._id}`}>
                            <button className="btn btn-warning btn-sm" title="Edit Details">
                                Edit
                            </button>
                        </Link>
                    }
                    {(row.navinVishay[0]?.type === "government") &&
                        <Link to={`/admin/update-course-govt/${row._id}`}>
                            <button className="btn btn-warning btn-sm" title="Edit Details">
                                Edit
                            </button>
                        </Link>
                    } </>}
            </>
            ),
            ignoreRowClick: true, // Prevent row click from triggering
            allowOverflow: true, // Allow overflow for the button
            button: true, // Optional: if your table library supports this
        }
    ];

    const createdAt = application ? new Date(application.createdAt) : null;

    const currentYear = createdAt ? createdAt.getFullYear() : '';
    const nextYear = createdAt ? (createdAt.getFullYear() + 1).toString().slice(2) : '';

    // console.log("Getting APplication", application);


    return (
        <>
            <Header />

            {/* Page content */}
            <Container className="mt--7" fluid>
                {/* Table to display institutes with pagination */}
                <Row className="mt-5">
                    <Modal style={{ maxWidth: "80%" }} isOpen={previewModal} toggle={togglePreviewModal}>
                        <ModalHeader toggle={togglePreviewModal}>Preview</ModalHeader>
                        <ModalBody>
                            <Row>
                                <Col >
                                    <Card className="bg-secondary shadow">
                                        <CardHeader className="bg-white border-0">
                                            <Row className="align-items-center">
                                                {application &&
                                                    application.navinVishay &&
                                                    application.navinVishay.length > 0 &&
                                                    (application.navinVishay[0].type === "janBhagidari" || application.navinVishay[0].type === "selfFinance") &&
                                                    <>
                                                        <Col>
                                                            <h2 className="mb-0 text-center">शैक्षणिक सत्र  {currentYear} - {nextYear} में स्ववित्तीय/जनभागीदारी मद से नवीन विषय/संकाय/कक्षा को प्रारंभ करने हेतु ऑनलाईन  <br /> आवेदन पत्र <br />
                                                                (टीप:- प्रत्येक नवीन संकाय/कक्षा हेतु अलग-अलग आवेदन देना अनिवार्य है।)
                                                            </h2>
                                                        </Col> </>}
                                                {application &&
                                                    application.navinVishay &&
                                                    application.navinVishay.length > 0 &&
                                                    (application.navinVishay[0].type === "government") &&
                                                    <>
                                                        <Col>
                                                            <h2 className="mb-0 text-center">शैक्षणिक सत्र {currentYear}- {(currentYear + 1).toString().slice(2)} में शासन अंतर्गत  नवीन विषय/संकाय/कक्षा को प्रारंभ करने हेतु ऑनलाईन  <br /> आवेदन पत्र <br />
                                                                (टीप:- प्रत्येक नवीन संकाय/कक्षा हेतु अलग-अलग आवेदन देना अनिवार्य है।)
                                                            </h2>
                                                        </Col>
                                                    </>}
                                            </Row>
                                        </CardHeader>
                                        <CardBody>
                                            <Form >
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            स्ववित्तीय जनभागीदारी मद से प्रारंभ करने वाले प्रस्तावित नवीन संकाय कक्षा का नाम एवं वांछित सीट संख्या <b> (स्नातक या स्नातकोत्तर स्तर का उल्लेख करें)</b>
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Table striped>
                                                        <thead>
                                                            <tr>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय का नाम</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}> संकाय का नाम</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कक्षा का नाम</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वांछित सीट संख्या</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {application?.navinVishay?.map((item, index) => (
                                                                <tr key={index}>
                                                                    <td>
                                                                        <Input
                                                                            type="text"
                                                                            name="subject"
                                                                            value={item.subject}
                                                                            placeholder="Subject"
                                                                            disabled
                                                                            required
                                                                        >
                                                                            <option>
                                                                                No options available
                                                                            </option>

                                                                        </Input>
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="text"
                                                                            name="course"
                                                                            value={item.course} disabled

                                                                            placeholder="Course"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="className"
                                                                            value={item.className}
                                                                            disabled
                                                                            placeholder="Class Name"
                                                                            required
                                                                        >
                                                                            <option value="" disabled>Select Year</option>
                                                                            <option value="1stYear">1st Year</option>
                                                                            <option value="2ndYear">2nd Year</option>
                                                                            <option value="3rdYear">3rd Year</option>
                                                                            <option value="4thYear">4th Year</option>
                                                                        </Input>

                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="requiredSeat"
                                                                            value={item.requiredSeat} disabled

                                                                            placeholder="Required Seat"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="type"
                                                                            value={item.type}
                                                                            disabled

                                                                            placeholder="Type"
                                                                            required
                                                                        >
                                                                            <option value="" disabled>Select Type</option>
                                                                            <option value="janBhagidari">जनभागीदारी</option>
                                                                            <option value="selfFinance">स्व-वित्तीय</option>
                                                                            <option value="government">शासकीय </option>
                                                                        </Input>
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </Table>
                                                </Row>

                                                {application?.courseType === "UG" && <>
                                                    <hr />
                                                    <Row className="mt-2">
                                                        <Col>
                                                            <Label>
                                                                <b>स्नातक स्तर पर</b>  प्रस्तावित नवीन नवीन संकाय/कक्षा प्रारंभ करने हेतु
                                                                पोषक शालाओं में संबंधित
                                                                संकाय/विषय में छात्र संख्या एवं
                                                                गतवर्ष उत्तीर्ण छात्र संख्या ।

                                                            </Label>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Table striped>
                                                            <thead>
                                                                <tr>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}> पोषक शाला का नाम</th>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कला</th>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विज्ञान</th>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वाणिज्य</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {application?.poshakShalaStudent?.map((item, index) => (
                                                                    <tr key={index}>
                                                                        <td>
                                                                            <Input
                                                                                type="text"
                                                                                name="school"
                                                                                value={item.school}
                                                                                disabled
                                                                                placeholder="School"
                                                                                required
                                                                            />
                                                                        </td>
                                                                        <td>
                                                                            <Input
                                                                                type="number"
                                                                                name="arts"
                                                                                value={item.arts}
                                                                                disabled
                                                                                placeholder="Arts"
                                                                                required
                                                                            />
                                                                        </td>
                                                                        <td>
                                                                            <Input
                                                                                type="number"
                                                                                name="science"
                                                                                value={item.science}
                                                                                disabled
                                                                                placeholder="Science"
                                                                                required
                                                                            />
                                                                        </td>
                                                                        <td>
                                                                            <Input
                                                                                type="number"
                                                                                name="commerce"
                                                                                value={item.commerce}
                                                                                disabled
                                                                                placeholder="Commerce"
                                                                                required
                                                                            />
                                                                        </td>

                                                                    </tr>
                                                                ))}
                                                            </tbody>
                                                        </Table>

                                                    </Row>
                                                </>}
                                                {application?.courseType === "PG" && <>
                                                    <hr />

                                                    <Row className="mt-2">
                                                        <Col>
                                                            <Label>
                                                                <b> स्नातकोत्तर स्तर पर </b> प्रस्तावित नवीन
                                                                नवीन संकाय/कक्षा प्रारंभ करने हेतु पोषक कक्षा <b>(संबंधित विषय के स्नातक अंतिम वर्ष में अध्ययनरत छात्र संख्या एवं गत वर्षों में उत्तीर्ण छात्र संख्या।)</b>
                                                            </Label>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Table striped>
                                                            <thead>
                                                                <tr>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय </th>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}>गत वर्ष स्नातक अंतिम <br /> वर्ष में उत्तीर्ण छात्र संख्या</th>
                                                                    <th style={{ fontSize: "12px", paddingLeft: "40px" }}>चालू सत्र में संबंधित विषय के स्नातक <br /> अंतिम वर्ष में अध्ययनरत छात्र संख्या</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {application?.snatakStudent?.map((item, index) => (
                                                                    <tr key={index}>
                                                                        <td>
                                                                            <Input
                                                                                type="text"
                                                                                name="subject"
                                                                                value={item.subject}
                                                                                disabled
                                                                                placeholder="Subject"
                                                                                required
                                                                            >
                                                                                <option>
                                                                                    No options available
                                                                                </option>

                                                                            </Input>
                                                                        </td>
                                                                        <td>
                                                                            <Input
                                                                                type="number"
                                                                                name="passedStudent"
                                                                                value={item.passedStudent}
                                                                                disabled
                                                                                placeholder="Passed Student"
                                                                                required
                                                                            />
                                                                        </td>
                                                                        <td>
                                                                            <Input
                                                                                type="number"
                                                                                name="lastYearStudent"
                                                                                value={item.lastYearStudent}
                                                                                disabled
                                                                                placeholder="Last Year Student"
                                                                                required
                                                                            />
                                                                        </td>

                                                                    </tr>
                                                                ))}
                                                            </tbody>
                                                        </Table>

                                                    </Row>
                                                </>}
                                                <hr />
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            उक्त प्रस्तावित नवीन संकाय/कक्षा की संबंद्धता किस विश्वविद्यालय से प्राप्त की जावेगी।     {"    "}
                                                            <b>(संबंधित विश्वविद्यालय से प्रस्तावित नवीन विषय/संकाय संचालित होने का प्रमाण पत्र अनिवार्य रूप से संलग्न करें)</b>
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Table striped>
                                                        <thead>
                                                            <tr>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>सम्बंधित विश्वविद्यालय का नाम{ } </th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विश्वविद्यालय द्वारा जारी प्रमाण पत्र <span className="text-danger">(केवल पीडीएफ फॉरमेट में )</span> </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {application?.sambandhitVishwaVidyalaya?.map((item, index) => (
                                                                <tr key={index}>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="university"
                                                                            value={item.university}
                                                                            disabled
                                                                            placeholder="University"
                                                                            required
                                                                        >
                                                                            <option value="">Select University</option>
                                                                            {university &&
                                                                                university.length > 0 &&
                                                                                university.map((type, index) => (
                                                                                    <option key={index} value={type._id}>
                                                                                        {type.name}
                                                                                    </option>
                                                                                ))}
                                                                        </Input>
                                                                    </td>
                                                                    <td>
                                                                        <a href={`https://heonline.cg.nic.in/${item.certificate}`} download>
                                                                            <Button className="btn-sm btn-primary" >
                                                                                {item.certificate ? <FaDownload size={18} /> : "No File"}
                                                                            </Button>
                                                                        </a>
                                                                    </td>

                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </Table>
                                                </Row>

                                                {application &&
                                                    application.navinVishay &&
                                                    application.navinVishay.length > 0 &&
                                                    (application.navinVishay[0].type === "janBhagidari" || application.navinVishay[0].type === "selfFinance") &&
                                                    <>
                                                        <hr />
                                                        <Row>
                                                            <Col>
                                                                <Label>
                                                                    क्या उक्त प्रस्तावित नवीन संकाय कक्षा में
                                                                    प्रवेश प्राप्त छात्रों के अध्यापन हेतु पृथक से
                                                                    स्ववित्तीय जनभागीदारी मद से किसी
                                                                    शैक्षणिक स्टॉफ नियुक्त किया
                                                                    जावेगा? <b>(यदि हॉ)</b>  तो शैक्षणिक स्टॉफ के
                                                                    अनुमानित मासिक वेतन की जानकारी (उदाहरण अनुसार)
                                                                </Label>
                                                            </Col>
                                                        </Row>
                                                        <Row className="d-block">
                                                            <table >
                                                                <thead>
                                                                    <tr>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px" }}>विषय</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px" }}>संकाय</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px" }}>कक्षा का नाम</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px", width: "300px" }}>शैक्षणिक स्टॉफ</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px", width: "60px" }}>पद संख्या</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px", width: "130px" }}>अनुमानित मासिक वेतन</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "4px", width: "150px" }}>वार्षिक वित्तीय भार (मासिक वेतन X पदों की संख्या X 12)
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {application?.selfTeacherEstimate?.map((item, index) => (
                                                                        <tr key={index}>
                                                                            <td>
                                                                                <Input
                                                                                    type="text"
                                                                                    name="subject"
                                                                                    value={item.subject}
                                                                                    disabled
                                                                                    placeholder="Subject"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                >
                                                                                    <option>
                                                                                        No options available
                                                                                    </option>

                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="text"
                                                                                    name="course"
                                                                                    value={item.course}
                                                                                    disabled
                                                                                    placeholder="Course"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    name="className"
                                                                                    type="select"
                                                                                    value={item.className}
                                                                                    disabled
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                >
                                                                                    <option value="" disabled>Select Year</option>
                                                                                    <option value="1stYear">1st Year</option>
                                                                                    <option value="2ndYear">2nd Year</option>
                                                                                    <option value="3rdYear">3rd Year</option>
                                                                                    <option value="4thYear">4th Year</option>
                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="select"
                                                                                    name="designation"
                                                                                    value={item.designation}
                                                                                    disabled
                                                                                    placeholder="Designation"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                >
                                                                                    <option value="">Select Designation</option>
                                                                                    {designations &&
                                                                                        designations.length > 0 &&
                                                                                        designations.map((type, index) => (
                                                                                            <option key={index} value={type._id}>
                                                                                                {type.designation}
                                                                                            </option>
                                                                                        ))}
                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="seats"
                                                                                    value={item.seats}
                                                                                    disabled
                                                                                    placeholder="Seats"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="estimatedSalaryMonthly"
                                                                                    value={item.estimatedSalaryMonthly}
                                                                                    disabled
                                                                                    placeholder="Estimated Salary Monthly"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="estimatedSalaryYearly"
                                                                                    value={item.estimatedSalaryYearly}
                                                                                    disabled
                                                                                    placeholder="Estimated Salary Yearly"
                                                                                    required
                                                                                    style={{ margin: 0 }} // Remove margin
                                                                                />
                                                                            </td>

                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </table>


                                                        </Row>
                                                        <hr />
                                                        <Row>
                                                            <Col>
                                                                <Label>
                                                                    उक्त प्रस्तावित नवीन संकाय कक्षा में प्रवेश हेतु प्रति छात्र कितना
                                                                    अनुमानित वार्षिक शुल्क निर्धारित होगा तथा सीट संख्या अनुसार
                                                                    प्रति छात्र हेतु निर्धारित वार्षिक शुल्क से कितना वार्षिक
                                                                    आय प्राप्त होगा, की जानकारी देवें।
                                                                </Label>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Table striped>
                                                                <thead>
                                                                    <tr>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रति छात्र हेतु <br /> निर्धारित वार्षिक शुल्क
                                                                        </th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वार्षिक आय (सीट संख्या X प्रति छात्र निर्धारित वार्षिक शुल्क)</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {application?.incomeFromStudent?.map((item, index) => (
                                                                        <tr key={index}>
                                                                            <td>
                                                                                <Input
                                                                                    type="select"
                                                                                    name="subject"
                                                                                    value={item.subject}
                                                                                    disabled
                                                                                    placeholder="Subject"
                                                                                    required
                                                                                >
                                                                                    <option>
                                                                                        No options available
                                                                                    </option>

                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="text"
                                                                                    name="course"
                                                                                    value={item.course}
                                                                                    disabled
                                                                                    placeholder="Course"
                                                                                    required
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="select"
                                                                                    name="className"
                                                                                    value={item.className}
                                                                                    disabled
                                                                                    placeholder="Class Name"
                                                                                    required
                                                                                >
                                                                                    <option value="" disabled>Select Year</option>
                                                                                    <option value="1stYear">1st Year</option>
                                                                                    <option value="2ndYear">2nd Year</option>
                                                                                    <option value="3rdYear">3rd Year</option>
                                                                                    <option value="4thYear">4th Year</option>
                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="yearlyFees"
                                                                                    value={item.yearlyFees}
                                                                                    disabled
                                                                                    placeholder="Yearly Fees"
                                                                                    required
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="totalIncome"
                                                                                    value={item.totalIncome}
                                                                                    disabled
                                                                                    placeholder="Total Income"
                                                                                    required
                                                                                />
                                                                            </td>

                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </Table>

                                                        </Row>
                                                        <hr />
                                                        <Row>
                                                            <Col>
                                                                <Label>
                                                                    पिछले 02 वर्षों में स्ववित्तीय/जनभागीदारी मद अंतर्गत प्राप्त आय एवं व्यय का विवरण

                                                                </Label>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Table striped>
                                                                <thead>
                                                                    <tr>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वर्ष</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्राप्त आय</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>व्यय राशि</th>
                                                                        <th style={{ fontSize: "12px", paddingLeft: "40px" }}>शेष राशि</th>

                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {application?.lastTwoYearRecord?.map((item, index) => (
                                                                        <tr key={index}>
                                                                            <td>
                                                                                <Input
                                                                                    type="select"
                                                                                    name="typeName"
                                                                                    value={item.typeName}
                                                                                    disabled
                                                                                    placeholder="Type Name"
                                                                                    required
                                                                                >
                                                                                    <option value="" disabled>Select Type</option>
                                                                                    <option value="janBhagidari">जनभागीदारी</option>
                                                                                    <option value="selfFinance">स्व-वित्तीय</option>
                                                                                    <option value="government">शासकीय </option>
                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="text"
                                                                                    name="year"
                                                                                    value={item.year}
                                                                                    disabled
                                                                                    placeholder="Year"
                                                                                    required
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="income"
                                                                                    value={item.income}
                                                                                    disabled
                                                                                    placeholder="Income"
                                                                                    required
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="expense"
                                                                                    value={item.expense}
                                                                                    disabled
                                                                                    placeholder="Expense"
                                                                                    required
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="balance"
                                                                                    value={item.balance}
                                                                                    disabled
                                                                                    placeholder="Balance"
                                                                                    required
                                                                                />
                                                                            </td>

                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </Table>

                                                        </Row>

                                                    </>}
                                                <hr />
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            महाविद्यालय में उपलब्ध भवन/
                                                            अध्ययन कक्ष/फर्नीचर संख्या/प्रयोगशाला कक्ष संख्या प्रस्तावित नवीन विषय/संकाय की पुस्तक संख्या की जानकारी

                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Table striped>
                                                        <thead>
                                                            <tr>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वयं का भवन (हॉ/नही)</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>अध्ययन कक्ष संख्या</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>फर्नीचर संख्या</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रयोगशाला कक्ष संख्या</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रस्तावित नवीन
                                                                    विषय संकाय की पुस्तक
                                                                    संख्या
                                                                </th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {application?.mahavidyalayaJankari?.map((item, index) => (
                                                                <tr key={index}>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="selfBuilding"
                                                                            value={item.selfBuilding}
                                                                            disabled
                                                                            placeholder="Self Building"
                                                                            required
                                                                        >
                                                                            <option value="" disabled>Select</option>
                                                                            <option value="हाँ">हाँ</option>
                                                                            <option value="नहीं">नहीं</option>
                                                                        </Input>
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="classroomNo"
                                                                            value={item.classroomNo}
                                                                            disabled
                                                                            placeholder="Classroom No"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="furnitureNo"
                                                                            value={item.furnitureNo}
                                                                            disabled
                                                                            placeholder="Furniture No"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="labsNo"
                                                                            value={item.labsNo}
                                                                            disabled
                                                                            placeholder="Labs No"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="booksNo"
                                                                            value={item.booksNo}
                                                                            disabled
                                                                            placeholder="Books No"
                                                                            required
                                                                        />
                                                                    </td>
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </Table>
                                                </Row>
                                                <hr />
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            महाविद्यालय में पूर्व से संचालित सभी
                                                            संकाय/कक्षा का नाम एवं निर्धारित सीट संख्या की जानकारी। टीपः केवल प्रथम वर्ष या प्रथम सेमेस्टर हेतु निर्धारित सीट संख्या की जानकारी देवें।.
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Table striped>
                                                        <thead>
                                                            <tr>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>निर्धारित सीट संख्या</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संचालन का प्रकार (स्ववित्तीय योजना/जनभागीदारी मद)</th>

                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {application?.previousCourseSubjects?.map((item, index) => (
                                                                <tr key={index}>
                                                                    <td>
                                                                        <Input
                                                                            type="text"
                                                                            name="subject"
                                                                            value={item.subject}
                                                                            disabled
                                                                            placeholder="Subject"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="text"
                                                                            name="course"
                                                                            value={item.course}
                                                                            disabled
                                                                            placeholder="Course"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="className"
                                                                            value={item.className}
                                                                            disabled
                                                                            placeholder="Class Name"
                                                                            required
                                                                        >
                                                                            <option value="" disabled>Select Year</option>
                                                                            <option value="1stYear">1st Year</option>
                                                                            <option value="2ndYear">2nd Year</option>
                                                                            <option value="3rdYear">3rd Year</option>
                                                                            <option value="4thYear">4th Year</option>
                                                                        </Input>
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="seats"
                                                                            value={item.seats}
                                                                            disabled
                                                                            placeholder="Seats"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="sanchalanType"
                                                                            value={item.sanchalanType}
                                                                            disabled
                                                                            placeholder="Sanchalan Type"
                                                                            required
                                                                        >
                                                                            <option value="" disabled>Select Type</option>
                                                                            <option value="janBhagidari">जनभागीदारी</option>
                                                                            <option value="selfFinance">स्व-वित्तीय</option>
                                                                            <option value="government">शासकीय </option>
                                                                        </Input>
                                                                    </td>

                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </Table>
                                                </Row>
                                                <hr />
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            वर्तमान में महाविद्यालय हेतु शासन से
                                                            स्वीकृत समस्त शैक्षणिक एवं
                                                            अशैक्षणिक पदों की जानकारी (उदाहरण:-
                                                            प्राध्यापक इतिहास या सहायक प्राध्यापक हिन्दी)
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Table striped>
                                                        <thead>
                                                            <tr>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>पदनाम</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वीकृत </th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कार्यरत</th>
                                                                <th style={{ fontSize: "12px", paddingLeft: "40px" }}>रिक्त</th>
                                                            </tr>
                                                        </thead>
                                                        <tbody>
                                                            {application?.currentStaff?.map((item, index) => (
                                                                <tr key={index}>
                                                                    <td>
                                                                        <Input
                                                                            type="select"
                                                                            name="designation"
                                                                            value={item.designation}
                                                                            disabled
                                                                            placeholder="Designation"
                                                                            required
                                                                        >
                                                                            <option value="">Select Designation</option>
                                                                            {designations &&
                                                                                designations.length > 0 &&
                                                                                designations.map((type, index) => (
                                                                                    <option key={index} value={type._id}>
                                                                                        {type.designation}
                                                                                    </option>
                                                                                ))}
                                                                        </Input>
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="sanctioned"
                                                                            value={item.sanctioned}

                                                                            disabled
                                                                            placeholder="Sanctioned"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="working"
                                                                            value={item.working}

                                                                            disabled
                                                                            placeholder="Working"
                                                                            required
                                                                        />
                                                                    </td>
                                                                    <td>
                                                                        <Input
                                                                            type="number"
                                                                            name="vacant"
                                                                            value={item.vacant}

                                                                            disabled
                                                                            placeholder="Vacant"
                                                                            required
                                                                        />
                                                                    </td>

                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </Table>

                                                </Row>

                                                {application &&
                                                    application.navinVishay &&
                                                    application.navinVishay.length > 0 &&
                                                    (application.navinVishay[0].type === "government") &&
                                                    <>
                                                        <hr />
                                                        <Row>
                                                            <Col>
                                                                <Label>

                                                                    प्रस्तावित नवीन विषय/संकाय/कक्षा
                                                                    प्रारंभ करने हेतु वांछित शैक्षणिक/
                                                                    अशैक्षणिक पदों की संख्या एवं पदों
                                                                    का का वित्तीय भार (उदाहरण:-
                                                                    सहा.प्राध्या. हिन्दी- 01 पद या
                                                                    प्राध्यापक इतिहास-01 पद)
                                                                </Label>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Table striped>
                                                                <thead>
                                                                    <tr>
                                                                        <th>Designation</th>
                                                                        <th>Seats</th>
                                                                        <th>Financial Load</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    {application?.requiredStaff.map((item, index) => (
                                                                        <tr key={index}>
                                                                            <td>
                                                                                <Input
                                                                                    type="select"
                                                                                    name="designation"
                                                                                    value={item.designation}
                                                                                    placeholder="Designation"
                                                                                    required
                                                                                    readOnly
                                                                                >
                                                                                    <option value="">Select Designation</option>
                                                                                    {designations &&
                                                                                        designations.length > 0 &&
                                                                                        designations.map((type, index) => (
                                                                                            <option key={index} value={type._id}>
                                                                                                {type.designation}
                                                                                            </option>
                                                                                        ))}
                                                                                </Input>
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="seats"
                                                                                    value={item.seats}
                                                                                    placeholder="Seats"
                                                                                    required
                                                                                    readOnly
                                                                                />
                                                                            </td>
                                                                            <td>
                                                                                <Input
                                                                                    type="number"
                                                                                    name="financialLoad"
                                                                                    value={item.financialLoad}
                                                                                    placeholder="Financial Load"
                                                                                    required
                                                                                    readOnly
                                                                                />
                                                                            </td>

                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </Table>

                                                        </Row>
                                                    </>}
                                                <hr />
                                                <Row>
                                                    <Col>
                                                        <Label>
                                                            प्रस्तावित नवीन संकाय/ कक्षा प्रारंभ
                                                            करने का औचित्य
                                                            (अनिवार्य रूप से उल्लेख करें)
                                                        </Label>
                                                    </Col>
                                                </Row>
                                                <Row>
                                                    <Col>
                                                        <Input
                                                            type="textarea"
                                                            name="remarkByPrincipal"
                                                            disabled
                                                            value={application.remarkByPrincipal}
                                                            placeholder="Remark"
                                                            required
                                                        />
                                                    </Col>
                                                </Row>

                                            </Form>
                                            <Row className="mt-3">

                                            </Row>
                                        </CardBody>
                                    </Card>
                                </Col>
                            </Row>
                        </ModalBody>
                        <ModalFooter>
                            <Button color="secondary" onClick={togglePreviewModal}>
                                Close
                            </Button>
                        </ModalFooter>
                    </Modal>
                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                                <Col xs="6">
                                    <h3 className="mb-0">
                                        Generate new course applied Report{" "}


                                    </h3>
                                </Col>
                                <Col className="text-right" xs="4">
                                    <Button
                                        color="primary"
                                        size="sm"
                                        onClick={exportToExcel}
                                    >
                                        Export To Excel
                                    </Button>
                                </Col>
                            </CardHeader>

                        </Card>
                    </Col>
                </Row>

                <Row className="mt-4">
                    <Col lg="12">
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">Applied For New Courses</h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <DataTable
                                    columns={columns}
                                    data={data}
                                    pagination
                                    highlightOnHover
                                    striped
                                />
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default NewCourseCollegeReport;
