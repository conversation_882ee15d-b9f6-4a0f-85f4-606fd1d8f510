import { useState, useEffect } from "react";
import {

    <PERSON>,
    Col,
    Button,
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    CardHeader,
    Table,
    ModalBody,
    ModalFooter,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { useParams, useNavigate } from "react-router-dom";
import formatDate from "../../utils/formateDate.jsx";

const PrintIPRForm = () => {
    const token = sessionStorage.getItem('authToken');
    const userName = sessionStorage.getItem("name");
    const [data, setData] = useState([]);
    const endPoint = import.meta.env.VITE_API_URL;
    const { id } = useParams();
    const [clgInfo, setClgInfo] = useState([]);
    const [user, setUser] = useState([]);
    const [showPreview, setShowPreview] = useState(false);
    const navigate = useNavigate();
    const [properties, setProperties] = useState([]);

    useEffect(() => {
        const getApplication = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/ipr/get-single/${id}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    const data = response.data; // Convert to YYYY-MM-DD
                    setData(data);
                    setProperties(data.properties);
                    // // console.log(data.properties);

                    handlePreview();
                }
            } catch (error) {
                console.error("Error fetching Application data:", error);
                alert("Failed to fetch Application data.");
            }
        };

        getApplication(); // Call the function inside useEffect
    }, [id, endPoint, token]); // Dependencies

    useEffect(() => {
        if (data && data.applicantId) {
            const fetchEmployeeData = async () => {
                try {
                    // // console.log(data,"Getting USer in Effect");

                    const response = await axios.get(
                        `${endPoint}/api/employee/get-employee/${data.applicantId}`,
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        const data = response.data;
                        setUser(data);

                    } else {
                        alert("Data Not Fetched.");
                        // navigate('/auth/Register');
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                    // navigate('/auth/Register');
                }
            };
            fetchEmployeeData();
        }
    }, [token, endPoint, data]);



    useEffect(() => {
        const fetchCollegeInfo = async () => {

            if (user && user.college) { // Check if user and college are defined

                try {
                    const response = await axios.get(
                        `${endPoint}/api/college/get-college/${user.college}`,
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );
                    if (response.status === 200) {
                        setClgInfo(response.data);
                    } else {
                        alert("College Not Found.");
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                }
            }
        };
        fetchCollegeInfo();
    }, [user]);




    const handlePreview = () => {
        setShowPreview(true);
    };

    const handlePrint = () => {
        const printWindow = window.open('', '_blank');
        const printContent = document.getElementById('print-container').innerHTML;
        const printDocument = printWindow.document;

        printDocument.write(`
            <html>
              <head>
                <title>Print Form</title>
                <style>
                  @page { 
                    size: A4 landscape; /* Set to A4 landscape */
                    margin: 20px; 
                  }
                  body { font-family: Arial, sans-serif; }
                  .print-container {
                    width: 29.7cm; /* Width for landscape */
                    height: 21cm; /* Height for landscape */
                    background: #fff;
                  }
                  .form-field {
                    margin-bottom: 15px;
                  }
                  .form-field label {
                    display: block;
                    margin-bottom: 5px;
                    font-weight: bold;
                  }
                  .form-field input {
                    width: 100%;
                    padding: 8px;
                    box-sizing: border-box;
                  }
                  h1 {
                    font-size: 24px;
                    margin-bottom: 20px;
                  }
                  p {
                    font-size: 16px;
                    margin-bottom: 10px;
                  }
                </style>
              </head>
              <body>
                <div class="print-container">
                  ${printContent}
                </div>
              </body>
            </html>
          `);
        printDocument.close();
        printWindow.print();
    };

    const currentDateTime = new Date().toLocaleString('en-IN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: true, // Use 12-hour clock
    });

    return (
        <>
            <Header />
            <Modal isOpen={showPreview} toggle={() => setShowPreview(false)} style={{ maxWidth: "90%" }}>
                <ModalHeader >
                    Print Preview
                </ModalHeader>
                <ModalBody>
                    <div id="print-container" >
                        <CardHeader>
                            <Row
                                style={{
                                    justifyContent: "center", // Center horizontally
                                    display: "flex", // Use flexbox for alignment
                                }}
                            >
                                <Col></Col>
                                <Col className="text-center">
                                    <span
                                        style={{
                                            fontSize: "24px",
                                            fontWeight: "bolder",
                                            display: "block", // Ensure block-level behavior
                                        }}
                                    >
                                        प्रपत्र - एक
                                    </span>
                                </Col>
                                <Col></Col>
                            </Row>
                            <Row
                                style={{
                                    justifyContent: "center",
                                    display: "flex",
                                    marginTop: "10px", // Add some spacing between rows
                                }}
                            >
                                <Col md="12" className="text-center">
                                    <span
                                        style={{
                                            fontSize: "24px",
                                            fontWeight: "bolder",
                                            display: "block",
                                        }}
                                    >
                                        'शासकीय सेवक का अचल सम्पत्ति का वार्षिक विवरण : - 31.12.
                                        {new Date(data.createdAt).getFullYear() - 1}
                                        की स्थिति में'
                                    </span>
                                </Col>
                            </Row>
                        </CardHeader>
                        <br /><br />
                        <table style={{ width: '100%', borderCollapse: 'collapse', marginBottom: "28px" }}>

                            <tbody>
                                <tr>
                                    <td>1.अधिकारी / कर्मचारी का पूरा नाम :</td>
                                    <td>{data.employeeName}</td>
                                </tr>
                                <tr>
                                    <td>2.वर्तमान पद :</td>
                                    <td>{data.currentDesignation}</td>
                                </tr>
                                <tr>
                                    <td>3.वर्तमान वेतन : </td>
                                    <td>{data.currentSalary}</td>
                                </tr>
                                <tr>
                                    <td>4.कार्यालय / महाविद्यालय का नाम / पता :</td>
                                    <td>{data.officeAddress}</td>
                                </tr>
                                <tr>
                                    <td>5.अगली वेतन वृद्धि की तारीख :</td>
                                    <td>
                                        {data.nextIncrementDate
                                            ? formatDate(data.nextIncrementDate)

                                            : ""}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <Row>
                            <table className='table-uneven' style={{ width: '99%', borderCollapse: 'collapse' }}>
                                <thead>
                                    <tr
                                        style={{
                                            border: '1px solid black',
                                            fontSize: "12px",
                                            textAlign: "center",
                                            padding: "10px",
                                            verticalAlign: "middle",
                                        }}
                                    >
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            क्र.
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            उस जिले, उपसंभाग <br /> तथा ग्राम का नाम <br />{" "}
                                            जिसमे संपत्ति स्थित हो
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            संपत्ति का नाम तथा <br /> ब्यौरा गृह तथा <br />{" "}
                                            भूमि अन्य भवन .
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            वर्तमान मूल्य{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            यदि स्वयं के नाम <br /> पर न हों तो बताइये{" "}
                                            <br /> कि वह जिसके <br /> नाम पर धारित है{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            और उनका <br />
                                            शासकीय कर्मचारी <br /> से क्या सम्बन्ध है{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            उसे किस प्रकार <br /> अर्जित किया गया ,<br />{" "}
                                            पट्टी बंधक ,विरासत भेंट
                                            <br /> या अन्य किसी प्रकार से{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            अर्जन वर्ष {" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            जिससे अर्जन की <br />
                                            गयी उसका नाम{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            जिससे अर्जन की <br /> गयी उसका ब्यौरा{" "}
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            संपत्ति से <br />
                                            वार्षिक आय
                                        </th>
                                        <th style={{ border: '1px solid black', margin: "0" }}>
                                            अभियुक्ति{" "}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {properties && properties.length > 0 ? (
                                        properties.map((detail, index) => (
                                            <tr style={{
                                                border: '1px solid black',
                                                fontSize: "12px",
                                                textAlign: "center",
                                                padding: "10px",
                                                verticalAlign: "middle",
                                            }} key={index}>
                                                {/* {location: '', details: '', price: '', ownerName: '', ownerRelation: '', purchaseType: '', purchaseDate: '', sellerName: '', sellerDetails: "", incomeFromProperty: '', remarks: '' } */}
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {index + 1}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.location}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.details}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.price}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.ownerName}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.ownerRelation}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.purchaseType}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.purchaseDate}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.sellerName}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.sellerDetails}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.incomeFromProperty}
                                                </td>
                                                <td
                                                    style={{ border: '1px solid black', margin: "0", fontSize: "16px" }}
                                                >
                                                    {detail.remarks}
                                                </td>
                                            </tr>
                                        ))
                                    ) : (
                                        <tr
                                            style={{
                                                border: '1px solid black',
                                                fontSize: "12px",
                                                textAlign: "center",
                                                padding: "10px",
                                                verticalAlign: "middle",
                                            }} >
                                            <td colSpan="12">निरंक</td>
                                        </tr>
                                    )}
                                </tbody>
                            </table>
                        </Row>
                    </div>

                </ModalBody>
                <ModalFooter>
                    <Button color="secondary" onClick={() => { setShowPreview(false); navigate("/"); }}>
                        Close
                    </Button>
                    <Button color="primary" onClick={() => { handlePrint(); navigate("/"); }}>
                        Print
                    </Button>
                </ModalFooter>
            </Modal >
        </>
    );
};

export default PrintIPRForm;