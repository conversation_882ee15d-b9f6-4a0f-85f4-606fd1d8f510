import { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Button,
  Container,
  Row,
  Col,
  Label,
} from "reactstrap";
import { useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import Swal from "sweetalert2";
import Header from "../../components/Headers/Header.jsx";

const UpdateDesignation = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [data, setData] = useState([]);
  const [formData, setFormData] = useState({
    dClass: "",
    designation: "",
    isVarified: "0",
  });

  useEffect(() => {
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/designation/getSingle/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setFormData(response.data);
        } else {
          alert("Failed to fetch designation details.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchDesignation();
  }, []);

  useEffect(() => {
    if (data && data.class) {
      setFormData({
        dClass: String(data.class) || "",
        designation: String(data.designation) || "",
        isVarified: false,
      });
    }
  }, [data]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();
    Swal.fire({
      title: "Are you sure?",
      text: "Please verify the details. If verification is not completed, the data will not be saved.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Verify",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        setFormData((prevData) => ({ ...prevData, isVarified: true }));
        try {
          const response = await axios.put(
            `${endPoint}/api/designation/update/${id}`,
            { ...formData, isVarified: "1" },
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            Swal.fire(
              "Success!",
              "Designation updated successfully!",
              "success"
            );
            navigate("/admin/designation-list");
          } else {
            Swal.fire("Error", "Failed to update designation.", "error");
          }
        } catch (error) {
          console.error("An error occurred while updating the data:", error);
          Swal.fire(
            "Error",
            "An error occurred. Please try again later.",
            "error"
          );
        }
      } else {
        Swal.fire("Cancelled", "Verification was not completed.", "info");
      }
    });
  };

  const [classData, setClassData] = useState([]);
  useEffect(() => { 
    const fetchClassData = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/class/getAll`, {
        headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
      });
      if (response.status === 200) {
        setClassData(response.data);
      } else {
        SwalMessageAlert("No Class Data Found", "error");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
    fetchClassData();
  }});
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">
                      Update Designation{" "}
                      <span style={{ color: "red", fontSize: "12px" }}>
                        ( Last Updated{" "}
                        {new Date(data.updatedAt).toLocaleDateString()} )
                      </span>
                    </h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-class">Class</Label>
                          <Input
                            type="select"
                            name="dClass"
                            id="input-class"
                            value={formData.dClass}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select Class</option>
                            {classData &&
                              classData.length > 0 &&
                              classData.map((type, index) => (
                                <option key={index} value={type._id} selected={String(type._id) === String(formData.dClass) ? true : false}>
                                  {type.className}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-designation">Designation</Label>
                          <Input
                            type="text"
                            name="designation"
                            id="input-designation"
                            placeholder="Designation"
                            value={formData.designation}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button color="primary" onClick={handleSubmit}>
                      Update
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateDesignation;
