import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  Container,
  Row,
  Col,
  Modal,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Spinner,
} from "reactstrap";
import { FormControl } from "react-bootstrap";
import ReactModal from "react-modal";
import Webcam from "react-webcam";
import { useNavigate } from "react-router-dom";

import * as faceapi from "face-api.js";
import "@tensorflow/tfjs";
import "./markAttendance.css";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import NavImage from "../../assets/img/theme/user-icon.png";
import Swal from "sweetalert2";
ReactModal.setAppElement("#root");

const Attendance = () => {
  const endPoint = "http:localhost:5000/attendance/api/recognize";
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [isModalOpen, setIsModalOpen] = useState(false);


  const toggleModal = () => setIsModalOpen(false);


  
  const [isFaceDetected, setIsFaceDetected] = useState(false);
  const [isLightOk, setIsLightOk] = useState(true);
  const [blinkCount, setBlinkCount] = useState(0);
  const [distanceCount, setDistanceCount] = useState(0);
  const navigate = useNavigate();

  const [lastBlinkTime, setLastBlinkTime] = useState(0);
  const webcamRef = useRef(null);
  const frameCountRef = useRef(0);
  const canvasRef = useRef(null);

  const calculateStayDuration = (loginTime, logoutTime) => {
    if (!loginTime || !logoutTime) {
      return "N/A";
    }
    const loginDate = new Date(loginTime);
    const logoutDate = new Date(logoutTime);
    const durationMs = logoutDate - loginDate;
    if (durationMs <= 0) {
      return "Invalid times";
    }
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    return `${hours}h ${minutes}m ${seconds}s`;
  };
  useEffect(() => {
    const loadModels = async () => {
      try {
        // console.log("Loading models...");
        await faceapi.nets.ssdMobilenetv1.loadFromUri("/models");
        await faceapi.nets.faceLandmark68Net.loadFromUri("/models");
        await faceapi.nets.faceRecognitionNet.loadFromUri("/models");
        // console.log("Models loaded successfully.");
      } catch (error) {
        console.error("Error loading models:", error);
      }
    };
    loadModels();
  }, []);

  const calculateEAR = (eye) => {
    const vertical1 = Math.hypot(eye[1].x - eye[5].x, eye[1].y - eye[5].y);
    const vertical2 = Math.hypot(eye[2].x - eye[4].x, eye[2].y - eye[4].y);
    const horizontal = Math.hypot(eye[0].x - eye[3].x, eye[0].y - eye[3].y);
    return (vertical1 + vertical2) / (2 * horizontal);
  };

  const checkLighting = (video) => {
    const canvas = document.createElement("canvas");
    const context = canvas.getContext("2d");
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
    const frame = context.getImageData(0, 0, canvas.width, canvas.height);
    const data = frame.data;
    let totalBrightness = 0;
    for (let i = 0; i < data.length; i += 4) {
      const brightness =
        0.2126 * data[i] + 0.7152 * data[i + 1] + 0.0722 * data[i + 2];
      totalBrightness += brightness;
    }
    // console.log(totalBrightness);
    const avgBrightness = totalBrightness / (data.length / 4);
    setIsLightOk(avgBrightness > 50);
  };
  const isRealFace = (landmarks) => {
    const leftEye = landmarks.getLeftEye();
    const rightEye = landmarks.getRightEye();
    const mouth = landmarks.getMouth();

    const eyeDistance = Math.hypot(
      leftEye[0].x - rightEye[3].x,
      leftEye[0].y - rightEye[3].y
    );
    const mouthWidth = Math.hypot(
      mouth[0].x - mouth[6].x,
      mouth[0].y - mouth[6].y
    );

    return eyeDistance > mouthWidth * 1.5;
  };

  const REAL_FACE_WIDTH = 0.15;
  const FOCAL_LENGTH = 500;

  const processVideo = async () => {
    if (
      webcamRef.current &&
      webcamRef.current.video &&
      webcamRef.current.video.readyState === 4
    ) {
      const video = webcamRef.current.video;
      if (frameCountRef.current % 20 === 0) {
        checkLighting(video);
      }
      const detections = await faceapi
        .detectAllFaces(video)
        .withFaceLandmarks();
      if (detections.length === 1) {
        const canvas = canvasRef.current;
        canvas.width = 650;
        canvas.height = 650;
        const context = canvas.getContext("2d");
        context.clearRect(0, 0, canvas.width, canvas.height);
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;
        const cameraCircleRadius = Math.min(centerX, centerY);
        context.beginPath();
        context.arc(centerX, centerY, cameraCircleRadius, 0, Math.PI * 2);
        context.strokeStyle = "blue";
        context.lineWidth = 2;
        context.stroke();
        const facesInCircle = detections.filter((detection) => {
          const { x, y, width, height } = detection.detection.box;
          const boxCorners = [
            { x, y },
            { x: x + width, y },
            { x, y: y + height },
            { x: x + width, y: y + height },
          ];
          return boxCorners.every((corner) => {
            const distance = Math.hypot(corner.x - centerX, corner.y - centerY);
            return distance <= cameraCircleRadius;
          });
        });
        if (facesInCircle.length === 1) {
          const landmarks = facesInCircle[0].landmarks;
          const { width } = facesInCircle[0].detection.box;
          const distance = (REAL_FACE_WIDTH * FOCAL_LENGTH) / width;
          setDistanceCount(distance);
          if (isRealFace(landmarks) && distance < 0.6 && distance > 0.4) {
            setIsFaceDetected(true);
            context.strokeStyle = "green";
            context.lineWidth = 2;
            context.stroke();
            const leftEye = landmarks.getLeftEye();
            const rightEye = landmarks.getRightEye();
            const leftEAR = calculateEAR(leftEye);
            const rightEAR = calculateEAR(rightEye);
            const avgEAR = (leftEAR + rightEAR) / 2;
            let EAR_THRESHOLD = 0.22;
            if (!isLightOk) {
              EAR_THRESHOLD = 0.18;
            }
            const currentTime = new Date().getTime();
            if (avgEAR < EAR_THRESHOLD) {
              if (currentTime - lastBlinkTime > 200) {
                setBlinkCount((prev) => prev + 1);
                setLastBlinkTime(currentTime);
              }
            } else {
              setLastBlinkTime(currentTime);
            }
          } else {
            setIsFaceDetected(false);
            context.strokeStyle = "red";
            context.lineWidth = 2;
            context.stroke();
          }
        } else {
          setIsFaceDetected(false);
        }
        frameCountRef.current += 1;
      } else {
        setIsFaceDetected(false);
      }
    }
    requestAnimationFrame(processVideo);
  };

  const captureCircularArea = () => {
    const canvas = canvasRef.current;
  
    if (!canvas) {
      console.error("Canvas is not available!");
      return;
    }
  
    const context = canvas.getContext("2d");
    const video = webcamRef.current?.video;
  
    if (!video || video.readyState !== 4) {
      console.error("Video is not ready!");
      return;
    }
  
    // Ensure canvas dimensions match the video feed
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  
    // Draw the video frame onto the canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
  
    // Center of the circular area
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const cameraCircleRadius = Math.min(centerX, centerY);
  
    // Create a new canvas to hold the circular image
    const circularCanvas = document.createElement("canvas");
    circularCanvas.width = cameraCircleRadius * 2; // Diameter
    circularCanvas.height = cameraCircleRadius * 2; // Diameter
    const circularContext = circularCanvas.getContext("2d");
  
    // Draw the circular area onto the new canvas
    circularContext.beginPath();
    circularContext.arc(
      cameraCircleRadius, // Center X of circular canvas
      cameraCircleRadius, // Center Y of circular canvas
      cameraCircleRadius * 0.9, // Radius (optional padding adjustment)
      0,
      Math.PI * 2
    );
    circularContext.closePath();
    circularContext.clip(); // Clip to the circular area
  
    // Adjust source position to copy the circular region from the original canvas
    circularContext.drawImage(
      canvas,
      centerX - cameraCircleRadius, // Source X (top-left corner of the circular area)
      centerY - cameraCircleRadius, // Source Y (top-left corner of the circular area)
      cameraCircleRadius * 2, // Source width (diameter of the circle)
      cameraCircleRadius * 2, // Source height (diameter of the circle)
      0,
      0,
      circularCanvas.width, // Destination width
      circularCanvas.height // Destination height
    );
  
    // Get the circular image data URL with high quality
    const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0); // Quality set to maximum
    // console.log("Captured Circular Image Data URL:", circularImageSrc);
  
    // Display the captured circular image (optional)
    const imgElement = document.createElement("img");
    imgElement.src = circularImageSrc;
    document.body.appendChild(imgElement); // Append the image to the body or any container
  };
  
  

  const [empCode, setEmpCode] = useState(null);
  const handleStart = (empCode) => {
    setEmpCode(empCode);
    setIsModalOpen(true);
    setTimeout(() => {
      requestAnimationFrame(processVideo);
    }, 100);
  };

  const [isModalRegisterOpen, setIsModalRegisterOpen] = useState(false);
  const toggleRegisterModal = () => setIsModalRegisterOpen(false);
  const handleRegisterFace = () => {
    setEmpCode(empCode);
    setIsModalRegisterOpen(true);
    setTimeout(() => {
      requestAnimationFrame(processVideo);
    }, 100);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setIsModalRegisterOpen(false);
  };
  const [loading, setLoading] = useState(false);
  const handleCapture = async () => {
    const canvas = canvasRef.current;
  
    if (!canvas) {
      console.error("Canvas is not available!");
      return;
    }
  
    const context = canvas.getContext("2d");
    const video = webcamRef.current?.video;
  
    if (!video || video.readyState !== 4) {
      console.error("Video is not ready!");
      return;
    }
  
    // Ensure canvas dimensions match the video feed
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  
    // Draw the video frame onto the canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
  
    // Center of the circular area
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const cameraCircleRadius = Math.min(centerX, centerY);
  
    // Create a new canvas to hold the circular image
    const circularCanvas = document.createElement("canvas");
    circularCanvas.width = cameraCircleRadius * 2; // Diameter
    circularCanvas.height = cameraCircleRadius * 2; // Diameter
    const circularContext = circularCanvas.getContext("2d");
  
    // Draw the circular area onto the new canvas
    circularContext.beginPath();
    circularContext.arc(
      cameraCircleRadius, // Center X of circular canvas
      cameraCircleRadius, // Center Y of circular canvas
      cameraCircleRadius * 0.9, // Radius (optional padding adjustment)
      0,
      Math.PI * 2
    );
    circularContext.closePath();
    circularContext.clip(); // Clip to the circular area
  
    // Adjust source position to copy the circular region from the original canvas
    circularContext.drawImage(
      canvas,
      centerX - cameraCircleRadius, // Source X (top-left corner of the circular area)
      centerY - cameraCircleRadius, // Source Y (top-left corner of the circular area)
      cameraCircleRadius * 2, // Source width (diameter of the circle)
      cameraCircleRadius * 2, // Source height (diameter of the circle)
      0,
      0,
      circularCanvas.width, // Destination width
      circularCanvas.height // Destination height
    );
  
    // Get the circular image data URL with high quality
    const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0); // Quality set to maximum
    const imageSrc = webcamRef.current.getScreenshot();
    if (circularImageSrc) {
      setLoading(true);
      try {
        const formData = new FormData();
        const response = await fetch(circularImageSrc);
        const blob = await response.blob();
        formData.append("image", blob, "captured-image.jpg");
        formData.append("username", empCode);
        const apiResponse = await fetch(
          "http://164.100.150.78/attendance/api/recognize",
          {
            method: "POST",
            body: formData,
          }
        );
        if (apiResponse.status === 200) {
          const data = await apiResponse.json();
          if (Number(data.recognized_user) === Number(empCode)) {
            try {
              const response = await axios.get(
                `http://164.100.150.78/lmsbackend/api/attendance/add?empCode=${empCode}`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    'web-url': window.location.href,
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              if (response.status === 200) {
                Swal.fire(
                  `Attendance Marked for ${empCode} Successfully!`,
                  "success"
                );
                // SwalMessageAlert(
                //   `Attendance Marked for ${empCode} Successfully!`,
                //   "success"
                // );
                // setTimeout(() => {
                //   window.location.reload();
                // }, 5000);
              } else {
                Swal.fire(
                  `Attendance Marked Failed. Due to ${response.data.msg}!`,
                  "error"
                );
                // SwalMessageAlert(
                //   `Attendance Marked Failed. Due to ${response.data.msg}!`,
                //   "error"
                // );
              }
            } catch (error) {
              Swal.fire(
                `Attendance Marked Failed. Due to ${error.message}!`,
                "error"
              );
              // SwalMessageAlert(
              //   `Attendance Marked Failed. Due to ${error.message}!`,
              //   "error"
              // );
            }
          } else {
            Swal.fire(
              `Attendance Marked Failed. Due to ${data.recognized_user}!`,
              "error"
            );
            // SwalMessageAlert(
            //   `Attendance Marked Failed. Due to ${data.recognized_user}!`,
            //   "error"
            // );
            // setTimeout(() => {
            //   window.location.reload();
            // }, 5000);
          }
        } else {
          Swal.fire(
            `Attendance Marked failed. Due to ${apiResponse}!`,
            "error"
          );
          // SwalMessageAlert(
          //   `Attendance Marked failed. Due to ${apiResponse}!`,
          //   "error"
          // );
          // setTimeout(() => {
          //   window.location.reload();
          // }, 5000);
        }
      } catch (error) {
        Swal.fire(
          `Attendance Marked Failed. Due to ${error.message}!`,
          "error"
        );
        // SwalMessageAlert(
        //   `Attendance Marked Failed. Due to ${error.message}!`,
        //   "error"
        // );
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRegisterCapture = async () => {
    const canvas = canvasRef.current;
  
    if (!canvas) {
      console.error("Canvas is not available!");
      return;
    }
  
    const context = canvas.getContext("2d");
    const video = webcamRef.current?.video;
  
    if (!video || video.readyState !== 4) {
      console.error("Video is not ready!");
      return;
    }
  
    // Ensure canvas dimensions match the video feed
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
  
    // Draw the video frame onto the canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);
  
    // Center of the circular area
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const cameraCircleRadius = Math.min(centerX, centerY);
  
    // Create a new canvas to hold the circular image
    const circularCanvas = document.createElement("canvas");
    circularCanvas.width = cameraCircleRadius * 2; // Diameter
    circularCanvas.height = cameraCircleRadius * 2; // Diameter
    const circularContext = circularCanvas.getContext("2d");
  
    // Draw the circular area onto the new canvas
    circularContext.beginPath();
    circularContext.arc(
      cameraCircleRadius, // Center X of circular canvas
      cameraCircleRadius, // Center Y of circular canvas
      cameraCircleRadius * 0.9, // Radius (optional padding adjustment)
      0,
      Math.PI * 2
    );
    circularContext.closePath();
    circularContext.clip(); // Clip to the circular area
  
    // Adjust source position to copy the circular region from the original canvas
    circularContext.drawImage(
      canvas,
      centerX - cameraCircleRadius, // Source X (top-left corner of the circular area)
      centerY - cameraCircleRadius, // Source Y (top-left corner of the circular area)
      cameraCircleRadius * 2, // Source width (diameter of the circle)
      cameraCircleRadius * 2, // Source height (diameter of the circle)
      0,
      0,
      circularCanvas.width, // Destination width
      circularCanvas.height // Destination height
    );
  
    // Get the circular image data URL with high quality
    const circularImageSrc = circularCanvas.toDataURL("image/jpeg", 1.0); // Quality set to maximum
    // console.log("Captured Circular Image Data URL:", circularImageSrc);
    const imageSrc = webcamRef.current.getScreenshot();
    if (circularImageSrc) {
      setLoading(true);
      try {
        const formData = new FormData();
        const response = await fetch(circularImageSrc);
        const blob = await response.blob();
        const empCode = document.querySelector(".username").value;
        formData.append("file", blob, "captured-image.jpg");
        const apiResponse = await fetch(
          `http://164.100.150.78/attendance/api/upload/training?username=${empCode}`,
          {
            method: "POST",
            body: formData,
          }
        );
        if (apiResponse.status === 200) {
          const data = await apiResponse.json();
          if (Number(data.recognized_user) === Number(empCode)) {
            try {
              const response = await axios.get(
                `http://164.100.150.78/lmsbackend/api/attendance/add?empCode=${empCode}`,
                {
                  headers: {
                    "Content-Type": "application/json",
                    'web-url': window.location.href,
                    Authorization: `Bearer ${token}`,
                  },
                }
              );
              if (response.status === 200) {
                SwalMessageAlert(
                  `Attendance Marked for ${empCode} Successfully!`,
                  "success"
                );
                setTimeout(() => {
                  window.location.reload();
                }, 5000);
              } else {
                SwalMessageAlert(
                  `Attendance Marked Failed. Due to ${response.data.msg}!`,
                  "error"
                );
              }
            } catch (error) {
              SwalMessageAlert(
                `Attendance Marked Failed. Due to ${error.message}!`,
                "error"
              );
            }
          } else {
            SwalMessageAlert(
              `Attendance Marked Failed. Due to ${data.recognized_user}!`,
              "error"
            );
            setTimeout(() => {
              window.location.reload();
            }, 5000);
          }
        } else {
          SwalMessageAlert(
            `Attendance Marked failed. Due to ${apiResponse}!`,
            "error"
          );
          setTimeout(() => {
            window.location.reload();
          }, 5000);
        }
      } catch (error) {
        SwalMessageAlert(
          `Attendance Marked Failed. Due to ${error.message}!`,
          "error"
        );
      } finally {
        setLoading(false);
      }
    }
  };
  const [filterText, setFilterText] = useState("");
  const [employee, setEmployee] = useState([]);
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `http://164.100.150.78/lmsbackend/api/employee/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setEmployee(response.data.getAllEmployeeCollegeWise);
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchCollege();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]);

  const [attendanceEmployees, setAttendanceEmployees] = useState([]);
  useEffect(() => {
    const fetchAttendance = async () => {
      try {
        const response = await axios.get(
          `http://164.100.150.78/lmsbackend/api/attendance/today-institute-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setAttendanceEmployees(response.data);
        } else {
          SwalMessageAlert(`${response.data.mss}`, "error");
        }
      } catch (error) {
        SwalMessageAlert(
          `An error occurred ${error.message}. Please try again later.`,
          "errpr"
        );
      }
    };
    fetchAttendance();
  }, [endPoint, token]);
  const columns = [
    {
      name: "Action",
      cell: (employee) => {
        const attendanceRecord = attendanceEmployees.find(
          (record) => record.empCode === employee.empCode
        );
        return (
          <>
            {attendanceRecord ? (
              <div>
                <div>
                  <strong>Login Time: </strong>
                  {attendanceRecord.loginTime
                    ? new Date(attendanceRecord.loginTime).toLocaleString()
                    : "No LogIn Time"}
                </div>
                <div>
                  <strong>Logout Time: </strong>
                  {attendanceRecord.logoutTime ? (
                    new Date(attendanceRecord.logoutTime).toLocaleString()
                  ) : (
                    <>
                      <span style={{ color: "red" }}>No Logout Time</span>
                      <br />
                    </>
                  )}
                </div>
                <div>
                  <strong>Stay Duration: </strong>
                  {attendanceRecord &&
                  attendanceRecord.loginTime &&
                  attendanceRecord.logoutTime
                    ? calculateStayDuration(
                        attendanceRecord.loginTime,
                        attendanceRecord.logoutTime
                      )
                    : "No Logout"}
                </div>
                <div>
                  <strong>Attendance Using: </strong>
                  {attendanceRecord
                    ? attendanceRecord.attendanceFrom
                    : "Not Given"}
                </div>
                <Button
                  color="primary"
                  onClick={() => handleStart(employee.empCode)}
                  size="sm"
                >
                  Mark Attendance
                </Button>
              </div>
            ) : (
              <Button
                color="primary"
                onClick={() => handleStart(employee.empCode)}
                size="sm"
              >
                Mark Attendance
              </Button>
            )}
          </>
        );
      },
    },
    
        {
          name: "Photo",
          cell: (employee) =>
            employee.encodedImage &&
              employee.faceVerified !== false &&
              employee.encodedImage !== "" ? (
              <img
                src={`data:image/png;base64,${employee.encodedImage}`}
                style={{ width: "50px" }}
              />
            ) : (
              <img src={NavImage} style={{ width: "50px" }} />
            ),
        },
    {
      name: "Photo",
      cell: (employee) =>
        employee.encodedImage &&
        employee.faceVerified !== false &&
        employee.encodedImage !== "" ? (
          <img
            src={`data:image/png;base64,${employee.encodedImage}`}
            style={{ width: "50px" }}
          />
        ) : (
          <img src={NavImage} style={{ width: "50px" }} />
        ),
    },
    {
      name: "Basic Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {employee.title} {employee.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {employee.email}
          </div>
          <div className="mb-2">
            <strong>Contact: </strong>
            {employee.contact}
          </div>
          <div className="mb-2">
            <strong>Emp Code: </strong>
            <strong className="badge-primary">{employee.empCode}</strong>
          </div>
        </div>
      ),
      sortable: true,
      sortFunction: (rowA, rowB) =>
        parseInt(rowA.empCode) - parseInt(rowB.empCode),
      wrap: true,
    },
    {
      name: "Class Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Class: </strong>
            {employee.class_details["className"]}
          </div>
          <div className="mb-2">
            <strong>Designation: </strong>
            {employee.designation_details["designation"]}
          </div>
        </div>
      ),
      wrap: true,
    },
  ];
  const filteredData =
    employee &&
    employee.filter((item) => {
      if (item?.activeStatus === true) {
        const filterTextLower = filterText.toLowerCase();

        // Check for "Verified" and "Not Verified"
        if (filterTextLower === "verified") {
          return item.verified === true; // Only include verified employees
        } else if (filterTextLower === "not verified") {
          return item.verified === false; // Only include verified employees
        } else if (filterTextLower === "not") {
          return item.verified === false; // Only include verified employees
        }

        // Default filtering for name, empCode, and contact
        return (
          (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
          (item.empCode &&
            item.empCode.toLowerCase().includes(filterTextLower)) ||
          (item.contact &&
            item.contact.toString().toLowerCase().includes(filterTextLower))
        );
      }
    });
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Attendance</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      onClick={handleRegisterFace}
                      size="sm"
                    >
                      Register Your Face
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Col>
                  <Card className="shadow">
                    <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                      <Col xs="6">
                        <h3 className="mb-0">Employee List</h3>
                      </Col>
                      <Col className="text-right" xs="4"></Col>
                      <FormControl
                        type="text"
                        placeholder="Search Employee..."
                        className="ml-auto"
                        value={filterText}
                        onChange={(e) => setFilterText(e.target.value)}
                        style={{ width: "250px", borderRadius: "30px" }}
                      />
                    </CardHeader>
                    <CardBody>
                      <DataTable
                        columns={columns}
                        data={filteredData}
                        pagination
                        paginationPerPage={10}
                        highlightOnHover
                        striped
                        sortable // Enable sorting
                        defaultSortField="name" // Sort by the 'name' column initially
                        defaultSortAsc={true} // Ascending order
                        customStyles={{
                          header: {
                            style: {
                              backgroundColor: "#f8f9fa", // Light background color for header
                              fontWeight: "bold",
                            },
                          },
                          rows: {
                            style: {
                              backgroundColor: "#fff", // Row color
                              borderBottom: "1px solid #ddd",
                            },
                            // Apply hover effect through the :hover pseudo-class directly in custom CSS
                            onHoverStyle: {
                              backgroundColor: "#ffff99", // Hover color
                            },
                          },
                        }}
                      />
                    </CardBody>
                  </Card>
                </Col>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Modal
          isOpen={isModalOpen}
          onRequestClose={handleCloseModal}
          style={{ maxWidth: "50%" }}
        >
          <ModalHeader onRequestClose={handleCloseModal}>
            <h2>Capture Photo</h2>
          </ModalHeader>
          <ModalBody>
            <Col lg="12">
              <CardBody>
                <div className="camera-popup">
                  <div
                    className={`camera-circle ${
                      isFaceDetected ? "green" : "red"
                    }`}
                  >
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      videoConstraints={{
                        facingMode: "user",
                      }}
                      style={{
                        position: "absolute",
                        top: -100,
                        left: -200,
                        zIndex: 1,
                        transform: "scaleX(-1)",
                      }}
                      screenshotFormat="image/jpeg"
                    />
                    <canvas
                      ref={canvasRef}
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        zIndex: 1,
                      }}
                    />
                  </div>
                  {isLightOk ? (
                    <div className="status">Lighting is ok</div>
                  ) : (
                    <div className="status error">
                      Error: Insufficient Lighting
                    </div>
                  )}
                  {distanceCount < 0.6 && distanceCount > 0.4 ? (
                    <div className="status">Face Detected</div>
                  ) : (
                    <div className="status error">Face Not Detected</div>
                  )}
                  <br />
                  <div className="">
                    {isLightOk && isFaceDetected ? (
                      <Button
                        className="btn btn-sm btn-success"
                        onClick={handleCapture}
                        disabled={loading}
                      >
                        {loading ? (
                          <Spinner size="sm" color="light" /> // Show spinner
                        ) : (
                          "Click"
                        )}
                      </Button>
                    ) : (
                      ""
                    )}
                  </div>
                </div>
              </CardBody>
            </Col>
            <br />
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={toggleModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
        <Modal
          isOpen={isModalRegisterOpen}
          onRequestClose={handleCloseModal}
          style={{ maxWidth: "50%" }}
        >
          <ModalHeader onRequestClose={handleCloseModal}>
            <h2>Capture Photo</h2>
          </ModalHeader>
          <ModalBody>
            <Col lg="12">
              <CardBody>
                <input type="text" className="form-control username" />
                <div className="camera-popup">
                  <div
                    className={`camera-circle ${
                      isFaceDetected ? "green" : "red"
                    }`}
                  >
                    <Webcam
                      audio={false}
                      ref={webcamRef}
                      videoConstraints={{
                        facingMode: "user",
                      }}
                      style={{
                        position: "absolute",
                        top: -100,
                        left: -200,
                        zIndex: 1,
                        transform: "scaleX(-1)",
                      }}
                      screenshotFormat="image/jpeg"
                    />
                    <canvas
                      ref={canvasRef}
                      style={{
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        zIndex: 1,
                      }}
                    />
                  </div>
                  {isLightOk ? (
                    <div className="status">Lighting is ok</div>
                  ) : (
                    <div className="status error">
                      Error: Insufficient Lighting
                    </div>
                  )}
                  {distanceCount < 0.6 && distanceCount > 0.4 ? (
                    <div className="status">Face Detected</div>
                  ) : (
                    <div className="status error">Face Not Detected</div>
                  )}
                  <br />
                  <div className="">
                    {isLightOk && isFaceDetected ? (
                      <Button
                        className="btn btn-sm btn-success"
                        onClick={handleRegisterCapture}
                        disabled={loading}
                      >
                        {loading ? (
                          <Spinner size="sm" color="light" /> // Show spinner
                        ) : (
                          "Click"
                        )}
                      </Button>
                    ) : (
                      ""
                    )}
                  </div>
                  <div>
                    {/* Your existing components */}
                    <button onClick={captureCircularArea}>
                      Capture Circular Area
                    </button>
                  </div>
                </div>
              </CardBody>
            </Col>
            <br />
          </ModalBody>
          <ModalFooter>
            <Button color="secondary" onClick={toggleRegisterModal}>
              Cancel
            </Button>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default Attendance;
