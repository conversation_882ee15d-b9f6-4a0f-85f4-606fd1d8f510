import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  Container,
  Row,
  Col,
  Form,
  FormGroup,
  Input,
  Label,
  Modal,
  Collapse,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Badge,
  Nav,
  NavItem,
  NavLink,
  TabContent,
  TabPane,
  Table,
  FormFeedback,
  CardTitle,
  FormText
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
import formatDate from "../../utils/formateDate.jsx";
import { Link, useNavigate, useParams } from "react-router-dom";
import { FaDownload } from "react-icons/fa";
import classnames from "classnames"; // For toggling active class
import Swal from "sweetalert2";
import SeatExtensionList from "./seatExtensionList.jsx";
import { MultiSelect } from "react-multi-select-component";

const InstituteProfile = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const id = sessionStorage.getItem("id");
  const [showPreview, setShowPreview] = useState(false);
  const [user, setUser] = useState([]);
  const [activeTab, setActiveTab] = useState("1");
  const [isOpen, setIsOpen] = useState(false);
  const toggleAccordian = () => setIsOpen(!isOpen);
  const [data, setData] = useState(null);
  const toggleTab = (tab) => {
    // console.log(`Active tab before change: ${activeTab}`);
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  };
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    //Basic Details
    collegeName: "",
    address: "",
    university: "",
    aisheCode: "",
    ddoCode: "",
    contactPerson: "",
    contactNumber: "",
    collegeEmail: "",
    govtEmail1: "",
    govtEmail2: "",
    collegeUrl: "",
    telephone: "",
    divison: "",
    district: "",
    block: "",
    lat: "",
    long: "",
    collegeType: "", // UG/PG
    girlsOnly: "", // girls/co-ed
    isAutonomous: "false", // Yes/No
    isModelCollege: "false", // Yes/No
    registrationNumber: "",
    englishMedium: "false", // Yes/No
    sangeetCollege: "false",//Y/N
    areaType: "", // journalsNo/SC area/Tribal Area
    demography: "", // Rural/Urban
    isLead: "false", // Yes/No
    vidhansabha: "",
    ConstituencyNumber: "",
    establishYear: "",
    takenOverDate: "",
    UGC2FRegistered: "",
    ugc2FDate: "",
    isRegistered12B: "",//y/n
    ugc12BDate: "",

    // NAAC Details
    eligibleForNAAC: "false", // Y/N
    naacEvaluated: "false", // Y/N
    naacYear: "",
    naacGrade: "",

    // Janbhagidari Samiti
    isJBRegistered: "false", // Y/N
    jbRegistrationDate: "",
    jbPresidentName: "",
    jbFundYear: "",
    jbFundIncome: "",
    jbFundExpenditure: "",
    jbFundBalance: "",
    jbFundAuditCompleted: "false", // Yes/No
    jbFundRemark: "",
    establishmentRecords: [],// for storing { date, file }
    // Janbhagidari Status

    // NSS & NCC Details
    nssAvailable: "false",
    nssEstablishmentDate: "",
    nssInChargeName: "",
    nssBoysCount: "",
    nssGirlsCount: "",
    nssTotalCount: "",
    nccAvailable: "false",
    nccEstablishmentDate: "",
    nccInChargeName: "",
    nccBoysCount: "",
    nccGirlsCount: "",
    nccTotalCount: "",
    // Infrastructure
    isLandAlloted: "false",
    landAreaSqFt: 0,
    buildingConstructed: "false", // Y/N
    teachingRooms: "",
    labs: "",
    girlsCommonRoom: "false", // Y/N
    library: "false", // Y/N
    digitalBookNo: "", // Conventional/Digital/Both
    physicalBooksNo: "",
    journalsNo: "",
    readingRoom: "false", // Y/N
    sportsGround: "false", // Y/N
    auditorium: "false", // Y/N
    smartClass: "false", // Y/N
    numberOfSmartClass: "",
    hostelAvailable: "false", // Y/N
    boysHostelCount: 0,
    girlsHostelCount: 0,
    boysHostels: [{ name: "", seats: 0, hostalCategory: "" }], // Array of { name: "", seats: "" }
    girlsHostels: [{ name: "", seats: 0, hostalCategory: "" }],
    // seatAvailablity: "",
    // hostalName: "",
    biometricAttendance: "", // Y/N
    wifi: "", // Y/N/Partial
    staffToilet: "false", // Y/N
    boysToilet: "false", // Y/N
    girlsToilet: "false", // Y/N
    rampAvailable: "false", // Y/N

    // NSP Details
    nspRegistered: "false", // Y/N
    nspInstituteId: "",
    nodalOfficerName: "",
    nodalOfficerMobile: "",
    nodalOfficerBioAuth: "false", // Y/N
    principalBioAuth: "false", // Y/N

    // Setup Details

    setupDetails: [{
      class: "",
      designationsList: "",
      sanctionedPosts: "", working: "", sanctionOrderNo: "", sanctionDate: "",
      isSanctionOrderFile: "false", uploadsSanctionOrderFile: null
    }],
    // Upload Documents Section

    //basic details (part I)
    uploadClgSanctionLetter: null,
    clgSanctionFileName: "",
    isClgFile: "false",
    //(basic details part II)
    uploadEstablishFile: null,
    establishFileName: "",
    isEstablishFile: "false",
    uploadTakenOverFile: null,
    takenOverFileName: "",
    isTakenOverFile: "false",
    upload2FUgcRegisteredFile: null,
    Ugc2FRegisterFileName: "",
    is2FUgcFile: "false",
    upload12BRegisteredFile: null,
    Registered12BFileName: "",
    is12BFile: "false",
    //NAAC details
    uploadGradeCycleFile: null,
    GradeCycleFileName: "",
    isGradeCycleFile: "false",
    //janbhagidari details
    uploadJBSRegisterFile: null,
    jBSRegisterFileName: "",
    isjBSRegisterFile: "false",

    uploadCaAuditFile: null,
    CaAuditFileName: "",
    isCaAuditFile: "false",

    //NSS AND NCC DETAILS
    // uploadsNssEstablishFile: null,
    NssEstablishFileName: "",
    isNssEstablishFile: "false",
    uploadsNssEstablishFile: [],//establishmentDate: "", 
    uploadsNccEstablishFile: [],
    // uploadsNccEstablishFile: null,
    NccEstablishFileName: "",
    isNccEstablishFile: "false",

    //infrastructure details
    uploadsLandAreaFile: null,
    LandAreaFileName: "",
    isLandAreaFile: "false",

    //setup details


  });
  const categories = ["ST", "SC", "OBC", "General"];
  const [selectedDate, setSelectedDate] = useState("");
  const handleDateChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };
  const validateForms = () => {
    const newErrors = {};

    if (!formData.class) newErrors.class = "Class is required";
    if (!formData.designationsList) newErrors.designationsList = "Designation is required";
    if (!formData.sanctionedPosts) newErrors.sanctionedPosts = "Sanctioned posts are required";
    if (!formData.working) newErrors.working = "Working count is required";
    if (!formData.sanctionOrderNo) newErrors.sanctionOrderNo = "Sanction order no. is required";
    if (!formData.sanctionDate) newErrors.sanctionDate = "Sanction date is required";

    if (formData.isSanctionOrderFile === "true") {
      if (!formData.uploadsSanctionOrderFile) newErrors.uploadsSanctionOrderFile = "File is required";
      // if (!formData.SanctionOrderFileName) newErrors.SanctionOrderFileName = "File name is required";
    }

    setErrors(newErrors);

    return Object.keys(newErrors).length === 0; // ✅ Valid only if no errors
  };


  const [tableData, setTableData] = useState([]);
  const [showFirstTable, setShowFirstTable] = useState(false);

  const handleAddRow1 = () => {
    if (!validateForms()) {
      SwalMessageAlert("Please fill all required fields", "warning");
      return;
    }
    // Create a new row
    const newRow = {  ...formData};
    if (editIndex !== null) {

      const updatedList = setupDetails.map((item, index) =>
        index === editIndex ? newRow : item
      );

      setTableData(updatedList);
      setEditIndex(null); // reset after editing
      SwalMessageAlert("Row updated successfully", "success");
    } else {
      setTableData(prev => [...prev, newRow]);
      // setTableData([...tableData, newRow]);
      SwalMessageAlert("Row added successfully", "success");
    }

    // setShowFirstTable(true);

    // Reset form fields
    setFormData({
      // ...prev,
      class: "",
      designationsList: "",
      designationName: "",
      className: "",
      sanctionedPosts: "",
      working: "",
      sanctionOrderNo: "",
      sanctionDate: "",
      isSanctionOrderFile: "false",
      uploadsSanctionOrderFile: null,
      SanctionOrderFileName: "",
    });
    setErrors({});
  //  setDesignationData([]);
  setShowFirstTable(true); 
    SwalMessageAlert("Row added successfully", "success");
  };

  // Handle remove row from table
  const handleRemoveRow1 = (index) => {
    Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    }).then((result) => {
      if (result.isConfirmed) {
        const newTableData = tableData.filter((_, i) => i !== index);
        setTableData(newTableData);
        SwalMessageAlert("Row removed successfully", "success");
      }
    });
  };
  const [setupDetailss, setSetupDetails] = useState([]);

  // const handleRemoveRow = (index) => {
  //   Swal.fire({
  //     title: "Are you sure?",
  //     text: "This row will be removed!",
  //     icon: "warning",
  //     showCancelButton: true,
  //     confirmButtonColor: "#3085d6",
  //     cancelButtonColor: "#d33",
  //     confirmButtonText: "Yes, delete it!",
  //   }).then((result) => {
  //     if (result.isConfirmed) {
  //       const updatedData = setupDetails.filter((_, i) => i !== index);
  //       setSetupDetails(updatedData); // <-- delete from state
  //       SwalMessageAlert("Row deleted successfully", "success");
  //     }
  //   });
  // };

  const [editIndex, setEditIndex] = useState(null);

  const handleEditRow = (index) => {
    const selectedRow = setupDetails[index];
    if (!selectedRow) return;

    setEditIndex(index); // save index to use later on save
    setFormData({ ...selectedRow });
    setFormData({
      class: selectedRow.class || '',
      designationsList: selectedRow.designationsList || '',
      sanctionedPosts: selectedRow.sanctionedPosts || '',
      working: selectedRow.working || '',
      sanctionOrderNo: selectedRow.sanctionOrderNo || '',
      sanctionDate: selectedRow.sanctionDate || '',
      isSanctionOrderFile: selectedRow.isSanctionOrderFile || 'false',
      uploadsSanctionOrderFile: selectedRow.uploadsSanctionOrderFile || null,
      SanctionOrderFileName: selectedRow.SanctionOrderFileName || '',
    });
  };

  const validateContactNumber = (number) => {
    const regex = /^[0-9]{10}$/; // Change as per your requirements (e.g., length, format)
    return regex.test(number);
  };
  const validateEmail = (email) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email format validation
    return regex.test(email);
  };
  const handleKeyPress = (e) => {
    const key = e.key;
    const isNumber = /^[0-9]$/.test(key);
    if (!isNumber && key !== "Backspace" && key !== "Tab") {
      e.preventDefault(); // Prevents the input of non-numeric characters
    }
  };
  const handleKeyOnPress = (e) => {
    const key = e.key;

    // Allow control/navigation keys
    if (
      key === "Backspace" ||
      key === "Tab" ||
      key === "ArrowLeft" ||
      key === "ArrowRight" ||
      key === "Delete"
    ) {
      return;
    }

    // Allow only digits
    if (!/^[0-9]$/.test(key)) {
      e.preventDefault();
      return;
    }

    const inputValue = e.target.value;
    const nextValue = inputValue + key;

    // Only allow numbers that start with 1-9
    const pattern = /^[1-9][0-9]*$/;
    if (!pattern.test(nextValue)) {
      e.preventDefault();
    }
  };
  const cellStyle = {
    border: '1px solid black',
    padding: '10px',
    fontSize: '15px',
  };

  //   const handleClassInputChange2 = async (e, setupDetails, index) => {
  //   const { value } = e.target;

  //   try {
  //     const response = await axios.get(
  //       `${endPoint}/api/degisnation-class-wise/${value}`,
  //       {
  //         headers: {
  //           "Content-Type": "application/json",

  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );

  //     if (response.status === 200) {
  //       const data = response.data;

  //        setDesignationData(data);

  //     } else {
  //       SwalMessageAlert("Designation Not Found", "error");
  //     }
  //   } catch (error) {
  //     console.error("An error occurred while fetching designations:", error);
  //     alert("An error occurred. Please try again later.");
  //   }

  //     setFormData(prev => {
  //       const updated = { ...prev };
  //       updated.setupDetails[index].class = value;  // Store file object
  //       return updated;
  //     });

  // };

  const handleClassInputChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/degisnation-class-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;

        setDesignationData(data);
      } else {
        SwalMessageAlert("Designation Not Found", "error");

      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      class: value,
    });
  };
  const labelStyle = {
    ...cellStyle,
    fontWeight: 'bold',
  };

  const valueStyle = {
    ...cellStyle,
    color: 'darkblue',
  };
  const headerStyle = {
    backgroundColor: "lightyellow",
    fontWeight: "bold",
    textAlign: "center",
    padding: "2px",
    border: "1px solid #ddd"
  };
  useEffect(() => {
    if (formData.isClgFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadClgSanctionLetter: "",
        // clgSanctionFileName: "",
      }));
    }
  }, [formData.isClgFile, setFormData]);
  useEffect(() => {
    if (formData.isEstablishFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadEstablishFile: "",
        // establishFileName: "",
      }));
    }
  }, [formData.isEstablishFile, setFormData]);
  useEffect(() => {
    if (formData.takenOver === "false") {
      setFormData(prevState => ({
        ...prevState,
        takenOverDate: "",
        isTakenOverFile: "",
      }));
    }
  }, [formData.takenOver, setFormData]);
  useEffect(() => {
    if (formData.isTakenOverFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadTakenOverFile: "",
        // takenOverFileName: "",
      }));
    }
  }, [formData.isTakenOverFile, setFormData]);

  useEffect(() => {
    if (formData.UGC2FRegistered === "false") {
      setFormData(prevState => ({
        ...prevState,
        ugc2FDate: "",
        is2FUgcFile: "",
      }));
    }
  }, [formData.UGC2FRegistered, setFormData]);
  useEffect(() => {
    if (formData.is2FUgcFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        upload2FUgcRegisteredFile: "",
        // Ugc2FRegisterFileName: "",
      }));
    }
  }, [formData.is2FUgcFile, setFormData]);

  useEffect(() => {
    if (formData.isRegistered12B === "false") {
      setFormData(prevState => ({
        ...prevState,
        ugc12BDate: "",
        is12BFile: "",
      }));
    }
  }, [formData.isRegistered12B, setFormData]);
  useEffect(() => {
    if (formData.is12BFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        upload12BRegisteredFile: "",
        // Registered12BFileName: "",
      }));
    }
  }, [formData.is12BFile, setFormData]);

  useEffect(() => {
    if (formData.smartClass === "false") {
      setFormData(prevState => ({
        ...prevState,
        numberOfSmartClass: "",
      }));
    }
  }, [formData.smartClass, setFormData]);
  useEffect(() => {
    if (formData.isLandAlloted === "false") {
      setFormData(prevState => ({
        ...prevState,
        landAreaSqFt: "",
        buildingConstructed: "",
        isLandAreaFile: "",
      }));
    }
  }, [formData.isLandAlloted, setFormData]);
  useEffect(() => {
    if (formData.isLandAreaFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadsLandAreaFile: "",
        LandAreaFileName: "",
      }));
    }
  }, [formData.isLandAreaFile, setFormData]);
  useEffect(() => {
    if (formData.library === "false") {
      setFormData(prevState => ({
        ...prevState,
        digitalBookNo: "",
        journalsNo: "",
        physicalBooksNo: "",
      }));
    }
  }, [formData.library, setFormData]);
  useEffect(() => {
    if (formData.hostelAvailable === "false") {
      setFormData(prevState => ({
        ...prevState,
        boysHostelCount: "",
        girlsHostelCount: "",

      }));
    }
  }, [formData.hostelAvailable, setFormData]);
  useEffect(() => {
    if (formData.eligibleForNAAC === "false") {
      setFormData(prevState => ({
        ...prevState,
        naacEvaluated: "", // Y/N
        naacYear: "",
        naacGrade: "",
        // isCaAuditFile:"",
        isGradeCycleFile: "",
      }));
    }
  }, [formData.eligibleForNAAC, setFormData]);

  useEffect(() => {
    if (formData.naacEvaluated === "false") {
      setFormData(prevState => ({
        ...prevState,
        naacYear: "",
        naacGrade: "",
        // isCaAuditFile:"",
        isGradeCycleFile: "",
      }));
    }
  }, [formData.naacEvaluated, setFormData]);
  useEffect(() => {
    if (formData.isCaAuditFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadCaAuditFile: "",
        // CaAuditFileName: "",
      }));
    }
  }, [formData.isCaAuditFile, setFormData]);
  useEffect(() => {
    if (formData.isGradeCycleFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadGradeCycleFile: "",
        // GradeCycleFileName: "",
      }));
    }
  }, [formData.isGradeCycleFile, setFormData]);
  useEffect(() => {
    if (formData.isJBRegistered === "false") {
      setFormData(prevState => ({
        ...prevState,
        jbRegistrationDate: "",
        jbPresidentName: "",
        jbFundYear: "",
        jbFundIncome: "",
        jbFundExpenditure: "",
        jbFundBalance: "",
        jbFundAuditCompleted: "", // Yes/No
        jbFundRemark: "",
        isjBSRegisterFile: "",
        isCaAuditFile: "",
      }));
    }
  }, [formData.isJBRegistered, setFormData]);
  useEffect(() => {
    if (formData.jbFundAuditCompleted === "false") {
      setFormData(prevState => ({
        ...prevState,
        isCaAuditFile: "",

      }));
    }
  }, [formData.jbFundAuditCompleted, setFormData]);
  useEffect(() => {
    if (formData.isjBSRegisterFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadJBSRegisterFile: "",
        // jBSRegisterFileName: "",
      }));
    }
  }, [formData.isjBSRegisterFile, setFormData]);
  useEffect(() => {
    if (formData.nssAvailable === "false") {
      setFormData(prevState => ({
        ...prevState,
        nssEstablishmentDate: "",
        nssInChargeName: "",
        nssBoysCount: "",
        nssGirlsCount: "",
        nssTotalCount: "",
        isNssEstablishFile: "",
        uploadsNssEstablishFile: "",
      }));
    }
  }, [formData.nssAvailable, setFormData]);
  useEffect(() => {
    if (formData.isNssEstablishFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadsNssEstablishFile: "",
        // NssEstablishFileName: "",
      }));
    }
  }, [formData.isNssEstablishFile, setFormData]);
  useEffect(() => {
    if (formData.nccAvailable === "false") {
      setFormData(prevState => ({
        ...prevState,
        nccEstablishmentDate: "",
        nccInChargeName: "",
        nccBoysCount: "",
        nccGirlsCount: "",
        nccTotalCount: "",
        isNccEstablishFile: "",
        uploadsNccEstablishFile: "",
      }));
    }
  }, [formData.nccAvailable, setFormData]);
  useEffect(() => {
    if (formData.isNccEstablishFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadsNccEstablishFile: "",
        // NccEstablishFileName: "",
      }));
    }
  }, [formData.isNccEstablishFile, setFormData]);
  useEffect(() => {
    if (formData.nspRegistered === "false") {
      setFormData(prevState => ({
        ...prevState,
        nspInstituteId: "",
        nodalOfficerName: "",
        nodalOfficerMobile: "",
        nodalOfficerBioAuth: "", // Y/N
        principalBioAuth: "", // Y/N

      }));
    }
  }, [formData.nspRegistered, setFormData]);
  useEffect(() => {
    if (formData.isSanctionOrderFile === "false") {
      setFormData(prevState => ({
        ...prevState,
        uploadsSanctionOrderFile: "",
        SanctionOrderFileName: "",
      }));
    }
  }, [formData.isSanctionOrderFile, setFormData]);
  // Helper function to convert boolean strings to Yes/No
  const formatBoolean = (value) => {
    if (value === "true") return "Yes";
    if (value === "false") return "No";
    return value;
  };
  const handlePreview = () => {
    const submitType = "finalSubmit"

    // console.log("Handling Preview");

    // e.preventDefault();
    const validateForm = (submitType) => {
      const newErrors = {};
      const errorFields = [];
      const errorSections = {};

      // Define required fields for each submit type
      const requiredFieldsByType = {
        basicDetails: {
          isClgFile: "false",//1 file
          isEstablishFile: "false",
        },
        moreDetails: {
          ddoCode: "",
          block: "",
          registrationNumber: "",
          areaType: "", // general/SC area/Tribal Area
          girlsOnly: "", // Yes/No
          isAutonomous: "false", // Yes/No
          isModelCollege: "false", // Yes/No
          sangeetCollege: "false",
          englishMedium: "false", // Yes/No
          demography: "", // Rural/Urban
          takenOver: "false",// Yes/No
          UGC2FRegistered: "false",// Yes/No
          isRegistered12B: "false",

        },
        naacDetails: {
          eligibleForNAAC: "false", // Y/N

        },

        janbhagidariDetails: {
          isJBRegistered: "false", // Y/N

        },

        nssAndNccDetails: {
          nssAvailable: "false",
          nccAvailable: "false",
        },

        infrastructureDetails: {
          isLandAlloted: "",
          teachingRooms: "",
          labs: "",
          girlsCommonRoom: "false", // Y/N
          library: "false", // Y/N
          // digitalBookNo: "", // Conventional/Digital/Both
          // physicalBooksNo: "",
          // journalsNo: "",
          readingRoom: "false", // Y/N
          sportsGround: "false", // Y/N
          auditorium: "false", // Y/N
          smartClass: "false", // Y/N
          hostelAvailable: "false", // Y/N
          biometricAttendance: "false", // Y/N
          wifi: "", // Y/N/Partial
          staffToilet: "false", // Y/N
          boysToilet: "false", // Y/N
          girlsToilet: "false", // Y/N
          rampAvailable: "false", // Y/N
        },

        nspRegistrationDetails: {
          nspRegistered: "false", // Y/N

        },

        setupDetails: {
          // designationsList: "",
          // sanctionedPosts: "",
          // working: "",
          // class: "",
          // sanctionOrderNo: "",
          // sanctionDate: "",
          // isSanctionOrderFile: "false",//12 file
        },

        // //basic details (part I)
        // uploadClgSanctionLetter: null,
        // //(basic details part II)
        // uploadEstablishFile: null,
        // uploadTakenOverFile: null,
        // upload2FUgcRegisteredFile: null,
        // upload12BRegisteredFile: null,
        // //NAAC details
        // uploadGradeCycleFile: null,
        // //janbhagidari details
        // uploadJBSRegisterFile: null,
        // uploadCaAuditFile: null,
        // //NSS AND NCC DETAILS
        // uploadsNssEstablishFile: null,
        // uploadsNccEstablishFile: null,
        // //infrastructure details
        // uploadsLandAreaFile: null,
        // //setup details
        // uploadsSanctionOrderFile: null,

      };

      const addConditionalFields = (formData) => {


        if (formData.isJBRegistered === "true") {
          requiredFieldsByType.janbhagidariDetails.jbRegistrationDate = "";
          requiredFieldsByType.janbhagidariDetails.jbPresidentName = "";
          requiredFieldsByType.janbhagidariDetails.jbFundYear = "";
          requiredFieldsByType.janbhagidariDetails.jbFundIncome = "";
          requiredFieldsByType.janbhagidariDetails.jbFundExpenditure = "";
          requiredFieldsByType.janbhagidariDetails.jbFundBalance = "";
          requiredFieldsByType.janbhagidariDetails.jbFundAuditCompleted = "";
          // requiredFieldsByType.jbFundRemark = "";
        }

        if (formData.nssAvailable === "true") {
          requiredFieldsByType.nssAndNccDetails.nssEstablishmentDate = "";
          requiredFieldsByType.nssAndNccDetails.nssInChargeName = "";
          requiredFieldsByType.nssAndNccDetails.nssBoysCount = "";
          requiredFieldsByType.nssAndNccDetails.nssGirlsCount = "";
          requiredFieldsByType.nssAndNccDetails.nssTotalCount = "";
          requiredFieldsByType.nssAndNccDetails.isNssEstablishFile = "";

        }

        if (formData.nccAvailable === "true") {
          requiredFieldsByType.nssAndNccDetails.nccEstablishmentDate = "";
          requiredFieldsByType.nssAndNccDetails.nccInChargeName = "";
          requiredFieldsByType.nssAndNccDetails.nccBoysCount = "";
          requiredFieldsByType.nssAndNccDetails.nccGirlsCount = "";
          requiredFieldsByType.nssAndNccDetails.nccTotalCount = "";
          requiredFieldsByType.nssAndNccDetails.isNccEstablishFile = "";

        }
        if (formData.takenOver === "true") {
          requiredFieldsByType.takenOverDate = "";
          requiredFieldsByType.moreDetails.isTakenOverFile = "";

        }
        if (formData.isClgFile === "true") {
          requiredFieldsByType.basicDetails.uploadClgSanctionLetter = "";

        }
        if (formData.isEstablishFile === "true") {
          requiredFieldsByType.basicDetails.uploadEstablishFile = "";

        }
        if (formData.isNssEstablishFile === "true") {
          requiredFieldsByType.nssAndNccDetails.uploadsNssEstablishFile = "";

        }
        if (formData.isNccEstablishFile === "true") {
          requiredFieldsByType.nssAndNccDetails.uploadsNccEstablishFile = "";

        }
        //  if(formData.jbFundAuditCompleted === "true"){
        //   requiredFieldsByType.janbhagidariDetails.isCaAuditFile = "";

        // }
        if (formData.isTakenOverFile === "true") {
          requiredFieldsByType.moreDetails.uploadTakenOverFile = "";

        }
        if (formData.isCaAuditFile === "true") {
          requiredFieldsByType.janbhagidariDetails.uploadCaAuditFile = "";

        }
        if (formData.isGradeCycleFile === "true") {
          requiredFieldsByType.naacDetails.uploadGradeCycleFile = "";

        }

        if (formData.isLandAreaFile === "true") {
          requiredFieldsByType.infrastructureDetails.uploadsLandAreaFile = "";

        }
        if (formData.isjBSRegisterFile === "true") {
          requiredFieldsByType.janbhagidariDetails.uploadJBSRegisterFile = "";

        }
        if (formData.UGC2FRegistered === "true") {
          requiredFieldsByType.moreDetails.ugc2FDate = "";
          requiredFieldsByType.moreDetails.is2FUgcFile = "";

        }
        if (formData.eligibleForNAAC === "true") {
          requiredFieldsByType.naacDetails.naacEvaluated = "";

        }
        if (formData.naacEvaluated === "true") {
          requiredFieldsByType.naacDetails.naacGrade = "";
          requiredFieldsByType.naacDetails.isGradeCycleFile = "";
          requiredFieldsByType.naacDetails.naacYear = "";

        }
        if (formData.isRegistered12B === "true") {
          requiredFieldsByType.moreDetails.ugc12BDate = "";
          requiredFieldsByType.moreDetails.is12BFile = "";

        }
        if (formData.smartClass === "true") {
          requiredFieldsByType.infrastructureDetails.numberOfSmartClass = "";

        }
        if (formData.is12BFile === "true") {
          requiredFieldsByType.moreDetails.upload12BRegisteredFile = "";
        }
        if (formData.is2FUgcFile === "true") {
          requiredFieldsByType.moreDetails.upload2FUgcRegisteredFile = "";
        }
        if (formData.hostelAvailable === "true") {
          requiredFieldsByType.infrastructureDetails.boysHostelCount = "";
          requiredFieldsByType.infrastructureDetails.girlsHostelCount = "";

        }
        if (formData.isLandAlloted === "true") {
          requiredFieldsByType.infrastructureDetails.landAreaSqFt = "";
          requiredFieldsByType.infrastructureDetails.buildingConstructed = "";
          requiredFieldsByType.infrastructureDetails.isLandAreaFile = "";

        }
        if (formData.nspRegistered === "true") {
          requiredFieldsByType.nspRegistrationDetails.nspInstituteId = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerName = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerMobile = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerBioAuth = "";
          requiredFieldsByType.nspRegistrationDetails.principalBioAuth = "";
        }
        //       if (tableData.length === 0 || setupDetails.length === 0) {
        //   requiredFieldsByType.setupDetails.designationsList = "";
        //   requiredFieldsByType.setupDetails.sanctionedPosts = "";
        //   requiredFieldsByType.setupDetails.working = "";
        //   requiredFieldsByType.setupDetails.class = "";
        //   requiredFieldsByType.setupDetails.sanctionOrderNo = "";
        //   requiredFieldsByType.setupDetails.sanctionDate = "";
        //   requiredFieldsByType.setupDetails.isSanctionOrderFile = "";

        //   if (formData.isSanctionOrderFile === "true") {
        //     requiredFieldsByType.setupDetails.uploadsSanctionOrderFile = "";
        //   }
        // }

      };
      const fieldDisplayNames = {
        girlsOnly: "Girls / Co-Ed",
        isAutonomous: "Is Autonomous",
        isModelCollege: "Is Model College",
        registrationNumber: "Registration Number",
        englishMedium: "English Medium",
        sangeetCollege: "Sangeet College",
        areaType: "Area Type",
        demography: "Demography",
        takenOver: "Taken Over",
        takenOverDate: "Taken Over Date",
        UGC2FRegistered: "2 F Registered unerUGC",
        ugc2FDate: "UGC 2F Date",
        isRegistered12B: "12 B Registered ",
        ugc12BDate: "UGC 12B Date",
        eligibleForNAAC: "Eligible for NAAC",
        naacEvaluated: "NAAC Evaluated",
        naacYear: "NAAC Year",
        naacGrade: "NAAC Grade",
        isJBRegistered: "JB Registered",
        jbRegistrationDate: "JB Registration Date",
        jbPresidentName: "JB President Name",
        jbFundYear: "JB Fund Year",
        jbFundIncome: "JB Income",
        jbFundExpenditure: "JB Expenditure",
        jbFundBalance: "JB Balance",
        jbFundAuditCompleted: "JB Audit Completed",
        nssAvailable: "NSS Available",
        nssEstablishmentDate: "NSS Establishment Date",
        nssInChargeName: "NSS In-Charge Name",
        nssBoysCount: "NSS Boys Count",
        nssGirlsCount: "NSS Girls Count",
        // nssTotalCount: "NSS Total Count",
        nccAvailable: "NCC Available",
        nccEstablishmentDate: "NCC Establishment Date",
        nccInChargeName: "NCC In-Charge Name",
        nccBoysCount: "NCC Boys Count",
        nccGirlsCount: "NCC Girls Count",
        // nccTotalCount: "NCC Total Count",
        isLandAlloted: "Land Alloted",
        landAreaSqFt: "Land Area (Sq. Ft)",
        buildingConstructed: "Building Constructed",
        teachingRooms: "Teaching Rooms",
        labs: "Labs",
        girlsCommonRoom: "Girls Common Room",
        library: "Library",
        digitalBookNo: "Book Type",
        physicalBooksNo: "Books Available",
        journalsNo: "journalsNo Facilities",
        readingRoom: "Reading Room",
        sportsGround: "Sports Ground",
        auditorium: "Auditorium",
        smartClass: "Smart Class",
        numberOfSmartClass: "Number of Smart Classes",
        hostelAvailable: "Hostel Available",
        boysHostelCount: "Boys Hostel Count",
        girlsHostelCount: "Girls Hostel Count",
        // seatAvailablity: "Seat Availability",
        // hostalName: "Hostel Name",
        biometricAttendance: "Biometric Attendance",
        wifi: "WiFi",
        staffToilet: "Staff Toilet",
        boysToilet: "Boys Toilet",
        girlsToilet: "Girls Toilet",
        rampAvailable: "Ramp Available",
        nspRegistered: "NSP Registered",
        nspInstituteId: "NSP Institute ID",
        nodalOfficerName: "Nodal Officer Name",
        nodalOfficerMobile: "Nodal Officer Mobile",
        nodalOfficerBioAuth: "Nodal Officer Bio-Auth",
        principalBioAuth: "Principal Bio-Auth",
        // designationsList: "Designation List",
        // class: "Class ",
        // sanctionOrderNo: "Sanction Order No",
        // sanctionDate: "Sanction Date",
        // sanctionedPosts: "Sanctioned Posts",
        // working: "Employee Working Count",
        isClgFile: "Collge Sanction Order File Upload",
        isEstablishFile: "Establishment relevant File Upload",
        isTakenOverFile: "Taken Over File Upload",
        is2FUgcFile: "UGC 2F Registered File Upload",
        is12BFile: "12B Registered File Upload",
        isLandAreaFile: "Land Area File Upload",
        isCaAuditFile: "CA Audit File Upload",
        isGradeCycleFile: "Grade File Upload",
        isjBSRegisterFile: "JBS File Upload",
        // isSanctionOrderFile: "Sanction Order File Upload",
        isNccEstablishFile: "NCC file upload",
        isNssEstablishFile: "NSS file Upload",
        uploadClgSanctionLetter: "College Sanction Letter",
        //(basic details part II)
        uploadEstablishFile: "Upload Establishment File",
        uploadTakenOverFile: "Taken Over File",
        upload2FUgcRegisteredFile: "2F UGC Registered File",
        upload12BRegisteredFile: "12B Registered File",
        //NAAC details
        uploadGradeCycleFile: "Grade Cycle File",
        //janbhagidari details
        uploadJBSRegisterFile: "JBS Register File",
        uploadCaAuditFile: "CA Audit File",
        //NSS AND NCC DETAILS
        uploadsNssEstablishFile: "Upload Nss Establishment File",
        uploadsNccEstablishFile: "Upload Ncc Establishment File",
        //infrastructure details
        uploadsLandAreaFile: "Land Area File",
        //setup details
        // uploadsSanctionOrderFile: "Sanction Order File",

      };

      addConditionalFields(formData); // to extend the fields conditionally

      const allRequiredFields = {
        ...requiredFieldsByType.basicDetails,
        ...requiredFieldsByType.moreDetails,
        ...requiredFieldsByType.infrastructureDetails,
        ...requiredFieldsByType.naacDetails,
        ...requiredFieldsByType.janbhagidariDetails,
        ...requiredFieldsByType.nssAndNccDetails,
        ...requiredFieldsByType.nspRegistrationDetails,
        ...requiredFieldsByType.setupDetails,

      };

      // console.log("Getting Submit Type");

      const fieldsToValidate = submitType === "finalSubmit" ? allRequiredFields : requiredFieldsByType[submitType];

      if (fieldsToValidate) {
        for (const field in fieldsToValidate) {
          if (fieldsToValidate.hasOwnProperty(field)) {
            if (!formData[field] || (typeof formData[field] === "string" && !formData[field].trim())) {
              newErrors[field] = "This Field is Required.";
              errorFields.push(field);

              for (const section in requiredFieldsByType) {
                if (requiredFieldsByType[section].hasOwnProperty(field)) {
                  if (!errorSections[section]) {
                    errorSections[section] = [];
                  }
                  errorSections[section].push(field);
                }
              }
            }
          }
        }
      } else {
        console.error(`No required fields defined for submit type: ${submitType}`);
      }

      // Construct alert message with custom field names
      if (Object.keys(errorSections).length > 0) {
        let alertMessage = "<strong>Please fill in the following fields:</strong><br><br>";

        for (const section in errorSections) {
          alertMessage += `<strong>${section}:</strong><br> - `;
          alertMessage += errorSections[section]
            .map(field => fieldDisplayNames[field] || field) // Use custom display names or fallback to the key
            .join(", ");
          alertMessage += "<br><br>";
        }

        Swal.fire({
          icon: "warning",
          title: "Missing Fields",
          html: alertMessage,
          confirmButtonText: "OK"
        });
      }

      return newErrors;
    };

    const validationErrors = validateForm(submitType);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }
    setShowPreview(!showPreview);
  };
  const handleHostelCountChange = (e) => {
    const { name, value } = e.target;
    const count = parseInt(value, 10) || 0;

    setFormData((prev) => {
      const hostelArray = Array.from({ length: count }, (_, i) =>
        prev[name === "boysHostelCount" ? "boysHostels" : "girlsHostels"]?.[i] || { name: "", seats: "", hostalCategory: "" }
      );

      return {
        ...prev,
        [name]: count,
        [name === "boysHostelCount" ? "boysHostels" : "girlsHostels"]: hostelArray,
      };
    });
  };


  const [getAllBlock, setGetAllBlock] = useState([]);

  useEffect(() => {
    const getBlock = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-dist-wise-block/${formData.district}`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setGetAllBlock(data);
        }
      } catch (error) {
        console.error("Error fetching block data:", error);
        alert("Failed to load block data.");
      }
    };
    if (formData.district) getBlock();
  }, [formData.district, endPoint, token]);

  const validBlocks = Array.isArray(getAllBlock)
    ? getAllBlock.filter(b => b.BlockNameEng && typeof b.BlockNameEng === "string")
    : [];

  useEffect(() => {
    const count = Math.max(0, Number(formData.boysHostelCount) || 0);
    const existing = Array.isArray(formData.boysHostels) ? formData.boysHostels : [];

    const updated = Array.from({ length: count }, (_, i) => {
      return existing[i] || { name: "", seats: "" };
    });

    setFormData(prev => ({
      ...prev,
      boysHostels: updated,
    }));
  }, [formData.boysHostelCount]);

  useEffect(() => {
    const count = Math.max(0, Number(formData.girlsHostelCount) || 0);
    const existing = Array.isArray(formData.girlsHostels) ? formData.girlsHostels : [];

    const updated = Array.from({ length: count }, (_, i) => {
      return existing[i] || { name: "", seats: "" };
    });

    setFormData(prev => ({
      ...prev,
      girlsHostels: updated,
    }));
  }, [formData.girlsHostelCount]);


  const handleBoysHostelChange = (index, field, value) => {
    setFormData(prev => {
      const updatedHostels = [...prev.boysHostels];
      updatedHostels[index] = {
        ...updatedHostels[index],
        [field]: value,
      };
      return { ...prev, boysHostels: updatedHostels };
    });
  };

  const handleGirlsHostelChange = (index, field, value) => {
    setFormData(prev => {
      const updatedHostels = [...prev.girlsHostels];
      updatedHostels[index] = {
        ...updatedHostels[index],
        [field]: value,
      };
      return { ...prev, girlsHostels: updatedHostels };
    });
  };

  const handleChange = (e, setupDetails, index) => {
    const { name, value, type, checked } = e.target;
    const newData = [...formData.setupDetails];
    newData[index][name] = type === "checkbox" ? (checked ? "true" : "false") : value;

    setFormData((prev) => ({
      ...prev,
      setupDetails: newData,
    }));
    //  handleClassInputChange(e);
  };

  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);
  const [isDivisionChanged, setIsDivisionChanged] = useState(false);
  const [division, setDivision] = useState([]);
  const [errors, setErrors] = useState({});
  const [classData, setClassData] = useState([]);
  const [updatedFields, setUpdatedFields] = useState({});
  const [isOtherSelected, setIsOtherSelected] = useState(false);
  const [submittedSections, setSubmittedSections] = useState({
    basicDetails: false,
    moreDetails: false,
    naacDetails: false,
    janbhagidariDetails: false,
    nssAndNccDetails: false,
    infrastructureDetails: false,
    nspRegistrationDetails: false,
    setupDetails: false,

  });
  // const handleChange = (e) => {
  //   const { name, value } = e.target;
  //   const setNestedValue = (obj, path, val) => {
  //     const keys = path.split(".");
  //     const lastKey = keys.pop();
  //     const deepRef = keys.reduce((acc, key) => {
  //       if (!acc[key]) acc[key] = {};
  //       return acc[key];
  //     }, obj);
  //     deepRef[lastKey] = val;
  //     return { ...obj };
  //   };

  //   setFormData((prev) => setNestedValue({ ...prev }, name, value));
  //   setErrors((prev) => setNestedValue({ ...prev }, name, ""));
  //   setUpdatedFields((prev) => setNestedValue({ ...prev }, name, value));

  // };
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    // const updatedTable = [...formData[setupDetails]];
    //     updatedTable[index][name] = value;

    // setFormData({ ...formData, [setupDetails]: updatedTable });
    // Helper function to set nested values
    const setNestedValue = (obj, path, val) => {
      const keys = path.split(".");
      const lastKey = keys.pop();
      const deepRef = keys.reduce((acc, key) => {
        if (!acc[key]) acc[key] = {};
        return acc[key];
      }, obj);
      deepRef[lastKey] = val;
      return { ...obj };
    };

    setFormData((prev) => {
      let updated = setNestedValue({ ...prev }, name, value);

      if (name === "nssBoysCount" || name === "nssGirlsCount") {
        const boys = parseInt(name === "nssBoysCount" ? value : prev.nssBoysCount || 0, 10);
        const girls = parseInt(name === "nssGirlsCount" ? value : prev.nssGirlsCount || 0, 10);
        updated = setNestedValue(updated, "nssTotalCount", (isNaN(boys) ? 0 : boys) + (isNaN(girls) ? 0 : girls));
      }
      if (name === "nccBoysCount" || name === "nccGirlsCount") {
        const boys = parseInt(name === "nccBoysCount" ? value : prev.nccBoysCount || 0, 10);
        const girls = parseInt(name === "nccGirlsCount" ? value : prev.nccGirlsCount || 0, 10);
        updated = setNestedValue(updated, "nccTotalCount", (isNaN(boys) ? 0 : boys) + (isNaN(girls) ? 0 : girls));
      }
      if (name === "contactNumber") {
        if (!validateContactNumber(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            contactNumber: "Please enter a valid 10-digit contact number.",
          }));
        }
      }
      if (name === "nodalOfficerMobile") {
        if (!validateContactNumber(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            nodalOfficerMobile: "Please enter a valid 10-digit contact number.",
          }));
        }
      }
      if (name === "govtEmail1") {
        if (!validateEmail(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            govtEmail1: "Please enter a valid email address.",
          }));
        }
      } if (name === "telephone") {
        if (!validateContactNumber(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            telephone: "Please enter a valid 10-digit telephone number.",
          }));
        }
      }

      if (name === "govtEmail2") {
        if (!validateEmail(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            govtEmail2: "Please enter a valid email address.",
          }));
        }
      }
      if (name === "collegeEmail") {
        if (!validateEmail(value)) {
          setErrors((prevErrors) => ({
            ...prevErrors,
            collegeEmail: "Please enter a valid email address.",
          }));
        }
      }
      return updated;
    });

    setErrors((prev) => setNestedValue({ ...prev }, name, ""));
    setUpdatedFields((prev) => setNestedValue({ ...prev }, name, value));
  };

  const handledivisonChange = async (e) => {
    const { value } = e.target;

    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setDistrict(response.data);
      } else {
        alert("Failed to fetch districts.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert("An error occurred. Please try again later.");
    }

    // Correct state update
    setFormData((prevData) => ({
      ...prevData,
      divison: value,
    }));
  };
  const [institueProfile, setInstitueProfile] = useState([]);
  const handleApiError = (error) => {
    const errorMessage =
      error.response?.data?.msg ||
      (error.request
        ? "No server response. Please check your network."
        : "Unexpected error occurred.");
    SwalMessageAlert(errorMessage, "error");
  };
  useEffect(() => {
    const getCollege = async () => {
      const formatDateForInput = (dateString) => {
        if (!dateString) return ''; // Return empty if no date
        const date = new Date(dateString);
        if (isNaN(date)) return ''; // Check if the date is valid
        return date.toISOString().split('T')[0];
      };
      try {
        const response = await axios
          .get(`${endPoint}/api/college/get-college/${id}`, {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          })
          .then(async (response) => {
            if (response.status === 200) {
              const data = response.data;
              let formattedDate;
              if (data.regDate) {
                formattedDate = new Date(data.regDate)
                  .toISOString()
                  .split("T")[0];
              } else {
                formattedDate = "";
              }
              setFormData({
                ...data,
                // ...formData,
                regDate: formattedDate,
                divison: data.divison || "",
                vidhansabha: data.vidhansabha || "",
                district: data.district || "",
                isLead: data.isLead,
                ConstituencyNumber: data.vidhansabha || "",
              });

              // console.log(data.principalType,"Value of Charge Type 3");

              try {
                const response = await axios.get(
                  `${endPoint}/api/district/getVidhansabha-district-wise/${data.district}`,
                  {
                    headers: {
                      "Content-Type": "application/json",
                      'web-url': window.location.href,
                      Authorization: `Bearer ${token}`,
                    },
                  }
                );
                if (response.status === 200) {
                  setVidhansabha(response.data);
                } else {
                  alert("Failed to fetch vidhansabha data.");
                }
              } catch (error) {
                console.error("Error fetching vidhansabha:", error);
                alert("An error occurred. Please try again later.");
              }
            }
          })
          .catch((err) => {
            handleApiError(err);
          });
      } catch (error) {
        handleApiError(err);
      }
    };
    getCollege(); // Call the function inside useEffect
  }, [id, endPoint, token]);

  const [currentClass, setCurrentClass] = useState('');

  useEffect(() => {
    const getDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-all-district`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDistrict(data);
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
        alert("Failed to load district data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`
          },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();

    getDistrict();
  }, [endPoint, token]); // Dependencies

  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    getDivision();
  }, [endPoint, token]); // Dependencies

  const handleDivisionChange = async (e) => {
    const { value } = e.target;
    setIsDivisionChanged(true); // Set to true when a new division is selected
    if (!value) {
      setDistrict([]); // District list ko empty kar do
      return;
    }
    setFormData((prevData) => ({
      ...prevData,
      division: value,
      district: "",
      vidhansabha: "",
      block: "",
    }));
    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // Check if the response is successful and contains valid data
      if (response.status === 200 && Array.isArray(response.data)) {
        setDistrict(response.data); // Set the fetched district data
      } else {
        alert("Failed to fetch districts. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching districts:", error);
      alert(
        "An error occurred while fetching districts. Please try again later."
      );
    }
  };

 

  const handleDistrictChange = async (e) => {
    const { value } = e.target;
    // Check if "Other" is selected for Home District

    setFormData({
      ...formData,
      district: value, // Correctly update the district in formData
    });

    try {
      const response = await axios.get(
        `${endPoint}/api/get-dist-wise-block/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // Check if the response is successful and contains valid data
      if (response.status === 200 && Array.isArray(response.data)) {
        setGetAllBlock(response.data); // Set the fetched Vidhansabha data
        // setVidhansabha(response.data);
      } else {
        alert("Failed to fetch Vidhansabha data. Please try again.");
      }
    } catch (error) {
      console.error("Error fetching Vidhansabha data:", error);
      alert(
        "An error occurred while fetching Vidhansabha data. Please try again later."
      );
    }
  };

  const handleVidhansabhaChange = (e) => {
    setFormData({
      ...formData,
      vidhansabha: e.target.value,
    });
  };


  const [category, setCategory] = useState([]);
  useEffect(() => {
    const getCategory = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-category-types`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setCategory(data);
        }
      } catch (error) {
        console.error("Error fetching Category data:", error);
        alert("Failed to load Category data.");
      }
    };

    getCategory(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies


  useEffect(() => {
    const fetchDistrict = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/get-division-district/${formData.divison}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setDistrict(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.divison) fetchDistrict();
  }, [formData.divison, endPoint, token]);

  useEffect(() => {
    const fetchVidhansabha = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/district/getVidhansabha-district-wise/${formData.district}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setVidhansabha(response.data);
        } else {
          alert("Failed to fetch district data.");
        }
      } catch (error) {
        console.error("Error fetching district data:", error);
      }
    };

    if (formData.vidhansabha) fetchVidhansabha();
  }, [formData.vidhansabha, endPoint, token]);

  const getClassName = (value) => {
    // console.log(value, classData, "adfjkahsd");
    const classObj = classData.find((classItem) => classItem._id === value);
    return classObj ? classObj.className : "Unknown Class Name";
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Helper function to format array values (for multiple select)
  const formatArray = (arr) => {
    if (!arr || arr.length === 0) return "N/A";
    return Array.isArray(arr) ? arr.join(", ") : arr;
  };

  const [designationData, setDesignationData] = useState([]);

  useEffect(() => {
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`
          },
        });
        if (response.status === 200) {
          setDesignationData(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDesignation();
  }, [endPoint, token]); // Dependencies


  const [showDesignation, setShowDesignation] = useState([]);

  useEffect(() => {
    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`
          },
        });
        if (response.status === 200) {
          setShowDesignation(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDesignation();
  }, [endPoint, token]); // Dependencies


  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      setLoading(true);
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [id, endPoint, token]);
  const removeHostel = (index) => {
    const updatedHostels = [...formData.boysHostels];
    updatedHostels.splice(index, 1);

    setFormData({
      ...formData,
      boysHostels: updatedHostels,
      boysHostelCount: (formData.boysHostelCount || 1) - 1  // ensure it doesn’t go below 0
    });
  };

  const removeGirlsHostel = (index) => {
    const updatedHostels = [...formData.girlsHostels];
    updatedHostels.splice(index, 1);

    setFormData({
      ...formData,
      girlsHostels: updatedHostels,
      girlsHostelCount: (formData.girlsHostelCount || 1) - 1
    });
  };
  // const [setupDetails, setSetupDetails] = useState([]);
  const getInstituteProfile = async () => {
    const formatDateForInput = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return isNaN(date) ? '' : date.toISOString().split('T')[0];
    };

    try {
      const response = await axios.get(
        `${endPoint}/api/get-institute-profile?clgId=${id}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const data = response.data;
        setInstitueProfile(data);
        // setSetupDetails(data?.setupDetails?.setupDetails || []);
        //   const boysHostels = (data?.infrastructureDetails?.boysHostels || []).map((hostel) => ({
        //     name: hostel.name ?? "",
        //     seats: hostel.seats ?? "",
        // }));
        const raw = data?.infrastructureDetails?.boysHostels ?? [];

        const boysHostels = (
          Array.isArray(raw) ? raw : [raw]
        ).flatMap(item => {
          if (typeof item === "string") {
            try {
              const parsed = JSON.parse(item);
              return Array.isArray(parsed) ? parsed : [parsed];
            } catch (e) {
              return [];
            }
          }
          return [item];
        });


        // const raw = data?.infrastructureDetails?.boysHostels ?? [];

        // const boysHostels = (Array.isArray(raw) ? raw : [raw]).map(item => {
        //   if (typeof item === "string") {
        //     try {
        //       const parsed = JSON.parse(item);
        //       return parsed; // either an object or array
        //     } catch (e) {
        //       return null; // invalid string
        //     }
        //   }
        //   return item; // already an object
        // }).flat().filter(Boolean); // flatten and remove null/undefined

        const girlsRaw = data?.infrastructureDetails?.girlsHostels ?? [];

        const girlsHostels = (Array.isArray(girlsRaw) ? girlsRaw : [girlsRaw]).map(item => {
          if (typeof item === "string") {
            try {
              const parsed = JSON.parse(item);
              return parsed; // either an object or array
            } catch (e) {
              return null; // invalid string
            }
          }
          return item; // already an object
        }).flat().filter(Boolean);

        console.log("boysHostels raw:", data?.infrastructureDetails?.boysHostels);
        console.log("Processed boysHostels:", boysHostels);

        setFormData((prevFormData) => ({
          ...prevFormData || {},

          // basicDetails
          govtEmail1: data?.basicDetails?.govtEmail1 ?? prevFormData.govtEmail1,
          govtEmail2: data?.basicDetails?.govtEmail2 ?? prevFormData.govtEmail2,
          telephone: data?.basicDetails?.telephone ?? prevFormData.telephone,

          // moreDetails

          // collegeUrl: data?.moreDetails?.collegeUrl ?? "",
          ddoCode: data?.moreDetails?.ddoCode ?? prevFormData.ddoCode,
          block: data?.moreDetails?.block ?? prevFormData.block,
          registrationNumber: data?.moreDetails?.registrationNumber ?? prevFormData.moreDetails,
          areaType: data?.moreDetails?.areaType ?? prevFormData.areaType,
          collegeType: data?.moreDetails?.collegeType ?? prevFormData.collegeType,
          degreeTypes: data?.moreDetails?.degreeTypes ?? prevFormData.degreeTypes,
          girlsOnly: data?.moreDetails?.girlsOnly ?? prevFormData.girlsOnly,
          isAutonomous: data?.moreDetails?.isAutonomous ?? prevFormData.isAutonomous ?? "false",
          isModelCollege: data?.moreDetails?.isModelCollege ?? prevFormData.isModelCollege ?? "false",
          englishMedium: data?.moreDetails?.englishMedium ?? prevFormData.englishMedium ?? "false",
          sangeetCollege: data?.moreDetails?.sangeetCollege ?? prevFormData.sangeetCollege ?? "false",
          demography: data?.moreDetails?.demography ?? prevFormData.demography,
          takenOverDate: data?.moreDetails?.takenOverDate ? formatDateForInput(data.moreDetails.takenOverDate) : prevFormData.takenOverDate,
          ugc2FDate: data?.moreDetails?.ugc2FDate ? formatDateForInput(data.moreDetails.ugc2FDate) : prevFormData.ugc2FDate,
          ugc12BDate: data?.moreDetails?.ugc12BDate ? formatDateForInput(data.moreDetails.ugc12BDate) : prevFormData.ugc12BDate,
          UGC2FRegistered: data?.moreDetails?.UGC2FRegistered ?? prevFormData.UGC2FRegistered,
          isRegistered12B: data?.moreDetails?.isRegistered12B ?? prevFormData.isRegistered12B,
          takenOver: data?.moreDetails?.takenOver ?? prevFormData.takenOver ?? "false",

          // naacDetails

          eligibleForNAAC: data?.naacDetails?.eligibleForNAAC ?? prevFormData.eligibleForNAAC ?? "false",
          naacEvaluated: data?.naacDetails?.naacEvaluated ?? prevFormData.naacEvaluated,
          naacYear: data?.naacDetails?.naacYear ?? prevFormData.naacYear,
          naacGrade: data?.naacDetails?.naacGrade ?? prevFormData.naacGrade,

          // janbhagidariDetails

          isJBRegistered: data?.janbhagidariDetails?.isJBRegistered ?? prevFormData.isJBRegistered ?? "false",
          jbRegistrationDate: data?.janbhagidariDetails?.jbRegistrationDate ? formatDateForInput(data.janbhagidariDetails.jbRegistrationDate) : prevFormData.jbRegistrationDate,
          jbPresidentName: data?.janbhagidariDetails?.jbPresidentName ?? prevFormData.jbPresidentName,
          jbFundYear: data?.janbhagidariDetails?.jbFundYear ?? prevFormData?.janbhagidariDetails?.jbFundYear,
          jbFundIncome: data?.janbhagidariDetails?.jbFundIncome ?? prevFormData?.janbhagidariDetails?.jbFundIncome,
          jbFundExpenditure: data?.janbhagidariDetails?.jbFundExpenditure ?? prevFormData?.janbhagidariDetails?.jbFundExpenditure,
          jbFundBalance: data?.janbhagidariDetails?.jbFundBalance ?? prevFormData?.janbhagidariDetails?.jbFundBalance,
          jbFundAuditCompleted: data?.janbhagidariDetails?.jbFundAuditCompleted ?? prevFormData?.janbhagidariDetails?.jbFundAuditCompleted ?? "false",
          jbFundRemark: data?.janbhagidariDetails?.jbFundRemark ?? prevFormData?.janbhagidariDetails?.jbFundRemark,

          // nssAndNccDetails
          //nss
          nssAvailable: data?.nssAndNccDetails?.nssAvailable ?? prevFormData.nssAvailable ?? "false",
          nssEstablishmentDate: formatDateForInput(data?.nssAndNccDetails?.nssEstablishmentDate),
          nssInChargeName: data?.nssAndNccDetails?.nssInChargeName ?? prevFormData.nssInChargeName,
          nssBoysCount: data?.nssAndNccDetails?.nssBoysCount ?? prevFormData.nssBoysCount,
          nssGirlsCount: data?.nssAndNccDetails?.nssGirlsCount ?? prevFormData.nssGirlsCount,
          nssTotalCount: data?.nssAndNccDetails?.nssTotalCount ?? prevFormData.nssTotalCount,
          //ncc
          nccAvailable: data?.nssAndNccDetails?.nccAvailable ?? prevFormData.nccAvailable ?? "false",
          nccEstablishmentDate: data?.nssAndNccDetails?.nccEstablishmentDate ? formatDateForInput(data.nssAndNccDetails.nccEstablishmentDate) : prevFormData.nccEstablishmentDate,
          nccInChargeName: data?.nssAndNccDetails?.nccInChargeName ?? prevFormData.nccInChargeName,
          nccBoysCount: data?.nssAndNccDetails?.nccBoysCount ?? prevFormData.nccBoysCount,
          nccGirlsCount: data?.nssAndNccDetails?.nccGirlsCount ?? prevFormData.nccGirlsCount,
          nccTotalCount: data?.nssAndNccDetails?.nccTotalCount ?? prevFormData.nccTotalCount,

          // infrastructureDetails: {
          isLandAlloted: data?.infrastructureDetails?.isLandAlloted ?? prevFormData.isLandAlloted ?? "false",
          landAreaSqFt: data?.infrastructureDetails?.landAreaSqFt ?? prevFormData.landAreaSqFt ?? 0,
          buildingConstructed: data?.infrastructureDetails?.buildingConstructed ?? prevFormData.buildingConstructed,
          teachingRooms: data?.infrastructureDetails?.teachingRooms ?? prevFormData.teachingRooms,
          labs: data?.infrastructureDetails?.labs ?? prevFormData.labs,
          girlsCommonRoom: data?.infrastructureDetails?.girlsCommonRoom ?? prevFormData.girlsCommonRoom,
          library: data?.infrastructureDetails?.library ?? prevFormData.library,
          digitalBookNo: data?.infrastructureDetails?.digitalBookNo ?? prevFormData.digitalBookNo,
          physicalBooksNo: data?.infrastructureDetails?.physicalBooksNo ?? prevFormData.physicalBooksNo,
          journalsNo: data?.infrastructureDetails?.journalsNo ?? prevFormData.journalsNo,
          readingRoom: data?.infrastructureDetails?.readingRoom ?? prevFormData.readingRoom,
          sportsGround: data?.infrastructureDetails?.sportsGround ?? prevFormData.sportsGround,
          auditorium: data?.infrastructureDetails?.auditorium ?? prevFormData.auditorium,
          smartClass: data?.infrastructureDetails?.smartClass ?? prevFormData.smartClass,
          numberOfSmartClass: data?.infrastructureDetails?.numberOfSmartClass ?? prevFormData.numberOfSmartClass,
          hostelAvailable: data?.infrastructureDetails?.hostelAvailable ?? prevFormData.hostelAvailable ?? "false",
          boysHostelCount: data?.infrastructureDetails?.boysHostelCount ?? prevFormData.boysHostelCount,
          girlsHostelCount: data?.infrastructureDetails?.girlsHostelCount ?? prevFormData.girlsHostelCount,
          // boysHostels: boysHostels.length > 0 ? boysHostels : (prevFormData.infrastructureDetails?.boysHostels ?? []),
          girlsHostels: girlsHostels.length > 0 ? girlsHostels : (prevFormData?.infrastructureDetails?.girlsHostels[0] ?? []),
          // boysHostels: boysHostels.length > 0 ? boysHostels : (prevFormData?.infrastructureDetails?.boysHostels[0] ?? []),

          boysHostels: data?.infrastructureDetails?.boysHostels ?? prevFormData.boysHostels ?? [],
          biometricAttendance: data?.infrastructureDetails?.biometricAttendance ?? prevFormData.biometricAttendance,
          wifi: data?.infrastructureDetails?.wifi ?? prevFormData.wifi,
          staffToilet: data?.infrastructureDetails?.staffToilet ?? prevFormData.staffToilet,
          boysToilet: data?.infrastructureDetails?.boysToilet ?? prevFormData.boysToilet,
          girlsToilet: data?.infrastructureDetails?.girlsToilet ?? prevFormData.girlsToilet,
          rampAvailable: data?.infrastructureDetails?.rampAvailable ?? prevFormData.rampAvailable,
          // nspRegistrationDetails
          nspRegistered: data?.nspRegistrationDetails?.nspRegistered ?? prevFormData.nspRegistered,
          nspInstituteId: data?.nspRegistrationDetails?.nspInstituteId ?? prevFormData.nspInstituteId,
          nodalOfficerName: data?.nspRegistrationDetails?.nodalOfficerName ?? prevFormData.nodalOfficerName,
          nodalOfficerMobile: data?.nspRegistrationDetails?.nodalOfficerMobile ?? prevFormData.nodalOfficerMobile,
          nodalOfficerBioAuth: data?.nspRegistrationDetails?.nodalOfficerBioAuth ?? prevFormData.nodalOfficerBioAuth,
          principalBioAuth: data?.nspRegistrationDetails?.principalBioAuth ?? prevFormData.principalBioAuth,
          // setupDetails
          // designationsList: data?.setupDetails?.setupDetails?.[0]?.designationsList ?? prevFormData.designationsList ?? [],
          // sanctionedPosts: data?.setupDetails?.setupDetails?.[0]?.sanctionedPosts ?? prevFormData.sanctionedPosts ?? [],
          // working: data?.setupDetails?.setupDetails?.[0]?.working ?? prevFormData.working ?? [],
          // class: data?.setupDetails?.setupDetails?.[0]?.class ?? prevFormData.class ?? [],
          // sanctionOrderNo: data?.setupDetails?.setupDetails?.[0]?.sanctionOrderNo ?? prevFormData.sanctionOrderNo ?? '',
          // sanctionDate: data?.setupDetails?.setupDetails?.[0]?.sanctionDate ?? prevFormData.sanctionDate ?? '',
          // isSanctionOrderFile: data?.setupDetails?.setupDetails?.[0]?.isSanctionOrderFile ?? prevFormData.isSanctionOrderFile ?? false,
          // uploadsSanctionOrderFile: data?.setupDetails?.setupDetails?.[0]?.uploadsSanctionOrderFile ?? prevFormData.uploadsSanctionOrderFile ?? [],
          // SanctionOrderFileName: (
          //   data?.setupDetails?.setupDetails?.[0]?.uploadsSanctionOrderFile?.name ??
          //   data?.setupDetails?.setupDetails?.[0]?.uploadsSanctionOrderFile?.split?.('/').pop() ??
          //   prevFormData.uploadsSanctionOrderFile
          // ),

          uploadClgSanctionLetter: data?.basicDetails?.uploadClgSanctionLetter ?? prevFormData.uploadClgSanctionLetter,
          uploadEstablishFile: data?.basicDetails?.uploadEstablishFile ?? prevFormData.uploadEstablishFile,
          uploadTakenOverFile: data?.moreDetails?.uploadTakenOverFile ?? prevFormData.uploadTakenOverFile,
          upload2FUgcRegisteredFile: data?.moreDetails?.upload2FUgcRegisteredFile ?? prevFormData.upload2FUgcRegisteredFile,
          upload12BRegisteredFile: data?.moreDetails?.upload12BRegisteredFile ?? prevFormData.upload12BRegisteredFile,
          uploadGradeCycleFile: data?.naacDetails?.uploadGradeCycleFile ?? prevFormData.uploadGradeCycleFile,
          uploadJBSRegisterFile: data?.janbhagidariDetails?.uploadJBSRegisterFile ?? prevFormData.uploadJBSRegisterFile,
          uploadCaAuditFile: data?.janbhagidariDetails?.uploadCaAuditFile ?? prevFormData.uploadCaAuditFile,
          uploadsNssEstablishFile: data?.nssAndNccDetails?.uploadsNssEstablishFile ?? prevFormData.uploadsNssEstablishFile ?? [],
          uploadsNccEstablishFile: data?.nssAndNccDetails?.uploadsNccEstablishFile ?? prevFormData.uploadsNccEstablishFile ?? [],
          uploadsLandAreaFile: data?.infrastructureDetails?.uploadsLandAreaFile ?? prevFormData.uploadsLandAreaFile,

          // clgSanctionFileName: (
          //   data?.basicDetails?.uploadClgSanctionLetter?.name ??
          //   data?.basicDetails?.uploadClgSanctionLetter?.split?.('/').pop() ??
          //   prevFormData.clgSanctionFileName
          // ),

          // establishFileName: data?.basicDetails?.uploadEstablishFile ?? prevFormData.uploadEstablishFile,
          // takenOverFileName: data?.moreDetails?.uploadTakenOverFile ?? prevFormData.uploadTakenOverFile,
          // Ugc2FRegisterFileName: data?.moreDetails?.upload2FUgcRegisteredFile ?? prevFormData.upload2FUgcRegisteredFile,
          // Registered12BFileName: data?.moreDetails?.upload12BRegisteredFile ?? prevFormData.upload12BRegisteredFile,
          // GradeCycleFileName: data?.naacDetails?.uploadGradeCycleFile ?? prevFormData.uploadGradeCycleFile,
          // jBSRegisterFileName: data?.janbhagidariDetails?.uploadJBSRegisterFile ?? prevFormData.uploadJBSRegisterFile,
          // CaAuditFileName: data?.janbhagidariDetails?.uploadCaAuditFile ?? prevFormData.uploadCaAuditFile,
          // NssEstablishFileName: Array.isArray(data?.nssAndNccDetails?.uploadsNssEstablishFile)
          //   ? data.nssAndNccDetails.uploadsNssEstablishFile.map(file => file.name)
          //   : prevFormData.uploadsNssEstablishFile ?? [],
          // NccEstablishFileName: Array.isArray(data?.nssAndNccDetails?.uploadsNccEstablishFile)
          //   ? data?.nssAndNccDetails.uploadsNccEstablishFile.map(file => file.name)
          //   : prevFormData.uploadsNccEstablishFile ?? [],
          // LandAreaFileName: data?.infrastructureDetails?.uploadsLandAreaFile ?? prevFormData.uploadsLandAreaFile,

          //added new
          isClgFile: data?.basicDetails?.isClgFile ?? prevFormData.isClgFile ?? "false",
          isEstablishFile: data?.basicDetails?.isEstablishFile ?? prevFormData.isEstablishFile ?? "false",
          isTakenOverFile: data?.moreDetails?.isTakenOverFile ?? prevFormData.isTakenOverFile ?? "false",
          is2FUgcFile: data?.moreDetails?.is2FUgcFile ?? prevFormData.is2FUgcFile ?? "false",
          is12BFile: data?.moreDetails?.is12BFile ?? prevFormData.is12BFile ?? "false",
          isLandAreaFile: data?.infrastructureDetails?.isLandAreaFile ?? prevFormData.isLandAreaFile ?? "false",
          isCaAuditFile: data?.janbhagidariDetails?.isCaAuditFile ?? prevFormData.isCaAuditFile ?? "false",
          isjBSRegisterFile: data?.janbhagidariDetails?.isjBSRegisterFile ?? prevFormData.isjBSRegisterFile ?? "false",
          isNssEstablishFile: data?.nssAndNccDetails?.isNssEstablishFile ?? prevFormData.isNssEstablishFile ?? "false",
          isNccEstablishFile: data?.nssAndNccDetails?.isNccEstablishFile ?? prevFormData.isNccEstablishFile ?? "false",
          // isSanctionOrderFile: data?.setupDetails?.isSanctionOrderFile ?? prevFormData.isSanctionOrderFile,
          isGradeCycleFile: data?.naacDetails?.isGradeCycleFile ?? prevFormData.isGradeCycleFile ?? "false",


        }));
      }
    } catch (error) {
      console.error("Error fetching Institute data:", error);
      alert("Failed to load institute profile data.");
    }
  };

  useEffect(() => {
    getInstituteProfile();

  }, [user]);

  const setupDetails = institueProfile?.setupDetails?.setupDetails || [];
  useEffect(() => {
    if (setupDetails.length > 0) {
      setTableData(setupDetails);
    }
  }, [institueProfile]);


  const handleSubmit = async (e, sectionName) => {
    e.preventDefault();
    try {

      const section = sectionName;

      const sections = {
        basicDetails: [
          "name", "university", "aisheCode", "contactPerson", "contactNumber",
          "collegeEmail", "govtEmail1", "govtEmail2", "telephone", "divison",
          "establishYear", "district", "vidhansabha", "lat", "long", "isLead",
          "ConstituencyNumber", "address", "isClgFile", "isEstablishFile",
        ],

        moreDetails: [
          "collegeUrl", "ddoCode", "block", "registrationNumber",
          "areaType", "collegeType", "degreeTypes", "girlsOnly", "isAutonomous",
          "isModelCollege", "englishMedium", "demography", "takenOverDate", "UGC2FRegistered",
          "ugc2FDate", "ugc12BDate", "takenOver", "isRegistered12B", "sangeetCollege", "isTakenOverFile",
          "is2FUgcFile", "is12BFile",
        ],

        naacDetails: [
          "eligibleForNAAC", "naacEvaluated", "naacYear", "naacGrade", "isGradeCycleFile",
        ],

        janbhagidariDetails: [
          "isJBRegistered", "jbRegistrationDate", "jbPresidentName",
          "jbFundYear", "jbFundIncome", "jbFundExpenditure", "jbFundBalance",
          "jbFundAuditCompleted", "jbFundRemark", "isjBSRegisterFile", "isCaAuditFile",
        ],

        nssAndNccDetails: [
          "nssAvailable", "nssEstablishmentDate", "nssInChargeName",
          "nssBoysCount", "nssGirlsCount", "nssTotalCount",
          "nccAvailable", "nccEstablishmentDate", "nccInChargeName",
          "nccBoysCount", "nccGirlsCount", "nccTotalCount", "isNssEstablishFile",
          "isNccEstablishFile",

        ],

        infrastructureDetails: [
          "isLandAlloted", "landAreaSqFt", "buildingConstructed", "teachingRooms", "labs",
          "girlsCommonRoom", "library", "digitalBookNo", "physicalBooksNo", "journalsNo",
          "readingRoom", "sportsGround", "auditorium", "smartClass",
          "numberOfSmartClass", "hostelAvailable", "boysHostelCount",
          "girlsHostelCount", "boyshoste",
          "biometricAttendance", "wifi", "staffToilet", "boysToilet",
          "girlsToilet", "rampAvailable", "isLandAreaFile",
        ],

        nspRegistrationDetails: [
          "nspRegistered", "nspInstituteId", "nodalOfficerName",
          "nodalOfficerMobile", "nodalOfficerBioAuth", "principalBioAuth"
        ],

        setupDetails: [

        ],

      };

      const fieldsToUpdate = sections[section];

      const formDataSubmit = new FormData();
      Object.keys(formData).forEach((key) => {
        if (fieldsToUpdate.includes(key) && formData[key] !== null && formData[key] !== undefined) {
          formDataSubmit.append(key, formData[key]);
        }
      });
      // formDataSubmit.append('conductedCoursesPG', selectedPGCourse.map(a => a.value).join(','));
      // const userType:selectedUser.map((a) => a.value)
      // Ensure these are arrays
      if (section === "infrastructureDetails") {
        const boysHostels = formData.boysHostels ?? [];
        const girlsHostels = formData.girlsHostels ?? [];

        // Append each boys hostel
        boysHostels.forEach((hostel, index) => {
          formDataSubmit.append(`boysHostels[${index}][name]`, hostel.name || '');
          formDataSubmit.append(`boysHostels[${index}][seats]`, hostel.seats || '');
          formDataSubmit.append(`boysHostels[${index}][hostalCategory]`, hostel.hostalCategory || '');
        });

        // Append each girls hostel
        girlsHostels.forEach((hostel, index) => {
          formDataSubmit.append(`girlsHostels[${index}][name]`, hostel.name || '');
          formDataSubmit.append(`girlsHostels[${index}][seats]`, hostel.seats || '');
          formDataSubmit.append(`girlsHostels[${index}][hostalCategory]`, hostel.hostalCategory || '');

        });
      }
      //  const file =[];
      

      if (section === "setupDetails") {
        const setupDetails = tableData.map((row) => ({
  ...row,
  class: row.class || '',
  designationsList: row.designationsList || '',
  sanctionedPosts: row.sanctionedPosts || '',
  working: row.working || '',
  sanctionOrderNo: row.sanctionOrderNo || '',
  sanctionDate: row.sanctionDate || '',
  isSanctionOrderFile: row.isSanctionOrderFile || "false",
  uploadsSanctionOrderFile: row.uploadsSanctionOrderFile || null,
  SanctionOrderFileName: row.SanctionOrderFileName || '',
}));
      //   const setupDetails = tableData.map((row) => ({
      //      ...row,
      //     class: row.class || '', // Default to empty string if undefined
      //     designationsList: row.designationsList || '',
      //     sanctionedPosts: row.sanctionedPosts || '',
      //     working: row.working || '',
      //     sanctionOrderNo: row.sanctionOrderNo || '',
      //     sanctionDate: row.sanctionDate || '',
      //     SanctionOrderFileName: row.SanctionOrderFileName || '',
      //     isSanctionOrderFile: row.isSanctionOrderFile || "false",
      //     uploadsSanctionOrderFile: row.uploadsSanctionOrderFile, // Assuming this is a File object
      //   }));

        setupDetails.forEach((data, index) => {
          formDataSubmit.append(`setupDetails[${index}][class]`, data.class || '');
          formDataSubmit.append(`setupDetails[${index}][designationsList]`, data.designationsList || '');
          formDataSubmit.append(`setupDetails[${index}][sanctionedPosts]`, data.sanctionedPosts || '');
          formDataSubmit.append(`setupDetails[${index}][working]`, data.working || '');
          formDataSubmit.append(`setupDetails[${index}][sanctionOrderNo]`, data.sanctionOrderNo || '');
          formDataSubmit.append(`setupDetails[${index}][sanctionDate]`, data.sanctionDate || '');
          formDataSubmit.append(`setupDetails[${index}][isSanctionOrderFile]`, String(data.isSanctionOrderFile === "true" ? "true" : "false"));
          formDataSubmit.append(`setupDetails[${index}][uploadsSanctionOrderFile]`, data.uploadsSanctionOrderFile || []);
          formDataSubmit.append(`setupDetails[${index}][SanctionOrderFileName]`, data.SanctionOrderFileName || '');
        });
      }


      formDataSubmit.append("infoType", section);
      formDataSubmit.append("collegeId", id);
      if (section === "basicDetails") {
        if (formData.uploadClgSanctionLetter instanceof File)
          formDataSubmit.append("uploadClgSanctionLetter", formData.uploadClgSanctionLetter);

        if (formData.uploadEstablishFile instanceof File)
          formDataSubmit.append("uploadEstablishFile", formData.uploadEstablishFile);
      }

      if (section === "moreDetails") {

        if (formData.uploadTakenOverFile instanceof File)
          formDataSubmit.append("uploadTakenOverFile", formData.uploadTakenOverFile);

        if (formData.upload2FUgcRegisteredFile instanceof File)
          formDataSubmit.append("upload2FUgcRegisteredFile", formData.upload2FUgcRegisteredFile);

        if (formData.upload12BRegisteredFile instanceof File)
          formDataSubmit.append("upload12BRegisteredFile", formData.upload12BRegisteredFile);
      }
      if (section === "naacDetails") {
        if (formData.uploadGradeCycleFile instanceof File)
          formDataSubmit.append("uploadGradeCycleFile", formData.uploadGradeCycleFile);
      }
      if (section === "janbhagidariDetails") {

        if (formData.uploadJBSRegisterFile instanceof File)
          formDataSubmit.append("uploadJBSRegisterFile", formData.uploadJBSRegisterFile);

        if (formData.uploadCaAuditFile instanceof File)
          formDataSubmit.append("uploadCaAuditFile", formData.uploadCaAuditFile);
      }
      if (section === "infrastructureDetails") {
        if (formData.uploadsLandAreaFile instanceof File)
          formDataSubmit.append("uploadsLandAreaFile", formData.uploadsLandAreaFile);
      }
      if (section === "nssAndNccDetails") {

        // For multi-file fields
        if (Array.isArray(formData.uploadsNssEstablishFile)) {
          // Append each NCC file to formDataSubmit
          formData.uploadsNssEstablishFile.forEach((item) => {
            formDataSubmit.append("uploadsNssEstablishFile", item.file);
          });
        }
        if (Array.isArray(formData.uploadsNccEstablishFile)) {
          // Append each NCC file to formDataSubmit
          formData.uploadsNccEstablishFile.forEach((item) => {
            formDataSubmit.append("uploadsNccEstablishFile", item.file);
          });
        }
      }
      // if (Array.isArray(formData.uploadsNccEstablishFile)) {
      //   formData.uploadsNccEstablishFile.forEach(file => {
      //     if (file instanceof File) {
      //       formDataSubmit.append("uploadsNccEstablishFile", file);

      //     }
      //   });
      // }



      // console.log("getting Section Name hitting in bottom", section);
      const response = await axios.post(
        `${endPoint}/api/instiute-profile/add`,
        formDataSubmit,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            // "Content-Type": "multipart/form-data",
            'web-url': window.location.href,
            "Content-Type": "multipart/form-data",
          },
        }
      );

      // Handle Response
      if (response.status === 200) {
        SwalMessageAlert("Data Saving", "success");
        // console.log("getting Section Name hitting in bottom with 200", section);

      } else {
        SwalMessageAlert(
          "Unexpected response from server. Please try again.",
          "error"
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      SwalMessageAlert(
        "Failed to submit. Please check the console for details.",
        "error"
      );
    }
  };

  const getErrorField = (fieldIndex) => {
    switch (fieldIndex) {
      case 1:
        return "uploadClgSanctionLetter";
      case 2:
        return "uploadsNccEstablishFile";
      case 3:
        return "uploadTakenOverFile";
      case 4:
        return "upload2FUgcRegisteredFile";
      case 5:
        return "upload12BRegisteredFile";
      case 6:
        return "uploadGradeCycleFile";
      case 7:
        return "uploadJBSRegisterFile";
      case 8:
        return "uploadCaAuditFile";
      case 9:
        return "uploadsNssEstablishFile";
      case 10:
        return "uploadsNccEstablishFile";
      case 11:
        return "uploadsLandAreaFile";
      case 12:
        return "uploadsSanctionOrderFile";
      default:
        return "";
    }
  };

  // Basic Details Part I
  const [uploadClgSanctionLetter, setUploadClgSanctionLetter] = useState([]);

  // Basic Details Part II
  const [uploadEstablishFile, setUploadEstablishFile] = useState([]);
  const [uploadTakenOverFile, setUploadTakenOverFile] = useState([]);
  const [upload2FUgcRegisteredFile, setUpload2FUgcRegisteredFile] = useState([]);
  const [upload12BRegisteredFile, setUpload12BRegisteredFile] = useState([]);

  // NAAC Details
  const [uploadGradeCycleFile, setUploadGradeCycleFile] = useState([]);

  // Janbhagidari Details
  const [uploadJBSRegisterFile, setUploadJBSRegisterFile] = useState([]);
  const [uploadCaAuditFile, setUploadCaAuditFile] = useState([]);

  // NSS & NCC Details
  const [uploadsNssEstablishFile, setUploadsNssEstablishFile] = useState([]);
  const [uploadsNccEstablishFile, setUploadsNccEstablishFile] = useState([]);

  // Infrastructure Details
  const [uploadsLandAreaFile, setUploadsLandAreaFile] = useState([]);

  // Setup Details
  const [uploadsSanctionOrderFile, setUploadsSanctionOrderFile] = useState([]);

  // console.log(currentClass,"Getting Fsdkfjsldkfjalksjdelfj");
  const handleImageChange = (e, fieldIndex) => {
    // console.log("Hitting here!");

    if (!e.target.files || e.target.files.length === 0) {
      // console.log("No file selected");
      return;
    }

    const file = e.target.files[0];


    // if (!currentDate) {
    //   alert("Please select Establishment Date before uploading the file.");
    //   return;
    // }
    const maxSize = 250 * 1024;
    if (file) {
      //  Check file type (Only PDF allowed)
      if (file.type !== "application/pdf") {
        e.target.value = ""; // Clear input field
        setErrors({
          ...errors,
          [getErrorField(fieldIndex)]: "Only PDF files are allowed!",
        });
        return;
      }

      //  Check file size (Max 250KB)
      if (file.size > maxSize) {
        e.target.value = ""; // Clear input field
        setErrors({
          ...errors,
          [getErrorField(fieldIndex)]: "File size should not exceed 250KB!",
        });
        return;
      }

      //  If valid, clear error
      setErrors({
        ...errors,
        [getErrorField(fieldIndex)]: "",
      });
    }
    const reader = new FileReader();
    reader.onload = () => {
      const imageDataURL = reader.result;
      switch (fieldIndex) {

        case 1:
          setFormData((prevState) => ({
            ...prevState,
            uploadClgSanctionLetter: file,
            // clgSanctionFileName: file.name,
          }));
          setUploadClgSanctionLetter(imageDataURL)

          break;
        case 2:
          setFormData((prevState) => ({
            ...prevState,
            uploadEstablishFile: file,
            // establishFileName: file.name,
          }));
          setUploadEstablishFile(imageDataURL);
          break;

        case 3:
          setFormData((prevState) => ({
            ...prevState,
            uploadTakenOverFile: file,
            // takenOverFileName: file.name,
          }));
          setUploadTakenOverFile(imageDataURL);
          break;

        case 4:
          setFormData((prevState) => ({
            ...prevState,
            upload2FUgcRegisteredFile: file,
            // Ugc2FRegisterFileName: file.name,
          }));
          setUpload2FUgcRegisteredFile(imageDataURL);
          break;

        case 5:
          setFormData((prevState) => ({
            ...prevState,
            upload12BRegisteredFile: file,
            // Registered12BFileName: file.name,
          }));
          setUpload12BRegisteredFile(imageDataURL);
          break;

        case 6:
          setFormData((prevState) => ({
            ...prevState,
            uploadGradeCycleFile: file,
            // GradeCycleFileName: file.name,
          }));
          setUploadGradeCycleFile(imageDataURL);
          break;

        case 7:
          setFormData((prevState) => ({
            ...prevState,
            uploadJBSRegisterFile: file,
            // jBSRegisterFileName: file.name,
          }));
          setUploadJBSRegisterFile(imageDataURL);
          break;

        case 8:
          setFormData((prevState) => ({
            ...prevState,
            uploadCaAuditFile: file,
            // CaAuditFileName: file.name,
          }));
          setUploadCaAuditFile(imageDataURL);
          break;

        case 9:
          setFormData((prevState) => ({
            ...prevState,
            //  uploadEstablishFile: file?.name || "", // for displaying filename
            uploadsNssEstablishFile: [
              ...(prevState.uploadsNssEstablishFile || []),
              { file, name: file.name }
            ]

            //             uploadsNssEstablishFile: [
            //   ...(prevState.uploadsNssEstablishFile || []),
            //   {
            //     date: currentDate,
            //     file: file,
            //     name: file.name
            //   }
            // ]

            // uploadsNssEstablishFile: file,
            // NssEstablishFileName: file.name,
          }));
          setUploadsNssEstablishFile(imageDataURL);
          break;

        case 10:
          setFormData((prevState) => ({
            ...prevState,
            uploadsNccEstablishFile: [
              ...(prevState.uploadsNccEstablishFile || []),
              { file, name: file.name }
            ]
            // uploadsNccEstablishFile: file,
            // NccEstablishFileName: file.name,
          }));
          setUploadsNccEstablishFile(imageDataURL);
          break;

        case 11:
          setFormData((prevState) => ({
            ...prevState,
            uploadsLandAreaFile: file,
            // LandAreaFileName: file.name,
          }));
          setUploadsLandAreaFile(imageDataURL);
          break;

        case 12:
          setFormData((prevState) => ({
            ...prevState,
            uploadsSanctionOrderFile: file,
            SanctionOrderFileName: file.name,
          }));
          setUploadsSanctionOrderFile(imageDataURL);
          break;
        default:
          break;
      }
    };
    reader.readAsDataURL(file);
    handleFileNameStore(file, fieldIndex);
  };

  const handleFileNameStore = (e, fieldIndex) => {
    // Check if a file is selected
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];


      // Update the formData based on fieldIndex
      setFormData((prevState) => {
        switch (fieldIndex) {

          case 1:
            return { ...prevState, uploadClgSanctionLetter: file.name };
          case 2:
            return { ...prevState, uploadEstablishFile: file.name };
          case 3:
            return { ...prevState, uploadTakenOverFile: file.name };
          case 4:
            return { ...prevState, upload2FUgcRegisteredFile: file.name };
          case 5:
            return { ...prevState, upload12BRegisteredFile: file.name };
          case 6:
            return { ...prevState, uploadGradeCycleFile: file.name };
          case 7:
            return { ...prevState, uploadJBSRegisterFile: file.name };
          case 8:
            return { ...prevState, uploadCaAuditFile: file.name };
          case 9:
            return { ...prevState, uploadsNssEstablishFile: file.name };
          case 10:
            return { ...prevState, uploadsNccEstablishFile: file.name };
          case 11:
            return { ...prevState, uploadsLandAreaFile: file.name };
          case 12:
            return { ...prevState, uploadsSanctionOrderFile: file.name };
          default:
            return prevState;
        }
      });
    }
  };


  const [proilePercent, setProfilePercent] = useState(0);

  useEffect(() => {
    const calculateProfilePercent = () => {
      if (institueProfile && institueProfile.SubmitStatus) {
        const trueCount = Object.values(institueProfile.SubmitStatus).filter(status => status === true).length;
        const percentage = (trueCount / 8) * 100;

        // Round to one decimal place
        setProfilePercent(parseFloat(percentage.toFixed(1)));
      }
    };

    calculateProfilePercent();
  }, [institueProfile]);

  const handleChangeEditable = async (e, editType) => {


    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह EDIT करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, EDIT",
        cancelButtonText: "No, cancel",
      });
      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/update-college-edit?clgId=${id}`,
          { "editType": editType },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert(response.data.msg, "success");
          getInstituteProfile();
        }
      } else {
        SwalMessageAlert("Cancelled", "error");
      }
    }
    catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  }


  const handleFinalSubmit = async (e, submitType) => {

    console.log(submitType, "Getting Submit Type");

    e.preventDefault();
    const validateForm = (submitType) => {
      const newErrors = {};
      const errorFields = []; // Array to hold names of fields with errors
      const errorSections = {}; // Object to hold sections and their corresponding fields with errors

      // Define required fields for each submit type
      // Define required fields for each submit type


      SwalMessageAlert("Setup Details Not Filled", "error");

      const requiredFieldsByType = {
        basicDetails: {
          isClgFile: "false",//1 file
          isEstablishFile: "false",

        },
        moreDetails: {

          ddoCode: "",
          block: "",
          registrationNumber: "",
          areaType: "", // general/SC area/Tribal Area
          girlsOnly: "", // Yes/No
          isAutonomous: "false", // Yes/No
          isModelCollege: "false", // Yes/No
          sangeetCollege: "false",
          englishMedium: "false", // Yes/No
          demography: "", // Rural/Urban
          takenOver: "false",// Yes/No
          UGC2FRegistered: "false",// Yes/No
          isRegistered12B: "false",

        },
        naacDetails: {
          eligibleForNAAC: "false", // Y/N

        },

        janbhagidariDetails: {
          isJBRegistered: "false", // Y/N

        },

        nssAndNccDetails: {
          nssAvailable: "false",
          nccAvailable: "false",

        },

        infrastructureDetails: {
          isLandAlloted: "",
          teachingRooms: "",
          labs: "",
          girlsCommonRoom: "false", // Y/N
          library: "false", // Y/N
          // digitalBookNo: "", // Conventional/Digital/Both
          // physicalBooksNo: "",
          // journalsNo: "",
          readingRoom: "false", // Y/N
          sportsGround: "false", // Y/N
          auditorium: "false", // Y/N
          smartClass: "false", // Y/N
          hostelAvailable: "false", // Y/N
          biometricAttendance: "false", // Y/N
          wifi: "", // Y/N/Partial
          staffToilet: "", // Y/N
          boysToilet: "", // Y/N
          girlsToilet: "", // Y/N
          rampAvailable: "", // Y/N
        },

        nspRegistrationDetails: {
          nspRegistered: "", // Y/N

        },

      };

      const addConditionalFields = (formData) => {

        if (formData.isJBRegistered === "true") {
          requiredFieldsByType.janbhagidariDetails.jbRegistrationDate = "";
          requiredFieldsByType.janbhagidariDetails.jbPresidentName = "";
          requiredFieldsByType.janbhagidariDetails.jbFundYear = "";
          requiredFieldsByType.janbhagidariDetails.jbFundIncome = "";
          requiredFieldsByType.janbhagidariDetails.jbFundExpenditure = "";
          requiredFieldsByType.janbhagidariDetails.jbFundBalance = "";
          requiredFieldsByType.janbhagidariDetails.jbFundAuditCompleted = "";
          // requiredFieldsByType.jbFundRemark = "";
        }

        if (formData.nssAvailable === "true") {
          requiredFieldsByType.nssAndNccDetails.nssEstablishmentDate = "";
          requiredFieldsByType.nssAndNccDetails.nssInChargeName = "";
          requiredFieldsByType.nssAndNccDetails.nssBoysCount = "";
          requiredFieldsByType.nssAndNccDetails.nssGirlsCount = "";
          requiredFieldsByType.nssAndNccDetails.nssTotalCount = "";
          requiredFieldsByType.nssAndNccDetails.isNssEstablishFile = "";

        }
        if (formData.eligibleForNAAC === "true") {
          requiredFieldsByType.naacDetails.naacEvaluated = "";
        }
        if (formData.naacEvaluated === "true") {
          requiredFieldsByType.naacDetails.naacGrade = "";
          requiredFieldsByType.naacDetails.isGradeCycleFile = "";
          requiredFieldsByType.naacDetails.naacYear = "";

        }
        if (formData.nccAvailable === "true") {
          requiredFieldsByType.nssAndNccDetails.nccEstablishmentDate = "";
          requiredFieldsByType.nssAndNccDetails.nccInChargeName = "";
          requiredFieldsByType.nssAndNccDetails.nccBoysCount = "";
          requiredFieldsByType.nssAndNccDetails.nccGirlsCount = "";
          requiredFieldsByType.nssAndNccDetails.nccTotalCount = "";
          requiredFieldsByType.nssAndNccDetails.isNccEstablishFile = "";

        }
        if (formData.takenOver === "true") {
          requiredFieldsByType.moreDetails.takenOverDate = "";
          requiredFieldsByType.moreDetails.isTakenOverFile = "";

        }
        if (formData.isClgFile === "true") {
          requiredFieldsByType.basicDetails.uploadClgSanctionLetter = "";

        }
        if (formData.isEstablishFile === "true") {
          requiredFieldsByType.basicDetails.uploadEstablishFile = "";

        }
        if (formData.isNssEstablishFile === "true") {
          requiredFieldsByType.nssAndNccDetails.uploadsNssEstablishFile = "";

        }
        if (formData.isNccEstablishFile === "true") {
          requiredFieldsByType.nssAndNccDetails.uploadsNccEstablishFile = "";

        }
        if (formData.isTakenOverFile === "true") {
          requiredFieldsByType.moreDetails.uploadTakenOverFile = "";

        }
        if (formData.isCaAuditFile === "true") {
          requiredFieldsByType.janbhagidariDetails.uploadCaAuditFile = "";

        }
        // if(formData.jbFundAuditCompleted === "true"){
        //   requiredFieldsByType.janbhagidariDetails.isCaAuditFile = "";

        // }
        if (formData.isGradeCycleFile === "true") {
          requiredFieldsByType.naacDetails.uploadGradeCycleFile = "";

        }

        if (formData.isLandAreaFile === "true") {
          requiredFieldsByType.infrastructureDetails.uploadsLandAreaFile = "";

        }
        if (formData.isjBSRegisterFile === "true") {
          requiredFieldsByType.janbhagidariDetails.uploadJBSRegisterFile = "";

        }
        if (formData.UGC2FRegistered === "true") {
          requiredFieldsByType.moreDetails.ugc2FDate = "";
          requiredFieldsByType.moreDetails.is2FUgcFile = "";
        }

        if (formData.isRegistered12B === "true") {
          requiredFieldsByType.moreDetails.ugc12BDate = "";
          requiredFieldsByType.moreDetails.is12BFile = "";

        }
        if (formData.is12BFile === "true") {
          requiredFieldsByType.moreDetails.upload12BRegisteredFile = "";
        }
        if (formData.is2FUgcFile === "true") {
          requiredFieldsByType.moreDetails.upload2FUgcRegisteredFile = "";
        }
        if (formData.hostelAvailable === "true") {
          requiredFieldsByType.infrastructureDetails.boysHostelCount = "";
          requiredFieldsByType.infrastructureDetails.girlsHostelCount = "";

        }
        if (formData.isLandAlloted === "true") {
          requiredFieldsByType.infrastructureDetails.landAreaSqFt = "";
          requiredFieldsByType.infrastructureDetails.buildingConstructed = "";
          requiredFieldsByType.infrastructureDetails.isLandAreaFile = "";

        }
        if (formData.smartClass === "true") {
          requiredFieldsByType.infrastructureDetails.numberOfSmartClass = "";
        }
        if (formData.nspRegistered === "true") {
          requiredFieldsByType.nspRegistrationDetails.nspInstituteId = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerName = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerMobile = "";
          requiredFieldsByType.nspRegistrationDetails.nodalOfficerBioAuth = "";
          requiredFieldsByType.nspRegistrationDetails.principalBioAuth = "";
        }
        // if( setupDetails.length === 0){
        //   requiredFieldsByType.setupDetails.designationsList = "",
        //     requiredFieldsByType.setupDetails.sanctionedPosts = "",
        //     requiredFieldsByType.setupDetails.working = "",
        //     requiredFieldsByType.setupDetails.class = "",
        //     requiredFieldsByType.setupDetails.sanctionOrderNo = "",
        //     requiredFieldsByType.setupDetails.sanctionDate = "",
        //     requiredFieldsByType.setupDetails.isSanctionOrderFile = ""
        //   if (formData.isSanctionOrderFile === "true") {
        //     requiredFieldsByType.setupDetails.uploadsSanctionOrderFile = "";
        //   }
        // }
      };
      const fieldDisplayNames = {
        girlsOnly: "Girls / Co-Ed",
        isAutonomous: "Is Autonomous",
        isModelCollege: "Is Model College",
        registrationNumber: "Registration Number",
        englishMedium: "English Medium",
        sangeetCollege: "Sangeet College",
        areaType: "Area Type",
        demography: "Demography",
        takenOver: "Taken Over",
        takenOverDate: "Taken Over Date",
        UGC2FRegistered: "2 F Registered unerUGC",
        ugc2FDate: "UGC 2F Date",
        isRegistered12B: "12 B Registered ",
        ugc12BDate: "UGC 12B Date",
        eligibleForNAAC: "Eligible for NAAC",
        naacEvaluated: "NAAC Evaluated",
        naacYear: "NAAC Year",
        naacGrade: "NAAC Grade",
        isJBRegistered: "JB Registered",
        jbRegistrationDate: "JB Registration Date",
        jbPresidentName: "JB President Name",
        jbFundYear: "JB Fund Year",
        jbFundIncome: "JB Income",
        jbFundExpenditure: "JB Expenditure",
        jbFundBalance: "JB Balance",
        jbFundAuditCompleted: "JB Audit Completed",
        nssAvailable: "NSS Available",
        nssEstablishmentDate: "NSS Establishment Date",
        nssInChargeName: "NSS In-Charge Name",
        nssBoysCount: "NSS Boys Count",
        nssGirlsCount: "NSS Girls Count",
        // nssTotalCount: "NSS Total Count",
        nccAvailable: "NCC Available",
        nccEstablishmentDate: "NCC Establishment Date",
        nccInChargeName: "NCC In-Charge Name",
        nccBoysCount: "NCC Boys Count",
        nccGirlsCount: "NCC Girls Count",
        // nccTotalCount: "NCC Total Count",
        isLandAlloted: "Land Alloted",
        landAreaSqFt: "Land Area (Sq. Ft)",
        buildingConstructed: "Building Constructed",
        teachingRooms: "Teaching Rooms",
        labs: "Labs",
        girlsCommonRoom: "Girls Common Room",
        library: "Library",
        digitalBookNo: "Book Type",
        physicalBooksNo: "Books Available",
        journalsNo: "journalsNo Facilities",
        readingRoom: "Reading Room",
        sportsGround: "Sports Ground",
        auditorium: "Auditorium",
        smartClass: "Smart Class",
        numberOfSmartClass: "Number of Smart Classes",
        hostelAvailable: "Hostel Available",
        boysHostelCount: "Boys Hostel Count",
        girlsHostelCount: "Girls Hostel Count",
        // seatAvailablity: "Seat Availability",
        // hostalName: "Hostel Name",
        biometricAttendance: "Biometric Attendance",
        wifi: "WiFi",
        staffToilet: "Staff Toilet",
        boysToilet: "Boys Toilet",
        girlsToilet: "Girls Toilet",
        rampAvailable: "Ramp Available",
        nspRegistered: "NSP Registered",
        nspInstituteId: "NSP Institute ID",
        nodalOfficerName: "Nodal Officer Name",
        nodalOfficerMobile: "Nodal Officer Mobile",
        nodalOfficerBioAuth: "Nodal Officer Bio-Auth",
        principalBioAuth: "Principal Bio-Auth",
        // designationsList: "Designation List",
        // class: "Class ",
        // sanctionOrderNo: "Sanction Order No",
        // sanctionDate: "Sanction Date",
        // sanctionedPosts: "Sanctioned Posts",
        // working: "Employee Working Count",
        isClgFile: "Collge Sanction Order File Upload",
        isEstablishFile: "Establishment relevant File Upload",
        isTakenOverFile: "Taken Over File Upload",
        is2FUgcFile: "UGC 2F Registered File Upload",
        is12BFile: "12B Registered File Upload",
        isLandAreaFile: "Land Area File Upload",
        isCaAuditFile: "CA Audit File Upload",
        isGradeCycleFile: "Grade File Upload",
        isjBSRegisterFile: "JBS File Upload",
        // isSanctionOrderFile: "Sanction Order File Upload",
        isNccEstablishFile: "NCC file upload",
        isNssEstablishFile: "NSS file Upload",
        uploadClgSanctionLetter: "College Sanction Letter",
        //(basic details part II)
        uploadEstablishFile: "Upload Establishment File",
        uploadTakenOverFile: "Taken Over File",
        upload2FUgcRegisteredFile: "2F UGC Registered File",
        upload12BRegisteredFile: "12B Registered File",
        //NAAC details
        uploadGradeCycleFile: "Grade Cycle File",
        //janbhagidari details
        uploadJBSRegisterFile: "JBS Register File",
        uploadCaAuditFile: "CA Audit File",
        //NSS AND NCC DETAILS
        uploadsNssEstablishFile: "Upload Nss Establishment File",
        uploadsNccEstablishFile: "Upload Ncc Establishment File",
        //infrastructure details
        uploadsLandAreaFile: "Land Area File",
        //setup details
        // uploadsSanctionOrderFile: "Sanction Order File",

      };



      addConditionalFields(formData); // to extend the fields conditionally


      // console.log("Getting Submit Type");
      const allRequiredFields = {
        ...requiredFieldsByType.basicDetails,
        ...requiredFieldsByType.moreDetails,
        ...requiredFieldsByType.infrastructureDetails,
        ...requiredFieldsByType.naacDetails,
        ...requiredFieldsByType.janbhagidariDetails,
        ...requiredFieldsByType.nssAndNccDetails,
        ...requiredFieldsByType.nspRegistrationDetails,
        ...requiredFieldsByType.setupDetails,

      };

      const fieldsToValidate = submitType === "finalSubmit" ? allRequiredFields : requiredFieldsByType[submitType];

      if (fieldsToValidate) {
        for (const field in fieldsToValidate) {
          if (fieldsToValidate.hasOwnProperty(field)) {
            if (!formData[field] || (typeof formData[field] === "string" && !formData[field].trim())) {
              newErrors[field] = "This Field is Required.";
              errorFields.push(field);

              for (const section in requiredFieldsByType) {
                if (requiredFieldsByType[section].hasOwnProperty(field)) {
                  if (!errorSections[section]) {
                    errorSections[section] = [];
                  }
                  errorSections[section].push(field);
                }
              }
            }
          }
        }
      } else {
        console.error(`No required fields defined for submit type: ${submitType}`);
      }

      // Construct alert message with custom field names
      if (Object.keys(errorSections).length > 0) {
        let alertMessage = "<strong>Please fill in the following fields:</strong><br><br>";

        for (const section in errorSections) {
          alertMessage += `<strong>${section}:</strong><br> - `;
          alertMessage += errorSections[section]
            .map(field => fieldDisplayNames[field] || field) // Use custom display names or fallback to the key
            .join(", ");
          alertMessage += "<br><br>";
        }

        Swal.fire({
          icon: "warning",
          title: "Missing Fields",
          html: alertMessage,
          confirmButtonText: "OK"
        });
      }

      return newErrors;
    };
    const validationErrors = validateForm(submitType);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // console.log(institueProfile, "Getting user Profile");



    if (submitType === "finalSubmit" && (institueProfile && institueProfile.SubmitStatus &&
      (institueProfile.SubmitStatus.firstInfo === false || institueProfile.SubmitStatus.secondInfo === false
        || institueProfile.SubmitStatus.thirdInfo === false || institueProfile.SubmitStatus.fourthInfo === false
        || institueProfile.SubmitStatus.fifthInfo === false || institueProfile.SubmitStatus.sixthInfo === false
        || institueProfile.SubmitStatus.seventhInfo === false || institueProfile.SubmitStatus.eightInfo === false))) {
      SwalMessageAlert("All Section is Not Completed", "error");
      return;
    }

    if (submitType === "verify" && (institueProfile && institueProfile.isFinalSubmit !== true)) {
      SwalMessageAlert("Final Submit is Not Completed", "error");
      return;
    }

    let result

    try {

      if (submitType === "verify") {
        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह VERIFY करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, Verify",
          cancelButtonText: "No, cancel",
        });

      } else if (submitType === "edit") {

        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह EDIT करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, EDIT",
          cancelButtonText: "No, cancel",
        });

      } else if (submitType === "finalSubmit") {

        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप Final Submit करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, submit",
          cancelButtonText: "No, cancel",
        });
      }
      else {

        result = await Swal.fire({
          title: "Confirmation",
          text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
          icon: "warning",
          showCancelButton: true,
          confirmButtonColor: "#3085d6",
          cancelButtonColor: "#d33",
          confirmButtonText: "Yes, submit",
          cancelButtonText: "No, cancel",
        });
      }


      if (result.isConfirmed) {
        const response = await axios.put(
          `${endPoint}/api/update-institute-profile?clgId=${id}`,
          { "submitType": submitType },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {

          if (submitType === "verify") {
            SwalMessageAlert("Profile Verfication Completed", "success");
            setTimeout(() => window.location.reload(), 3000);

          } else if (submitType === "edit") {
            SwalMessageAlert("Open For Edit and Update", "success");
            setTimeout(() => window.location.reload(), 1000);

          } else if (submitType === "finalSubmit") {
            SwalMessageAlert("Final Submitteed Successfuly", "success");
            setTimeout(() => window.location.reload(), 1000);

          } else {
            SwalMessageAlert(response.data.msg, "success");
            getInstituteProfile();
          }
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const handleCheckboxChange = (e) => {
    const { name, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: checked ? "Yes" : "No",
    }));
  };

  const currentDate = new Date();

  const minDate = new Date();
  minDate.setFullYear(currentDate.getFullYear() - 18);

  // Calculate the maximum date (65 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(currentDate.getFullYear() - 65);

  // Format dates to YYYY-MM-DD for the input
  const formattedMinDate = minDate.toISOString().split('T')[0];
  const formattedMaxDate = maxDate.toISOString().split('T')[0];

  const formatTime = (dateString) => {
    if (!dateString) return "No update";

    const date = new Date(dateString);

    // Format the time as hh:mm:ss AM/PM
    const optionsTime = {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: true, // Use false for 24-hour format
    };

    return date.toLocaleTimeString("en-US", optionsTime);
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row className="justify-content-center md-7">

          <Col className="order-xl-2" xl="12">
            <Card style={{ boxShadow: "1px 5px 10px 3px black" }}>
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h2 className="mb-0 text-primary">
                      {/* <strong>
                        {" "}
                        <i className="ni ni-single-02 text-primary"></i>{" "}
                        Institute Profile 
                      </strong> */}
                      <strong className="flex flex-column">
  <i className="ni ni-single-02 text-primary"></i>{" "}
  Institute Profile <br />
<small>  <span className="text-danger">
    <strong>Note :</strong>  Locked Field की जानकारी को अपडेट करने के लिए ड्रॉपडाउन में My Profile पर क्लिक करें |  या   
    <a href="/admin/update/institute-profile" className="text-primary text-decoration-underline">
      <Button className="btn ml-1 btn-sm text-white bg-danger">My Profile</Button>
    </a> पर जाएँ |
  </span></small>
</strong>

                    </h2>

                    <span style={{ fontWeight: "bold" }}>
                      Profile Completed  -
                    </span>
                    <span> {proilePercent >= 80 ? <span className="text-success ml-2"> {proilePercent} %  </span> : <span className="text-danger ml-2">{proilePercent} %  </span>}</span>


                    <span style={{ fontWeight: "bold" }}>
                      {", "}Profile Status  -
                    </span>
                    <span> {institueProfile && institueProfile.isVerified === true ? <Badge className="bg-success text-white ml-2">VERIFIED</Badge> : <Badge className="bg-danger text-white ml-2">NOT VERIFIED</Badge>}</span>


                    <span style={{ fontWeight: "bold" }} className="ml-2">
                      {", "} Last Verified  -
                    </span>
                    <span className="ml-2" style={{ fontWeight: "bold", color: "green" }}> {institueProfile && institueProfile.verificationDate !== undefined ? institueProfile && formatDate(institueProfile.verificationDate) : <span style={{ color: "red" }}>Not Verified</span>}</span>



                  </Col>
                  <Col xs="4" style={{ display: "flex", justifyContent: "right" }}>
                    <h2 className="mb-0 text-primary text-right">
                      < Link to={`/admin/college-print/${id}`}>
                        <Button className=" btn btn-sm mr-2" style={{ backgroundColor: "orange" }}>
                          <i className="fas fa-print"></i>
                        </Button>
                      </Link>
                    </h2>
                    {institueProfile && institueProfile.isFinalSubmit === true &&
                      <h2 className="mb-0 text-primary text-right">
                        {institueProfile && institueProfile.isVerified === false &&
                          <Button
                            color="warning"
                            className="mr-2"
                            onClick={(e) =>
                              handleFinalSubmit(e, "verify")
                            }
                            size="sm"
                          >
                            Verify Profile
                          </Button>}
                        {institueProfile && institueProfile.isVerified === true &&
                          <Button
                            color="success"
                            className="mr-2"
                            size="sm"
                          >
                            Verified
                          </Button>}
                      </h2>}

                    <h2 className="mb-0 text-primary text-right">
                      {institueProfile && institueProfile.isFinalSubmit === false && <Button
                        color="warning"
                        onClick={handlePreview}

                        size="sm"
                      >
                        Final Submit
                      </Button>
                      }
                      {institueProfile && institueProfile.isFinalSubmit === true && <Button
                        color="success"
                        size="sm"
                      >
                        Final Submitted
                      </Button>
                      }
                    </h2>
                  </Col>
                  <Col className="text-right" xs="4"></Col>
                </Row>
              </CardHeader>
              <CardBody>

                <Nav tabs>

                  <NavItem>

                    <NavLink
                      className={classnames({ active: activeTab === "1" })}
                      onClick={() => toggleTab("1")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.firstInfo ? "green" : "red",
                        borderBottom: activeTab === "1" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Basic Details (Part I)<br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"} :{" "}
                        {institueProfile && institueProfile.basicDetails && institueProfile.basicDetails.lastUpdate
                          ? formatDate(institueProfile.basicDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.basicDetails?.lastUpdate)}</h5>
                    </NavLink>

                  </NavItem>
                  <NavItem>

                    <NavLink
                      className={classnames({ active: activeTab === "2" })}
                      onClick={() => toggleTab("2")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.secondInfo ? "green" : "red",
                        borderBottom: activeTab === "2" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Basic Details (Part II)<br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"} :{" "}
                        {institueProfile && institueProfile.moreDetails && institueProfile.moreDetails.lastUpdate
                          ? formatDate(institueProfile.moreDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.moreDetails?.lastUpdate)}</h5>
                    </NavLink>

                  </NavItem>



                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "3" })}
                      onClick={() => toggleTab("3")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.thirdInfo ? "green" : "red",
                        borderBottom: activeTab === "3" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Infrastructure Details<br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.infrastructureDetails && institueProfile.infrastructureDetails.lastUpdate
                          ? formatDate(institueProfile.infrastructureDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.infrastructureDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames("rounded-0", { active: activeTab === "4" })}
                      onClick={() => toggleTab("4")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.fourthInfo ? "green" : "red",
                        borderBottom: activeTab === "4" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      NAAC Details <br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.naacDetails && institueProfile.naacDetails.lastUpdate
                          ? formatDate(institueProfile.naacDetails.lastUpdate)
                          : "No update"}  <br />{formatTime(institueProfile?.naacDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "5" })}
                      onClick={() => toggleTab("5")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.fifthInfo ? "green" : "red",
                        borderBottom: activeTab === "5" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Janbhagidari Details <br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.janbhagidariDetails && institueProfile.janbhagidariDetails.lastUpdate
                          ? formatDate(institueProfile.janbhagidariDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.janbhagidariDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "6" })}
                      onClick={() => toggleTab("6")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.sixthInfo ? "green" : "red",
                        borderBottom: activeTab === "6" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      NSS and NCC Details <br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.nssAndNccDetails && institueProfile.nssAndNccDetails.lastUpdate
                          ? formatDate(institueProfile.nssAndNccDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.nssAndNccDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>

                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "7" })}
                      onClick={() => toggleTab("7")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.seventhInfo ? "green" : "red",
                        borderBottom: activeTab === "7" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      NSP Registration Details<br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.nspRegistrationDetails && institueProfile.nspRegistrationDetails.lastUpdate
                          ? formatDate(institueProfile.nspRegistrationDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.nspRegistrationDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>
                  <NavItem>
                    <NavLink
                      className={classnames({ active: activeTab === "8" })}
                      onClick={() => toggleTab("8")}
                      style={{
                        backgroundColor: institueProfile?.SubmitStatus?.eightInfo ? "green" : "red",
                        borderBottom: activeTab === "8" ? "4px solid blue" : "none",
                        color: "white",
                        cursor: "pointer",
                      }}
                    >
                      Setup Details<br /><h5 style={{ color: "yellow" }}>{institueProfile && institueProfile.isVerified !== true ? "Draft Saved" : "Updated on"}:{" "}
                        {institueProfile && institueProfile.setupDetails && institueProfile.setupDetails.lastUpdate
                          ? formatDate(institueProfile.setupDetails.lastUpdate)
                          : "No update"} <br />{formatTime(institueProfile?.setupDetails?.lastUpdate)}</h5>
                    </NavLink>
                  </NavItem>

                </Nav>
                <TabContent activeTab={activeTab}>
                  <TabPane tabId="1">
                    <Form className="mt-3">
                      <Card className="bg-secondary shadow-lg mb-4">
                        <CardBody>
                          <h4 className="mb-4" style={{ justifyContent: "space-between" }}>
                            <i className="ni ni-single-02 text-primary"></i>{" "}
                            Basic Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  firstInfo: true,
                                  secondInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: false,

                                }
                              })}
                            // onClick={(e) =>
                            //   handleChangeEditable(e, "firstInfo")
                            // }
                            >
                              Edit
                            </Button>}
                          </h4>

                          <hr />

                          <Row>

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-dob"
                                >
                                  College Name
                                </label>
                                <Input
                                  id="input-dob"
                                  name="name"
                                  value={formData.name}
                                  onChange={handleInputChange}
                                  type="text"
                                  readOnly

                                />
                                {errors.name && (
                                  <small className="text-danger">
                                    {errors.name}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-aisheCode"
                                >
                                  AISHE Code
                                </label>
                                <Input
                                  id="input-aisheCode"
                                  name="aisheCode"
                                  value={formData.aisheCode}
                                  onChange={handleInputChange}
                                  type="text"
                                  disabled
                                />
                                {errors.aisheCode && (
                                  <small className="text-danger">
                                    {errors.aisheCode}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-university"
                                >
                                  University
                                </label>
                                <Input
                                  id="input-university"
                                  type="select"
                                  name="university"
                                  value={formData.university} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  disabled
                                >
                                  <option value="">Select University</option>
                                  {university &&
                                    university.length > 0 &&
                                    university.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.name}
                                      </option>
                                    ))}
                                </Input>
                                {errors.university && (
                                  <small className="text-danger">
                                    {errors.university}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-gender"
                                >
                                  Principal Name
                                </label>
                                <Input
                                  id="input-gender"
                                  type="text"
                                  name="contactPerson" // Add name attribute for handleInputChange
                                  value={formData.contactPerson} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  readOnly
                                  disabled
                                >

                                </Input>
                                {errors.contactPerson && (
                                  <small className="text-danger">
                                    {errors.contactPerson}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-contactNumber"
                                >
                                  Principal Mobile No
                                </label>
                                <Input
                                  id="input-contactNumber"
                                  type="text"
                                  pattern="^[1-9][0-9]*$"
                                  name="contactNumber"
                                  value={formData.contactNumber} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  invalid={!!errors.contactNumber}
                                  onKeyPress={handleKeyPress}
                                  maxLength={10}
                                  disabled
                                />
                                {errors.contactNumber && (
                                  <small className="text-danger">
                                    {errors.contactNumber}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-universityType"
                                >
                                  Is Lead College
                                </label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isLead"
                                      id="input-lead"
                                      value={1}
                                      checked={formData.isLead === 1}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isLead"
                                      id="input-lead"
                                      value={0}
                                      checked={formData.isLead === 0}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-collegeEmail"
                                >
                                  College E-mail ID
                                </label>
                                <Input
                                  name="collegeEmail"
                                  id="input-district"
                                  type="text"
                                  value={formData.collegeEmail}
                                  onChange={handleInputChange}
                                  disabled
                                >
                                </Input>
                                {errors.collegeEmail && (
                                  <small className="text-danger">
                                    {errors.collegeEmail}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="establishYear">
                                  Establishment Year
                                </label>
                                <Input
                                  id="establishYear"
                                  name="establishYear"
                                  type="text"
                                  value={formData.establishYear}
                                  onChange={handleInputChange}
                                  disabled
                                  onKeyPress={handleKeyPress}
                                />
                                {errors.establishYear && <small className="text-danger">{errors.establishYear}</small>}
                              </FormGroup>
                            </Col>


                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-division"
                                >
                                  Division
                                </label>
                                <Input
                                  name="divison"
                                  id="input-division"
                                  type="select"
                                  value={formData.divison}
                                  onChange={handledivisonChange}
                                  readOnly
                                  disabled
                                >
                                  <option value="" disabled>
                                    Select Division
                                  </option>
                                  {division &&
                                    division.length > 0 &&
                                    division.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.divisionCode}
                                        selected={
                                          Number(type.divisionCode) ===
                                          Number(formData.divison)
                                        }
                                      >
                                        {type.name}
                                      </option>
                                    ))}
                                </Input>
                                {errors.divison && (
                                  <small style={{ color: "red" }}>
                                    {errors.divison}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-district"
                                >
                                  District
                                </label>
                                <Input
                                  name="district"
                                  id="input-district"
                                  type="select"
                                  value={formData.district}
                                  // readOnly
                                  disabled
                                  onChange={handleInputChange}

                                >
                                  <option value="" disabled>
                                    Select Division
                                  </option>
                                  {district &&
                                    district.length > 0 &&
                                    district.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.LGDCode}
                                        selected={
                                          Number(type.LGDCode) ===
                                          Number(formData.district)
                                        }
                                      >
                                        {type.districtNameEng}
                                      </option>
                                    ))}
                                  {/* Add more districts as needed */}
                                </Input>
                                {errors.district && (
                                  <small style={{ color: "red" }}>
                                    {errors.district}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-vidhansabha"
                                >
                                  Vidhansabha Name
                                </label>
                                <Input
                                  name="vidhansabha"
                                  id="input-vidhansabha"
                                  type="select"
                                  value={formData.vidhansabha}
                                  onChange={handleInputChange}
                                  readOnly
                                  disabled
                                >
                                  <option value="" disabled>
                                    Select Vidhansabha
                                  </option>
                                  {vidhansabha &&
                                    vidhansabha.length > 0 &&
                                    vidhansabha.map((type, index) => (
                                      <option
                                        key={index}
                                        value={type.ConstituencyNumber}
                                        selected={
                                          Number(type.ConstituencyNumber) ===
                                          Number(formData.vidhansabha)
                                        }
                                      >
                                        {type.ConstituencyName}
                                      </option>
                                    ))}
                                </Input>
                                {errors.vidhansabha && (
                                  <small style={{ color: "red" }}>
                                    {errors.vidhansabha}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>


                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="ConstituencyNumber">
                                  Vidhansabha Number
                                </label>
                                <Input
                                  id="ConstituencyNumber"
                                  name="ConstituencyNumber"
                                  type="text"
                                  value={formData.ConstituencyNumber}
                                  onChange={handleInputChange}
                                  onKeyPress={handleKeyPress}
                                  disabled
                                >

                                </Input>
                                {errors.ConstituencyNumber && <small className="text-danger">{errors.ConstituencyNumber}</small>}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="lat">
                                  Latitude
                                </label>
                                <Input
                                  id="lat"
                                  name="lat"
                                  type="text"
                                  value={formData.lat}
                                  onChange={handleInputChange}
                                  disabled


                                />
                                {errors.lat && <small className="text-danger">{errors.lat}</small>}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="long">
                                  Longitude
                                </label>
                                <Input
                                  id="long"
                                  name="long"
                                  type="text"
                                  value={formData.long}
                                  onChange={handleInputChange}
                                  disabled
                                />
                                {errors.long && <small className="text-danger">{errors.long}</small>}
                              </FormGroup>
                            </Col>
                            <Col lg="6">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-address"
                                >
                                  Address
                                </label>
                                <Input
                                  id="input-address"
                                  type="text"
                                  name="address" // Add name attribute for handleInputChange
                                  value={formData.address} // Bind state
                                  onChange={handleInputChange} // Update state on change
                                  readOnly
                                ></Input>
                                {errors.address && (
                                  <small className="text-danger">
                                    {errors.address}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Is College Sanction Letter Upload</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isClgFile"
                                      id="input-isClgFile"
                                      value={"true"}
                                      checked={formData.isClgFile === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                    />
                                    <Label check >Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isClgFile"
                                      id="input-isClgFile"
                                      value={"false"}
                                      checked={formData.isClgFile === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.isClgFile && (
                                  <small className="text-danger">{errors.isClgFile}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.isClgFile === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="num-uploadClgSanctionLetter"
                                    >
                                      College Sanction Letter
                                    </label>
                                    <Input
                                      id="num-uploadClgSanctionLetter"
                                      type="file"
                                      name="uploadClgSanctionLetter"
                                      accept=".pdf" // Restrict allowed file types
                                      // onChange={handleFileChange} // Handle file selection
                                      onChange={(e) => {
                                        // console.log("File input changed");
                                        handleImageChange(e, 1);
                                      }}

                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.firstInfo === false}

                                    />
                                    <a href={`https://heonline.cg.nic.in/${formData.uploadClgSanctionLetter}`} download>
                                      <Button className="btn-sm btn-primary" >
                                        {formData.uploadClgSanctionLetter ? <FaDownload size={18} /> : "No File"}
                                      </Button>
                                    </a>
                                    {errors.uploadClgSanctionLetter && (
                                      <small className="text-danger">
                                        {errors.uploadClgSanctionLetter}
                                      </small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" color="muted"
                                        value={formData.clgSanctionFileName || "No file selected."}
                                        disabled>{formData.clgSanctionFileName || "No file selected."}</Input>

                                    </Col> */}


                              </>

                            )}

                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category-of-appointment"
                                >
                                  Government E-mail ID 1 (optional)
                                </label>
                                <Input
                                  name="govtEmail1"
                                  id="input-district"
                                  type="text"
                                  value={formData.govtEmail1}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}

                                />
                                {errors.govtEmail1 && (
                                  <small className="text-danger">
                                    {errors.govtEmail1}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-govtEmail2"
                                >
                                  Government E-mail ID 2 (optional)
                                </label>
                                <Input
                                  name="govtEmail2"
                                  id="input-govtEmail2"
                                  type="text"
                                  value={formData.govtEmail2}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}

                                />
                                {errors.govtEmail2 && (
                                  <small className="text-danger">
                                    {errors.govtEmail2}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Is Establishment File Upload</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isEstablishFile"
                                      id="input-isEstablishFile"
                                      value={"true"}
                                      checked={formData.isEstablishFile === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isEstablishFile"
                                      id="input-isEstablishFile"
                                      value={"false"}
                                      checked={formData.isEstablishFile === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.isEstablishFile && (
                                  <small className="text-danger">{errors.isEstablishFile}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.isEstablishFile === "true" && (
                              <>


                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="uploadEstablishFile">Establishment File</label>
                                    <Input
                                      type="file"
                                      name="uploadEstablishFile"
                                      accept=".pdf"
                                      onChange={(e) => handleImageChange(e, 2)}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                    />
                                    <a href={`https://heonline.cg.nic.in/${formData.uploadEstablishFile}`} download>
                                      <Button className="btn-sm btn-primary mt-1">
                                        {formData.uploadEstablishFile ? <FaDownload size={18} /> : "No File"}
                                      </Button>
                                    </a>
                                    {errors.uploadEstablishFile && (
                                      <small className="text-danger">{errors.uploadEstablishFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.establishFileName || "No file selected."} disabled />
                                    </Col> */}

                              </>
                            )}
                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="telephone">
                                  Telephone No.
                                </label>
                                <Input
                                  id="telephone"
                                  name="telephone"
                                  type="text"
                                  value={formData.telephone}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.firstInfo === false}
                                  onKeyPress={handleKeyPress}
                                  maxLength={10}

                                />
                                {/* {errors.telephone && <small className="text-danger">{errors.telephone}</small>} */}
                              </FormGroup>
                            </Col>
                          </Row>

                        </CardBody>
                      </Card>
                      {(institueProfile?.editSection?.firstInfo === true || institueProfile?.editSection?.firstInfo === undefined) &&
                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) =>
                              handleSubmit(e, "basicDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "basicDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "basicDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>
                          }
                        </div>}
                    </Form>
                  </TabPane>
                  <TabPane tabId="2">
                    <Form className="mt-3">
                      <Card className="bg-secondary shadow-lg mb-4">
                        <CardBody>
                          <h4 className="mb-4" style={{ justifyContent: "space-between" }}>
                            <i className="ni ni-single-02 text-primary"></i>{" "}
                            Basic Details (Part II)
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: true,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: false,

                                }
                              })}
                            // onClick={(e) =>
                            //   handleChangeEditable(e, "firstInfo")
                            // }
                            >
                              Edit
                            </Button>}
                          </h4>

                          <hr />
                          <Row>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-category-of-appointment"
                                >
                                  College Website
                                </label>
                                <Input
                                  name="collegeUrl"
                                  id="input-district"
                                  type="text"
                                  value={formData.collegeUrl}
                                  onChange={handleInputChange}
                                  disabled
                                />

                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-universityType"
                                >
                                  College Type
                                </label>
                                <Input
                                  name="collegeType"
                                  id="input-collegeType"
                                  type="select"
                                  value={formData.collegeType}
                                  onChange={handleInputChange}
                                  disabled

                                >
                                  <option value="">Select College Type</option>
                                  <option value="1">Govt.</option>
                                  <option value="2">Private</option>
                                  {/* Add more university types if needed */}
                                </Input>
                                {errors.collegeType && (
                                  <p style={{ color: "red" }}>{errors.collegeType}</p>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-degreeTypes"
                                >
                                  Degree Types
                                </label>
                                <Input
                                  type="select"
                                  name="degreeTypes"
                                  id="input-degreeTypes"
                                  value={formData.degreeTypes}
                                  onChange={handleInputChange}
                                  disabled

                                >
                                  <option value="">Select College Type</option>
                                  <option value="UG">UG</option>
                                  <option value="PG">PG</option>
                                  {/* Add more options as needed */}
                                </Input>
                                {errors.degreeTypes && (
                                  <small style={{ color: "red" }}>
                                    {errors.degreeTypes}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="registrationNumber">
                                  Registration Number
                                </label>
                                <Input
                                  id="registrationNumber"
                                  name="registrationNumber"
                                  type="text"
                                  onChange={handleInputChange}
                                  value={formData.registrationNumber}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                />
                                {errors.registrationNumber && <small className="text-danger">{errors.registrationNumber}</small>}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="areaType">
                                  Area Type
                                </label>
                                <Input
                                  type="select"
                                  name="areaType"
                                  id="areaType"
                                  value={formData.areaType}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                >
                                  <option value="">Select</option>
                                  <option value="General">General</option>
                                  <option value="SC area">SC area</option>
                                  <option value="Tribal Area">Tribal Area</option>
                                </Input>
                                {errors.areaType && <small className="text-danger">{errors.areaType}</small>}
                              </FormGroup>
                            </Col>


                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-ddoCode"
                                >
                                  DDO Code
                                </label>
                                <Input
                                  id="input-ddoCode"
                                  name="ddoCode"
                                  value={formData.ddoCode}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                  type="text"

                                />
                                {errors.ddoCode && (
                                  <small className="text-danger">
                                    {errors.ddoCode}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="block">
                                  Block
                                </label>
                                <Input
                                  id="block"
                                  name="block"
                                  type="select"
                                  value={formData.block}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                  onChange={handleInputChange}
                                >
                                  <option value="" disabled>
                                    Select Block
                                  </option>
                                  {validBlocks.length > 0 ? (
                                    validBlocks.map((block, index) => (
                                      <option key={block._id} value={block._id}
                                      >
                                        {block.BlockNameEng}
                                      </option>
                                    ))
                                  ) : (
                                    <option value="">No block available</option>
                                  )}

                                </Input>
                                {errors.block && <small className="text-danger">{errors.block}</small>}
                              </FormGroup>
                            </Col>



                            <Col lg="3">
                              <FormGroup>
                                <label
                                  className="form-control-label"
                                  htmlFor="input-girlsOnly"
                                >
                                  Girls / Co-Ed
                                </label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsOnly"
                                      id="input-girlsOnly"
                                      value={"Girls"}
                                      checked={formData.girlsOnly === "Girls"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Girls</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsOnly"
                                      id="input-girlsOnly"
                                      value={"Co-Ed"}
                                      checked={formData.girlsOnly === "Co-Ed"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />

                                    <Label check>Co-Ed</Label>
                                  </FormGroup>
                                </div>
                                {errors.girlsOnly && (
                                  <small style={{ color: "red" }}>
                                    {errors.girlsOnly}
                                  </small>
                                )}
                              </FormGroup>
                            </Col>

                            {[

                              { id: "isAutonomous", label: "Autonomous College" },
                              { id: "isModelCollege", label: "Model College" },
                              { id: "englishMedium", label: "English Medium" },
                              { id: "sangeetCollege", label: "Sangeet College" },
                            ].map(({ id, label }) => (
                              <Col lg="3" key={id}>
                                <FormGroup check className="mb-3">
                                  <Label check>
                                    <Input
                                      type="checkbox"
                                      id={id}
                                      name={id}
                                      checked={formData[id] === "true"}
                                      onChange={(e) =>
                                        setFormData((prev) => ({
                                          ...prev,
                                          [id]: e.target.checked ? "true" : "false",
                                        }))
                                      }
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                    />
                                    <span
                                      className={
                                        formData[id] === "true"
                                          ? "text-success font-weight-bold ml-2"
                                          : "text-danger font-weight-bold ml-2"
                                      }
                                    >
                                      {label}
                                    </span>
                                  </Label>
                                  {errors[id] && <div className="text-danger small">{errors[id]}</div>}
                                </FormGroup>
                              </Col>
                            ))}

                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="demography">
                                  Demography
                                </label>
                                <Input
                                  type="select"
                                  name="demography"
                                  id="demography"
                                  value={formData.demography}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                >
                                  <option value="">Select</option>
                                  <option value="Rural">Rural</option>
                                  <option value="Urban">Urban</option>
                                </Input>
                                {errors.demography && <small className="text-danger">{errors.demography}</small>}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Is Taken Over</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="takenOver"
                                      id="input-takenOver"
                                      value={"true"}
                                      checked={formData.takenOver === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="takenOver"
                                      id="input-takenOver"
                                      value={"false"}
                                      checked={formData.takenOver === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.takenOver && (
                                  <small className="text-danger">{errors.takenOver}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.takenOver === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="takenOverDate">Taken Over Date</label>
                                    <Input
                                      id="takenOverDate"
                                      name="takenOverDate"
                                      type="date"
                                      value={formData.takenOverDate}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                    />
                                    {errors.takenOverDate && <small className="text-danger">{errors.takenOverDate}</small>}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>Is Taken Over File Upload</Label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="isTakenOverFile"
                                          id="input-isTakenOverFile"
                                          value={"true"}
                                          checked={formData.isTakenOverFile === "true"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="isTakenOverFile"
                                          id="input-isTakenOverFile"
                                          value={"false"}
                                          checked={formData.isTakenOverFile === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />

                                        <Label check>No</Label>
                                      </FormGroup>
                                    </div>
                                    {errors.isTakenOverFile && (
                                      <small className="text-danger">{errors.isTakenOverFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.isTakenOverFile === "true" && (
                                  <>


                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="uploadTakenOverFile">Taken Over File</label>
                                        <Input
                                          type="file"
                                          name="uploadTakenOverFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 3)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.uploadTakenOverFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.uploadTakenOverFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.uploadTakenOverFile && (
                                          <small className="text-danger">{errors.uploadTakenOverFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.takenOverFileName || "No file selected."} disabled />
                                    </Col> */}

                                  </>
                                )}
                              </>
                            )}
                            <Col lg="3">
                              <FormGroup>
                                <Label>2F Registered under UGC</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="UGC2FRegistered"
                                      id="input-UGC2FRegistered"
                                      value={"true"}
                                      checked={formData.UGC2FRegistered === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="UGC2FRegistered"
                                      id="input-UGC2FRegistered"
                                      value={"false"}
                                      checked={formData.UGC2FRegistered === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.UGC2FRegistered && (
                                  <small className="text-danger">{errors.UGC2FRegistered}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.UGC2FRegistered === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="ugc2FDate">UGC 2F Registration Date</label>
                                    <Input
                                      id="ugc2FDate"
                                      name="ugc2FDate"
                                      type="date"
                                      value={formData.ugc2FDate}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />
                                    {errors.ugc2FDate && <small className="text-danger">{errors.ugc2FDate}</small>}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>Is 2F Registered File Upload</Label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="is2FUgcFile"
                                          id="input-is2FUgcFile"
                                          value={"true"}
                                          checked={formData.is2FUgcFile === "true"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="is2FUgcFile"
                                          id="input-is2FUgcFile"
                                          value={"false"}
                                          checked={formData.is2FUgcFile === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />

                                        <Label check>No</Label>
                                      </FormGroup>
                                    </div>
                                    {errors.is2FUgcFile && (
                                      <small className="text-danger">{errors.is2FUgcFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.is2FUgcFile === "true" && (
                                  <>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="upload2FUgcRegisteredFile">UGC 2F Registered File</label>
                                        <Input
                                          type="file"
                                          name="upload2FUgcRegisteredFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 4)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.upload2FUgcRegisteredFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.upload2FUgcRegisteredFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.upload2FUgcRegisteredFile && (
                                          <small className="text-danger">{errors.upload2FUgcRegisteredFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.Ugc2FRegisterFileName || "No file selected."} disabled />
                                    </Col> */}

                                  </>
                                )}

                              </>
                            )}
                            <Col lg="3">
                              <FormGroup>
                                <Label>Is 12B Registered </Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isRegistered12B"
                                      id="input-isRegistered12B"
                                      value={"true"}
                                      checked={formData.isRegistered12B === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isRegistered12B"
                                      id="input-isRegistered12B"
                                      value={"false"}
                                      checked={formData.isRegistered12B === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.isRegistered12B && (
                                  <small className="text-danger">{errors.isRegistered12B}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.isRegistered12B === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="ugc12BDate">12B Registration Date</label>
                                    <Input
                                      id="ugc12BDate"
                                      name="ugc12BDate"
                                      type="date"
                                      value={formData.ugc12BDate}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                    />
                                    {errors.ugc12BDate && <small className="text-danger">{errors.ugc12BDate}</small>}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>Is 12B Registered File Upload </Label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="is12BFile"
                                          id="input-is12BFile"
                                          value={"true"}
                                          checked={formData.is12BFile === "true"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="is12BFile"
                                          id="input-is12BFile"
                                          value={"false"}
                                          checked={formData.is12BFile === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}
                                        />

                                        <Label check>No</Label>
                                      </FormGroup>
                                    </div>
                                    {errors.is12BFile && (
                                      <small className="text-danger">{errors.is12BFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.is12BFile === "true" && (
                                  <>
                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="upload12BRegisteredFile">12B Registered File</label>
                                        <Input
                                          type="file"
                                          name="upload12BRegisteredFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 5)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.secondInfo === false}

                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.upload12BRegisteredFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.upload12BRegisteredFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.upload12BRegisteredFile && (
                                          <small className="text-danger">{errors.upload12BRegisteredFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* <Col sm={3}>
                                    <br/>
                                      <Input type="text" value={formData.Registered12BFileName || "No file selected."} disabled />
                                    </Col> */}
                                  </>
                                )}
                              </>
                            )}
                          </Row>
                        </CardBody>
                      </Card>
                      {(institueProfile?.editSection?.secondInfo === true || institueProfile?.editSection?.secondInfo === undefined) &&

                        <div className="text-center">

                          <Button
                            color="info"
                            size="sm"

                            onClick={(e) =>
                              handleSubmit(e, "moreDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "moreDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "moreDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}

                        </div>}
                    </Form>
                  </TabPane>
                  <TabPane tabId="3">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            Infrastructure Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: true,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: false,
                                }
                              })}
                            // onClick={(e) =>handleChangeEditable(e, "fifthInfo")}
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row></Row>


                          <Row>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Land Alloted </Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isLandAlloted"
                                      id="input-isLandAlloted"
                                      value={"true"}
                                      checked={formData.isLandAlloted === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="isLandAlloted"
                                      id="input-isLandAlloted"
                                      value={"false"}
                                      checked={formData.isLandAlloted === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.isLandAlloted && (
                                  <small className="text-danger">{errors.isLandAlloted}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.isLandAlloted === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>Land Area (sq.ft)</Label>
                                    <Input type="text" name="landAreaSqFt"
                                      value={formData.landAreaSqFt}
                                      onChange={handleInputChange}
                                      onKeyPress={handleKeyPress}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                    />
                                    {errors.landAreaSqFt && (
                                      <small className="text-danger">{errors.landAreaSqFt}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup check className="mb-3">
                                    <Label check>
                                      <Input
                                        type="checkbox"
                                        id="isLandAreaFile"
                                        name="isLandAreaFile"
                                        checked={formData.isLandAreaFile === "true"}
                                        onChange={(e) =>
                                          setFormData((prev) => ({
                                            ...prev,

                                            isLandAreaFile: e.target.checked ? "true" : "false",

                                          }))
                                        }
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                      />
                                      <span
                                        className={
                                          formData.isLandAreaFile === "true"
                                            ? "text-success font-weight-bold ml-2"
                                            : "text-danger font-weight-bold ml-2"
                                        }
                                      >
                                        Is File Upload (Land Altoted)
                                      </span>
                                    </Label>
                                    {errors.isLandAreaFile && (
                                      <small className="text-danger">{errors.isLandAreaFile}</small>
                                    )}
                                  </FormGroup>

                                </Col>
                                {formData.isLandAreaFile === "true" && (
                                  <>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="uploadsLandAreaFile">Land Area File</label>
                                        <Input
                                          type="file"
                                          name="uploadsLandAreaFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 11)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.uploadsLandAreaFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.uploadsLandAreaFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.uploadsLandAreaFile && (
                                          <small className="text-danger">{errors.uploadsLandAreaFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.LandAreaFileName || "No file selected."} disabled />
                                    </Col> */}

                                  </>
                                )}

                                <Col lg="3">
                                  <FormGroup>
                                    <label
                                      className="form-control-label"
                                      htmlFor="input-universityType"
                                    >
                                      Building Constructed
                                    </label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="buildingConstructed"
                                          id="input-lead"
                                          value={"true"}
                                          checked={formData.buildingConstructed === "true"}
                                          onChange={handleInputChange}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                          readOnly
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="buildingConstructed"
                                          id="input-lead"
                                          value={"false"}
                                          checked={formData.buildingConstructed === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                        />

                                        <Label check>No</Label>
                                      </FormGroup>
                                    </div>
                                    {errors.buildingConstructed && (
                                      <small className="text-danger">{errors.buildingConstructed}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                              </>
                            )}

                            <Col lg="3">
                              <FormGroup>
                                <Label>No. Of Teaching Rooms</Label>
                                <Input type="text"

                                  name="teachingRooms"
                                  value={formData.teachingRooms}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                  onKeyPress={handleKeyOnPress}
                                />
                                {errors.teachingRooms && (
                                  <small className="text-danger">{errors.teachingRooms}</small>
                                )} </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>No. Of Labs</Label>
                                <Input
                                  type="text"
                                  name="labs"
                                  value={formData.labs}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                  onKeyPress={handleKeyOnPress}
                                />
                                {errors.labs && (
                                  <small className="text-danger">{errors.labs}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>Girls Common Room</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsCommonRoom"
                                      id="input-girlsCommonRoom"
                                      value={"true"}
                                      checked={formData.girlsCommonRoom === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsCommonRoom"
                                      id="input-girlsCommonRoom"
                                      value={"false"}
                                      checked={formData.girlsCommonRoom === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                    />

                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.girlsCommonRoom && (
                                  <small className="text-danger">{errors.girlsCommonRoom}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>Library (Availability)</Label>

                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="library"
                                      id="input-library"
                                      value={"true"}
                                      checked={formData.library === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="library"
                                      id="input-library"
                                      value={"false"}
                                      checked={formData.library === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.library && (
                                  <small className="text-danger">{errors.library}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.library === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>No. of Physical Books</Label>
                                    <Input type="text"
                                      onKeyPress={handleKeyOnPress}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      name="physicalBooksNo" value={formData.physicalBooksNo} onChange={handleInputChange} >

                                    </Input>
                                    {errors.physicalBooksNo && (
                                      <small className="text-danger">{errors.physicalBooksNo}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <Label>No. of Digital Books</Label>
                                    <Input
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      type="text"
                                      onKeyPress={handleKeyOnPress} name="digitalBookNo" value={formData.digitalBookNo} onChange={handleInputChange}>

                                    </Input>
                                    {errors.digitalBookNo && (
                                      <small className="text-danger">{errors.digitalBookNo}</small>
                                    )}
                                  </FormGroup>
                                </Col>


                                <Col lg="3">
                                  <FormGroup>
                                    <Label>No. of Journals</Label>
                                    <Input type="text"
                                      onKeyPress={handleKeyOnPress}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      name="journalsNo" value={formData.journalsNo} onChange={handleInputChange} >
                                    </Input>
                                    {errors.journalsNo && (
                                      <small className="text-danger">{errors.journalsNo}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}

                            <Col lg="3">
                              <FormGroup>
                                <Label>Reading Room (Availability)</Label>

                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="readingRoom"
                                      id="input-readingRoom"
                                      value={"true"}
                                      checked={formData.readingRoom === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="readingRoom"
                                      id="input-readingRoom"
                                      value={"false"}
                                      checked={formData.readingRoom === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.readingRoom && (
                                  <small className="text-danger">{errors.readingRoom}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>Sports Ground</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="sportsGround"
                                      id="input-lead"
                                      value={"true"}
                                      checked={formData.sportsGround === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="sportsGround"
                                      id="input-lead"
                                      value={"false"}
                                      checked={formData.sportsGround === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.sportsGround && (
                                  <small className="text-danger">{errors.sportsGround}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>Auditorium</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="auditorium"
                                      id="input-auditorium"
                                      value={"true"}
                                      checked={formData.auditorium === "true"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      readOnly
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="auditorium"
                                      id="input-auditorium"
                                      value={"false"}
                                      checked={formData.auditorium === "false"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      readOnly
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.auditorium && (
                                  <small className="text-danger">{errors.auditorium}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Smart Class</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="smartClass"
                                      id="input-smartClass"
                                      value={"true"}
                                      checked={formData.smartClass === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="smartClass"
                                      id="input-smartClass"
                                      value={"false"}
                                      checked={formData.smartClass === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.smartClass && (
                                  <small className="text-danger">{errors.smartClass}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.smartClass === "true" && (
                              <Col lg="3">
                                <FormGroup>
                                  <Label>Number of Smart Classes</Label>
                                  <Input
                                    type="text"
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    name="numberOfSmartClass" value={formData.numberOfSmartClass} onChange={handleInputChange}
                                    onKeyPress={handleKeyOnPress} />
                                  {errors.numberOfSmartClass && (
                                    <small className="text-danger">{errors.numberOfSmartClass}</small>
                                  )}
                                </FormGroup>
                              </Col>
                            )}

                            <Col lg="3">
                              <FormGroup>
                                <Label>Hostel Available</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="hostelAvailable"
                                      id="input-hostelAvailable"
                                      value={"true"}
                                      checked={formData.hostelAvailable === "true"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      readOnly
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="hostelAvailable"
                                      id="input-hostelAvailable"
                                      value={"false"}
                                      checked={formData.hostelAvailable === "false"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      readOnly
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.hostelAvailable && (
                                  <small className="text-danger">{errors.hostelAvailable}</small>
                                )}
                              </FormGroup>
                            </Col>

                            {formData.hostelAvailable === "true" && (
                              <>

                                <Col lg="3">
                                  <FormGroup>
                                    <Label>No. of Boys Hostel Capacity</Label>
                                    <Input type="text"
                                      onKeyPress={handleKeyPress}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      name="boysHostelCount" value={formData.boysHostelCount} onChange={handleHostelCountChange} />
                                    {errors.boysHostelCount && (
                                      <small className="text-danger">{errors.boysHostelCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="3">
                                  <FormGroup>
                                    <Label>No. of Girls Hostel Capacity</Label>
                                    <Input type="text"
                                      onKeyPress={handleKeyPress}
                                      name="girlsHostelCount" value={formData.girlsHostelCount}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                      onChange={handleHostelCountChange} />
                                    {errors.girlsHostelCount && (
                                      <small className="text-danger">{errors.girlsHostelCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}

                            {formData.boysHostelCount > 0 && (
                              <Col lg="12" className="mt-4">

                                <div className="card shadow">
                                  <div className="m-2 d-flex justify-content-end ">
                                    <Button
                                      color="grey"
                                      size="sm"
                                      onClick={() => setFormData((prev) => ({
                                        ...prev,
                                        boysHostelCount: 0,
                                        boysHostels: [],
                                      }))}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    >
                                      X
                                    </Button>
                                  </div>
                                  <div className="card-body">
                                    <div >
                                      <table style={{ width: '100%', borderCollapse: 'collapse' }} className="table table-bordered">
                                        <thead>
                                          <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                                            <td colSpan="5" className="text-center">Boys Hostel Details</td>
                                          </tr>
                                          <tr style={{ backgroundColor: "#e9ecef", fontWeight: "bold" }}>
                                            <th style={{ width: "30%" }}>Hostel Name</th>
                                            <th style={{ width: "20%" }}>Seats</th>
                                            <th style={{ width: "20%" }}>Hostel Category</th>
                                            <th style={{ width: "5%" }}>Action</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {Array.isArray(formData.boysHostels) && formData.boysHostels.length > 0 ? (
                                            formData.boysHostels.map((hostel, index) => (
                                              <tr key={`boys-${index}`}>
                                                <td>
                                                  <Input
                                                    type="text"
                                                    name="name"
                                                    value={hostel.name}
                                                    placeholder={hostel.name ? `Hostel: ${hostel.name}` : "Enter hostel name"}

                                                    // placeholder={`Boys Hostel Name ${index + 1}`}
                                                    onChange={(e) => handleBoysHostelChange(index, "name", e.target.value)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                                  />
                                                </td>
                                                <td>
                                                  <Input
                                                    type="text"
                                                    onKeyPress={handleKeyPress}
                                                    name="seats"
                                                    value={hostel.seats}
                                                    placeholder={`Seats ${index + 1}`}
                                                    onChange={(e) => handleBoysHostelChange(index, "seats", e.target.value)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                                  />
                                                </td>
                                                <td>
                                                  <Input
                                                    type="select"
                                                    id="hostalCategory"
                                                    value={hostel.hostalCategory}
                                                    onChange={(e) =>
                                                      handleBoysHostelChange(index, "hostalCategory", e.target.value)
                                                    }
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                                  >
                                                    <option value="">Select Category</option>
                                                    {categories.map((cat, idx) => (
                                                      <option key={idx} value={cat}>
                                                        {cat}
                                                      </option>
                                                    ))}
                                                  </Input>
                                                </td>
                                                <td className="text-center align-middle">
                                                  <button
                                                    type="button"
                                                    className="btn btn-sm btn-danger"
                                                    onClick={() => removeHostel(index)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}

                                                  >
                                                    &times;
                                                  </button>
                                                </td>
                                              </tr>
                                            ))
                                          ) : (
                                            <tr>
                                              <td colSpan="3" className="text-center text-muted">No hostel entries added.</td>
                                            </tr>
                                          )}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>

                                </div>
                              </Col>
                            )}
                            {formData.girlsHostelCount > 0 && (
                              <Col lg="12" className="mt-4">
                                <div className="card shadow">
                                  <div className="m-2 d-flex justify-content-end ">
                                    <Button
                                      color="gray"
                                      size="sm"
                                      onClick={() => setFormData((prev) => ({
                                        ...prev,
                                        girlsHostelCount: 0,
                                        girlsHostels: [],
                                      }))}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    >
                                      X
                                    </Button>
                                  </div>

                                  <div className="card-body">
                                    <div >
                                      <table style={{ width: '100%', borderCollapse: 'collapse' }} className="table table-bordered">
                                        <thead>
                                          <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                                            <td colSpan="5" className="text-center">Girls Hostel Details</td>
                                          </tr>
                                          <tr style={{ backgroundColor: "#e9ecef", fontWeight: "bold" }}>
                                            <th style={{ width: "30%" }}>Hostel Name</th>
                                            <th style={{ width: "20%" }}>Seats</th>
                                            <th style={{ width: "20%" }}>Hostel Category</th>
                                            <th style={{ width: "5%" }}>Action</th>
                                          </tr>
                                        </thead>
                                        <tbody>
                                          {Array.isArray(formData.girlsHostels) && formData.girlsHostels.length > 0 ? (
                                            formData.girlsHostels.map((hostel, index) => (
                                              <tr key={`girls-${index}`}>
                                                <td>
                                                  <Input
                                                    type="text"
                                                    name="name"
                                                    value={hostel.name}
                                                    placeholder={`Girls Hostel Name ${index + 1}`}
                                                    onChange={(e) => handleGirlsHostelChange(index, "name", e.target.value)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                                  />
                                                </td>
                                                <td>
                                                  <Input
                                                    type="text"
                                                    onKeyPress={handleKeyPress}
                                                    name="seats"
                                                    value={hostel.seats}
                                                    placeholder={`Seats ${index + 1}`}
                                                    onChange={(e) => handleGirlsHostelChange(index, "seats", e.target.value)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                                  />
                                                </td>
                                                <td>
                                                  <Input
                                                    type="select"
                                                    id="hostalCategory"
                                                    value={hostel.hostalCategory}
                                                    onChange={(e) =>
                                                      handleGirlsHostelChange(index, "hostalCategory", e.target.value)
                                                    }
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                                  >
                                                    <option value="">Select Category</option>
                                                    {categories.map((cat, idx) => (
                                                      <option key={idx} value={cat}>
                                                        {cat}
                                                      </option>
                                                    ))}
                                                  </Input>
                                                </td>
                                                <td className="text-center align-middle">
                                                  <button
                                                    type="button"
                                                    className="btn btn-sm btn-danger"
                                                    onClick={() => removeGirlsHostel(index)}
                                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                                  >
                                                    &times;
                                                  </button>
                                                </td>
                                              </tr>
                                            ))
                                          ) : (
                                            <tr>
                                              <td colSpan="3" className="text-center text-muted">No hostel entries added.</td>
                                            </tr>
                                          )}
                                        </tbody>
                                      </table>
                                    </div>
                                  </div>


                                </div>
                              </Col>
                            )}
                            <br></br>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Biometric Attendance</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="biometricAttendance"
                                      id="input-biometricAttendance"
                                      value={"true"}
                                      checked={formData.biometricAttendance === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="biometricAttendance"
                                      id="input-biometricAttendance"
                                      value={"false"}
                                      checked={formData.biometricAttendance === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>
                                </div>
                                {errors.biometricAttendance && (
                                  <small className="text-danger">{errors.biometricAttendance}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3" className="mt-2">
                              <FormGroup>
                                <Label>Wi-Fi available</Label>
                                <Input type="select"
                                  name="wifi"
                                  value={formData.wifi}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                >
                                  <option value="">Select</option>
                                  <option value="Yes">Yes</option>
                                  <option value="No">No</option>
                                  <option value="Partial">Partial</option>
                                </Input>
                                {errors.wifi && (
                                  <small className="text-danger">{errors.wifi}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3" className="mt-2">
                              <FormGroup>
                                <Label>Staff Toilet</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="staffToilet"
                                      id="input-staffToilet"
                                      value={"true"}
                                      checked={formData.staffToilet === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="staffToilet"
                                      id="input-staffToilet"
                                      value={"false"}
                                      checked={formData.staffToilet === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>

                                </div>
                                {errors.staffToilet && (
                                  <small className="text-danger">{errors.staffToilet}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3" className="mt-2">
                              <FormGroup>
                                <Label>Boys Toilet</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="boysToilet"
                                      id="input-boysToilet"
                                      value={"true"}
                                      checked={formData.boysToilet === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="boysToilet"
                                      id="input-boysToilet"
                                      value={"false"}
                                      checked={formData.boysToilet === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>

                                </div>
                                {errors.boysToilet && (
                                  <small className="text-danger">{errors.boysToilet}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3" className="mt-2">
                              <FormGroup>
                                <Label>Girls Toilet</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsToilet"
                                      id="input-girlsToilet"
                                      value={"true"}
                                      checked={formData.girlsToilet === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="girlsToilet"
                                      id="input-girlsToilet"
                                      value={"false"}
                                      checked={formData.girlsToilet === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>

                                </div>
                                {errors.girlsToilet && (
                                  <small className="text-danger">{errors.girlsToilet}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3" className="mt-2">
                              <FormGroup>
                                <Label>Ramp Available</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="rampAvailable"
                                      id="input-rampAvailable"
                                      value={"true"}
                                      checked={formData.rampAvailable === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="rampAvailable"
                                      id="input-rampAvailable"
                                      value={"false"}
                                      checked={formData.rampAvailable === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.thirdInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>

                                </div>
                                {errors.rampAvailable && (
                                  <small className="text-danger">{errors.rampAvailable}</small>
                                )}

                              </FormGroup>
                            </Col>
                          </Row>


                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.thirdInfo === true || institueProfile?.editSection?.thirdInfo === undefined) &&

                        <div className="text-center  mt-4">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"
                            onClick={(e) =>
                              handleSubmit(e, "infrastructureDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "infrastructureDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "infrastructureDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}
                        </div>}
                    </Form>
                  </TabPane>

                  <TabPane tabId="4">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            NAAC Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: true,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: false,


                                }
                              })}
                            // onClick={(e) =>handleChangeEditable(e, "secondInfo")}
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="3">
                              <FormGroup>
                                <label className="form-control-label" htmlFor="eligibleForNAAC">
                                  Eligible for NAAC
                                </label>
                                <div className="row"
                                  style={{ justifyContent: "space-evenly" }}>
                                  <Label check className=" font-weight-bold">
                                    <Input
                                      type="radio"
                                      name="eligibleForNAAC"
                                      value="true"
                                      checked={formData.eligibleForNAAC === "true"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                    />{" "}
                                    Yes
                                  </Label>

                                  <Label check className=" font-weight-bold">
                                    <Input
                                      type="radio"
                                      name="eligibleForNAAC"
                                      value="false"
                                      checked={formData.eligibleForNAAC === "false"}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                    />{" "}
                                    No
                                  </Label>
                                </div>

                                {errors.eligibleForNAAC && (
                                  <small className="text-danger">{errors.eligibleForNAAC}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.eligibleForNAAC === "true" && (
                              <>

                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label d-block" htmlFor="naacEvaluated">
                                      NAAC Evaluated
                                    </label>
                                    <div className="row"
                                      style={{ justifyContent: "space-evenly" }}>
                                      <Label check className=" font-weight-bold">
                                        <Input
                                          type="radio"
                                          name="naacEvaluated"
                                          value="true"
                                          checked={formData.naacEvaluated === "true"}
                                          onChange={handleInputChange}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                        />{" "}
                                        Yes
                                      </Label>

                                      <Label check className="text-danger font-weight-bold">
                                        <Input
                                          type="radio"
                                          name="naacEvaluated"
                                          value="false"
                                          checked={formData.naacEvaluated === "false"}
                                          onChange={handleInputChange}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                        />{" "}
                                        No
                                      </Label>
                                    </div>
                                    {errors.naacEvaluated && (
                                      <small className="text-danger d-block">{errors.naacEvaluated}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.naacEvaluated === "true" && (
                                  <>
                                    <Col lg="3">
                                      <FormGroup>
                                        <label className="form-control-label" htmlFor="naacYear">
                                          NAAC Evaluation Year
                                        </label>
                                        <Input
                                          id="naacYear"
                                          name="naacYear"
                                          type="text"
                                          onKeyPress={handleKeyPress}
                                          maxLength={4}
                                          value={formData.naacYear}
                                          onChange={handleInputChange}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                        />
                                        {errors.naacYear && <small className="text-danger">{errors.naacYear}</small>}
                                      </FormGroup>
                                    </Col>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label className="form-control-label" htmlFor="naacGrade">
                                          NAAC Grade
                                        </label>
                                        <Input
                                          id="naacGrade"
                                          name="naacGrade"
                                          type="text"
                                          value={formData.naacGrade}
                                          onChange={handleInputChange}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                        />
                                        {errors.naacGrade && <small className="text-danger">{errors.naacGrade}</small>}
                                      </FormGroup>
                                    </Col>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label className="form-control-label d-block " htmlFor="naacEvaluated">
                                          Is NAAC Grade File Upload
                                        </label>
                                        <div className="row"
                                          style={{ justifyContent: "space-evenly" }}>
                                          <Label check className="text-green font-weight-bold">
                                            <Input
                                              type="radio"
                                              name="isGradeCycleFile"
                                              value="true"
                                              checked={formData.isGradeCycleFile === "true"}
                                              onChange={handleInputChange}
                                              disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                            />{" "}
                                            Yes
                                          </Label>

                                          <Label check className="text-danger font-weight-bold">
                                            <Input
                                              type="radio"
                                              name="isGradeCycleFile"
                                              value="false"
                                              checked={formData.isGradeCycleFile === "false"}
                                              onChange={handleInputChange}
                                              disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                            />{" "}
                                            No
                                          </Label>
                                        </div>
                                        {errors.isGradeCycleFile && (
                                          <small className=" d-block">{errors.isGradeCycleFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* === NAAC Details === */}
                                    {formData.isGradeCycleFile === "true" && (
                                      <>

                                        <Col lg="3">
                                          <FormGroup>
                                            <label htmlFor="uploadGradeCycleFile">NAAC Grade Cycle File</label>
                                            <Input
                                              type="file"
                                              name="uploadGradeCycleFile"
                                              accept=".pdf"
                                              onChange={(e) => handleImageChange(e, 6)}
                                              disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fourthInfo === false}
                                            />
                                            <a href={`https://heonline.cg.nic.in/${formData.uploadGradeCycleFile}`} download>
                                              <Button className="btn-sm btn-primary mt-1">
                                                {formData.uploadGradeCycleFile ? <FaDownload size={18} /> : "No File"}
                                              </Button>
                                            </a>
                                            {errors.uploadGradeCycleFile && (
                                              <small className="text-danger">{errors.uploadGradeCycleFile}</small>
                                            )}
                                          </FormGroup>
                                        </Col>
                                        {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.GradeCycleFileName || "No file selected."} disabled />
                                    </Col> */}

                                      </>
                                    )}
                                  </>
                                )}
                              </>
                            )}

                          </Row>

                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.fourthInfo === true || institueProfile?.editSection?.fourthInfo === undefined) &&

                        <div className="text-center">

                          <Button
                            color="info"
                            size="sm"

                            onClick={(e) =>
                              handleSubmit(e, "naacDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "naacDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "naacDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}

                        </div>}
                    </Form>
                  </TabPane>

                  <TabPane tabId="5">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="mt-2">
                          <h4 className="mb-4">
                            <i className="ni ni-single-02 text-primary"></i>{" "}
                            Janbhagidari Samiti Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: true,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: false,


                                }
                              })}
                            // onClick={(e) =>handleChangeEditable(e, "thirdInfo")}
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="3">
                              <FormGroup check className="mb-3">
                                <Label check>
                                  <Input
                                    type="checkbox"
                                    id="isJBRegistered"
                                    name="isJBRegistered"
                                    checked={formData.isJBRegistered === "true"}
                                    onChange={(e) =>
                                      setFormData((prev) => ({
                                        ...prev,
                                        isJBRegistered: e.target.checked ? "true" : "false",
                                      }))
                                    }
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                  />
                                  <span
                                    className={
                                      formData.isJBRegistered === "true"
                                        ? "text-success font-weight-bold ml-2"
                                        : "text-danger font-weight-bold ml-2"
                                    }
                                  >
                                    Janbhagidari Samiti Registered
                                  </span>
                                </Label>
                                {errors.isJBRegistered && (
                                  <div className="text-danger small">{errors.isJBRegistered}</div>
                                )}
                              </FormGroup>
                            </Col>
                          </Row>

                          {formData.isJBRegistered === "true" && (
                            <>
                              <Row>
                                <Col lg="3">
                                  <FormGroup check className="mb-3">
                                    <Label check>
                                      <Input
                                        type="checkbox"
                                        id="isjBSRegisterFile"
                                        name="isjBSRegisterFile"
                                        checked={formData.isjBSRegisterFile === "true"}
                                        onChange={(e) =>
                                          setFormData((prev) => ({
                                            ...prev,
                                            isjBSRegisterFile: e.target.checked ? "true" : "false",
                                          }))
                                        }
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                      />
                                      <span
                                        className={
                                          formData.isjBSRegisterFile === "true"
                                            ? "text-success font-weight-bold ml-2"
                                            : "text-danger font-weight-bold ml-2"
                                        }
                                      >
                                        Is JBS File Upload
                                      </span>
                                    </Label>
                                    {errors.isjBSRegisterFile && (
                                      <div className="text-danger small">{errors.isjBSRegisterFile}</div>
                                    )}
                                  </FormGroup>
                                </Col>
                                {/* === Janbhagidari Details === */}
                                {formData.isjBSRegisterFile === "true" && (
                                  <>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="uploadJBSRegisterFile">JB Samiti Registration File</label>
                                        <Input
                                          type="file"
                                          name="uploadJBSRegisterFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 7)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.uploadJBSRegisterFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.uploadJBSRegisterFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.uploadJBSRegisterFile && (
                                          <small className="text-danger">{errors.uploadJBSRegisterFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.jBSRegisterFileName || "No file selected."} disabled />
                                    </Col> */}

                                  </>
                                )}
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbRegistrationDate">
                                      Janbhagidari Registration Date
                                    </label>
                                    <Input
                                      type="date"
                                      name="jbRegistrationDate"
                                      id="jbRegistrationDate"
                                      value={formData.jbRegistrationDate}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {errors.jbRegistrationDate && (
                                      <small className="text-danger">{errors.jbRegistrationDate}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbPresidentName">
                                      JBS President Name
                                    </label>
                                    <Input
                                      type="text"
                                      name="jbPresidentName"
                                      id="jbPresidentName"
                                      value={formData.jbPresidentName}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {errors.jbPresidentName && (
                                      <small className="text-danger">{errors.jbPresidentName}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </Row>

                              <br></br>
                              <h4 className="mt-4">
                                <i className="ni ni-single-02 text-primary"></i>{" "}
                                Janbhagidari Fund Info

                              </h4>
                              <hr></hr>

                              <Row>


                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbFundYear">
                                      Fund Year
                                    </label>
                                    <Input
                                      type="text"
                                      name="jbFundYear"
                                      id="jbFundYear"
                                      value={formData.jbFundYear || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                      maxLength={4}
                                      onKeyPress={handleKeyPress}
                                    />
                                    {errors.jbFundYear && (
                                      <small className="text-danger">{errors.jbFundYear}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbFundIncome">
                                      Income
                                    </label>
                                    <Input
                                      type="text"
                                      name="jbFundIncome"
                                      onKeyPress={handleKeyOnPress}
                                      id="jbFundIncome"
                                      value={formData.jbFundIncome || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {errors.jbFundIncome && (
                                      <small className="text-danger">{errors.jbFundIncome}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbFundExpenditure">
                                      Expenditure
                                    </label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyOnPress}
                                      name="jbFundExpenditure"
                                      value={formData.jbFundExpenditure || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {errors.jbFundExpenditure && (
                                      <small className="text-danger">{errors.jbFundExpenditure}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbFundBalance">
                                      Balance
                                    </label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyOnPress}
                                      name="jbFundBalance"
                                      id="jbFundBalance"
                                      value={formData.jbFundBalance || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {errors.jbFundBalance && (
                                      <small className="text-danger">{errors.jbFundBalance}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="3">
                                  <FormGroup check className="mb-3">
                                    <Label check>
                                      <Input
                                        type="checkbox"
                                        id="jbFundAuditCompleted"
                                        name="jbFundAuditCompleted"
                                        checked={formData.jbFundAuditCompleted === "true"}
                                        onChange={(e) =>
                                          setFormData((prev) => ({
                                            ...prev,
                                            jbFundAuditCompleted: e.target.checked ? "true" : "false",
                                          }))
                                        }
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                      />
                                      <span
                                        className={
                                          formData.jbFundAuditCompleted === "true"
                                            ? "text-success font-weight-bold ml-2"
                                            : "text-danger font-weight-bold ml-2"
                                        }
                                      >
                                        CA Audit Completed ?
                                      </span>
                                    </Label>
                                    {errors.jbFundAuditCompleted && (
                                      <small className="text-danger">{errors.jbFundAuditCompleted}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {formData.jbFundAuditCompleted === "true" && (
                                  <>
                                    <Col lg="3">
                                      <FormGroup check className="mb-3">
                                        <Label check>
                                          <Input
                                            type="checkbox"
                                            id="isCaAuditFile"
                                            name="isCaAuditFile"
                                            checked={formData.isCaAuditFile === "true"}
                                            onChange={(e) =>
                                              setFormData((prev) => ({
                                                ...prev,
                                                isCaAuditFile: e.target.checked ? "true" : "false",
                                              }))
                                            }
                                            disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                          />
                                          <span
                                            className={
                                              formData.isCaAuditFile === "true"
                                                ? "text-success font-weight-bold ml-2"
                                                : "text-danger font-weight-bold ml-2"
                                            }
                                          >
                                            Is Audit File Upload ?
                                          </span>
                                        </Label>
                                        {errors.isCaAuditFile && (
                                          <small className="text-danger">{errors.isCaAuditFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>
                                    {formData.isCaAuditFile === "true" && (
                                      <>

                                        <Col lg="3">
                                          <FormGroup>
                                            <label htmlFor="uploadCaAuditFile">CA Audit File</label>
                                            <Input
                                              type="file"
                                              name="uploadCaAuditFile"
                                              accept=".pdf"
                                              onChange={(e) => handleImageChange(e, 8)}
                                              disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                            />
                                            <a href={`https://heonline.cg.nic.in/${formData.uploadCaAuditFile}`} download>
                                              <Button className="btn-sm btn-primary mt-1">
                                                {formData.uploadCaAuditFile ? <FaDownload size={18} /> : "No File"}
                                              </Button>
                                            </a>
                                            {errors.uploadCaAuditFile && (
                                              <small className="text-danger">{errors.uploadCaAuditFile}</small>
                                            )}
                                          </FormGroup>
                                        </Col>
                                        {/* <Col sm={3}>
                                    <br></br>
                                      <Input type="text" value={formData.CaAuditFileName || "No file selected."} disabled />
                                    </Col> */}

                                      </>
                                    )}
                                  </>
                                )}

                                <Col lg="3">
                                  <FormGroup>
                                    <label className="form-control-label" htmlFor="jbFundRemark">
                                      Remark
                                    </label>
                                    <Input
                                      type="text"
                                      name="jbFundRemark"
                                      id="jbFundRemark"
                                      value={formData.jbFundRemark || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.fifthInfo === false}
                                    />
                                    {/* {errors.jbFundRemark && (
                                      <small className="text-danger">{errors.jbFundRemark}</small>
                                    )} */}
                                  </FormGroup>
                                </Col>

                              </Row>
                            </>
                          )}


                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.fifthInfo === true || institueProfile?.editSection?.fifthInfo === undefined) &&

                        <div className="text-center">

                          <Button
                            color="info"
                            size="sm"

                            onClick={(e) =>
                              handleSubmit(e, "janbhagidariDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "janbhagidariDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "janbhagidariDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}

                        </div>}
                    </Form>
                  </TabPane>

                  <TabPane tabId="6">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            NSS and NCC Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: true,
                                  seventhInfo: false,
                                  eightInfo: false,


                                }
                              })}
                            // onClick={(e) =>handleChangeEditable(e, "fourthInfo")}
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="3">
                              <FormGroup check className="mb-3">
                                <Label check>
                                  <Input
                                    type="checkbox"
                                    id="nssAvailable"
                                    name="nssAvailable"
                                    checked={formData.nssAvailable === "true"}
                                    onChange={(e) =>
                                      setFormData((prev) => ({
                                        ...prev,

                                        nssAvailable: e.target.checked ? "true" : "false",

                                      }))
                                    }
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                  />
                                  <span
                                    className={
                                      formData.nssAvailable
                                        ? "text-success font-weight-bold ml-2"
                                        : "text-danger font-weight-bold ml-2"
                                    }
                                  >
                                    NSS Available
                                  </span>
                                </Label>
                                {errors.nssAvailable && (
                                  <small className="text-danger">{errors.nssAvailable}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {formData.nssAvailable === "true" && (
                              <>

                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nssEstablishmentDate">Establishment Date</label>
                                    <Input
                                      type="date"
                                      name="nssEstablishmentDate"
                                      id="nssEstablishmentDate"
                                      value={formData.nssEstablishmentDate || ""}
                                      onChange={handleDateChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nssEstablishmentDate && (
                                      <small className="text-danger">{errors.nssEstablishmentDate}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="3">
                                  <FormGroup check className="mb-3">
                                    <Label check>
                                      <Input
                                        type="checkbox"
                                        id="isNssEstablishFile"
                                        name="isNssEstablishFile"
                                        checked={formData.isNssEstablishFile === "true"}
                                        onChange={(e) =>
                                          setFormData((prev) => ({
                                            ...prev,

                                            isNssEstablishFile: e.target.checked ? "true" : "false",

                                          }))
                                        }
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                      />
                                      <span
                                        className={
                                          formData.isNssEstablishFile
                                            ? "text-success font-weight-bold ml-2"
                                            : "text-danger font-weight-bold ml-2"
                                        }
                                      >
                                        Is File Upload(Establishment)
                                      </span>
                                    </Label>
                                    {errors.isNssEstablishFile && (
                                      <small className="text-danger">{errors.isNssEstablishFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                {/* === NSS & NCC Details === */}
                                {formData.isNssEstablishFile === "true" && (

                                  <>
                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="uploadsNssEstablishFile">NSS Establishment File (Incremental)</label>
                                        <Input
                                          id="uploadsNssEstablishFile"
                                          type="file"
                                          name="uploadsNssEstablishFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 9)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.uploadsNssEstablishFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.uploadsNssEstablishFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.uploadsNssEstablishFile && (
                                          <small className="text-danger">{errors.uploadsNssEstablishFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>

                                    {/* <Col sm={3}>
                                      {Array.isArray(formData.uploadsNssEstablishFile) && formData.uploadsNssEstablishFile.length > 0 ? (
                                        <FormGroup>
                                          <Label>Uploaded NSS Establishment Files</Label>
                                          <div className="border p-2 rounded" >
                                            {formData.uploadsNssEstablishFile.map((item, index) => {
                                              let name = "Unnamed file";
                                              // let date = "";

                                              if (typeof item === "string") {
                                                name = item.split('/').pop();
                                              } else if (typeof item === "object") {
                                                name = item.name || item.originalname || "Unnamed file";
                                                // date = item.nssEstablishmentDate || "";
                                              }

                                              return (
                                                <div key={index} className="mb-2">
                                                  <div><Input type="text" value={name || "no such a file"} disabled></ Input></div>
                                                  {/* {date && <div className="ms-2 text-muted">Date: {date}</div>} */}
                                    {/* </div>
                                              );
                                            })}
                                          </div>
                                        </FormGroup>
                                      ) : (
                                        <div>No file uploaded</div>
                                      )}
                                    </Col> */}
                                  </>
                                )}
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nssInChargeName">In-Charge Name</label>
                                    <Input
                                      type="text"
                                      name="nssInChargeName"
                                      id="nssInChargeName"
                                      value={formData.nssInChargeName || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nssInChargeName && (
                                      <small className="text-danger">{errors.nssInChargeName}</small>
                                    )}
                                  </FormGroup>
                                </Col>



                              </>
                            )}
                          </Row>
                          {formData.nssAvailable === "true" && (
                            <>
                              <h4 className="text-primary"> No of NSS Participant students </h4>
                              <hr></hr>
                              <Row>

                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nssBoysCount">Boys Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nssBoysCount"
                                      id="nssBoysCount"
                                      value={formData.nssBoysCount || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nssBoysCount && (
                                      <small className="text-danger">{errors.nssBoysCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nssGirlsCount">Girls Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nssGirlsCount"
                                      id="nssGirlsCount"
                                      value={formData.nssGirlsCount || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nssGirlsCount && (
                                      <small className="text-danger">{errors.nssGirlsCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nssTotalCount">Total Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nssTotalCount"
                                      id="nssTotalCount"
                                      value={formData.nssTotalCount || ""}
                                      onChange={handleInputChange}
                                      disabled
                                    />
                                    {errors.nssTotalCount && (
                                      <small className="text-danger">{errors.nssTotalCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </Row>

                            </>
                          )}
                          <br></br>
                          <Row>
                            <Col lg="3">
                              <FormGroup check className="mb-3">
                                <Label check>
                                  <Input
                                    type="checkbox"
                                    id="nccAvailable"
                                    name="nccAvailable"
                                    checked={formData.nccAvailable === "true"}
                                    onChange={(e) =>
                                      setFormData((prev) => ({
                                        ...prev,

                                        nccAvailable: e.target.checked ? "true" : "false",

                                      }))
                                    }
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                  />
                                  <span
                                    className={
                                      formData.nccAvailable
                                        ? "text-success font-weight-bold ml-2"
                                        : "text-danger font-weight-bold ml-2"
                                    }
                                  >
                                    NCC Available
                                  </span>
                                </Label>
                                {errors.nccAvailable && (
                                  <small className="text-danger">{errors.nccAvailable}</small>
                                )}
                              </FormGroup>

                            </Col>
                            {formData.nccAvailable === "true" && (
                              <>
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nccEstablishmentDate">Establishment Date</label>
                                    <Input
                                      type="date"
                                      name="nccEstablishmentDate"
                                      id="nccEstablishmentDate"
                                      value={formData.nccEstablishmentDate || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nccEstablishmentDate && (
                                      <small className="text-danger">{errors.nccEstablishmentDate}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup check className="mb-3">
                                    <Label check>
                                      <Input
                                        type="checkbox"
                                        id="isNccEstablishFile"
                                        name="isNccEstablishFile"
                                        checked={formData.isNccEstablishFile === "true"}
                                        onChange={(e) =>
                                          setFormData((prev) => ({
                                            ...prev,

                                            isNccEstablishFile: e.target.checked ? "true" : "false",

                                          }))
                                        }
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                      />
                                      <span
                                        className={
                                          formData.isNccEstablishFile
                                            ? "text-success font-weight-bold ml-2"
                                            : "text-danger font-weight-bold ml-2"
                                        }
                                      >
                                        Is Establishment File Upload
                                      </span>
                                    </Label>
                                    {errors.isNccEstablishFile && (
                                      <small className="text-danger">{errors.isNccEstablishFile}</small>
                                    )}
                                  </FormGroup>

                                </Col>
                                {formData.isNccEstablishFile === "true" && (
                                  <>

                                    <Col lg="3">
                                      <FormGroup>
                                        <label htmlFor="uploadsNccEstablishFile">NCC Establishment File (Incremental)</label>
                                        <Input
                                          id="uploadsNccEstablishFile"
                                          type="file"
                                          name="uploadsNccEstablishFile"
                                          accept=".pdf"
                                          onChange={(e) => handleImageChange(e, 10)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                        />
                                        <a href={`https://heonline.cg.nic.in/${formData.uploadsNccEstablishFile}`} download>
                                          <Button className="btn-sm btn-primary mt-1">
                                            {formData.uploadsNccEstablishFile ? <FaDownload size={18} /> : "No File"}
                                          </Button>
                                        </a>
                                        {errors.uploadsNccEstablishFile && (
                                          <small className="text-danger">{errors.uploadsNccEstablishFile}</small>
                                        )}
                                      </FormGroup>
                                    </Col>

                                    {/* <Col sm={3}>
                                      {Array.isArray(formData.uploadsNccEstablishFile) && formData.uploadsNccEstablishFile.length > 0 ? (
                                        <FormGroup>
                                          <Label>Uploaded NCC Establishment Files</Label>
                                          <div className="border p-2 rounded" >
                                            {formData.uploadsNccEstablishFile.map((item, index) => {
                                              let name = "Unnamed file";
                                              
                                              if (typeof item === "string") {
                                                name = item.split('/').pop();
                                              } else if (typeof item === "object") {
                                                name = item.name || item.originalname || "Unnamed file";
                                               
                                              }

                                              return (
                                                <div key={index} className="mb-2">
                                                  <div><Input type="text" value={name} disabled></ Input></div>                                                 
                                                </div>
                                              );
                                            })}
                                          </div>
                                        </FormGroup>
                                      ) : (
                                        <div>No file uploaded</div>
                                      )}
                                    </Col> */}

                                  </>
                                )}
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nccInChargeName">In-Charge Name</label>
                                    <Input
                                      type="text"
                                      name="nccInChargeName"
                                      id="nccInChargeName"
                                      value={formData.nccInChargeName || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nccInChargeName && (
                                      <small className="text-danger">{errors.nccInChargeName}</small>
                                    )}
                                  </FormGroup>
                                </Col>


                              </>
                            )}
                          </Row>
                          {formData.nccAvailable === "true" && (
                            <>

                              <h4 className="text-primary"> No. of NCC Participant students </h4>
                              <hr></hr>
                              <Row>


                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nccBoysCount">Boys Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nccBoysCount"
                                      id="nccBoysCount"
                                      value={formData.nccBoysCount || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nccBoysCount && (
                                      <small className="text-danger">{errors.nccBoysCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nccGirlsCount">Girls Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nccGirlsCount"
                                      id="nccGirlsCount"
                                      value={formData.nccGirlsCount || ""}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.sixthInfo === false}
                                    />
                                    {errors.nccGirlsCount && (
                                      <small className="text-danger">{errors.nccGirlsCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col lg="3">
                                  <FormGroup>
                                    <label htmlFor="nccTotalCount">Total Count</label>
                                    <Input
                                      type="text"
                                      onKeyPress={handleKeyPress}
                                      name="nccTotalCount"
                                      id="nccTotalCount"
                                      value={formData.nccTotalCount || ""}
                                      onChange={handleInputChange}
                                      disabled
                                    />
                                    {errors.nccTotalCount && (
                                      <small className="text-danger">{errors.nccTotalCount}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </Row>
                            </>
                          )}

                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.sixthInfo === true || institueProfile?.editSection?.sixthInfo === undefined) &&

                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            size="sm"
                            color="info"
                            onClick={(e) =>
                              handleSubmit(e, "nssAndNccDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "nssAndNccDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "nssAndNccDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}
                        </div>}
                    </Form>
                  </TabPane>

                  <TabPane tabId="7">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            NSP Registration Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: true,
                                  eightInfo: false,


                                }
                              })}
                            // onClick={(e) =>handleChangeEditable(e, "secondInfo")}
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="4">
                              <FormGroup>
                                <Label>Registered on National Scholarship Portal</Label>
                                <div
                                  className="row"
                                  style={{ justifyContent: "space-evenly" }}
                                >
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="nspRegistered"
                                      id="input-nspRegistered"
                                      value={"true"}
                                      checked={formData.nspRegistered === "true"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                    />
                                    <Label check>Yes</Label>
                                  </FormGroup>
                                  <FormGroup check>
                                    <Input
                                      type="radio"
                                      name="nspRegistered"
                                      id="input-nspRegistered"
                                      value={"false"}
                                      checked={formData.nspRegistered === "false"}
                                      onChange={handleInputChange}
                                      readOnly
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                    />
                                    <Label check>No</Label>
                                  </FormGroup>

                                </div>
                                {errors.nspRegistered && (
                                  <small className="text-danger">{errors.nspRegistered}</small>
                                )}

                              </FormGroup>
                            </Col>
                            {formData.nspRegistered === "true" && (
                              <>

                                <Col lg="4">
                                  <FormGroup>
                                    <Label>NSP Institute ID</Label>
                                    <Input
                                      type="text"
                                      name="nspInstituteId"
                                      value={formData.nspInstituteId}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                    />
                                    {errors.nspInstituteId && (
                                      <small className="text-danger">{errors.nspInstituteId}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="4">
                                  <FormGroup>
                                    <Label>Nodal Officer Name</Label>
                                    <Input
                                      type="text"
                                      name="nodalOfficerName"
                                      value={formData.nodalOfficerName}
                                      onChange={handleInputChange}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                    />
                                    {errors.nodalOfficerName && (
                                      <small className="text-danger">{errors.nodalOfficerName}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="4">
                                  <FormGroup>
                                    <Label>Nodal Officer Mobile</Label>
                                    <Input
                                      type="text"
                                      name="nodalOfficerMobile"
                                      value={formData.nodalOfficerMobile}
                                      maxLength={10}
                                      onChange={(e) => {
                                        const value = e.target.value;

                                        // Check if the input is empty
                                        if (value === "") {
                                          handleInputChange(e);
                                          return;
                                        }

                                        // Regex to match numbers starting with 6, 7, 8, or 9
                                        const validStartRegex = /^[6-9]/;

                                        if (validStartRegex.test(value)) {
                                          // If valid, pass 'true' as the second argument to handleInputChange
                                          handleInputChange(e);
                                        } else {
                                          // Show alert if the input starts with invalid numbers
                                          // alert("Mobile number must start with digits 6, 7, 8, or 9.");
                                          SwalMessageAlert(
                                            " Mobile number must start with digits 6, 7, 8, or 9.",
                                            "warning"
                                          );

                                        }
                                      }}

                                      invalid={!!errors.nodalOfficerMobile}
                                      onKeyPress={handleKeyPress}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                    />
                                    {errors.nodalOfficerMobile && <FormFeedback>{errors.nodalOfficerMobile}</FormFeedback>}

                                  </FormGroup>
                                </Col>

                                <Col lg="4">
                                  <FormGroup>
                                    <Label>Nodal Officer Bio-Auth</Label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="nodalOfficerBioAuth"
                                          id="input-nodalOfficerBioAuth"
                                          value={"true"}
                                          checked={formData.nodalOfficerBioAuth === "true"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="nodalOfficerBioAuth"
                                          id="input-nodalOfficerBioAuth"
                                          value={"false"}
                                          checked={formData.nodalOfficerBioAuth === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                        />
                                        <Label check>No</Label>
                                      </FormGroup>

                                    </div>
                                    {errors.nodalOfficerBioAuth && (
                                      <small className="text-danger">{errors.nodalOfficerBioAuth}</small>
                                    )}
                                  </FormGroup>
                                </Col>

                                <Col lg="4">
                                  <FormGroup>
                                    <Label>Principal Bio-Auth</Label>
                                    <div
                                      className="row"
                                      style={{ justifyContent: "space-evenly" }}
                                    >
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="principalBioAuth"
                                          id="input-principalBioAuth"
                                          value={"true"}
                                          checked={formData.principalBioAuth === "true"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                        />
                                        <Label check>Yes</Label>
                                      </FormGroup>
                                      <FormGroup check>
                                        <Input
                                          type="radio"
                                          name="principalBioAuth"
                                          id="input-principalBioAuth"
                                          value={"false"}
                                          checked={formData.principalBioAuth === "false"}
                                          onChange={handleInputChange}
                                          readOnly
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.seventhInfo === false}
                                        />
                                        <Label check>No</Label>
                                      </FormGroup>

                                    </div>
                                    {errors.principalBioAuth && (
                                      <small className="text-danger">{errors.principalBioAuth}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                              </>
                            )}
                          </Row>


                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.seventhInfo === true || institueProfile?.editSection?.seventhInfo === undefined) &&

                        <div className="text-center">
                          {/* <Button color="primary"
                            size="sm"
                            onClick={toggleModal}>
                            <i className="fa fa-eye"></i>
                          </Button> */}
                          <Button
                            color="info"
                            size="sm"

                            onClick={(e) =>
                              handleSubmit(e, "nspRegistrationDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "nspRegistrationDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "nspRegistrationDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}

                        </div>}
                    </Form>
                  </TabPane>

                  <TabPane tabId="8">
                    <Form className="mt-3">
                      <Card>
                        <CardBody className="pl-lg-4">
                          <h4 className="mb-4">
                            <i className="ni ni-books text-warning mr-2"></i>{" "}
                            Setup Details
                            {institueProfile && institueProfile.isVerified === true && <Button
                              color="warning"
                              className="mr-2 ml-2"
                              size="sm"
                              onClick={() => setInstitueProfile({
                                ...institueProfile,
                                editSection: {
                                  secondInfo: false,
                                  firstInfo: false,
                                  thirdInfo: false,
                                  fourthInfo: false,
                                  fifthInfo: false,
                                  sixthInfo: false,
                                  seventhInfo: false,
                                  eightInfo: true,


                                }
                              })
                              }
                            >
                              Edit
                            </Button>}
                          </h4>
                          <hr />
                          <Row>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Class</Label>
                                <Input
                                  type="select"
                                  name="class"
                                  value={formData.class}
                                  onChange={handleClassInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                >
                                  <option value="">Select Class</option>
                                  {classData &&
                                    classData.length > 0 &&
                                    classData.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.className}
                                      </option>
                                    ))}
                                </Input>


                                {errors.class && (
                                  <small className="text-danger">{errors.class}</small>
                                )}
                              </FormGroup>
                            </Col>
                            {/* {designationData && designationData.length > 0 && ( */}
                              <Col lg="3">
                                <FormGroup>
                                  <Label>Designation</Label>
                                  <Input
                                    type="select"
                                    name="designationsList"
                                    value={formData.designationsList}
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}
                                    onChange={handleInputChange}
                                  >
                                    <option value="">Select Designation</option>
                                    {designationData &&
                                      designationData.length > 0 &&
                                      designationData.map((type, index) => (
                                        <option
                                          key={index}
                                          value={type._id}
                                          selected={
                                            String(type._id) ===
                                              String(
                                                formData.designationsList
                                              )
                                              ? true
                                              : false
                                          }
                                        >
                                          {type.designation}
                                        </option>
                                      ))}
                                  </Input>
                                  {errors.designationsList && (
                                    <small className="text-danger">{errors.designationsList}</small>
                                  )}
                                </FormGroup>
                              </Col>
                            {/* // )} */}
                            <Col lg="3">
                              <FormGroup>
                                <Label>No. of Sanctioned Posts</Label>
                                <Input
                                  type="text"
                                  onKeyPress={handleKeyPress}
                                  name="sanctionedPosts"
                                  value={formData.sanctionedPosts}
                                  onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}
                                />
                                {errors.sanctionedPosts && (
                                  <small className="text-danger">{errors.sanctionedPosts}</small>
                                )}
                              </FormGroup>
                            </Col>

                            <Col lg="3">
                              <FormGroup>
                                <Label>No of working employee</Label>
                                <Input
                                  type="text"
                                  name="working"
                                  onKeyPress={handleKeyPress}
                                  value={formData.working}
                                  onChange={(e) => setFormData({ ...formData, working: e.target.value })}
                                  // onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                />
                                {errors.working && (
                                  <small className="text-danger">{errors.working}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Sanction Order No</Label>
                                <Input
                                  type="text"
                                  name="sanctionOrderNo"
                                  value={formData.sanctionOrderNo}
                                  onChange={(e) => setFormData({ ...formData, sanctionOrderNo: e.target.value })}

                                  // onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                />
                                {errors.sanctionOrderNo && (
                                  <small className="text-danger">{errors.sanctionOrderNo}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3">
                              <FormGroup>
                                <Label>Sanction Date</Label>
                                <Input
                                  type="date"
                                  name="sanctionDate"
                                  onChange={(e) => setFormData({ ...formData, sanctionDate: e.target.value })}

                                  value={formData.sanctionDate}
                                  // onChange={handleInputChange}
                                  disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                />
                                {errors.sanctionDate && (
                                  <small className="text-danger">{errors.sanctionDate}</small>
                                )}
                              </FormGroup>
                            </Col>
                            <Col lg="3" className="mt-4">
                              <FormGroup check className="mb-3">
                                <Label check>
                                  <Input
                                    type="checkbox"
                                    id="isSanctionOrderFile"
                                    name="isSanctionOrderFile"
                                    checked={formData.isSanctionOrderFile === "true"}
                                    onChange={(e) =>
                                      setFormData((prev) => ({
                                        ...prev,

                                        isSanctionOrderFile: e.target.checked ? "true" : "false",

                                      }))
                                    }
                                    disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}
                                  />
                                  <span
                                    className={
                                      formData.isSanctionOrderFile
                                        ? "text-success font-weight-bold ml-2"
                                        : "text-danger font-weight-bold ml-2"
                                    }
                                  >
                                    Is File Upload (Sanction Order)
                                  </span>
                                </Label>
                                {errors.isSanctionOrderFile && (
                                  <small className="text-danger">{errors.isSanctionOrderFile}</small>
                                )}
                              </FormGroup>

                            </Col>
                            {/* === Setup Details === */}
                            {formData.isSanctionOrderFile === "true" && (
                              <>


                                <Col lg="4">
                                  <FormGroup>
                                    <label htmlFor="uploadsSanctionOrderFile">Sanction Order File</label>
                                    <Input
                                      type="file"
                                      name="uploadsSanctionOrderFile"
                                      accept=".pdf"
                                      onChange={(e) => handleImageChange(e, 12)}
                                      disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                    />
                                    <a href={`https://heonline.cg.nic.in/${formData.uploadsSanctionOrderFile}`} download>
                                      <Button className="btn-sm btn-primary mt-1">
                                        {formData.uploadsSanctionOrderFile ? <FaDownload size={18} /> : "No File"}
                                      </Button>
                                    </a>
                                    {errors.uploadsSanctionOrderFile && (
                                      <small className="text-danger">{errors.uploadsSanctionOrderFile}</small>
                                    )}
                                  </FormGroup>
                                </Col>
                                <Col sm={4}>
                                  <Input type="text" value={formData.SanctionOrderFileName || "No file selected."} disabled />
                                </Col>

                              </>
                            )}

                          </Row>
                          <Row>
                            <Col className="text-center mt-4">
                              <Button
                                onClick={handleAddRow1}
                                className={`btn btn-sm ${editIndex !== null ? "btn-warning" : "btn-info"}`}
                                disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                              >
                                {editIndex !== null ? "Update Row" : "Add Row"}
                              </Button>
                              {/* <Button color="primary" className="btn btn-sm" onClick={handleAddRow1}
                                disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                              >
                                Add Row
                              </Button> */}
                            </Col>
                          </Row>


                          <br></br>
                          {showFirstTable ? (
                            <Table responsive bordered>
                              <thead>
                                <tr>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Class</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Designation</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanctioned Posts</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Working</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanction Order No</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanction Date</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Has File</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Uploaded File name</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                {tableData.length > 0 ? (
                                  tableData.map((row, index) => (
                                    <tr key={index}>
                                      <td>
                                        {classData.find(cls => cls._id === row.class)?.className || ""}
                                       
                                      </td>
                                      <td>
                                        {showDesignation.find((type) => String(type._id) === String(row.designationsList))?.designation || "N/A"}
                                        {/* {row.designationName || "N/A"} */}
                                        {/* {designationData.find(des => des._id === row.designationsList)?.designation || ""} */}
                                      </td>
                                      <td>{row.sanctionedPosts}</td>
                                      <td>{row.working}</td>
                                      <td>{row.sanctionOrderNo}</td>
                                      <td>{formatDate(row.sanctionDate)}</td>
                                      <td>{row.isSanctionOrderFile === "true" ? "Yes" : "No"}</td>
                                      <td>{row.SanctionOrderFileName}</td>
                                      <td>
                                        <Button
                                          color="danger"
                                          className="btn btn-sm"
                                          size="sm"
                                          onClick={() => handleRemoveRow1(index)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                        >
                                          X
                                        </Button>
                                      </td>
                                    </tr>
                                  ))
                                ) : (
                                  <tr>
                                    <td colSpan="8" className="text-center">
                                      No data available
                                    </td>
                                  </tr>
                                )}
                              </tbody>
                            </Table>
                          ) : (
                            <Table responsive bordered>
                              <thead>
                                <tr>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Class</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Designation</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanctioned Posts</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Working</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanction Order No</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Sanction Date</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Has File</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Uploaded File name</th>
                                  <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Actions</th>
                                </tr>
                              </thead>
                              <tbody>
                                {setupDetails.map((item, index) => (


                                  <tr key={index}>
                                    <td>
                                      { classData.find(cls => cls._id === item.class)?.className || "N/A"}
                                     
                                    </td>
                                    <td>
                                      {/* {designationData.find((type) => String(type._id) === String(item.designationsList))?.designation || "N/A"} */}
                                      { showDesignation.find((type) => String(type._id) === String(item.designationsList))?.designation || "N/A" }
                                      {/* {designationData.find(des => des._id === row.designationsList)?.designation || ""} */}
                                    </td>
                                    <td>{item.sanctionedPosts}</td>
                                    <td>{item.working}</td>
                                    <td>{item.sanctionOrderNo}</td>
                                    <td>{formatDate(item.sanctionDate)}</td>
                                    <td>{item.isSanctionOrderFile === "true" ? "Yes" : "No"}</td>
                                    <td>{item.SanctionOrderFileName}</td>
                                    <td>
                                      <button type="button"
                                        className="btn btn-sm btn-success" onClick={() => handleEditRow(index)}
                                        disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}
                                      >
                                        Edit
                                      </button>
                                      {/* <Button
                                          color="danger"
                                          className="btn btn-sm ml-2"
                                          size="sm"
                                          onClick={() => handleRemoveRow(index)}
                                          disabled={institueProfile && institueProfile.editSection && institueProfile.editSection.eightInfo === false}

                                        >
                                          X
                                        </Button> */}

                                    </td>
                                  </tr>

                                ))}
                              </tbody>
                            </Table>
                          )}

                        </CardBody>
                      </Card>
                      <br></br>
                      {(institueProfile?.editSection?.eightInfo === true || institueProfile?.editSection?.eightInfo === undefined) &&

                        <div className="text-center">

                          <Button
                            color="info"
                            size="sm"

                            onClick={(e) =>
                              handleSubmit(e, "setupDetails")
                            }
                          >
                            Save as Draft
                          </Button>
                          {institueProfile && institueProfile.isDrafted === true &&
                            <Button
                              color="success"
                              onClick={(e) => {
                                handleSubmit(e, "setupDetails");
                                setTimeout(() => {
                                  handleFinalSubmit(e, "setupDetails");
                                }, 1000); // 1000ms = 1 second
                              }}
                              size="sm"
                            >
                              Submit
                            </Button>}

                        </div>}
                    </Form>
                  </TabPane>


                </TabContent>
                <Modal isOpen={showPreview} toggle={() => setShowPreview(false)} style={{ maxWidth: "70%" }}>
                  <ModalHeader toggle={handlePreview} >
                    Print Preview
                  </ModalHeader>
                  <ModalBody>
                    <div id="print-container">
                      <Row>


                        <Col sm={12}>

                          <div className="profile-table">
                            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                              <tbody>
                                {/* === Section: Basic Details === */}

                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                                  <td colspan="12" style={labelStyle} className="text-center">Basic Details (Part I)</td>
                                </tr>

                                {/* Row 1 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>College Name</td>
                                  <td colSpan="2" style={valueStyle}>{formData.name || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Is College Sanction Letter Uploaded</td>
                                  <td colSpan="2" style={valueStyle}>{formData.isClgFile === "true" ? "Yes" : "No"}</td>
                                  <td colSpan="2" style={labelStyle}>University</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {university.find((type) => String(type._id) === String(formData.university))?.name || "N/A"}
                                  </td>

                                </tr>

                                {/* Row 2 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>AISHE Code</td>
                                  <td colSpan="2" style={valueStyle}>{formData.aisheCode || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Principal Name</td>
                                  <td colSpan="2" style={valueStyle}>{formData.contactPerson || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Principal Mobile No</td>
                                  <td colSpan="2" style={valueStyle}>{formData.contactNumber || "N/A"}</td>

                                </tr>

                                {/* Row 3 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Is Lead College</td>
                                  <td colSpan="2" style={valueStyle}>{formData.isLead === 1 ? "Yes" : "No"}</td>
                                  <td colSpan="2" style={labelStyle}>College Mail ID</td>
                                  <td colSpan="2" style={valueStyle}>{formData.collegeEmail || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Establishment Year</td>
                                  <td colSpan="2" style={valueStyle}>{formData.establishYear || "N/A"}</td>

                                </tr>

                                {/* Row 4 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Is Establishment File Uploaded</td>
                                  <td colSpan="2" style={valueStyle}>{formData.isEstablishFile === "true" ? "Yes" : "No"}</td>
                                  <td colSpan="2" style={labelStyle}>Government E-mail ID 1</td>
                                  <td colSpan="2" style={valueStyle}>{formData.govtEmail1 || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Government E-mail ID 2</td>
                                  <td colSpan="2" style={valueStyle}>{formData.govtEmail2 || "N/A"}</td>

                                </tr>

                                {/* Row 5 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Telephone No.</td>
                                  <td colSpan="2" style={valueStyle}>{formData.telephone || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Division</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {division.find((type) => Number(type.divisionCode) === Number(formData.divison))?.name || "N/A"}
                                  </td>
                                  <td colSpan="2" style={labelStyle}>District</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {district.find((type) => Number(type.LGDCode) === Number(formData.district))?.districtNameEng || "N/A"}
                                  </td>
                                </tr>

                                {/* Row 6 */}
                                <tr>

                                  <td colSpan="2" style={labelStyle}>Vidhansabha Name</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {vidhansabha.find((type) => Number(type.ConstituencyNumber) === Number(formData.vidhansabha))?.ConstituencyName || "N/A"}
                                  </td>
                                  <td colSpan="2" style={labelStyle}>Vidhansabha Number</td>
                                  <td colSpan="2" style={valueStyle}>{formData.ConstituencyNumber || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>GPS Coordinates</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {formData.lat && formData.long ? `Lat: ${formData.lat}, Long: ${formData.long}` : "N/A"}
                                  </td>
                                </tr>

                                {/* Row 7 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Address</td>
                                  <td colSpan="10" style={valueStyle}>{formData.address || "N/A"}</td>
                                </tr>

                                {/* Row 8 */}


                                {/* === Section: More Details === */}

                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }}>
                                  <td colSpan="12" style={labelStyle} className="text-center">Basic Details (Part II)</td>
                                </tr>

                                {/* Row 1 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Registration Number</td>
                                  <td colSpan="2" style={valueStyle}>{formData.registrationNumber || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>Area Type</td>
                                  <td colSpan="2" style={valueStyle}>{formData.areaType || "N/A"}</td>
                                  <td colSpan="2" style={labelStyle}>College Website</td>
                                  <td colSpan="2" style={valueStyle}>{formData.collegeUrl || "N/A"}</td>
                                </tr>

                                {/* Row 2 */}
                                <tr>
                                  <td colSpan="4" style={labelStyle}>DDO Code</td>
                                  <td colSpan="2" style={valueStyle}>{formData.ddoCode || "N/A"}</td>

                                  <td colSpan="4" style={labelStyle}>Block</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {(validBlocks?.find(b => String(b._id) === String(formData.block))?.BlockNameEng) || "N/A"}
                                  </td>
                                </tr>

                                {/* Row 3 */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>College Type</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {formData.collegeType === "1" ? "Govt." :
                                      formData.collegeType === "2" ? "Private" : "N/A"}
                                  </td>
                                  <td colSpan="2" style={labelStyle}>Degree Types</td>
                                  <td colSpan="2" style={valueStyle}>
                                    {formData.degreeTypes === "UG" ? "Under Graduate" :
                                      formData.degreeTypes === "PG" ? "Post Graduate" : "N/A"}
                                  </td>
                                  <td colSpan="2" style={labelStyle}>Demography</td>
                                  <td colSpan="2" style={valueStyle}>{formData.demography || "N/A"}</td>
                                </tr>

                                {/* Row 4 - College Features/Flags */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>College Features</td>
                                  <td colSpan="10" style={valueStyle}>
                                    {[

                                      { id: "isAutonomous", label: "Autonomous College" },
                                      { id: "isModelCollege", label: "Model College" },
                                      { id: "englishMedium", label: "English Medium" },
                                      { id: "sangeetCollege", label: "Sangeet College" },
                                    ].map((item, index) => (
                                      <span key={item.id} className="mr-3">
                                        <span style={{ fontWeight: "bold" }}>{item.label}: </span>
                                        <span style={{ color: formData[item.id] === "true" ? "green" : "red" }}>
                                          {formData[item.id] === "true" ? "true" : "false"}
                                        </span>
                                        {index < 3 ? " | " : ""}
                                      </span>
                                    ))}
                                  </td>
                                </tr>

                                {/* Row 5 - Dates */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Taken Over</td>
                                  <td colSpan="2" style={valueStyle}>{formData.takenOver === "true" ? "Yes" : "No"}</td>
                                  {formData.takenOver === "true" && (

                                    <>
                                      <td colSpan="2" style={labelStyle}>Taken Over Date</td>
                                      <td colSpan="2" style={valueStyle}>{formatDate(formData.takenOverDate)}</td>
                                      <td colSpan="2" style={labelStyle}>Is Taken Over File Upload</td>
                                      <td colSpan="2" style={valueStyle}>{formData.isTakenOverFile === "true" ? "Yes" : "No"}</td>
                                    </>)}

                                </tr>

                                {/* Row 6 - Course Information */}
                                <tr>
                                  {formData.UGC2FRegistered === "true" && (

                                    <>
                                      <td colSpan="2" style={labelStyle}>UGC 2F Date</td>
                                      <td colSpan="2" style={valueStyle}>{formatDate(formData.ugc2FDate)}</td>
                                      <td colSpan="4" style={labelStyle}>Is UGC 2F Registred File Upload</td>
                                      <td colSpan="8" style={valueStyle}>{formData.is2FUgcFile === "true" ? "Yes" : "No"}</td>
                                    </>)}

                                </tr>
                                <tr>

                                  {formData.isRegistered12B === "true" && (
                                    <>
                                      <td colSpan="4" style={labelStyle}>Is 12B Registred File Upload</td>
                                      <td colSpan="2" style={valueStyle}>{formData.is12BFile === "true" ? "Yes" : "No"}</td>
                                      <td colSpan="4" style={labelStyle}>UGC 12B Date</td>
                                      <td colSpan="2" style={valueStyle}>{formatDate(formData.ugc12BDate)}</td>
                                    </>
                                  )}

                                </tr>

                                {/* === Section: NAAC Details === */}
                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                                  <td colSpan="12" style={labelStyle}>NAAC Details</td>
                                </tr>
                                {/* Row 2 - NAAC Details */}

                                <tr>
                                  <td colSpan="4" style={labelStyle}>Eligible for NAAC</td>
                                  <td colSpan="8" style={valueStyle}>
                                    <span className={formData.eligibleForNAAC === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                      {formData.eligibleForNAAC === "true" ? "Yes" : "No"}
                                    </span>
                                  </td>
                                </tr>
                                {formData.eligibleForNAAC === "true" && (
                                  <>
                                    <tr>
                                      <td colSpan="4" style={labelStyle}>NAAC Evaluated</td>
                                      <td colSpan="8" style={valueStyle}>
                                        <span className={formData.naacEvaluated === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                          {formData.naacEvaluated === "true" ? "Yes" : "No"}
                                        </span>
                                      </td>
                                    </tr>
                                    {formData.naacEvaluated === "true" && (
                                      <>
                                        <tr>
                                          <td colSpan="2" style={labelStyle}>NAAC Year</td>
                                          <td colSpan="1" style={valueStyle}>{formData.naacYear || "N/A"}</td>
                                          <td colSpan="1" style={labelStyle}>Grade</td>
                                          <td colSpan="2" style={valueStyle}>{formData.naacGrade || "N/A"}</td>
                                          <td colSpan="4" style={labelStyle}>Is Grade of Latest Cycle File Upload</td>
                                          <td colSpan="2" style={valueStyle}>{formData.isGradeCycleFile === "true" ? "Yes" : "No"}</td>
                                        </tr>
                                      </>
                                    )}
                                  </>
                                )}


                                {/* === Section: Janbhagidari Details === */}
                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                                  <td colSpan="12" style={labelStyle}>Janbhagidari Details</td>
                                </tr>
                                <tr>
                                  <td colSpan="3" style={labelStyle}>Janbhagidari Samiti Registered</td>
                                  <td colSpan="1" style={valueStyle}>
                                    <span className={formData.isJBRegistered === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                      {formData.isJBRegistered === "true" ? "Yes" : "No"}
                                    </span>
                                  </td>

                                  {formData.isJBRegistered === "true" && (
                                    <>
                                      <td colSpan="1" style={labelStyle}>Is JBS File Upload</td>
                                      <td colSpan="1" style={valueStyle}>
                                        <span className={formData.isjBSRegisterFile === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                          {formData.isjBSRegisterFile === "true" ? "Yes" : "No"}
                                        </span>
                                      </td>
                                      <td colSpan="2" style={labelStyle}>Registration Date</td>
                                      <td colSpan="1" style={valueStyle}>{formData.jbRegistrationDate || "N/A"}</td>
                                      <td colSpan="2" style={labelStyle}>President Name</td>
                                      <td colSpan="1" style={valueStyle}>{formData.jbPresidentName || "N/A"}</td>
                                    </>
                                  )}
                                </tr>

                                {/* Row 4 - Janbhagidari Fund Info */}
                                {formData.isJBRegistered === "true" && (
                                  <>
                                    <tr>
                                      <td colSpan="12" style={headerStyle} className="text-center">Janbhagidari Fund Info</td>
                                    </tr>
                                    <tr>
                                      <td colSpan="1" style={labelStyle}>Fund Year</td>
                                      <td colSpan="2" style={valueStyle}>{formData.jbFundYear || "N/A"}</td>
                                      <td colSpan="1" style={labelStyle}>Income</td>
                                      <td colSpan="2" style={valueStyle}>₹ {formData.jbFundIncome || "0"}</td>
                                      <td colSpan="1" style={labelStyle}>Expenditure</td>
                                      <td colSpan="2" style={valueStyle}>₹ {formData.jbFundExpenditure || "0"}</td>
                                      <td colSpan="1" style={labelStyle}>Balance</td>
                                      <td colSpan="2" style={valueStyle}>₹ {formData.jbFundBalance || "0"}</td>
                                    </tr>
                                    <tr>
                                      <td colSpan="2" style={labelStyle}>Audit Completed</td>
                                      <td colSpan="2" style={valueStyle}>
                                        <span className={formData.jbFundAuditCompleted === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                          {formData.jbFundAuditCompleted === "true" ? "Yes" : "No"}
                                        </span>
                                      </td>
                                      <td colSpan="2" style={labelStyle}>Is Audit File Upload ?</td>
                                      <td colSpan="2" style={valueStyle}>{formData.isCaAuditFile === "true" ? "Yes" : "No"}</td>

                                      <td colSpan="2" style={labelStyle}>Remark</td>
                                      <td colSpan="2" style={valueStyle}>{formData.jbFundRemark || "N/A"}</td>
                                    </tr>

                                  </>

                                )}

                                {/* === Section: NSS and NCC Details === */}
                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                                  <td colSpan="12" style={labelStyle}>NSS / NCC Details</td>
                                </tr>
                                <tr>
                                  <td colSpan="12" style={headerStyle} className="text-center">NSS Details</td>
                                </tr>
                                <tr>
                                  <td colSpan="2" style={labelStyle}>NSS Available</td>
                                  <td colSpan="1" style={valueStyle}>
                                    <span className={formData.nssAvailable === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                      {formData.nssAvailable === "true" ? "Yes" : "No"}
                                    </span>
                                  </td>

                                  {formData.nssAvailable === "true" && (
                                    <>
                                      <td colSpan="3" style={labelStyle}>Establishment Date</td>
                                      <td colSpan="2" style={valueStyle}>{formData.nssEstablishmentDate || "N/A"}</td>
                                      <td colSpan="2" style={labelStyle}>In-Charge Name</td>
                                      <td colSpan="2" style={valueStyle}>{formData.nssInChargeName || "N/A"}</td>

                                    </>
                                  )}
                                </tr>
                                {formData.nssAvailable === "true" && (
                                  <>
                                    <tr>
                                      <td colSpan="2" style={labelStyle}>Total Count</td>
                                      <td colSpan="4" style={valueStyle}>
                                        Boys: {formData.nssBoysCount || "0"},
                                        Girls: {formData.nssGirlsCount || "0"},
                                        Total: {formData.nssTotalCount || "0"}
                                      </td>
                                      <td colSpan="4" style={labelStyle}>Is File Upload(Establishment) </td>
                                      <td colSpan="2" style={valueStyle}>{formData.isNssEstablishFile === "true" ? "Yes" : "No"}</td>

                                    </tr>
                                  </>
                                )}

                                {/* Row 6 - NCC Details */}
                                <tr>
                                  <td colSpan="12" style={headerStyle} className="text-center">NCC Details</td>
                                </tr>
                                <tr>
                                  <td colSpan="2" style={labelStyle}>NCC Available</td>
                                  <td colSpan="1" style={valueStyle}>
                                    <span className={formData.nccAvailable === "true" ? "text-success font-weight-bold" : "text-danger font-weight-bold"}>
                                      {formData.nccAvailable === "true" ? "Yes" : "No"}
                                    </span>
                                  </td>

                                  {formData.nccAvailable === "true" && (
                                    <>
                                      <td colSpan="1" style={labelStyle}>Establishment Date</td>
                                      <td colSpan="2" style={valueStyle}>{formData.nccEstablishmentDate || "N/A"}</td>
                                      <td colSpan="2" style={labelStyle}>In-Charge Name</td>
                                      <td colSpan="4" style={valueStyle}>{formData.nccInChargeName || "N/A"}</td>


                                    </>
                                  )}
                                </tr>
                                {formData.nccAvailable === "true" && (
                                  <>

                                    <tr>
                                      <td colSpan="1" style={labelStyle}>Total Count</td>
                                      <td colSpan="4" style={valueStyle}>
                                        Boys: {formData.nccBoysCount || "0"},
                                        Girls: {formData.nccGirlsCount || "0"},
                                        Total: {formData.nccTotalCount || "0"}
                                      </td>
                                      <td colSpan="3" style={labelStyle}>Is File Upload(Establishment) </td>
                                      <td colSpan="4" style={valueStyle}>{formData.isNccEstablishFile === "true" ? "Yes" : "No"}</td>

                                    </tr>
                                  </>
                                )}
                                {/* === Section: Infrastructure === */}
                                <tr style={{ backgroundColor: "#f0f0f0", fontWeight: "bold" }} className="text-center">
                                  <td colSpan="12" style={labelStyle}>Infrastructure Details</td>
                                </tr>


                                {/* Basic Infrastructure */}
                                <tr>
                                  <td colSpan="2" style={labelStyle}>Is Land Alloted</td>
                                  <td colSpan="1" style={valueStyle}>{formData.isLandAlloted === "true" ? "Yes" : "No"}</td>

                                  {formData.isLandAlloted === "true" && (
                                    <>
                                      <td colSpan="1" style={labelStyle}>Land Area (sq.ft)</td>
                                      <td colSpan="1" style={valueStyle}>{formData.landAreaSqFt}</td>
                                      <td colSpan="2" style={labelStyle}>Building Constructed</td>
                                      <td colSpan="2" style={valueStyle}>{formatBoolean(formData.buildingConstructed)}</td>
                                      <td colSpan="2" style={labelStyle}>Is Land Area File Upload</td>
                                      <td colSpan="1" style={valueStyle}>{formData.isLandAreaFile === "true" ? "Yes" : "No"}</td>

                                    </>
                                  )}
                                </tr>
                                <tr >

                                  <td colSpan="2" style={labelStyle}>Number of Teaching Rooms</td>
                                  <td colSpan="2" style={valueStyle}>{formData.teachingRooms}</td>
                                  <td colSpan="2" style={labelStyle}>Number of Labs</td>
                                  <td colSpan="1" style={valueStyle}>{formData.labs}</td>
                                  <td colSpan="2" style={labelStyle}>Girls Common Room</td>
                                  <td colSpan="1" style={valueStyle}>{formatBoolean(formData.girlsCommonRoom)}</td>
                                  <td colSpan="1" style={labelStyle}>Library</td>
                                  <td colSpan="1" style={valueStyle}>{formatBoolean(formData.library)}</td>
                                </tr>

                                <tr>




                                </tr>
                                {formData.library === "true" && (
                                  <tr>

                                    <td colSpan="2" style={labelStyle}>No. of Physical Books</td>
                                    <td colSpan="2" style={valueStyle}>{formData.physicalBooksNo}</td>
                                    <td colSpan="2" style={labelStyle}>No. of Digital Books</td>
                                    <td colSpan="2" style={valueStyle}>{formData.digitalBookNo}</td>
                                    <td colSpan="2" style={labelStyle}>No. of Journals</td>
                                    <td colSpan="2" style={valueStyle}>{formData.journalsNo}</td>
                                  </tr>


                                )}
                                <tr>
                                  <td colSpan="4" style={labelStyle}>Reading Room</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.readingRoom)}</td>
                                  <td colSpan="4" style={labelStyle}>journalsNo Room</td>
                                  <td colSpan="2" style={valueStyle}>{formData.journalsNo}</td>
                                </tr>

                                <tr>
                                  <td colSpan="4" style={labelStyle}>Sports Ground</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.sportsGround)}</td>
                                  <td colSpan="4" style={labelStyle}>Auditorium</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.auditorium)}</td>
                                </tr>

                                {/* Smart Classes */}
                                <tr>
                                  <td colSpan="4" style={labelStyle}>Smart Class</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.smartClass)}</td>
                                  {formData.smartClass === "true" ? (
                                    <>
                                      <td colSpan="4" style={labelStyle}>Number of Smart Classes</td>
                                      <td colSpan="2" style={valueStyle}>{formData.numberOfSmartClass}</td>
                                    </>
                                  ) : (
                                    <td colSpan="2" style={valueStyle}></td>
                                  )}
                                </tr>

                                {/* Hostel Details */}
                                {/* <tr>
                            <td colSpan="4" style={headerStyle}>HOSTEL FACILITIES</td>
                                 </tr> */}

                                <tr>
                                  <td colSpan="4" style={labelStyle}>Hostel Available</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.hostelAvailable)}</td>

                                </tr>

                                {formData.hostelAvailable === "true" && (
                                  <>
                                    <tr>
                                      <td colSpan="4" style={labelStyle}>Boys Hostel Count</td>
                                      <td colSpan="2" style={valueStyle}>{formData.boysHostelCount}</td>
                                      <td colSpan="4" style={labelStyle}>Girls Hostel Count</td>
                                      <td colSpan="2" style={valueStyle}>{formData.girlsHostelCount}</td>
                                    </tr>

                                    {/* Boys Hostels Details */}

                                    {formData.boysHostelCount > 0 && (
                                      <>
                                        <tr>
                                          <td colSpan="12" style={headerStyle}>Boys Hostels Details</td>
                                        </tr>
                                        {formData.boysHostels.map((hostel, index) => (
                                          <tr key={`boys-hostel-${index}`}>
                                            <td colSpan="2" style={labelStyle}>Hostel {index + 1} Name</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.name}</td>
                                            <td colSpan="2" style={labelStyle}>Seats Available</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.seats}</td>
                                            <td colSpan="2" style={labelStyle}>Hostel Category</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.hostalCategory}</td>
                                          </tr>
                                        ))}
                                      </>
                                    )}

                                    {/* Girls Hostels Details */}
                                    {formData.girlsHostelCount > 0 && (
                                      <>
                                        <tr>
                                          <td colSpan="12" style={headerStyle}>Girls Hostels Details</td>
                                        </tr>
                                        {formData.girlsHostels.map((hostel, index) => (
                                          <tr key={`girls-hostel-${index}`}>
                                            <td colSpan="2" style={labelStyle}>Hostel {index + 1} Name</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.name}</td>
                                            <td colSpan="2" style={labelStyle}>Seats Available</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.seats}</td>
                                            <td colSpan="2" style={labelStyle}>Hostel Category</td>
                                            <td colSpan="2" style={valueStyle}>{hostel.hostalCategory}</td>
                                          </tr>
                                        ))}
                                      </>
                                    )}
                                  </>
                                )}

                                {/* Other Facilities */}
                                <tr>
                                  <td colSpan="12" style={headerStyle}>OTHER FACILITIES</td>
                                </tr>
                                <tr>
                                  <td colSpan="4" style={labelStyle}>Biometric Attendance</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.biometricAttendance)}</td>
                                  <td colSpan="4" style={labelStyle}>Wi-Fi</td>
                                  <td colSpan="2" style={valueStyle}>{formData.wifi}</td>
                                </tr>
                                {/* Toilet Facilities */}
                                {/* <tr>
                           <td colSpan="4" style={{...headerStyle, backgroundColor: "#f5f5f5"}}>TOILET FACILITIES</td>
                            </tr> */}

                                <tr>
                                  <td colSpan="4" style={labelStyle}>Staff Toilet</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.staffToilet)}</td>
                                  <td colSpan="4" style={labelStyle}>Boys Toilet</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.boysToilet)}</td>
                                </tr>
                                <tr>
                                  <td colSpan="4" style={labelStyle}>Girls Toilet</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.girlsToilet)}</td>
                                  <td colSpan="4" style={labelStyle}>Ramp Available</td>
                                  <td colSpan="2" style={valueStyle}>{formatBoolean(formData.rampAvailable)}</td>
                                </tr>
                                {/* === Section: NSP Registration === */}
                                <tr style={{
                                  border: '3px solid black',
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }} className="text-center">
                                  <td colSpan="12" style={{ ...labelStyle, width: "16.66%" }}>NSP Registration Details</td>
                                </tr>
                                <tr style={{
                                  border: '1px solid black',
                                  fontSize: "14px",
                                  textAlign: "left",
                                }}>
                                  <td colspan="4" style={labelStyle}>Registered on NSP</td>
                                  <td colspan="2" style={valueStyle}>{formData.nspRegistered === "true" ? "Yes" : "No"}</td>
                                  <td colspan="4" style={labelStyle}>NSP Institute ID</td>
                                  <td colspan="2" style={valueStyle}>{formData.nspInstituteId || "N/A"}</td>
                                </tr>
                                {formData.nspRegistered === "true" && (
                                  <>
                                    <tr style={{
                                      border: '1px solid black',
                                      fontSize: "14px",
                                      textAlign: "left",
                                    }}>
                                      <td colspan="4" style={labelStyle}>Nodal Officer Name</td>
                                      <td colspan="2" style={valueStyle}>{formData.nodalOfficerName || "N/A"}</td>
                                      <td colspan="4" style={labelStyle}>Nodal Officer Mobile</td>
                                      <td colspan="2" style={valueStyle}>{formData.nodalOfficerMobile || "N/A"}</td>
                                    </tr>
                                    <tr>
                                      <td colspan="4" style={labelStyle}>Nodal Officer Bio-Auth</td>
                                      <td colspan="2" style={valueStyle}>{formData.nodalOfficerBioAuth === "true" ? "Yes" : "No"}</td>
                                      <td colspan="4" style={labelStyle}>Principal Bio-Auth</td>
                                      <td colspan="2" style={valueStyle}>{formData.principalBioAuth === "true" ? "Yes" : "No"}</td>
                                    </tr>
                                  </>
                                )}

                                {/* === Section: Setup === */}

                                <tr style={{
                                  border: '3px solid black',
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }} className="text-center">
                                  <td colSpan="12" style={{ ...labelStyle, width: "16.66%" }}>Setup Details</td>
                                </tr>

                              </tbody>
                            </table>
                            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                              <thead>
                                <tr style={{
                                  border: '1px solid black',
                                  fontSize: "14px",
                                  textAlign: "center",
                                  padding: "10px",
                                  backgroundColor: "lightgray",
                                  width: "100%",
                                  verticalAlign: "middle",
                                }}>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Class</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Designation</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Sanctioned Posts</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Working</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Sanction Order No</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Sanction Date</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Has File</th>
                                  <th style={{ backgroundColor: "#f0f0f0", fontWeight: "bold", border: '1px solid black' }}>Uploaded File name</th>

                                </tr>
                              </thead>
                              <tbody>
                                {setupDetails.map((item, index) => (

                                  <tr key={index} tyle={{
                                    border: '1px solid black',
                                    fontSize: "14px",
                                    textAlign: "left",
                                  }}>
                                    <td style={valueStyle}>
                                      {classData.find(cls => cls._id === item.class)?.className || ""}

                                      {/* {formData.class} */}
                                    </td>
                                    <td style={valueStyle}>
                                      {showDesignation.find((type) => String(type._id) === String(item.designationsList))?.designation || "N/A"}

                                      {/* {designationData.find(des => des._id === row.designationsList)?.designation || ""} */}
                                    </td>
                                    <td style={valueStyle}>{item.sanctionedPosts}</td>
                                    <td style={valueStyle}>{item.working}</td>
                                    <td style={valueStyle}>{item.sanctionOrderNo}</td>
                                    <td style={valueStyle}>{formatDate(item.sanctionDate)}</td>
                                    <td style={valueStyle}>{item.isSanctionOrderFile === "true" ? "Yes" : "No"}</td>
                                    <td style={valueStyle}>{item.SanctionOrderFileName}</td>

                                  </tr>

                                ))}
                              </tbody>
                            </table>
                          </div>
                        </Col>

                      </Row>
                    </div>
                  </ModalBody>
                  <ModalFooter>
                    <Button color="secondary" onClick={() => handlePreview()}>
                      Close
                    </Button>
                    <Button color="primary" onClick={(e) =>
                      handleFinalSubmit(e, "finalSubmit")
                    }>
                      Final Submit
                    </Button>
                  </ModalFooter>
                </Modal >
              </CardBody>
            </Card>
          </Col>

        </Row>
      </Container >
    </>
  );
};

export default InstituteProfile;
