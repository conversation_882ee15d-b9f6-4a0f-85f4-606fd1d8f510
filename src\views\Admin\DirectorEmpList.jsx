import { useEffect, useState } from "react";
import {
    <PERSON>,
    CardHeader,
    Input,
    Container,
    Row,
    Col,

    Spinner,
    Button,
} from "reactstrap";

import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import DataTable from "react-data-table-component";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";

import * as XLSX from "xlsx";
import { saveAs } from "file-saver";


const DirectorEmpList = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 5; // Number of items per page for pagination
    const [searchTerm, setSearchTerm] = useState("");
    const [filteredDirector, setFilteredDirector] = useState([]);
    const [director, setDirector] = useState([]);
    const [classData, setClassData] = useState([]);
    const [designations, setDesigantions] = useState({});
    const [filterDesignation, setFilterDesignation] = useState("");
    const [filterClass, setFilterClass] = useState("");
    const [division, setDivision] = useState([]);
    const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
    const[onDeputationFilter,setOnDeputationFilter] = useState("");
    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    const data = response.data;
                    setDivision(data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };
        const fetchClassData = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/class/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`
                    },
                });
                if (response.status === 200) {
                    setClassData(response.data);
                } else {
                    SwalMessageAlert("No Class Data Found", "error");
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };
        const fetchDesignation = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/designation/getAll`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`
                    },
                });
                if (response.status === 200) {
                    setDesigantions(response.data);
                } else {
                    SwalMessageAlert("No Designation Data Found", "error");
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };

        fetchDesignation();
        fetchClassData();
        getDivision();
    }, [endPoint, token]); // Dependencies




    useEffect(() => {
        const fetchDirector = async () => {
            try {
                const params = {};

                if (filterDesignation.trim() !== "") params.designation = filterDesignation.trim();
                if (filterClass.trim() !== "") params.classData = filterClass.trim();
                if (isVerifiedFilter === "true" || isVerifiedFilter === "false" || isVerifiedFilter !== "") {
                    params.verified = isVerifiedFilter === "true" ? true : isVerifiedFilter === "false" ? false : undefined;
                }
                 if (onDeputationFilter === "true" || onDeputationFilter === "false" || onDeputationFilter !== "") {
                    params.onDeputation = onDeputationFilter === "true" ? true : onDeputationFilter === "false" ? false : undefined;
                }
                const response = await axios.get(`${endPoint}/api/director/get-all-director`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    },
                    params,
                });
                if (response.status === 200) {
                    setDirector(response.data);
                    setFilteredDirector(response.data);

                } else {
                    alert("Failed to director  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDirector();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token, filterDesignation, filterClass, isVerifiedFilter, onDeputationFilter]); 


    useEffect(() => {
        const filteredItems = director.filter((item) => {
            return Object.keys(item).some((key) =>
                String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
            );
        });

        setFilteredDirector(filteredItems);
        setCurrentPage(1); // Reset to first page on filter change
    }, [searchTerm, director]);



    const handleClearFilters = () => {
        setFilterClass("");
        setFilterDesignation("");
        setIsVerifiedFilter("");
        setOnDeputationFilter("");
    };



    // Handle Pagination
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    const currentItems = filteredDirector.slice(indexOfFirstItem, indexOfLastItem);
    const totalPages = Math.ceil(director.length / itemsPerPage);

    const handlePageChange = (pageNumber) => setCurrentPage(pageNumber);

    const updateStatus = async (directorId) => {

        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                const response = await axios.put(
                    `${endPoint}/api/director/update-status/${directorId}`,
                    {}, // Pass empty object if no data needs to be sent in the body
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) { // Check the status code to ensure success
                    // window.location.reload();
                    setTimeout(() => window.location.reload(), 2000);
                }
            } else {
                alert("Failed to fetch College data. Please try again.");
            }
        } catch (error) {
            console.error("An error occurred while fetching the data:", error);
            alert("An error occurred. Please try again later.");
        }
    };

    const changePassword = async (id) => {
        try {

            const response = await axios.get(
                `${endPoint}/api/change-password?id=${id}&key=Director`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            if (response.status === 200) {
                const data = response.data // Check the status code to ensure success
                copyToClipboard(data.password);
                SwalMessageAlert("Password Change Successfully", "success");
            } else {
                SwalMessageAlert("Password Change Failed", "error");
            }
        } catch (error) {
            console.error("An error occurred while fetching the data:", error);
            alert("An error occurred. Please try again later.");
        }
    };

    function copyTextFallback(text) {
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand("copy");
            // console.log("Text copied using fallback!");
        } catch (err) {
            console.error("Fallback copy failed:", err);
        }
        document.body.removeChild(textArea);
    }
    const copyToClipboard = (text) => {
        if (navigator.clipboard && navigator.clipboard.writeText) {
            navigator.clipboard.writeText(text)
                .then(() => console.log("Text copied to clipboard!"))
                .catch((err) => console.error("Failed to copy text:", err));
        } else {
            copyTextFallback(text);
        }
    };


    //   For Export Exel Data 

    const exportToExcel = () => {
        try {
            const dataToExport = filteredDirector.map((director, index) => ({
                "S.No": index + 1,
                "Name": director.name,
                "Email": director.email,
                "Contact": director.contact,
                "Emp Code": director.empCode,
                "Section": Array.isArray(director.section)
                    ? director.section
                        .map((sectionId) => {
                            const sectionDetail = director.sectionData?.find(
                                (data) => data._id === sectionId
                            );
                            return sectionDetail ? sectionDetail.sectionName : sectionId;
                        })
                        .join(", ")
                    : "No sections",
                "Class":
                    classData.find((cls) => cls._id === director.classData)?.className || "N/A",
                "Designation": director.designationName,
                "Posting Location": director.postingLocation,
                "Division": division.find((d) => d.divisionCode === director.divison)?.name || "N/A",
                "District": director.districtName || "N/A",
                "Vidhan Sabha": director.vidhansabhaName || "N/A",
                "On Deputation": director.onDeputation ? "Yes" : "No",
                "Verified": director.verified ? "Yes" : "No"
            }));

            const worksheet = XLSX.utils.json_to_sheet(dataToExport);
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Employees");

            const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
            const blob = new Blob([excelBuffer], {
                type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            });

            saveAs(blob, "Directorate_Employees.xlsx");
        } catch (error) {
            console.error("Excel export failed:", error);
            alert("Failed to export data. See console for details.");
        }
    };



    const columns = [
        { name: "S.No", selector: (row, index) => index + 1, sortable: true, width: "80px" },
        {
            name: "Action",
            width: "190px",
            cell: (filteredDirector) => (
                <div className="d-flex gap-2">
                    {filteredDirector.onDeputation === false ? (
                        <>
                            {filteredDirector.verified === false ? (
                                <button className="btn btn-warning btn-sm" onClick={() => updateStatus(filteredDirector._id)}>
                                    <Spinner size="sm" color="white" style={{ marginRight: '8px' }} />Verify
                                </button>
                            ) : (
                                <button className="btn btn-success btn-sm">Verified</button>
                            )}
                            <Link to={`/admin/update-director/${filteredDirector._id}`}>
                                <button className="btn btn-warning btn-sm">Edit</button>
                            </Link>
                            <button className="btn btn-primary btn-sm ml-2" onClick={() => changePassword(filteredDirector._id)}>
                                <span className="fa fa-lock"></span>
                            </button>
                        </>
                    ) : (
                        <button className="btn btn-warning btn-sm">On Deputed</button>
                    )}
                </div>
            ),
            ignoreRowClick: true,
            allowOverflow: true,
            button: true,
        },
        {
            name: "Basic Detail",
            width: "270px",
            cell: (filteredDirector) => (
                <div >
                    <div className="mb-2">
                        <strong>Name: </strong> {filteredDirector.name}

                    </div>
                    <div className="mb-2">
                        <strong>Email: </strong> {filteredDirector.email}

                    </div>
                    <div className="mb-2">
                        <strong>Contact: </strong> {filteredDirector.contact}

                    </div>
                    <div className="mb-2">
                        <strong>Emp Code: </strong> <strong className=" text-primary">{filteredDirector.empCode}</strong>

                    </div>
                </div>
            ),
            sortable: true,
        },
        {
            name: "Posting Details",
            cell: (filteredDirector) => (
                <div className="m-2">
                    <div className="mb-2">
                        <strong>Section: </strong>
                        {Array.isArray(filteredDirector.section) && filteredDirector.section.length > 0
                            ? filteredDirector.section.map((sectionId) => {
                                const sectionDetail = filteredDirector.sectionData?.find((data) => data._id === sectionId);
                                return sectionDetail ? sectionDetail.sectionName : sectionId;
                            }).join(", ")
                            : "No sections available"}{" "}

                    </div>
                    <div className="mb-2">
                        <strong>Class: </strong>
                        {classData.find((type) => type._id === filteredDirector.classData)?.className || "N/A"}
                    </div>
                    <div className="mb-2">
                        <strong>Designation: </strong> {filteredDirector.designationName}


                    </div>
                    <div className="mb-2">
                        <strong>Posting Location: </strong> {filteredDirector.postingLocation}

                    </div>
                </div>
            ),
            sortable: true,
        },
        {
            name: "Division/ District / VidhanSabha",
            cell: (filteredDirector) => (
                <>
                    {division.find((type) => type.divisionCode === filteredDirector.divison)?.name} /
                    {filteredDirector.districtName} / {filteredDirector.vidhansabhaName}
                </>
            ),
            sortable: true,
        },
    ];




    return (
        <>
            <Header />


            {/* Page content */}
            <Container className="mt--7" fluid>
                <Row >

                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0 d-flex">
                                <Col md={7}> <h3 className="mb-0">Directorate Employees List</h3></Col>
                                <Col md={2}> <Button className=" btn-sm bg-primary btn-outline-white text-white" onClick={exportToExcel} >Export to Excel</Button> </Col>

                                <Col md={3}>
                                    <Input
                                        type="text"
                                        placeholder="Search Director.."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        style={{ marginBottom: "10px" }}
                                    />
                                </Col>

                            </CardHeader>
                            <Row className="ml-4">
                                <Col xs="3">
                                <label>Filter by Designation</label>
                                    <select value={filterDesignation} onChange={(e) => setFilterDesignation(e.target.value)} className="form-control">
                                        <option value="">Select Designation</option>
                                        {designations &&
                                            designations.length > 0 &&
                                            designations.map((type, index) => (
                                                <option key={index} value={type._id}>
                                                    {type.designation}
                                                </option>
                                            ))}
                                    </select>
                                </Col>
                                <Col xs="2">
                                <label>Filter by Class</label>
                                    <Input
                                        id="filterClass"
                                        type="select"
                                        value={filterClass}
                                        onChange={(e) => setFilterClass(e.target.value)}
                                        placeholder="Enter class"
                                    >
                                        <option value="">Select Class</option>
                                        {classData &&
                                            classData.length > 0 &&
                                            classData.map((type, index) => (
                                                <option key={index} value={type._id}>
                                                    {type.className}
                                                </option>
                                            ))}
                                    </Input>
                                </Col>
                                <Col xs="2">
                                <label>Filter by Verify</label>
                                    <select value={isVerifiedFilter}
                                        onChange={(e) => setIsVerifiedFilter(e.target.value)} className="form-control">
                                        <option value=""><strong>All</strong></option>
                                        <option value="true"><strong>Verified</strong></option>
                                        <option value="false"><strong>Not Verified</strong></option>
                                    </select>
                                </Col>
                                <Col xs="2">
                                <label>Filter by Deputation</label>
                                    <select value={onDeputationFilter}
                                        onChange={(e) => setOnDeputationFilter(e.target.value)} className="form-control">
                                        <option value=""><strong>All</strong></option>
                                        <option value="true"><strong>On Deputation</strong></option>
                                        <option value="false"><strong>Not onDeputation</strong></option>
                                    </select>
                                </Col>
                                <Col xs="2">
                                    <br></br>
                                    <Button onClick={handleClearFilters} size="sm" className="btn btn-danger mt-2">
                                        Clear Filters
                                    </Button>

                                </Col>
                            </Row>

                            <DataTable
                                columns={columns}
                                data={filteredDirector}
                                pagination
                                highlightOnHover
                                striped
                                responsive
                                className="mt-3 shadow-sm rounded"
                            />
                            {/* <Table className="align-items-center table-flush" responsive>
                                <thead className="thead-light">
                                    <tr>
                                        <th>sno</th>
                                        <th scope="col">Action</th>
                                        <th scope="col">Basic Detail</th>
                                        <th scope="col">Posting Details</th>
                                        <th scope="col">Division/ District / VidhanSabha</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    {currentItems && currentItems.length > 0 && currentItems.map((director, index) => (
                                        <tr key={index}>
                                            <td>{indexOfFirstItem + index + 1}</td>
                                            {director.onDeputation === false && <td>
                                                {director.verified === false ? <button className="btn btn-warning btn-sm" onClick={() => updateStatus(director._id)}><Spinner size="sm" color="white" style={{ marginRight: '8px' }} />Verify</button> : <button className="btn btn-success btn-sm" >Verified</button>}
                                                <Link to={`/admin/update-director/${director._id}`}>
                                                    <button className="btn btn-warning btn-sm">Edit</button>
                                                </Link>
                                                &nbsp;
                                                &nbsp;
                                                {<button className="btn btn-primary btn-sm" onClick={() => changePassword(director._id)}><span className="fa fa-lock"></span></button>}
                                            </td>}
                                            {director.onDeputation === true &&  <td>
                                                <button className="btn btn-warning btn-sm">on Deputed</button>
                                            </td>}
                                            <td><strong> Name : </strong> {director.name} <br />
                                                <strong> Email :</strong>{director.email} <br />
                                                <strong> Contact :</strong>{director.contact} <br />
                                                <strong> Emp Code :</strong> <span style={{ color: "blue", fontWeight: "bolder", backgroundColor: "lightgray" }}>{director.empCode} </span><br />
                                            </td>
                                            <td><strong> Section : </strong>{Array.isArray(director.section) && director.section.length > 0
                                                ? director.section.map((sectionId) => {
                                                    const sectionDetail = director.sectionData?.find(
                                                        (data) => data._id === sectionId
                                                    );
                                                    return sectionDetail ? sectionDetail.sectionName : sectionId;
                                                }).join(", ")
                                                : "No sections available"} <br />
                                                <strong> Class :</strong>{classData.find(
                                                    (type) => type._id === director.classData
                                                )?.className || "N/A"}<br />
                                                <strong> Designation :</strong>{director.designationName} <br />
                                                <strong> Posting Location :</strong>{director.postingLocation} <br />
                                            </td>
                                            <td>
                                                {division.find((type) => type.divisionCode === director.divison
                                                )?.name}

                                                /{director.districtName}


                                                /{director.vidhansabhaName}</td>

                                        </tr>
                                    ))}
                                </tbody>
                            </Table>
                            <CardFooter className="py-4">
                                <nav aria-label="...">
                                    <Pagination className="pagination justify-content-end mb-0">
                                        {[...Array(totalPages)].map((_, i) => (
                                            <PaginationItem key={i} className={currentPage === i + 1 ? "active" : ""}>
                                                <PaginationLink
                                                    href="#"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        handlePageChange(i + 1);
                                                    }}
                                                >
                                                    {i + 1}
                                                </PaginationLink>
                                            </PaginationItem>
                                        ))}
                                    </Pagination>
                                </nav>
                            </CardFooter> */}
                        </Card>


                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default DirectorEmpList;
