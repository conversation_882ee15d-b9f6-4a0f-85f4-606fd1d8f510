﻿<%@ Page Title="" Language="C#" MasterPageFile="~/CandMaster.master" AutoEventWireup="true" CodeFile="candidate.aspx.cs" Inherits="Admin_CandMaster" %>

<asp:Content ID="Content1" ContentPlaceHolderID="cpHeaderSection" runat="Server">
    <style type="text/css">
        input[type=checkbox], input[type=radio] {
            margin: 4px 10px 0;
        }
    </style>
</asp:Content>
<asp:Content ID="Content2" ContentPlaceHolderID="mainContent" runat="Server">
    <div class="container" id="dvMsg" runat="server">
        <div class="panel panel-primary" style="margin-top: 50px">
            <div class="panel-heading">
                <h5 style="font-size: large"></h5>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="sirow clrleft mb_10">
                            <asp:Label runat="server" ID="lblmg" ForeColor="Red" Font-Bold="true">आपने अभी तक फाइनल सबमिट नहीं किया है| कृपया फाइनल सबमिट करें|</asp:Label>&nbsp;&nbsp; 
                            <a href="<%=updateurl %>">क्लिक करें</a>
                        </div>
                        <br />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container" id="dvupdate" runat="server">
        <div class="panel panel-primary" style="margin-top: 5px">
            <div class="panel-heading">
                <h5 style="font-size: large">ऑनलाइन आवदेन त्रुटि सुधारे</h5>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-5">
                        <div class="sirow clrleft mb_10">
                            <asp:CheckBox runat="server" ID="chkupdate" Text="क्या आप अपने आवेदन की त्रुटि सुधार करना चाहते है ?" Style="padding: 10px;" AutoPostBack="true" OnCheckedChanged="chkupdate_CheckedChanged" />
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="sirow clrleft mb_10">
                            <asp:Button CssClass="btn btn-info" runat="server" ID="btnredirect" Text="अपडेट करे" OnClick="btnredirect_Click" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <%--<div class="container" id="noti_cont" runat="server">
        <div class="panel panel-primary" style="margin-top: 50px">
            <div class="panel-heading">
                <h5 style="font-size: large">ऑनलाइन आवदेन निर्देश</h5>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="sirow clrleft mb_10">
                            <label>ऑनलाइन आवदेन करने के निर्देश के लिए यहाँ</label>&nbsp;&nbsp; <a href="nirdesh/nirdesh.pdf" target="_blank">क्लिक करें</a>
                        </div>
                        <br />
                    </div>
                    <div class="col-md-12">
                        <div class="sirow clrleft mb_10">
                            <asp:CheckBox runat="server" ID="chknirdesh" Text="मेरे द्वारा सभी दिशा निर्देश पढ़ लिए गए है|" Style="padding: 10px;" AutoPostBack="true" OnCheckedChanged="chknirdesh_CheckedChanged" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>--%>
    <div class="container" id="dvfinal" runat="server">
        <div class="panel panel-primary" style="margin-top: 5px">
            <div class="panel-heading">
                <h5 style="font-size: large">आपने आवेदन भर दिया है। कृपया यहां से पीडीएफ डाउनलोड करें।</h5>
            </div>
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="sirow clrleft mb_10">
                            <a href="ApplicationPrintOfCandidate.aspx">Download Application</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</asp:Content>
<asp:Content ID="Content3" ContentPlaceHolderID="cpFooter" runat="Server">
</asp:Content>

