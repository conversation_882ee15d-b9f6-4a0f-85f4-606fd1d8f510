import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardBody,
  FormGroup,
  Form,
  Input,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Row,
  Col,
  Container,
  UncontrolledCollapse,
  NavbarBrand,
  Navbar,
  NavItem,
  NavLink,
  Nav,
  Label,
  Modal,
  ModalHeader,
  <PERSON>dalBody,
  ModalFooter,
} from "reactstrap";

import { useEffect, useState } from "react";
import { FaUser } from "react-icons/fa";
import axios from "axios";
import { Link, useNavigate } from "react-router-dom";
import logo from "../../assets/img/brand/CGGov.png";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import Swal from "sweetalert2";

const defaultErrors = {
  name: "",
  empCode: "",
  email: "",
  contact: "",
  divison: "",
  district: "",
  vidhansabha: "",
  college: "",
  designation: "",
  class: "",
  address: "",
  workType: "",
  gender: "",
  title: "",
};
const Register = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const inputStyle = {
    color: "gray", // Set the desired gray color
    borderColor: "#ccc", // Optional: Set border color for better visibility
  };
  const [formData, setFormData] = useState({
    name: "",
    empCode: "",
    email: "",
    contact: "",
    divison: "",
    district: "",
    vidhansabha: "",
    designation: "",
    class: "",
    address: "",
    workType: "",
    college: "",
    currentSalary: "",
    nextIncrementDate: "",
    applicableNextIncrement: "",
    gender: "",
    title: "",
    onDeputation: "false",
    deputedDesignation: "",
    postingLocation: "",
    employeeType: "INSTITUTE",
  });
  const [district, setDistrict] = useState([]);
  const [vidhansabha, setVidhansabha] = useState([]);

  const [classData, setClassData] = useState([]);

  const [division, setDivision] = useState([]);
  const navigate = useNavigate();
  const [resendTimer, setResendTimer] = useState(0); // Timer in seconds
const [verifyLoading, setVerifyLoading] = useState(false);






  const startResendTimer = () => {
    setResendTimer(180); // 180 seconds = 3 minutes
    const timer = setInterval(() => {
      setResendTimer((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };


  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
    getDivision();
  }, [endPoint, token]); // Dependencies
  const handleDivisionChange = async (e) => {
    const { name, value } = e.target;
    setErrors({ ...errors, [name]: "" });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/get-division-district/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setDistrict(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      divison: value,
    });
  };

  const handleDistrictChange = async (e) => {
    const { name, value } = e.target;
    setErrors({ ...errors, [name]: "" });
    setFormData({
      ...formData,
      district: value,
    });
    try {
      const response = await axios.get(
        `${endPoint}/api/district/getVidhansabha-district-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setVidhansabha(response.data);
        // navigate("admin/Dashboard");
      } else {
        alert("Login failed. Please check your credentials and try again.");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
  };
  const [college, setCollege] = useState([]);

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchCollege();
  }, [endPoint, token]);

  const [errors, setErrors] = useState(defaultErrors);

  // --- OTP State and Logic ---
  const [otpModal, setOtpModal] = useState(false);
  const [otp, setOtp] = useState("");
  const [otpError, setOtpError] = useState("");
  const [otpLoading, setOtpLoading] = useState(false);
  const [finalSubmitLoading, setFinalSubmitLoading] = useState(false);

  const handleSendOtp = async () => {
    setOtpLoading(true);
    setOtpError("");
    try {
      // Ensure mobile is present and valid before sending OTP
      if (!formData.contact || !/^[6-9]\d{9}$/.test(formData.contact)) {
        setOtpError("Valid mobile number is required.");
        setOtpLoading(false);
        return;
      }
    
      const response = await axios.post(
        `${endPoint}/api/send-otp`,
        {},
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
   
          },
          params: {
            mobile: formData.contact,
            userType: "Employee", // or the correct userType for your flow
          },
        }
      );
      if (response.status === 200 && response.data.success) {
        setOtpModal(true);
        setOtp("");
        Swal.fire({
          html: `<div class="p-0 m-0">
            OTP has been sent to <strong>${formData?.contact?.toString().substring(0, 2)}XXXXXX${formData?.contact?.toString().substring(8)}</strong>
          </div>`,
          icon: "success",
          confirmButtonColor: '#0d6efd',
          confirmButtonText: "OK",
          timer: 5000, 
          timerProgressBar: true, 
        });


        startResendTimer();

      } else {
        setOtpError(response.data.message || "Failed to send OTP. Please try again.");
      }
    } catch (err) {
      setOtpError(
        err?.response?.data?.message || "Failed to send OTP. Please try again."
      );
    } finally {
      setOtpLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    setOtpLoading(true);
    setOtpError("");
    try {
      if (!formData.contact || !otp) {
        setOtpError("Mobile number and OTP are required.");
        setOtpLoading(false);
        setVerifyLoading(true);

        return;
      }
      const response = await axios.post(
        `${endPoint}/api/verify-otp`,
        {
          mobile: formData.contact,
          otp,
        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
          
          },
        }
      );
      if (response.status === 200 && response.data.success) {
        setVerifyLoading(false);
        setOtpModal(false);
        SwalMessageAlert("OTP verified successfully!", "success");
        // Automatically submit after OTP is validated
        await handleFinalSubmit();
      } else {
        setOtpError(response.data.message || "Invalid OTP. Please try again.");
        setVerifyLoading(false);
      }
    } catch (err) {
      setOtpError(
        err?.response?.data?.message || "Invalid OTP. Please try again."
         
      );
    } finally {
      setOtpLoading(false);
      setVerifyLoading(false);
    }
  };

  const handleSubmit = async () => {
    setOtpError("");
    setOtpModal(false);
    setOtp("");
    await handleSendOtp();
  };

  const handleFinalSubmit = async () => {
    setFinalSubmitLoading(true);
    try {
      const body = {
        name: formData.name,
        empCode: String(formData.empCode),
        email: formData.email,
        contact: String(formData.contact),
        divison: String(formData.divison),
        district: String(formData.district),
        vidhansabha: String(formData.vidhansabha),
        college: String(formData.college),
        designation: formData.designation,
        classData: String(formData.class),
        address: formData.address,
        workType: String(formData.workType),
        currentSalary: String(formData.currentSalary),
        nextIncrementDate: String(formData.nextIncrementDate),
        applicableNextIncrement: String(formData.applicableNextIncrement),
        gender: String(formData.gender),
        title: String(formData.title),
        onDeputation: String(formData.onDeputation),
        deputedDesignation: String(formData.deputedDesignation),
        postingLocation: String(formData.postingLocation),
        employeeType: String(formData.employeeType),
      };
      const response = await axios.post(
        `${endPoint}/api/employee/add`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
          },
        }
      );
      if (response.status === 201) {
        window.setTimeout(function () {
          SwalMessageAlert("Registration Done Successfully", "success");
          window.setTimeout(function () {
            navigate("/");
          }, 1000);
        }, 1000);

      } else {
        alert("Registration Failed Please Try Again!");
      }
    } catch {
      alert("An error occurred. Please try again later.");
    } finally {
      setFinalSubmitLoading(false);
    }
  };

  const [designationData, setDesignationData] = useState([]);
  const handleClassInputChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/degisnation-class-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            'web-url': window.location.href,
          },
        }
      );
      if (response.status === 200) {
        setDesignationData(response.data);
      } else {
        SwalMessageAlert("Designation Not Found", "error");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      class: value,
    });
  };
  const [modalCheckEmpCodeChange, setModalCheckEmpCodeChange] = useState(false);
  useEffect(() => {
    setModalCheckEmpCodeChange(true);
  }, []);

  const toggleModalCheckEmpCodeChange = () =>
    setModalCheckEmpCodeChange(!modalCheckEmpCodeChange);

  const [empCodeformData, setEmpCodeformData] = useState({
    employeeCode: "",
    mobile: "",
  });
  const [employeeCodeerrors, setEmployeeCodeErrors] = useState(empCodeformData);
  const validateEmpCodeFields = () => {
    const newErrors = {};

    Object.keys(empCodeformData).forEach((key) => {
      if (!empCodeformData[key]) {
        newErrors[key] = "This field is required";
      }
    });

    setEmployeeCodeErrors(newErrors);
    return Object.keys(newErrors).length === 0; // Returns true if there are no errors
  };

  const handleCheckEmployeeCode = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    e.preventDefault();
    if (!validateEmpCodeFields()) {
      return;
    }
    // Create an array of the field values to validate
    const valuesToValidate = [
      empCodeformData.employeeCode,
      empCodeformData.mobile,
    ];

    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    // Condition for empty fields
    if (hasEmptyFields) {
      SwalMessageAlert(
        "Please fill out all fields before submitting!",
        "error"
      );
      return; // Prevent form submission
    }

    try {
      const response = await axios.get(
        `${endPoint}/api/employee-code/check?empCode=${empCodeformData.employeeCode}&contact=${empCodeformData.mobile}&processBy=self`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      // if (response.status === 200) {
      //   if (response.data.msg === "FOUND") {
      //     const empCode = response.data.getEmployeeCode["empCode"];
      //     const contact = response.data.getEmployeeCode["contact"];
      //     const name = response.data.getEmployeeCode["name"];
      //     const college = response.data.getEmployeeCode["college"];

      //     SwalMessageAlert("Employee Code Found", "success");
      //     setModalCheckEmpCodeChange(!modalCheckEmpCodeChange);
      //     setFormData({
      //       ...formData, // Keep existing form data
      //       empCode: empCode,
      //       contact: contact,
      //       name: name,
      //       college: college,
      //     });
      //   } else if (response.data.msg === "FOUND BUT USED") {
      //     const getCollegeDetail = await axios.get(
      //       `${endPoint}/api/college/get-single-college/${response.data.getEmployeeDetails["college"]}`,
      //       {
      //         headers: {
      //           "Content-Type": "application/json",
      //           'web-url': window.location.href,
      //           Authorization: `Bearer ${token}`,
      //         },
      //       }
      //     );
      //     if (getCollegeDetail) {
      //       const string =
      //         response.data.getEmployeeDetails["contact"].toString();
      //       const lastFive = string.substr(string.length - 4);
      //       SwalMessageAlert(
      //         "",
      //         "warning1",
      //         `<span style="text-align:left, justify-content: space-between">Employee Code Activated By</span><br><span>Name: ${response.data.getEmployeeDetails["name"]} </span><br><span>Mobile: ******${lastFive} </span><br><span>College: ${getCollegeDetail.data.name} </span>`
      //       );
      //     }
      //   } else if (response.data.msg === "NOT FOUND") {
      //     SwalMessageAlert("Employee Code Not Found", "error");
      //   } else {
      //     SwalMessageAlert(response.data.msg, "error");
      //   }
      //   setEmpCodeformData({
      //     employeeCode: "",
      //     mobile: "",
      //   });
      //   //window.location.replace("admin/employee-add");
      // }
    
      if (response.status === 200) {
  if (response.data.msg === "FOUND") {
    const empCode = response.data.getEmployeeCode["empCode"];
    const contact = response.data.getEmployeeCode["contact"];
    const name = response.data.getEmployeeCode["name"];
    const college = response.data.getEmployeeCode["college"];
    const processBy = response.data.getEmployeeCode["processBy"]; // NEW LINE

    if (processBy === 'college') {
      Swal.fire({
        title: "⚠️ Registration In Progress",
        text: "Registration already initiated by the college.",
        icon: "warning",
        confirmButtonText: "OK",
        confirmButtonColor: "#f39c12",
      });

    } else {
      SwalMessageAlert("Employee Code Found", "success");
      setModalCheckEmpCodeChange(!modalCheckEmpCodeChange);
      setFormData({
        ...formData, // Keep existing form data
        empCode: empCode,
        contact: contact,
        name: name,
        college: college,
      });
    }

  } else if (response.data.msg === "FOUND BUT USED") {
    const getCollegeDetail = await axios.get(
      `${endPoint}/api/college/get-single-college/${response.data.getEmployeeDetails["college"]}`,
      {
        headers: {
          "Content-Type": "application/json",
          'web-url': window.location.href,
          Authorization: `Bearer ${token}`,
        },
      }
    );
    if (getCollegeDetail) {
      const string = response.data.getEmployeeDetails["contact"].toString();
      const lastFive = string.substr(string.length - 4);
      SwalMessageAlert(
        "",
        "warning1",
        `<span style="text-align:left; justify-content: space-between">Employee Code Activated By</span><br><span>Name: ${response.data.getEmployeeDetails["name"]} </span><br><span>Mobile: ******${lastFive} </span><br><span>College: ${getCollegeDetail.data.name} </span>`
      );
    }

  } else if (response.data.msg === "NOT FOUND") {
    SwalMessageAlert("Employee Code Not Found", "error");

  } else {
    SwalMessageAlert(response.data.msg, "error");
  }

  setEmpCodeformData({
    employeeCode: "",
    mobile: "",
  });
  //window.location.replace("admin/employee-add");
}

      
      else {
        SwalMessageAlert(
          "No Employee Code Found/Enter Correct Employee Code",
          "error"
        );
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate("admin/university");
    }
  };

  const [proceedButton, setProceedButton] = useState(true);
  const [showNextIncrementDate, setShowNextIncrementDate] = useState(false);
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    setEmpCodeformData({ ...empCodeformData, [name]: value });
    setErrors({ ...errors, [name]: "" });
    setEmployeeCodeErrors({ ...employeeCodeerrors, [name]: "" });
    if (name === "employeeCode") {
      if (value.length !== 11) {
        setEmployeeCodeErrors({
          ...employeeCodeerrors,
          employeeCode: "Employee Code must be exactly 11 digits",
        });
        setProceedButton(false);
      } else {
        setEmployeeCodeErrors({ ...employeeCodeerrors, employeeCode: "" });
        setProceedButton(true);
      }
    }

    if (name === "email") {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email regex pattern
      if (!emailPattern.test(value)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: "Please enter a valid email address.",
        }));
      } else {
        // Clear the error if the email is valid
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: "",
        }));
      }
    }

    if (name === "mobile") {
      if (value.length !== 10 || !/^[6-9]\d{9}$/.test(value)) {
        setEmployeeCodeErrors({
          ...employeeCodeerrors,
          contact:
            "Mobile number must be 10 digits and start with 6, 7, 8, or 9",
        });
        setProceedButton(false);
      } else {
        setEmployeeCodeErrors({ ...employeeCodeerrors, contact: "" });
        setProceedButton(true);
      }
    }
    if (name === "applicableNextIncrement") {
      if (value === "Yes") {
        setShowNextIncrementDate(true);
      } else {
        setShowNextIncrementDate(false);
      }
    }
  };
  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);

  const showPreview = () => {
    const valuesToValidate = [
      formData.name,
      formData.empCode,
      formData.email,
      formData.contact,
      formData.divison,
      formData.district,
      formData.vidhansabha,
      formData.designation,
      formData.class,
      formData.address,
      formData.workType,
      formData.currentSalary,
      formData.applicableNextIncrement &&
      formData.applicableNextIncrement === "Yes" &&
      formData.nextIncrementDate,
      formData.gender,
      formData.title,
      formData.onDeputation,
    ];
    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    if (formData.email) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email regex pattern
      if (!emailPattern.test(formData.email)) {
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: "Please enter a valid email address.",
        }));
        return;
      } else {
        // Clear the error if the email is valid
        setErrors((prevErrors) => ({
          ...prevErrors,
          email: "",
        }));
      }
    }

    // Condition for empty fields
    if (hasEmptyFields) {
      alert("Please fill out all fields before submitting.");
      return; // Prevent form submission
    }

    // Condition for filled fields (you can implement additional logic here)
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }

    setPreviewModal(true);
  };

  useEffect(() => {
    if (sessionStorage.getItem("reloadOnce") === "true") {
      sessionStorage.removeItem("reloadOnce");

      // Fake Navigation to prevent flicker
      navigate("/temp-placeholder", { replace: true });

      setTimeout(() => {
        window.location.replace(window.location.origin + "/register");
      }, 0); // Instant reload
    }
  }, [navigate]);
  return (
    <>
      <div className="main-content">
        <Modal
          style={{ maxWidth: "800px", minWidth: "400px" }}
          isOpen={modalCheckEmpCodeChange}
          toggle={toggleModalCheckEmpCodeChange}
          backdrop="static" // Makes the backdrop static
          keyboard={false} // Prevents closing with the 'Escape' key
        >
          <ModalHeader lg="12">
            <Row lg="12">
              <Col lg="6">Check Employee Code here</Col>
              <Col lg="6">
                <NavLink className="btn btn-sm btn-primary" to="/" tag={Link}>
                  <span
                    className="nav-link-inner--text"
                    style={{ color: "white" }}
                  >
                    Go to Login
                  </span>
                </NavLink>
              </Col>
            </Row>
          </ModalHeader>
          <Form >
            <ModalBody>
              <div className="pl-lg-4">
                <Row>
                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-employeeCode"
                      >
                        Employee Code
                      </label>
                      <Input
                        name="employeeCode"
                        id="input-employeeCode"
                        placeholder="Enter Employee Code"

                        type="text" // Change type to 'text' to allow maxlength
                        value={empCodeformData.employeeCode}
                        onChange={handleInputChange}
                        autoComplete="nope" // Disable autocomplete
                        required
                        maxLength="11" // Now enforced by JavaScript
                        onInput={(e) => {
                          const value = e.target.value;
                          // Ensure only numeric input and limit to 11 characters
                          if (!/^\d*$/.test(value)) {
                            e.preventDefault();
                          }
                          if (value.length > 11) {
                            e.target.value = value.slice(0, 11); // Limit to 11 characters
                          }
                        }}
                      />
                      {employeeCodeerrors.employeeCode && (
                        <p style={{ color: "red" }}>
                          {employeeCodeerrors.employeeCode}
                        </p>
                      )}
                    </FormGroup>
                  </Col>

                  <Col lg="6">
                    <FormGroup>
                      <label
                        className="form-control-label"
                        htmlFor="input-mobile"
                      >
                        Mobile Number
                      </label>
                      <Input
                        name="mobile"
                        id="input-mobile"
                        placeholder="Enter Mobile Number"
                        type="text" // Change type to 'text' to allow maxlength
                        value={empCodeformData.mobile}
                        onChange={handleInputChange}
                        required
                        maxLength="10" // Now enforced by JavaScript
                        onInput={(e) => {
                          const value = e.target.value;
                          // Ensure only numeric input starting with 6, 7, 8, or 9
                          if (!/^[6-9]\d*$/.test(value)) {
                            e.preventDefault();
                          }
                          if (value.length > 10) {
                            e.target.value = value.slice(0, 10); // Limit to 10 characters
                          }
                        }}
                      />
                      {employeeCodeerrors.contact && (
                        <p style={{ color: "red" }}>
                          {employeeCodeerrors.contact}
                        </p>
                      )}
                    </FormGroup>
                  </Col>
                </Row>
              </div>
            </ModalBody>

            <ModalFooter>
              <Button
                color="primary"
                onClick={handleCheckEmployeeCode}
                className="btn btn-sm"
                style={{ display: proceedButton ? "" : "none" }}
              >
                Proceed
              </Button>
            </ModalFooter>
          </Form>
        </Modal>
        <Navbar
          className="navbar-top navbar-horizontal navbar-dark"
          expand="md"
        >
          <Container fluid className="px-4">
            <NavbarBrand style={{ display: "flex" }} to="/" tag={Link}>
              <img
                alt="..."
                src={logo}
                style={{ height: "60px" }} // Change to your desired height
              />
            </NavbarBrand>
            <h1 className="Title">
              Department of Higher Education <br /> Chhattisgarh
            </h1>
            <button className="navbar-toggler" id="navbar-collapse-main">
              <span className="navbar-toggler-icon" />
            </button>
            <UncontrolledCollapse navbar toggler="#navbar-collapse-main">
              <div className="navbar-collapse-header d-md-none">
                <Row>
                  <Col className="collapse-brand" xs="6">
                    <Link to="/">
                      <img alt="..." src={logo} />
                    </Link>
                  </Col>
                  <Col className="collapse-close" xs="6">
                    <button
                      className="navbar-toggler"
                      id="navbar-collapse-main"
                    >
                      <span />
                      <span />
                    </button>
                  </Col>
                </Row>
              </div>
              <Nav className="ml-auto" navbar>
                <NavItem>
                  <NavLink className="nav-link-icon" to="/register" tag={Link}>
                    <i className="ni ni-circle-08" />
                    <span className="nav-link-inner--text">Register</span>
                  </NavLink>
                </NavItem>
                <NavItem>
                  <NavLink className="nav-link-icon" to="/" tag={Link}>
                    <i className="ni ni-key-25" />
                    <span className="nav-link-inner--text">Login</span>
                  </NavLink>
                </NavItem>
              </Nav>
            </UncontrolledCollapse>
          </Container>
        </Navbar>
        <div className="header bg-gradient-info py-7 py-lg-8">
          <div className="separator separator-bottom separator-skew zindex-100">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              preserveAspectRatio="none"
              version="1.1"
              viewBox="0 0 2560 100"
              x="0"
              y="0"
            >
              <polygon
                className="fill-default"
                points="2560 0 2560 100 0 100"
              />
            </svg>
          </div>
        </div>
        <Container className="mt--8 pb-5">
          <Row className="justify-content-center">
            <Col lg="12" md="8">
              <Card className="bg-secondary shadow border-0">
                <CardHeader className="bg-transparent pb--5 d-block">
                  <div className="text-center  mb-3">
                    <FaUser
                      style={{
                        height: "40px",
                        width: "40px",
                        color: "#003ca7",
                        textAlign: "center",
                      }}
                    />
                  </div>
                  <div className="text-center ">
                    <h2
                      style={{
                        color: "#005cff",
                        marginTop: "10px",
                        fontFamily:
                          "Cambria, Cochin, Georgia, Times, 'Times New Roman', serif",
                      }}
                    >
                      Employee Registration Form
                    </h2>
                  </div>
                </CardHeader>
                <CardBody className="">
                  <Form role="form" >
                    <Row>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="empCode" className="form-control-label">
                            Employee Code
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-hat-3" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="empCode"
                              name="empCode"
                              placeholder="Employee Code"
                              type="text"
                              readOnly
                              style={inputStyle}
                              value={formData.empCode || ""}
                              onChange={handleInputChange}
                            />
                          </InputGroup>
                          {errors.empCode && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.empCode}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="title" className="form-control-label">
                            Title
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="fas fa-user"></i>
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="title"
                              name="title"
                              type="select" // Change type to "select"
                              value={formData.title || ""}
                              style={{ ...inputStyle, padding: "10px" }} // Adjust inputStyle as needed
                              onChange={handleInputChange}
                            >
                              <option value="">-- Select Title --</option>
                              <option value="Mr.">Mr.</option>
                              <option value="Mrs.">Mrs.</option>
                              <option value="Ms.">Ms.</option>
                              <option value="Miss">Miss</option>
                              <option value="Dr.">Dr.</option>
                              <option value="Prof.">Prof.</option>
                              <option value="Hon.">Hon.</option>
                            </Input>
                            {errors.title && (
                              <p style={{ color: "red" }}>{errors.title}</p>
                            )}
                          </InputGroup>
                        </FormGroup>
                      </Col>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="name" className="form-control-label">
                            Name
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-hat-3" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="name"
                              name="name"
                              placeholder="Name"
                              type="text"
                              readOnly
                              style={inputStyle}
                              value={formData.name || ""}
                              onChange={handleInputChange}
                            />
                          </InputGroup>
                          {errors.name && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.name}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label for="email" className="form-control-label">
                            Email
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-email-83" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="email"
                              placeholder="Email"
                              name="email"
                              type="email"
                              style={inputStyle}
                              autoComplete="new-email"
                              onChange={handleInputChange}
                            />
                          </InputGroup>
                          {errors.email && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.email}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>

                    <Row>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="contact" className="form-control-label">
                            Contact
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-mobile-button" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="contact"
                              name="contact"
                              placeholder="Contact"
                              type="tel"
                              readOnly
                              style={inputStyle}
                              value={formData.contact || ""}
                              onChange={handleInputChange}
                            />
                          </InputGroup>
                          {errors.contact && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.contact}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label for="gender" className="form-control-label">
                            Gender
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i
                                  className="fas fa-male"
                                  style={{ marginRight: "5px" }}
                                ></i>
                                <i className="fas fa-female"></i>
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="gender"
                              name="gender"
                              type="select" // Change type to "select"
                              value={formData.gender || ""}
                              style={inputStyle}
                              onChange={handleInputChange}
                            >
                              <option value="">-- Select Gender --</option>
                              <option value="Male">Male</option>
                              <option value="Female">Female</option>
                              <option value="Other">Other</option>
                            </Input>
                            {errors.gender && (
                              <p style={{ color: "red" }}>{errors.gender}</p>
                            )}
                          </InputGroup>
                        </FormGroup>
                      </Col>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="division" className="form-control-label">
                            Select Division
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-map-big" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              name="divison"
                              id="input-division"
                              type="select"
                              onChange={handleDivisionChange}
                              style={inputStyle}
                            >
                              <option value="">Select Division</option>
                              {division &&
                                division.length > 0 &&
                                division.map((type, index) => (
                                  <option key={index} value={type.divisionCode}>
                                    {type.name}
                                  </option>
                                ))}
                            </Input>
                          </InputGroup>
                          {errors.divison && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.divison}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label for="district" className="form-control-label">
                            Select District
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-map-big" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              name="district"
                              id="input-district"
                              type="select"
                              value={formData.district}
                              onChange={handleDistrictChange}
                              style={inputStyle}
                            >
                              <option value="">Select District</option>
                              {district &&
                                district.length > 0 &&
                                district.map((type, index) => (
                                  <option key={index} value={type.LGDCode}>
                                    {type.districtNameEng}
                                  </option>
                                ))}
                            </Input>
                          </InputGroup>
                          {errors.district && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.district}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>

                    <Row>
                      <Col xs="3">
                        <FormGroup>
                          <Label
                            for="vidhanSabha"
                            className="form-control-label"
                          >
                            Select Vidhan Sabha
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-map-big" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              name="vidhansabha"
                              id="input-vidhansabha"
                              type="select"
                              value={formData.vidhansabha}
                              onChange={handleInputChange}
                            >
                              <option value="">Select Vidhan Sabha</option>
                              {vidhansabha &&
                                vidhansabha.length > 0 &&
                                vidhansabha.map((type, index) => (
                                  <option
                                    key={index}
                                    value={type.ConstituencyNumber}
                                  >
                                    {type.ConstituencyName}
                                  </option>
                                ))}
                            </Input>
                          </InputGroup>
                          {errors.vidhansabha && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.vidhansabha}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label for="college" className="form-control-label">
                            Select College
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-building" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="college"
                              name="college"
                              type="select"
                              readOnly
                              style={inputStyle}
                              onChange={handleInputChange}
                            >
                              <option value="">Select College</option>
                              {college &&
                                college.length > 0 &&
                                college.map((type, index) => (
                                  <option
                                    key={index}
                                    value={type._id}
                                    selected={
                                      formData.college === String(type._id)
                                        ? true
                                        : false
                                    }
                                  >
                                    {type.name}
                                  </option>
                                ))}
                            </Input>
                          </InputGroup>
                          {errors.college && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.college}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col xs="3">
                        <FormGroup>
                          <Label
                            for="vidhanSabha"
                            className="form-control-label"
                          >
                            Work Type
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-map-big" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              name="workType"
                              id="input-workType"
                              type="select"
                              value={formData.workType}
                              onChange={handleInputChange}
                            >
                              <option value="">Select Work Type</option>
                              <option value="TEACHING">TEACHING</option>
                              <option value="NON TEACHING">NON TEACHING</option>
                            </Input>
                            {errors.vidhansabha && (
                              <p style={{ color: "red" }}>
                                {errors.vidhansabha}
                              </p>
                            )}
                          </InputGroup>
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label
                            for="currentSalary"
                            className="form-control-label"
                          >
                            Basic Salary
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-money-coins" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="currentSalary"
                              name="currentSalary"
                              placeholder="Basic Salary"
                              type="text"
                              style={inputStyle}
                              value={formData.currentSalary || ""}
                              onChange={handleInputChange}
                              maxLength={6}
                            />
                          </InputGroup>
                          {errors.currentSalary && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.currentSalary}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="6" md="12">
                        <Row>
                          <Col lg="6">
                            <FormGroup>
                              <Label
                                for="nextIncrementDate"
                                className="form-control-label"
                              >
                                Applicable For Next Increment
                              </Label>
                              <InputGroup className="input-group-alternative mb-3">
                                <InputGroupAddon addonType="prepend">
                                  <InputGroupText>
                                    <i className="ni ni-calendar-grid-58" />
                                  </InputGroupText>
                                </InputGroupAddon>
                                <Input
                                  id="applicableNextIncrement"
                                  type="select"
                                  disabled={
                                    formData.applicableNextIncrement === "false"
                                  }
                                  name="applicableNextIncrement"
                                  style={{ maxWidth: "100%" }}
                                  onChange={handleInputChange}
                                >
                                  <option value="">Select</option>
                                  <option value="Yes">Yes</option>
                                  <option value="No">No</option>
                                </Input>
                              </InputGroup>
                              {errors.applicableNextIncrement && (
                                <p style={{ color: "red", marginTop: "-15px" }}>
                                  {errors.applicableNextIncrement}
                                </p>
                              )}
                            </FormGroup>
                          </Col>
                          <Col
                            lg="6"
                            style={{
                              display:
                                showNextIncrementDate !== true ? "none" : "",
                            }}
                          >
                            <FormGroup>
                              <Label
                                for="nextIncrementDate"
                                className="form-control-label"
                              >
                                Next Increment Date
                              </Label>
                              <InputGroup className="input-group-alternative mb-3">
                                <InputGroupAddon addonType="prepend">
                                  <InputGroupText>
                                    <i className="ni ni-calendar-grid-58" />
                                  </InputGroupText>
                                </InputGroupAddon>
                                <Input
                                  id="nextIncrementDate"
                                  name="nextIncrementDate"
                                  placeholder="Next Increment Date"
                                  type="date"
                                  style={inputStyle}
                                  value={formData.nextIncrementDate || ""}
                                  min={new Date().toISOString().split("T")[0]}
                                  onChange={handleInputChange}
                                />
                              </InputGroup>
                              {errors.nextIncrementDate && (
                                <p style={{ color: "red", marginTop: "-15px" }}>
                                  {errors.nextIncrementDate}
                                </p>
                              )}
                            </FormGroup>
                          </Col>
                        </Row>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label for="class" className="form-control-label">
                            Select Class
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-book-bookmark" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="class"
                              type="select"
                              name="class"
                              style={inputStyle}
                              onChange={handleClassInputChange}
                            >
                              <option value="">Select Class</option>
                              {classData &&
                                classData.length > 0 &&
                                classData.map((type, index) => (
                                  <option key={index} value={type._id}>
                                    {type.className}
                                  </option>
                                ))}
                            </Input>
                            {errors.class && (
                              <p style={{ color: "red" }}>{errors.class}</p>
                            )}
                          </InputGroup>
                          {errors.class && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.class}
                            </p>
                          )}
                        </FormGroup>
                      </Col>

                      <Col xs="3">
                        <FormGroup>
                          <Label
                            for="designation"
                            className="form-control-label"
                          >
                            Designation
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-briefcase-24" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="designation"
                              type="select"
                              name="designation"
                              style={inputStyle}
                              onChange={handleInputChange}
                            >
                              <option value="">Select Designation</option>
                              {designationData &&
                                designationData.length > 0 &&
                                designationData.map((type, index) => (
                                  <option key={index} value={type._id}>
                                    {type.designation}
                                  </option>
                                ))}
                            </Input>
                          </InputGroup>
                          {errors.designation && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.designation}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                      <Col xs="3">
                        <FormGroup>
                          <Label for="address" className="form-control-label">
                            Residential Address
                          </Label>
                          <InputGroup className="input-group-alternative mb-3">
                            <InputGroupAddon addonType="prepend">
                              <InputGroupText>
                                <i className="ni ni-map-pin" />
                              </InputGroupText>
                            </InputGroupAddon>
                            <Input
                              id="address"
                              placeholder="Address"
                              name="address"
                              type="text"
                              style={inputStyle}
                              onChange={handleInputChange}
                            />
                          </InputGroup>
                          {errors.address && (
                            <p style={{ color: "red", marginTop: "-15px" }}>
                              {errors.address}
                            </p>
                          )}
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      {/* Deputed Designation Field */}
                      <Col xs="3">
                        <FormGroup>
                          <Label
                            for="onDeputation"
                            className="form-control-label"
                          >
                            On Deputation
                          </Label>
                          <div>
                            <Label
                              style={{
                                paddingLeft: "50px",
                                paddingRight: "50px",
                              }}
                              check
                            >
                              <Input
                                type="radio"
                                name="onDeputation"
                                value="true"
                                onChange={handleInputChange}
                                checked={formData.onDeputation === "true"}
                              />
                              Yes
                            </Label>
                            <Label check>
                              <Input
                                type="radio"
                                name="onDeputation"
                                value="false"
                                onChange={handleInputChange}
                                checked={formData.onDeputation === "false"}
                              />
                              No
                            </Label>
                          </div>
                        </FormGroup>
                      </Col>
                      {formData.onDeputation === "true" && (
                        <>
                          <Col xs="3">
                            <FormGroup>
                              <Label
                                for="deputedDesignation"
                                className="form-control-label"
                              >
                                Deputed Designation
                              </Label>
                              <InputGroup className="input-group-alternative mb-3">
                                <InputGroupAddon addonType="prepend">
                                  <InputGroupText>
                                    <i className="ni ni-briefcase-24" />
                                  </InputGroupText>
                                </InputGroupAddon>
                                <Input
                                  id="deputedDesignation"
                                  type="select"
                                  name="deputedDesignation"
                                  style={{ maxWidth: "100%" }}
                                  disabled={formData.onDeputation === "false"}
                                  value={formData.deputedDesignation}
                                  onChange={handleInputChange}
                                >
                                  <option value="">Select Designation</option>
                                  {designationData &&
                                    designationData.length > 0 &&
                                    designationData.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.designation}
                                      </option>
                                    ))}
                                </Input>
                              </InputGroup>
                            </FormGroup>
                          </Col>

                          {/* Main Posting Location Field */}
                          <Col xs="3">
                            <FormGroup>
                              <Label
                                for="postingLocation"
                                className="form-control-label"
                              >
                                Posting Location
                              </Label>
                              <InputGroup className="input-group-alternative mb-3">
                                <InputGroupAddon addonType="prepend">
                                  <InputGroupText>
                                    <i className="ni ni-map-big" />
                                  </InputGroupText>
                                </InputGroupAddon>
                                <Input
                                  id="postingLocation"
                                  type="text"
                                  name="postingLocation"
                                  style={inputStyle}
                                  disabled={formData.onDeputation === "false"}
                                  value={formData.postingLocation || ""}
                                  onChange={handleInputChange}
                                />
                              </InputGroup>
                            </FormGroup>
                          </Col>

                          {/* Employee Type Field */}
                          <Col xs="3">
                            <FormGroup>
                              <Label
                                for="employeeType"
                                className="form-control-label"
                              >
                                Employee Type
                              </Label>
                              <InputGroup className="input-group-alternative mb-3">
                                <InputGroupAddon addonType="prepend">
                                  <InputGroupText>
                                    <i className="ni ni-briefcase-24" />
                                  </InputGroupText>
                                </InputGroupAddon>
                                <Input
                                  id="employeeType"
                                  type="select"
                                  disabled={formData.onDeputation === "false"}
                                  name="employeeType"
                                  style={{ maxWidth: "100%" }}
                                  onChange={handleInputChange}
                                >
                                  <option value="">Select Employee Type</option>
                                  <option value="DIRECTORATE">
                                    Directorate
                                  </option>
                                  <option value="COMMISSIONER">
                                    Commissioner
                                  </option>
                                  <option value="INSTITUTE">Institute</option>
                                </Input>
                              </InputGroup>
                            </FormGroup>
                          </Col>
                        </>
                      )}
                    </Row>

                    {/* <Row>
                <Col lg="12">
                  <FormGroup>
                    <Label for="profileUpload" className="form-control-label">Upload Profile</Label>
                    <InputGroup className="input-group-alternative mb-3">
                      <InputGroupAddon addonType="prepend"></InputGroupAddon>
                      <Input id="profileUpload" type="file" accept="image/*" style={inputStyle} />
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row> */}

                    {/* <Row className="my-4">
                <Col xs="12">
                  <div className="custom-control custom-control-alternative custom-checkbox">
                    <input
                      className="custom-control-input"
                      id="customCheckRegister"
                      type="checkbox"
                    />
                    <label
                      className="custom-control-label"
                      htmlFor="customCheckRegister"
                    >
                      <span className="text-muted">
                        I agree with the{" "}
                        <a href="#" onClick={(e) => e.preventDefault()}>
                          Privacy Policy
                        </a>
                      </span>
                    </label>
                  </div>
                </Col>
              </Row> */}

                    <div className="text-center">
                      <Button
                        className="mt-4"
                        color="primary"
                        onClick={showPreview}
                      >
                        Preview & Submit
                      </Button>
                    </div>
                  </Form>
                </CardBody>
              </Card>
            </Col>
            {/* Preview Modal */}
            <Modal
              isOpen={previewModal}
              toggle={togglePreviewModal}
              style={{
                maxWidth: "1000px",
                width: "90%",
              }}
            >
              <ModalHeader toggle={togglePreviewModal}>
                <h2>Preview Of Registration</h2>
                <br />
                <h4 className="text-danger">Note - Check Carefully before Submit</h4>
              </ModalHeader>
              <ModalBody>
                <Col>
                  <Row>
                    <Col>
                      <p>
                        <strong>Employee Code:</strong>
                        <br />
                        <input
                          type="text"
                          value={formData.employeeCode}
                          disabled
                        />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Contact:</strong>
                        <br />{" "}
                        <input type="text" value={formData.contact} disabled />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Title:</strong>
                        <br />{" "}
                        <input type="text" value={formData.title} disabled />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Name:</strong>
                        <br />{" "}
                        <input type="text" value={formData.name} disabled />
                      </p>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <p>
                        <strong>Gender:</strong>
                        <br />{" "}
                        <input type="text" value={formData.gender} disabled />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Email:</strong>
                        <br />{" "}
                        <input type="text" value={formData.email} disabled />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Division:</strong>
                        <br />{" "}
                        <input
                          type="text"
                          value={
                            division.find(
                              (type) =>
                                String(type.divisionCode) ===
                                String(formData.divison)
                            )?.name || "N/A"
                          }
                          disabled
                        />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>District:</strong>
                        <br />{" "}
                        <input
                          type="text"
                          value={
                            district.find(
                              (type) =>
                                String(type.LGDCode) ===
                                String(formData.district)
                            )?.districtNameEng || "N/A"
                          }
                          disabled
                        />
                      </p>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <p>
                        <strong>Vidhan Sabha:</strong>
                        <br />{" "}
                        <input
                          type="text"
                          value={
                            vidhansabha.find(
                              (type) =>
                                String(type.ConstituencyNumber) ===
                                String(formData.vidhansabha)
                            )?.ConstituencyName || "N/A"
                          }
                          disabled
                        />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Work Type:</strong>
                        <br />{" "}
                        <input type="text" value={formData.workType} disabled />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Class:</strong>
                        <br />
                        <input
                          type="text"
                          value={
                            classData.find(
                              (type) => type._id === formData.class
                            )?.className || "N/A"
                          }
                          disabled
                        />
                      </p>
                    </Col>
                    <Col>
                      <p>
                        <strong>Designation:</strong>
                        <br />{" "}
                        <input
                          type="text"
                          value={
                            designationData.find(
                              (type) => type._id === formData.designation
                            )?.designation || "N/A"
                          }
                          disabled
                        />
                      </p>
                    </Col>
                  </Row>

                  <Row>
                    <Col lg="3">
                      <p>
                        <strong> Basic Salary:</strong>
                        <br />
                        <input
                          type="text"
                          value={formData.currentSalary}
                          disabled
                        />
                      </p>
                    </Col>
                    <Col lg="3">
                      <p style={{ maxWidth: "100%" }}>
                        <strong>Applicable Next Increment:</strong>
                        <br />
                        <input
                          type="text"
                          value={formData.applicableNextIncrement}
                          style={{ maxWidth: "100%" }}
                          disabled
                        />
                      </p>
                    </Col>
                    <Col
                      lg="3"
                      style={{
                        display: showNextIncrementDate !== true ? "none" : "",
                      }}
                    >
                      <p style={{ maxWidth: "100%" }}>
                        <strong>Next Increment Date:</strong>
                        <br />
                        <input
                          type="text"
                          value={formatDate(formData.nextIncrementDate)}
                          style={{ maxWidth: "100%" }}
                          disabled
                        />
                      </p>
                    </Col>
                    <Col xs="6">
                      <p>
                        <strong>Residential Address:</strong>
                        <br />
                        <input
                          style={{ width: "100%" }}
                          type="text"
                          value={formData.address}
                          disabled
                        />
                      </p>
                    </Col>
                  </Row>
                  <Row>
                    <Col xs="3">
                      <FormGroup>
                        <Label
                          for="onDeputation"
                          className="form-control-label"
                        >
                          On Deputation
                        </Label>
                        <div>
                          <Label
                            style={{
                              paddingLeft: "50px",
                              paddingRight: "50px",
                            }}
                            check
                          >
                            <Input
                              type="radio"
                              name="onDeputation"
                              value="true"
                              onChange={handleInputChange}
                              disabled
                              checked={formData.onDeputation === "true"}
                            />
                            Yes
                          </Label>
                          <Label check>
                            <Input
                              type="radio"
                              name="onDeputation"
                              value="false"
                              disabled
                              onChange={handleInputChange}
                              checked={formData.onDeputation === "false"}
                            />
                            No
                          </Label>
                        </div>
                      </FormGroup>
                    </Col>

                    {formData.onDeputation === "true" && (
                      <>
                        <Col xs="3">
                          <p>
                            <strong>Deputed Designation:</strong>
                            <br />{" "}
                            <input
                              type="text"
                              value={
                                designationData.find(
                                  (type) =>
                                    type._id === formData.deputedDesignation
                                )?.designation || "N/A"
                              }
                              disabled
                            />
                          </p>
                        </Col>

                        {/* Main Posting Location Field */}
                        <Col xs="3">
                          <p>
                            <strong>Posting Location:</strong>
                            <br />{" "}
                            <input
                              type="text"
                              value={formData.postingLocation}
                              disabled
                            />
                          </p>
                        </Col>

                        {/* Employee Type Field */}
                        <Col xs="3">
                          <p>
                            <strong>Employee Type:</strong>
                            <br />{" "}
                            <input
                              type="text"
                              value={formData.employeeType}
                              disabled
                            />
                          </p>
                        </Col>
                      </>
                    )}
                  </Row>
                </Col>
              </ModalBody>
              <ModalFooter>
                <Button color="secondary" onClick={togglePreviewModal}>
                  Cancel
                </Button>
                <Button color="primary" onClick={handleSubmit} disabled={otpLoading || finalSubmitLoading}>
                  {otpLoading ? "Sending OTP..." : finalSubmitLoading ? "Submitting..." : "Submit"}
                </Button>
              </ModalFooter>
            </Modal>
            {/* OTP Modal */}
            <Modal isOpen={otpModal} toggle={() => setOtpModal(false)} backdrop="static" keyboard={false}>
              <ModalHeader
                className="bg-primary text-white justify-content-center border-0"
              >
                <h3 className="text-white p-0 m-0" >OTP Verification</h3>
              </ModalHeader>

              <ModalBody>
                   <p className="text-center p-0 m-0">
                    Enter the OTP sent to <strong>{`${formData?.contact?.toString().substring(0, 2)}XXXXXX${formData?.contact?.toString().substring(8)}`}</strong>
                  </p>
                <hr />
                <div className="d-flex justify-content-center">
                  <Input
                    className="col-6 "
                    type="text"
                    value={otp}
                    onChange={(e) => {
                      const value = e.target.value;
                      if (/^\d*$/.test(value)) {
                        setOtp(value);
                      }
                    }}
                    maxLength={6}
                    placeholder="Enter 6 Digit OTP"
                    autoFocus
                  />
                </div>


                {otpError && <p style={{ color: "red" }}>{otpError}</p>}
              </ModalBody>
              <ModalFooter className="d-flex justify-content-center">
                <Button color="primary" onClick={handleSendOtp} disabled={otpLoading || resendTimer > 0}>  {resendTimer > 0 ? `Resend OTP (${resendTimer}s)` : "Resend OTP"}</Button>
                <Button
                  color="success"
                  onClick={handleVerifyOtp}
                  disabled={verifyLoading || otp.length !== 6}
                >
                  {verifyLoading ? "Verifying..." : "Verify OTP"}
                </Button>

              </ModalFooter>
            </Modal>
          </Row>
        </Container>
      </div>
    </>
  );
};

export default Register;

