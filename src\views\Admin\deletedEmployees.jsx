import { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormControl,
  Row,
  Col,
} from "react-bootstrap";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
import NavImage from "../../assets/img/theme/user-icon.png";
const DeletedEmployeeList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [employee, setEmployee] = useState([]);
  const [filterText, setFilterText] = useState("");
  const [deletedEmployees, setDeletedEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [colleges, setColleges] = useState([]);
  const [classes, setClasses] = useState([]);
  const [designations, setDesignations] = useState([]);
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setEmployee(response.data.getAllEmployeeCollegeWise);
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
  }, [endPoint, token, collegeId]);

  // Fetch divisions for mapping division codes to names
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching division data:", error);
        alert("Failed to load division data.");
      }
    };

    getDivision();
  }, [endPoint, token]);



  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };



  const changePassword = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/change-password?id=${id}&key=Employee`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data; // Check the status code to ensure success
        copyToClipboard(data.password);
        SwalMessageAlert("Password Change Successfully","success");
      } else {
        SwalMessageAlert("Password Change Failed","error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };

  useEffect(() => {
    const fetchCollegeInfo = async () => {
      if (!collegeId) {
        console.warn("No collegeId provided");
        return;
      }
  
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-college/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
   
        if (response.status === 200 && response.data) {
          setColleges(response.data);
        } else {
          alert("College Not Found.");
        }
      } catch (error) {
        console.error("An error occurred while fetching college data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
  
    fetchCollegeInfo();
  }, [collegeId, token]);
  const getCollegeName = () => {
    return colleges && colleges.name ? colleges.name : "N/A";
  };
    
  const deleteEmployee = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure you want to delete this Employee?",
      text: "You won't be able to revert this!",
      icon: "warning",
      input: "text", // Adds a text input field
      inputPlaceholder: "Enter reason for cancellation...",
      inputAttributes: {
        "aria-label": "Reason for cancellation",
      },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, cancel it!",
      cancelButtonText: "No, keep it",
      preConfirm: (reason) => {
        if (!reason) {
          Swal.showValidationMessage("Reason for cancellation is required");
        }
        return reason;
      },
    });

    if (result.isConfirmed) {
      const reason = result.value; // Capture the reason entered by the user

      try {
        // Update status to 6 with cancellation reason using PUT method
        const response = await axios.put(
          `${endPoint}/api/delete-employee?id=${id}&key=Employee`,
          { reason },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          const data = response.data;
          SwalMessageAlert("Employee Deleted Successfully", "success");
        } else {
          SwalMessageAlert(
            "Delete Employee Failed. Please try again!",
            "error"
          );
        }
      } catch (error) {
        SwalMessageAlert(
          "An error occurred while Deleting Employee. Please try again later!",
          "error"
        );
        console.error("An error occurred while fetching the data:", error);
      }
    }
  };



  function copyTextFallback(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      // console.log("Text copied using fallback!");
    } catch (err) {
      console.error("Fallback copy failed:", err);
    }
    document.body.removeChild(textArea);
  }
  const copyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() =>  console.log("Text copied to clipboard!"))
        .catch((err) => console.error("Failed to copy text:", err));
    } else {
      copyTextFallback(text);
    }
  };
  useEffect(() => {
    const fetchClasses = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });

        if (response.status === 200) {
          setClasses(response.data);
        }
      } catch (error) {
        console.error("Error fetching class data:", error);
      }
    };

    fetchClasses();
  }, [endPoint, token]);

  // Fetch Designation Data
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: { 
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });

        if (response.status === 200) {
          setDesignations(response.data);
        }
      } catch (error) {
        console.error("Error fetching designation data:", error);
      }
    };

    fetchDesignations();
    // deleteEmployee();
  }, [endPoint, token]);

const getClassName = (classData) => {
  const classObj = classes.find((classItem) => classItem._id === classData);
  return classObj ? classObj.className : "Unknown Class Name";
};

const getDesignationName = (designation) => {
 
  const designationObj = designations.find((dname) => dname._id === designation);
  return designationObj ? designationObj.designation : "Unknown designation Name";
};
 
  useEffect(() => {
    const fetchTrashEmployees = async () => {
      try {
        const response = await axios.get(
          // `${endPoint}/api/trash-employee/trash-details`,
          `${endPoint}/api/trash-employee/trash-details?college=${collegeId}`,
          {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const data = response.data;
          
          setDeletedEmployees(data);
        
            
        } else {
          console.warn("No deleted employees found.");
        }
      } catch (error) {
        console.error("Error fetching deleted employees:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTrashEmployees();
  }, []);
  
  const handleRestore = async (employee) => {
    
    Swal.fire({
      title: "Are you sure?",
      html: `<strong>Emp Code: ${employee.empCode}</strong><br>You want to restore <strong>${employee.name}</strong>?`,
     imageUrl:  employee.encodedImage &&
         employee.faceVerified !== false &&
         employee.encodedImage !== ""
           ? `data:image/png;base64,${employee.encodedImage}`
           :  NavImage,
         imageWidth: 100,
         imageHeight: 100,
         imageAlt: "Employee Photo",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, Restore!",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await axios.put(
            `${endPoint}/api/restore-employee/${employee._id}`,
            {},
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );
          
          if (response.status === 200) {
            Swal.fire({
              title: "Restored!",
              text: "Employee restored successfully.",
              icon: "success",
            });
            
            setTimeout(() => {
              window.location.reload();
            }, 1000);
          }
        } catch (error) {
          console.error("Restore error:", error);
          
         
          let errorMessage = "Failed to restore employee.";
          
          if (error.response) {
           
            errorMessage = error.response.data.message || errorMessage;
          } else if (error.request) {
           
            errorMessage = "No response received from server.";
          } else {
            
            errorMessage = error.message || errorMessage;
          }
          
          Swal.fire({
            title: "Error!",
            text: errorMessage,
            icon: "error",
          });
        }
      }
    });
  };
  
  const columns = [
    {
      name: "Action",
      cell: (employee) => (
        <>
          <button
            title="Delete Employee"
            className="btn btn-light btn-sm text-danger "
            disabled
          >
            <span className="fa fa-trash"></span> Deleted
          </button>
          <button
            title="revert Employee"
            className="btn btn-success btn-sm"
            onClick={() => handleRestore(employee)}
          >
            <span className="fa fa-refresh"></span> Restore 
          </button>
        </>
      ),
    },
    {
      name: "Basic Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {employee.name}
          </div>
          <div className="mb-2">
            <strong>Email: </strong>
            {employee.email}
          </div>
          <div className="mb-2">
            <strong>Contact: </strong>
            {employee.contact}
          </div>
          <div className="mb-2">
            <strong>Emp Code: </strong>
            <strong className="badge-primary">{employee.empCode}</strong>
          </div>
        </div>
      ),
      sortable: true,
      sortFunction: (rowA, rowB) =>
        parseInt(rowA.empCode) - parseInt(rowB.empCode),
      wrap: true,
    },
    {
      name: "Class Details",
      cell: (employee) => (
        <div>
          <div className="mb-2">
            <strong>College: </strong>
            {getCollegeName(employee.collegeId)}
          </div>
          <div className="mb-2">
            <strong>Class: </strong>
            {getClassName(employee.classData)} 
          </div>
          <div className="mb-2">
            <strong>Designation: </strong>
            {getDesignationName(employee.designation)}
          </div>
        
        </div>
      ),
      wrap: true,
    },
    {
      name: "Division/District/Vidhansabha",
      selector: (row) =>
        `${getDivisionName(row.divison)} / ${row.districtName} / ${
          row.vidhansabhaName
        }`,
    },
  ];
  const filteredData = deletedEmployees.filter((item) => {
    

    if (item.activeStatus === false) {
      const filterTextLower = filterText.toLowerCase();

      // Check for "Verified" and "Not Verified"
      if (filterTextLower === "verified") {
        return item.verified === true; // Only include verified employees
      } else if (filterTextLower === "not verified") {
        return item.verified === false; // Only include not verified employees
      } else if (filterTextLower === "not") {
        return item.verified === false; // Only include not verified employees
      }

      // Default filtering for name, empCode, and contact
      return (
        (item.name && item.name.toLowerCase().includes(filterTextLower)) ||
        (item.empCode &&
          item.empCode.toLowerCase().includes(filterTextLower)) ||
        (item.contact &&
          item.contact.toString().toLowerCase().includes(filterTextLower))
      );
    }
  });
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <h3 className="mb-0">Deleted Employee List</h3>
                <FormControl
                  type="text"
                  placeholder="Search Employee..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                />
              </CardHeader>
              <CardBody>
                <DataTable
                  columns={columns}
                  data={filteredData}
                  // filter={filteredData}
                  progressPending={loading}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa", // Light background color for header
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff", // Row color
                        borderBottom: "1px solid #ddd",
                      },
                      // Apply hover effect through the :hover pseudo-class directly in custom CSS
                      onHoverStyle: {
                        backgroundColor: "#ffff99", // Hover color
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default DeletedEmployeeList;
