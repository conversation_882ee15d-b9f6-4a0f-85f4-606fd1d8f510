import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
} from "reactstrap";

import { FormControl } from "react-bootstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
const ACRDateForm = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [formData, setFormData] = useState({
    dateFrom: "",
    dateTo: "",
  });

  const [acrDateList, setAcrDateList] = useState([]);

  useEffect(() => {
    const fetchAcrDate = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/acr-date/logs`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
            setAcrDateList(response.data);
        } else {
          SwalMessageAlert(
           `${response.data.msg}, Please Add Date Range`,
            "error"
          );
        }
      } catch (error) {
        if (error.response) {
          // Extract the error message from the API response
          const errorMessage =
            error.response.data?.msg ||
            "Something went wrong. Please try again.";
          SwalMessageAlert(errorMessage, "error");
        } else if (error.request) {
          // Handle no response from server (network issues, server down, etc.)
          SwalMessageAlert(
            "No response from the server. Please check your network or try again later.",
            "error"
          );
        } else {
          // Handle other unexpected errors
          SwalMessageAlert(
            "An unexpected error occurred. Please try again.",
            "error"
          );
        }
      }
    };

    fetchAcrDate();
  }, [endPoint, token]);

  
  const [filterText, setFilterText] = useState("");
  const columns = [
    {
      name: "Date From",
      cell: (acrDateList, index) => index === 0 ? (<>{formatDate(acrDateList.dateFrom)} &nbsp;&nbsp;<span className="btn btn-sm btn-primary">New</span></> ) : formatDate(acrDateList.dateFrom),
    },
    {
      name: "Date To",
      cell: (acrDateList) => formatDate(acrDateList.dateTo),
    },
    {
      name: "Last Update Date",
      cell: (acrDateList) => formatDate(acrDateList.updatedAt),
    },
  ];

  const filteredData = acrDateList.filter((item) => {
    
    const filterTextLower = filterText.toLowerCase();
    return (
      (item.dateFrom && item.dateFrom.toLowerCase().includes(filterTextLower)) ||
      (item.dateTo && item.dateTo.toLowerCase().includes(filterTextLower))
    );
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {


    e.preventDefault();
    const valuesToValidate = [formData.dateFrom, formData.dateTo];
    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );

    if (hasEmptyFields) {
      SwalMessageAlert(
        "Please fill out all fields before submitting.",
        "warning"
      );
      return; // Prevent form submission
    }

    if (allFieldsFilled) {
      SwalMessageAlert(
        "All fields are filled. You can proceed with submission.",
        "warning"
      );
    }
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);

    try {
      const body = {
        dateFrom: String(formData.dateFrom),
        dateTo: String(formData.dateTo),
      };
      const response = await axios.post(
        `${endPoint}/api/acr-date/add`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
          dateFrom: "",
          dateTo: "",
        });
        window.setTimeout(function () {
          location.reload();
        }, 3000);
      } else {
        SwalMessageAlert("Date Range Not Updated.", "error");
      }
    } catch (error) {
      if (error.response) {
        const errorMessage =
          error.response.data?.msg || "Something went wrong. Please try again.";
        SwalMessageAlert(errorMessage, "error");
      } else if (error.request) {
        SwalMessageAlert(
          "No response from the server. Please check your network or try again later.",
          "error"
        );
      } else {
        SwalMessageAlert(
          "An unexpected error occurred. Please try again.",
          "error"
        );
      }
    }
  };
  return (
    <>
      <Header />

      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Extend ACR Date Form</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            Date From
                          </label>
                          <Input
                            name="dateFrom"
                            id="input-name"
                            placeholder="Division Name"
                            type="date"
                            value={formData.dateFrom}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col>

                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-divisionCode"
                          >
                            Date To
                          </label>
                          <Input
                            name="dateTo"
                            id="input-divisionCode"
                            placeholder="dateTo"
                            type="date"
                            autoComplete="pope"
                            value={formData.dateTo}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button  onClick={handleSubmit} color="primary" >
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>

        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <h3 className="mb-0">Date Range List</h3>
                <FormControl
                  type="text"
                  placeholder="Search..."
                  className="ml-auto"
                  value={filterText}
                  onChange={(e) => setFilterText(e.target.value)}
                  style={{ width: "250px", borderRadius: "30px" }}
                />
              </CardHeader>
              <CardBody>
                <DataTable
                  columns={columns}
                  data={filteredData}
                  pagination
                  paginationPerPage={10}
                  highlightOnHover
                  striped
                  customStyles={{
                    header: {
                      style: {
                        backgroundColor: "#f8f9fa", // Light background color for header
                        fontWeight: "bold",
                      },
                    },
                    rows: {
                      style: {
                        backgroundColor: "#fff", // Row color
                        borderBottom: "1px solid #ddd",
                      },
                      // Apply hover effect through the :hover pseudo-class directly in custom CSS
                      onHoverStyle: {
                        backgroundColor: "#ffff99", // Hover color
                      },
                    },
                  }}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default ACRDateForm;
