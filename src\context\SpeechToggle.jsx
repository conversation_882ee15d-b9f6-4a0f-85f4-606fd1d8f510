import { useState } from "react";
import { useSpeech } from "./SpeechContext.jsx";
import {
  Button,
  Dropdown,
  ButtonGroup,
  Card,
  ButtonToolbar,
} from "react-bootstrap";
import {
  VolumeUpFill,
  VolumeMuteFill,
  ChevronLeft,
  ChevronRight,
} from "react-bootstrap-icons";

const SpeechToggle = () => {
  const { isSpeechOn, toggleSpeech, language, setLanguage } = useSpeech();
  const [expanded, setExpanded] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);


  return (
    <div
      className="position-fixed"
      style={{
        top: "20px",
        right: expanded ? "20px" : "0",
        zIndex: 1050,
        transition: "right 0.3s ease-in-out",
      }}
    >
      <Card
        bg="light"
        className="shadow-sm border-0 d-flex flex-row align-items-center"
        style={{
          padding: "10px",
          borderRadius: "1rem 0 0 1rem",
          backdropFilter: "blur(5px)",
        }}
      >
        {/* Collapse / Expand Toggle Button */}
        <Button
          variant="outline-secondary"
          size="sm"
          onClick={() => setExpanded((prev) => !prev)}
          className="d-flex align-items-center justify-content-center"
          style={{
            borderRadius: "50%",
            width: "30px",
            height: "30px",
          }}
        >
          {expanded ? <ChevronLeft /> : <ChevronRight />}
        </Button>

        {expanded && (
          <ButtonToolbar className="ms-2 d-flex align-items-center gap-2">
            {/* Speech Toggle Button */}
            <Button
              variant={isSpeechOn ? "success" : "danger"}
              onClick={toggleSpeech}
              className="d-flex align-items-center gap-2"
              size="sm"
            >
              {isSpeechOn ? <VolumeUpFill /> : <VolumeMuteFill />}
              {isSpeechOn ? "Speech ON" : "Speech OFF"}
            </Button>

            {/* Language Dropdown */}
            <Dropdown as={ButtonGroup} show={dropdownOpen} onToggle={setDropdownOpen}>
              <Dropdown.Toggle variant="secondary" size="sm">
                {language === "hi-IN" ? "🇮🇳 हिन्दी" : "🇬🇧 English"}
              </Dropdown.Toggle>
              <Dropdown.Menu>
                <Dropdown.Item onClick={() => setLanguage("en-IN")}>
                  🇬🇧 English
                </Dropdown.Item>
                <Dropdown.Item onClick={() => setLanguage("hi-IN")}>
                  🇮🇳 हिन्दी
                </Dropdown.Item>
              </Dropdown.Menu>
            </Dropdown>
          </ButtonToolbar>
        )}
      </Card>
    </div>
  );
};

export default SpeechToggle;
