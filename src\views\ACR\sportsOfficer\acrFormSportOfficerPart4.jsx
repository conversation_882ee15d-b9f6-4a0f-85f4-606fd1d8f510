import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    FormGroup,
    Col,
    Label,
    Button,
    Input,
} from "reactstrap";

import Header from "../../../components/Headers/Header";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";
import axios from "axios";


const ACRForSportsOfficerPart4 = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);

    // // console.log(employee, "employeeGetting Employee");


    const [formData, setFormData] = useState({
        applicationId: employee[0].applicationId,
        employeeId: employee[0].employeeId,
        employeeName: employee[0].employeeName,
        collegeName: employee[0].collegeName,
        collegeId: employee[0].collegeId,
        vartmanVetan: employee.currentSalary,
        shreniKaran2: "",
        remark2: "",
    })
    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" });
    };

    const handleFinalSubmit = async (e) => {

        // Validation function to check for required fields
        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {
                shreniKaran2: "",
                remark2: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";
                    }
                }
            }
            return newErrors;
        };



        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }
        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
        e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert("अंतिम मतांकन सफल हुआ", "success");
                    setTimeout(() => window.location.reload(), 3000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };

    return (
        <>
            <Container className="mt--4" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2">कार्यालय, आयुक्त, उच्च शिक्षा, छत्तीसगढ़ रायपुर</h2>
                                <h2> गोपनीय प्रतिवेदन प्रपत्र </h2>
                                <h3>31 मार्च {year} को समाप्त होने वाले वर्ष के लिये</h3>
                                <br /><h2>क्रीड़ा अधिकारी संवर्ग</h2>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong> नाम
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={employee[0].basicDetails[0].employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                {employee[0].basicDetails[0].gender === "Female" &&

                                                    <Col md="3" className="mt--4">
                                                        <Label><strong>1.(अ)</strong>(महिला अधिकारी विवाह के पूर्व का नाम भी लिखें)</Label>
                                                        <Input
                                                            type="text"
                                                            name="secondaryName"
                                                            value={employee[0].basicDetails[0].secondaryName} // Use employee data here
                                                            className="form-control"
                                                            readOnly
                                                            onChange={handleInputChange}
                                                        />
                                                        {errors.secondaryName && (
                                                            <small className="text-danger">
                                                                {errors.secondaryName}
                                                            </small>
                                                        )}
                                                    </Col>}
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पिता/पति का नाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="pitaPatiKaNaam"
                                                        readOnly
                                                        value={employee[0].basicDetails[0].pitaPatiKaNaam} // Use employee data here
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.pitaPatiKaNaam && (
                                                        <small className="text-danger">
                                                            {errors.pitaPatiKaNaam}
                                                        </small>
                                                    )}

                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong> जन्म तिथि</Label>
                                                    <Input
                                                        type="date"
                                                        name="dateOfBirth"
                                                        value={employee[0].basicDetails[0].dateOfBirth} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.dateOfBirth && (
                                                        <small className="text-danger">
                                                            {errors.dateOfBirth}
                                                        </small>
                                                    )}

                                                </Col>

                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="3">
                                                    <Label>
                                                        <strong>4.</strong> शैक्षणिक योग्यता:
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="saikshanikYogyta"
                                                        value={employee[0].basicDetails[0].saikshanikYogyta} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.saikshanikYogyta && (
                                                        <small className="text-danger">
                                                            {errors.saikshanikYogyta}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(अ )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति का दिनांक
                                                    </Label>
                                                    <Input
                                                        type="date"
                                                        name="niyuktiDinank"
                                                        value={employee[0].basicDetails[0].niyuktiDinank} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.niyuktiDinank && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiDinank}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3" className="mt--4">
                                                    <Label><strong>5.(ब )</strong>वर्तमान पद पर नियुक्ति / पदोन्नति प्रकार
                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="niyuktiPrakar"
                                                        value={employee[0].basicDetails[0].niyuktiPrakar} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.niyuktiPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyuktiPrakar}
                                                        </small>
                                                    )}
                                                </Col>

                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row>
                                        <Col>
                                            <Row className="d-block mt-3 mb-3">
                                                <Col>
                                                    <Row>
                                                        <Col>
                                                            <Label><strong>6.</strong> वेतनमान
                                                            </Label>
                                                        </Col>
                                                    </Row>
                                                    <Row>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(अ) वर्तमान वेतनमान
                                                            </Label>
                                                            <Input
                                                                type="text"
                                                                name="vartmanVetan"
                                                                readOnly
                                                                value={employee[0].basicDetails[0].vartmanVetan} // Use employee data here
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.vartmanVetan && (
                                                                <small className="text-danger">
                                                                    {errors.vartmanVetan}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(ब) वरिष्ठ श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="varisthShreniParitDinak"
                                                                readOnly
                                                                value={employee[0].basicDetails[0].varisthShreniParitDinak} // Use employee data here
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.varisthShreniParitDinak && (
                                                                <small className="text-danger">
                                                                    {errors.varisthShreniParitDinak}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="3">
                                                            <Label style={{ fontSize: "15px" }}>(स) प्रवर श्रेणी वेतनमान पारित दिनांक
                                                            </Label>
                                                            <Input
                                                                type="date"
                                                                name="prawarShreniParitDinank"
                                                                readOnly
                                                                value={employee[0].basicDetails[0].prawarShreniParitDinank} // Use employee data here
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.prawarShreniParitDinank && (
                                                                <small className="text-danger">
                                                                    {errors.prawarShreniParitDinank}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>7.</strong> वर्ष में किस महाविद्यालय में पदस्थ रहे.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="sewaKalMahavidyalayaPadankit"
                                                        value={employee[0].basicDetails[0].sewaKalMahavidyalayaPadankit} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}

                                                    />
                                                    {errors.sewaKalMahavidyalayaPadankit && (
                                                        <small className="text-danger">
                                                            {errors.sewaKalMahavidyalayaPadankit}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt--4">
                                                    <Label><strong>8. </strong>विचाराधीन वर्ष में खेलकूद आदि की व्यवस्था बढ़ाने में प्रमुख महयोग / प्रयास
                                                        ( क्रीडा अधिकारी उन्हें सौपे गये कार्यों के निष्पादन प्रतिवेदन संलग्न करें)
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="vicharDhinYogdan"
                                                        value={employee[0].basicDetails[0].vicharDhinYogdan} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}

                                                    />
                                                    {errors.vicharDhinYogdan && (
                                                        <small className="text-danger">
                                                            {errors.vicharDhinYogdan}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="text-center mt-5">
                                                <Col>
                                                    <h2><u>प्रतिवेदक अधिकारी की अभ्युक्ति</u></h2>
                                                </Col>
                                            </Row>
                                            <Row className="d-block mt-5 mb-3">
                                                <Col>
                                                    <Row className="mb-4">
                                                        <Col md="6" >
                                                            <Label> <strong>1. </strong>विचाराधीन वर्ष में खेलकूद आदि की
                                                                व्यवस्था बढ़ाने में प्रमुख सहयोग / प्रयास
                                                            </Label>
                                                            <Input
                                                                type="textarea"
                                                                name="viharaDhinSafalta"
                                                                value={employee[0].levelDetails[0].data.viharaDhinSafalta} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.viharaDhinSafalta && (
                                                                <small className="text-danger">
                                                                    {errors.viharaDhinSafalta}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="6" >
                                                            <Label> <strong>2. </strong>उच्चाधिकारियों, सहकर्मियों तथा अधिनस्थों से संबंध :
                                                            </Label>
                                                            <Input
                                                                type="textarea"
                                                                name="sahkarmiyonSeSambandh"
                                                                value={employee[0].levelDetails[0].data.sahkarmiyonSeSambandh} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.sahkarmiyonSeSambandh && (
                                                                <small className="text-danger">
                                                                    {errors.sahkarmiyonSeSambandh}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="6">
                                                            <Label> <strong>3. </strong>समग्र व्यक्तित्व :</Label>
                                                            <Input
                                                                type="textarea"
                                                                name="samgraVyaktitva"
                                                                value={employee[0].levelDetails[0].data.samgraVyaktitva} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.samgraVyaktitva && (
                                                                <small className="text-danger">
                                                                    {errors.samgraVyaktitva}
                                                                </small>
                                                            )}
                                                        </Col>

                                                        <Col md="6">
                                                            <Label> <strong>4. </strong>कोई प्रतिकूल टीका / दंड दिया गया हो तो उल्लेख करें :</Label>
                                                            <Input
                                                                type="textarea"
                                                                name="pratikulDandUlekh"
                                                                value={employee[0].levelDetails[0].data.pratikulDandUlekh} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.pratikulDandUlekh && (
                                                                <small className="text-danger">
                                                                    {errors.pratikulDandUlekh}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="6">
                                                            <Label> <strong>5. </strong>निष्ठा :</Label>
                                                            <Input
                                                                type="textarea"
                                                                name="nishtha"
                                                                value={employee[0].levelDetails[0].data.nishtha} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.nishtha && (
                                                                <small className="text-danger">
                                                                    {errors.nishtha}
                                                                </small>
                                                            )}
                                                        </Col>
                                                        <Col md="6">
                                                            <Label> <strong>6. </strong>(अ)समग्र मूल्याकंन
                                                                :</Label>
                                                            <Input
                                                                type="select"
                                                                name="samagraMulyankan"
                                                                value={employee[0].levelDetails[0].data.samagraMulyankan} // Use employee data here
                                                                className="form-control"
                                                                readOnly
                                                                onChange={handleInputChange}
                                                            >
                                                                <option value="">विकल्प चुने </option>
                                                                <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                <option value="अच्छा">अच्छा</option>
                                                                <option value="साधारण">साधारण</option>
                                                                <option value="घटिया">घटिया</option>

                                                            </Input>
                                                            {errors.samagraMulyankan && (
                                                                <small className="text-danger">
                                                                    {errors.samagraMulyankan}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <Row className="mt-4">
                                                        <Col md="12">
                                                            <Label> <strong>6. </strong>(ब) (उत्कृष्ठ या घटिया वर्गीकरण करने पर इसका
                                                                औचित्य भी स्पष्टतः अकिंत करें ) :</Label>
                                                            <Input
                                                                type="textarea"
                                                                name="grading"
                                                                readOnly
                                                                style={{ height: "100px" }}

                                                                value={employee[0].levelDetails[0].data.grading} // Use employee data here
                                                                className="form-control"
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.grading && (
                                                                <small className="text-danger">
                                                                    {errors.grading}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <hr />
                                                    <Row className="mb-3">
                                                        <Col>
                                                            <h2 style={{ textAlign: "center" }}><u> समीक्षक अधिकारी की टिप्पणी </u>
                                                            </h2>
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="3">
                                                            <Label>श्रेणीकरण</Label>
                                                            <Input
                                                                name="shreniKaran1"
                                                                type="select"
                                                                id="recordsPerPage"
                                                                readOnly
                                                                value={employee[0].levelDetails[1].data.shreniKaran1}
                                                                onChange={handleInputChange}

                                                            >
                                                                <option value="">चुनें</option>
                                                                <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                <option value="अच्छा">अच्छा</option>
                                                                <option value="साधारण">साधारण</option>
                                                                <option value="घटिया">घटिया</option>
                                                            </Input>
                                                            {errors.shreniKaran1 && (
                                                                <small className="text-danger">
                                                                    {errors.shreniKaran1}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="12">
                                                            <Label>रिमार्क</Label>
                                                            <Input
                                                                name="remark1"
                                                                type="textarea"
                                                                style={{ height: "100px" }}
                                                                readOnly
                                                                value={employee[0].levelDetails[1].data.remark1}
                                                                onChange={handleInputChange}

                                                            />
                                                            {errors.remark1 && (
                                                                <small className="text-danger">
                                                                    {errors.remark1}
                                                                </small>)}
                                                        </Col>
                                                    </Row>
                                                    <hr />
                                                    <Row className="mb-3">
                                                        <Col>
                                                            <h2 style={{ textAlign: "center" }}>
                                                                <u>स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                            </h2>
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="3">
                                                            <Label>श्रेणीकरण</Label>
                                                            <Input
                                                                name="shreniKaran2"
                                                                type="select"
                                                                id="recordsPerPage"
                                                                value={formData.shreniKaran2}
                                                                onChange={handleInputChange}
                                                            >
                                                                <option value="">चुनें</option>
                                                                <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                <option value="अच्छा">अच्छा</option>
                                                                <option value="साधारण">साधारण</option>
                                                                <option value="घटिया">घटिया</option>
                                                            </Input>
                                                            {errors.shreniKaran2 && (
                                                                <small className="text-danger">
                                                                    {errors.shreniKaran2}
                                                                </small>
                                                            )}
                                                        </Col>
                                                    </Row>
                                                    <Row className="mb-4">
                                                        <Col md="12">
                                                            <Label>रिमार्क</Label>
                                                            <Input
                                                                name="remark2"
                                                                type="textarea"
                                                                style={{ height: "100px" }}
                                                                maxLength={500}
                                                                value={formData.remark2}
                                                                onChange={handleInputChange}
                                                            />
                                                            {errors.remark2 && (
                                                                <small className="text-danger">{errors.remark2}</small>
                                                            )}
                                                        </Col>
                                                    </Row>

                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Button color="success" onClick={handleFinalSubmit}>
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRForSportsOfficerPart4.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRForSportsOfficerPart4;
