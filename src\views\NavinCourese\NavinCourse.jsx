import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Badge,
  Table,
  CardFooter,
  Container,
  Row,
  Col,

  Modal,
  <PERSON>dalHeader,
  <PERSON>dal<PERSON>ody,
  <PERSON>dalFooter,

  Label,
  Spinner,
} from "reactstrap";
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";
import { useNavigate } from "react-router-dom";

const JanBhagiDariCourse = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const navigate = useNavigate()

  const [modal, setModal] = useState(true);
  const toggle = () => setModal(!modal);

  const id = sessionStorage.getItem("id");

  const [clgInfo, setClgInfo] = useState([]);
  const [collegeType, setCollegeType] = useState('');
  const [sanchalanType, setSanchalanType] = useState('');
  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);


  useEffect(() => {
    const fetchCollegeInfo = async () => {

      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-college/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setClgInfo(response.data);
          // console.log(response.data.degreeTypes, "Geting degree Type");
          // console.log(response.data, "Geting degree Type");


          setCollegeType(response.data.degreeTypes)
        } else {
          alert("College Not Found.");
        }
      } catch (error) {
        console.error("An error occurred while Getting Data:", error);
        alert("An error occurred. Please try again later.");
      }
    }
    fetchCollegeInfo();
  }, []);





  const handleProceed = (e) => {

    e.preventDefault();

    if (collegeType != "" && sanchalanType !== "") {
      toggle();
    }
    else {
      SwalMessageAlert("Please Select Above Option", "error")
    }

    if (sanchalanType === "government") {
      navigate('/admin/new-course-govt')
    }

    const newNavinVishay = [{
      subject: '',
      course: '',
      className: '',
      requiredSeat: '',
      type: sanchalanType // Set the type to collegeType
    }];

    setFormData(prevFormData => ({
      ...prevFormData,
      courseType: collegeType,
      navinVishay: newNavinVishay // Update navinVishay with the new array
    }));
  };

  const currentYear = new Date().getFullYear();

  const [formData, setFormData] = useState({
    collegeId: id,
    courseType: collegeType,
    navinVishay: [{ subject: '', course: '', className: '', requiredSeat: '', type: sanchalanType }],
    poshakShalaStudent: [{ school: '', arts: '', science: '', commerce: '' }],
    snatakStudent: [{ subject: '', passedStudent: '', lastYearStudent: '' }],
    sambandhitVishwaVidyalaya: [{ university: '', certificate: '' }],
    mahavidyalayaJankari: [{ selfBuilding: '', classroomNo: '', furnitureNo: '', labsNo: '', booksNo: '' }],
    previousCourseSubjects: [{ subject: '', course: '', className: '', seats: '', sanchalanType: '' }],
    currentStaff: [{ designation: '', sanctioned: '', working: '', vacant: '' }],
    requiredStaff: [{ designation: '', seats: '', financialLoad: '' }],
    selfTeacherEstimate: [{ subject: '', course: '', className: '', designation: '', seats: '', estimatedSalaryMonthly: '', estimatedSalaryYearly: '' }],
    incomeFromStudent: [{ subject: '', course: '', className: '', yearlyFees: '', totalIncome: '' }],
    lastTwoYearRecord: [{
      typeName: 'selfFinance',
      year: `${currentYear - 1}-${(currentYear).toString().slice(2)}`,
      income: '',
      expense: '',
      balance: ''
    },
    {
      typeName: 'selfFinance',
      year: `${currentYear - 2}-${(currentYear - 1).toString().slice(2)}`,
      income: '',
      expense: '',
      balance: ''
    },
    {
      typeName: 'janBhagidari',
      year: `${currentYear - 1}-${(currentYear).toString().slice(2)}`,
      income: '',
      expense: '',
      balance: ''
    },
    {
      typeName: 'janBhagidari',
      year: `${currentYear - 2}-${(currentYear - 1).toString().slice(2)}`,
      income: '',
      expense: '',
      balance: ''
    }],
    remarkByPrincipal: "",
  });

  const handleInputRemark = (e) => {
    const { value } = e.target;
    setFormData(prevFormData => ({
      ...prevFormData,
      remarkByPrincipal: value // Update the specific property
    }));


  }



  const [designations, setDesignations] = useState([]);
  useEffect(() => {
    const fetchDesignations = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.status === 200) {
          const verifiedDesignations = response.data.filter(
            (designation) => designation.isVerified === 1
          );

          setDesignations(verifiedDesignations);
        } else {
          alert("Failed to fetch designations. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchDesignations();
  }, [endPoint, token]);


  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/university/get-all-university`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          setUniversity(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    // Call the function
    fetchUniversity();

    // Optionally add dependencies in the dependency array
  }, [endPoint, token]);

  const [subjectData, setSubjectData] = useState([]);
  useEffect(() => {
    const fetchSubjectList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-subjects`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setSubjectData(response.data);
        } else {
          SwalMessageAlert("No Subject Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchSubjectList();
  }, [endPoint, token]);

  const [courseData, setcourseData] = useState([]);
  useEffect(() => {
    const fetchcourseList = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/get-course`, {
          headers: {'web-url': window.location.href, Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setcourseData(response.data);
        } else {
          SwalMessageAlert("No course Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchcourseList();
  }, [endPoint, token]);

  const handleChange = (tableName, index, e) => {
    const { name, value, files } = e.target;

    // console.log(name, "Geting name");

    const updatedTable = [...formData[tableName]];


    if (name === "certificate") {
      // Handle file input
      if (files && files.length > 0) {
        // Assuming you want to store the file object
        updatedTable[index][name] = files[0]; // Store the first file
      }
    }


    updatedTable[index][name] = value;
    setFormData({ ...formData, [tableName]: updatedTable });


  };

  const handleAddRow = (tableName) => {
    const newRow = {};
    // Initialize new row based on the table structure
    switch (tableName) {
      case 'navinVishay':
        Object.assign(newRow, { subject: '', course: '', className: '', requiredSeat: '', type: '' });
        break;
      case 'poshakShalaStudent':
        Object.assign(newRow, { school: '', arts: '', science: '', commerce: '' });
        break;
      case 'snatakStudent':
        Object.assign(newRow, { subject: '', passedStudent: '', lastYearStudent: '' });
        break;
      case 'sambandhitVishwaVidyalaya':
        Object.assign(newRow, { university: '', certificate: '' });
        break;
      case 'mahavidyalayaJankari':
        Object.assign(newRow, { selfBuilding: '', classroomNo: '', furnitureNo: '', labsNo: '', booksNo: '' });
        break;
      case 'previousCourseSubjects':
        Object.assign(newRow, { subject: '', course: '', className: '', seats: '', sanchalanType: '' });
        break;
      case 'currentStaff':
        Object.assign(newRow, { designation: '', sanctioned: '', working: '', vacant: '' });
        break;
      case 'requiredStaff':
        Object.assign(newRow, { designation: '', seats: '', financialLoad: '' });
        break;
      case 'selfTeacherEstimate':
        Object.assign(newRow, { subject: '', course: '', className: '', designation: '', seats: '', estimatedSalaryMonthly: '', estimatedSalaryYearly: "" });
        break;
      case 'incomeFromStudent':
        Object.assign(newRow, { subject: '', course: '', className: '', yearlyFees: '', totalIncome: '' });
        break;
      case 'lastTwoYearRecord':
        Object.assign(newRow, { typeName: '', year: '', income: '', expense: '', balance: '' });
        break;
      default:
        break;
    }
    setFormData({ ...formData, [tableName]: [...formData[tableName], newRow] });
  };

  const handleCertificateChange = (e, index) => {
    const file = e.target.files[0];
  
    setFormData(prev => {
      const updated = { ...prev };
      updated.sambandhitVishwaVidyalaya[index].certificate = file;  // Store file object
      return updated;
    });
  };

  // console.log("Getting Form Data",formData);
  

  const handleRemoveRow = (tableName, index) => {
    const updatedTable = formData[tableName].filter((_, i) => i !== index);
    setFormData({ ...formData, [tableName]: updatedTable });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const form = new FormData();

    form.append("collegeId", formData.collegeId);
    form.append("courseType", formData.courseType);
    form.append("remarkByPrincipal", formData.remarkByPrincipal);
  
    // Utility to loop nested array
    const appendNestedArray = (key, array) => {
      array.forEach((item, i) => {
        Object.entries(item).forEach(([subKey, value]) => {
          if (subKey === "certificate" && value instanceof File) {
            form.append(`${key}[${i}][${subKey}]`, value);
          } else {
            form.append(`${key}[${i}][${subKey}]`, value ?? "");
          }
        });
      });
    };
  
    // Append all nested arrays
    appendNestedArray("navinVishay", formData.navinVishay);
    appendNestedArray("poshakShalaStudent", formData.poshakShalaStudent);
    appendNestedArray("snatakStudent", formData.snatakStudent);
    appendNestedArray("sambandhitVishwaVidyalaya", formData.sambandhitVishwaVidyalaya);
    appendNestedArray("mahavidyalayaJankari", formData.mahavidyalayaJankari);
    appendNestedArray("previousCourseSubjects", formData.previousCourseSubjects);
    appendNestedArray("currentStaff", formData.currentStaff);
    appendNestedArray("requiredStaff", formData.requiredStaff);
    appendNestedArray("selfTeacherEstimate", formData.selfTeacherEstimate);
    appendNestedArray("incomeFromStudent", formData.incomeFromStudent);
    appendNestedArray("lastTwoYearRecord", formData.lastTwoYearRecord);


    try {

      // console.log('Form Data:', formData);
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {

        const response = await axios.post(`${endPoint}/api/newCourse/add`, form, {
          headers: {
            "Content-Type": "multipart/form-data",
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.status === 200) {
          // setFormData({});
          SwalMessageAlert("New Course Application Requested successfully.", "success");
          setTimeout(() => navigate('/admin/new-course-application-list'), 2000);
        }
        else {
          SwalMessageAlert(response.data.msg, "error");
        }
      } else {
        SwalMessageAlert("Application Request Failed", "error");
      }

    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("Application Request Failed.");
    }
  };

  const [SeatSubjectEnableData, setSeatSubjectEnableData] = useState([]);
  useEffect(() => {
    const getSeatSubjectEnableData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/seat-subject/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          }
        });
        if (response.status === 200) {
          setSeatSubjectEnableData(response.data);

          // console.log(response.data[2].type,"Log Getting Type of enable");

          if (response.data[1].status === false && response.data[2].status === false) {
            SwalMessageAlert("नवीन विषय हेतु आवेदन अभी बंद है", "error");
            navigate("/admin/dashboard")
          }

        } else {
          SwalMessageAlert("No Data Found", "error");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    }
    getSeatSubjectEnableData();
  }, [endPoint, token]);


  return (
    <>
      <Header />
      <Modal backdrop="static"
        keyboard={false} isOpen={modal} toggle={toggle}>
        <ModalHeader toggle={toggle}>संस्था का प्रकार एवं संचालन का प्रकार चुनें </ModalHeader>
        <ModalBody className="d-flex">
          <Col>
            <Input
              type="select"
              value={collegeType}
              onChange={(e) => setCollegeType(e.target.value)}
              required
              // disabled={clgInfo.degreeTypes === "UG"}
            >
              <option value="" disabled>Select College Type</option>
              <option value="UG">UG</option>
              <option value="PG">PG</option>
            </Input>
          </Col>
          <Col>
            <Input
              type="select"
              value={sanchalanType}
              onChange={(e) => setSanchalanType(e.target.value)}
              required
            >
              <option value="" disabled>Select Sanchalan Type</option>
              {SeatSubjectEnableData[2]?.status === true && <>
                <option value="janBhagidari">जनभागीदारी</option>
                <option value="selfFinance">स्व-वित्तीय</option> </>}

              {SeatSubjectEnableData[1]?.status === true &&
                <option value="government">शासकीय </option>
              }
            </Input>
          </Col>
        </ModalBody>
        <ModalFooter>
          <Button color="primary" onClick={handleProceed}>Proceed</Button>
          <Button color="secondary" onClick={() => { navigate('/admin/dashboard'); }}>Cancel</Button>
        </ModalFooter>
      </Modal>

      <Modal style={{ maxWidth: "80%" }} isOpen={previewModal} toggle={togglePreviewModal}>
        <ModalHeader toggle={togglePreviewModal}>Preview</ModalHeader>
        <ModalBody>
          <Row>
            <Col >
              <Card className="bg-secondary shadow">
                <CardHeader className="bg-white border-0">
                  <Row className="align-items-center">
                    <Col>
                      <h2 className="mb-0 text-center">शैक्षणिक सत्र {currentYear}- {(currentYear + 1).toString().slice(2)} में स्ववित्तीय/जनभागीदारी मद से नवीन विषय/संकाय/कक्षा को प्रारंभ करने हेतु ऑनलाईन  <br /> आवेदन पत्र <br />
                        (टीप:- प्रत्येक नवीन संकाय/कक्षा हेतु अलग-अलग आवेदन देना अनिवार्य है।)
                      </h2>
                    </Col>
                  </Row>
                </CardHeader>
                <CardBody>
                  <Form >
                    <Row>
                      <Col>
                        <Label>
                          स्ववित्तीय जनभागीदारी मद से प्रारंभ करने वाले प्रस्तावित नवीन संकाय कक्षा का नाम एवं वांछित सीट संख्या <b> (स्नातक या स्नातकोत्तर स्तर का उल्लेख करें)</b>
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}> संकाय का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कक्षा का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वांछित सीट संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.navinVishay.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="subject"
                                  value={item.subject}
                                  placeholder="Subject"
                                  disabled
                                  required
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {subjectData.length > 0 ? (
                                    subjectData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.subjectName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="course"
                                  value={item.course} 
                                  disabled
                                  placeholder="Course"
                                  required
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="className"
                                  value={item.className}
                                  disabled
                                  placeholder="Class Name"
                                  required
                                >
                                  <option value="" disabled>Select Year</option>
                                  <option value="1stYear">1st Year</option>
                                  <option value="2ndYear">2nd Year</option>
                                  <option value="3rdYear">3rd Year</option>
                                  <option value="4thYear">4th Year</option>
                                </Input>

                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="requiredSeat"
                                  value={item.requiredSeat} disabled

                                  placeholder="Required Seat"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="type"
                                  value={item.type}
                                  disabled

                                  placeholder="Type"
                                  required
                                >
                                  <option value="" disabled>Select Type</option>
                                  <option value="janBhagidari">जनभागीदारी</option>
                                  <option value="selfFinance">स्व-वित्तीय</option>
                                </Input>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Row>

                    {collegeType === "UG" && <>
                      <hr />
                      <Row className="mt-2">
                        <Col>
                          <Label>
                            <b>स्नातक स्तर पर </b>  प्रस्तावित नवीन नवीन संकाय/कक्षा प्रारंभ करने हेतु
                            पोषक शालाओं में संबंधित
                            संकाय/विषय में छात्र संख्या एवं
                            गतवर्ष उत्तीर्ण छात्र संख्या ।

                          </Label>
                        </Col>
                      </Row>
                      <Row>
                        <Table striped>
                          <thead>
                            <tr>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}> पोषक शाला का नाम</th>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कला</th>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विज्ञान</th>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वाणिज्य</th>
                            </tr>
                          </thead>
                          <tbody>
                            {formData.poshakShalaStudent.map((item, index) => (
                              <tr key={index}>
                                <td>
                                  <Input
                                    type="text"
                                    name="school"
                                    value={item.school}
                                    disabled
                                    placeholder="School"
                                    required
                                  />
                                </td>
                                <td>
                                  <Input
                                    type="number"
                                    name="arts"
                                    value={item.arts}
                                    disabled
                                    placeholder="Arts"
                                    required
                                  />
                                </td>
                                <td>
                                  <Input
                                    type="number"
                                    name="science"
                                    value={item.science}
                                    disabled
                                    placeholder="Science"
                                    required
                                  />
                                </td>
                                <td>
                                  <Input
                                    type="number"
                                    name="commerce"
                                    value={item.commerce}
                                    disabled
                                    placeholder="Commerce"
                                    required
                                  />
                                </td>

                              </tr>
                            ))}
                          </tbody>
                        </Table>

                      </Row>

                    </>}
                    {collegeType === "PG" && <>
                      <hr />

                      <Row className="mt-2">
                        <Col>
                          <Label>
                            <b> स्नातकोत्तर स्तर पर </b> प्रस्तावित नवीन
                            नवीन संकाय/कक्षा प्रारंभ करने हेतु पोषक कक्षा <b>(संबंधित विषय के स्नातक अंतिम वर्ष में अध्ययनरत छात्र संख्या एवं गत वर्षों में उत्तीर्ण छात्र संख्या।)</b>
                          </Label>
                        </Col>
                      </Row>
                      <Row>
                        <Table striped>
                          <thead>
                            <tr>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय </th>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}>गत वर्ष स्नातक अंतिम <br /> वर्ष में उत्तीर्ण छात्र संख्या</th>
                              <th style={{ fontSize: "12px", paddingLeft: "40px" }}>चालू सत्र में संबंधित विषय के स्नातक <br /> अंतिम वर्ष में अध्ययनरत छात्र संख्या</th>
                            </tr>
                          </thead>
                          <tbody>
                            {formData.snatakStudent.map((item, index) => (
                              <tr key={index}>
                                <td>
                                  <Input
                                    type="select"
                                    name="subject"
                                    value={item.subject}
                                    disabled
                                    placeholder="Subject"
                                    required
                                  >
                                    <option>
                                      No options available
                                    </option>
                                    {subjectData.length > 0 ? (
                                      subjectData.map((type, index) => (
                                        <option key={index} value={type.id}>
                                          {type.subjectName}
                                        </option>
                                      ))
                                    ) : (
                                      <option disabled>
                                        No options available
                                      </option>
                                    )}
                                  </Input>
                                </td>
                                <td>
                                  <Input
                                    type="number"
                                    name="passedStudent"
                                    value={item.passedStudent}
                                    disabled
                                    placeholder="Passed Student"
                                    required
                                  />
                                </td>
                                <td>
                                  <Input
                                    type="number"
                                    name="lastYearStudent"
                                    value={item.lastYearStudent}
                                    disabled
                                    placeholder="Last Year Student"
                                    required
                                  />
                                </td>

                              </tr>
                            ))}
                          </tbody>
                        </Table>

                      </Row>
                    </>}
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          उक्त प्रस्तावित नवीन संकाय/कक्षा की संबंद्धता किस विश्वविद्यालय से प्राप्त की जावेगी।     {"    "}
                          <b>(संबंधित विश्वविद्यालय से प्रस्तावित नवीन विषय/संकाय संचालित होने का प्रमाण पत्र अनिवार्य रूप से संलग्न करें)</b>
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>सम्बंधित विश्वविद्यालय का नाम </th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विश्वविद्यालय द्वारा जारी प्रमाण पत्र <span className="text-danger">(केवल पीडीएफ फॉरमेट में )</span> </th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.sambandhitVishwaVidyalaya.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="university"
                                  value={item.university}
                                  disabled
                                  placeholder="University"
                                  required
                                >
                                  <option value="">Select University </option>
                                  {university &&
                                    university.length > 0 &&
                                    university.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.name}
                                      </option>
                                    ))}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="name"
                                  name="certificate"
                                  value={item.certificate.name}
                                  disabled
                                  placeholder="Certificate"
                                  required
                                />
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          क्या उक्त प्रस्तावित नवीन संकाय कक्षा में
                          प्रवेश प्राप्त छात्रों के अध्यापन हेतु पृथक से
                          स्ववित्तीय जनभागीदारी मद से किसी
                          शैक्षणिक स्टॉफ नियुक्त किया
                          जावेगा? <b>(यदि हॉ)</b>  तो शैक्षणिक स्टॉफ के
                          अनुमानित मासिक वेतन की जानकारी (उदाहरण अनुसार)
                        </Label>
                      </Col>
                    </Row>
                    <Row className="d-block">
                      <table >
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "4px" }}>विषय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px" }}>संकाय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px" }}>कक्षा का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px", width: "300px" }}>शैक्षणिक स्टॉफ</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px", width: "60px" }}>पद संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px", width: "130px" }}>अनुमानित मासिक वेतन</th>
                            <th style={{ fontSize: "12px", paddingLeft: "4px", width: "150px" }}>वार्षिक वित्तीय भार (मासिक वेतन X पदों की संख्या X 12)
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.selfTeacherEstimate.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="subject"
                                  value={item.subject}
                                  disabled
                                  placeholder="Subject"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {subjectData.length > 0 ? (
                                    subjectData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.subjectName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="course"
                                  value={item.course}
                                  disabled
                                  placeholder="Course"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                               
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  name="className"
                                  type="select"
                                  value={item.className}
                                  disabled
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                >
                                  <option value="" disabled>Select Year</option>
                                  <option value="1stYear">1st Year</option>
                                  <option value="2ndYear">2nd Year</option>
                                  <option value="3rdYear">3rd Year</option>
                                  <option value="4thYear">4th Year</option>
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="designation"
                                  value={item.designation}
                                  disabled
                                  placeholder="Designation"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                >
                                  <option value="">Select Designation</option>
                                  {designations &&
                                    designations.length > 0 &&
                                    designations.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.designation}
                                      </option>
                                    ))}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="seats"
                                  value={item.seats}
                                  disabled
                                  placeholder="Seats"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="estimatedSalaryMonthly"
                                  value={item.estimatedSalaryMonthly}
                                  disabled
                                  placeholder="Estimated Salary Monthly"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="estimatedSalaryYearly"
                                  value={item.estimatedSalaryYearly}
                                  disabled
                                  placeholder="Estimated Salary Yearly"
                                  required
                                  style={{ margin: 0 }} // Remove margin
                                />
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </table>


                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          उक्त प्रस्तावित नवीन संकाय कक्षा में प्रवेश हेतु प्रति छात्र कितना
                          अनुमानित वार्षिक शुल्क निर्धारित होगा तथा सीट संख्या अनुसार
                          प्रति छात्र हेतु निर्धारित वार्षिक शुल्क से कितना वार्षिक
                          आय प्राप्त होगा, की जानकारी देवें।
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रति छात्र हेतु <br /> निर्धारित वार्षिक शुल्क
                            </th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वार्षिक आय (सीट संख्या X प्रति छात्र निर्धारित वार्षिक शुल्क)</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.incomeFromStudent.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="subject"
                                  value={item.subject}
                                  disabled
                                  placeholder="Subject"
                                  required
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {subjectData.length > 0 ? (
                                    subjectData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.subjectName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="course"
                                  value={item.course}
                                  disabled
                                  placeholder="Course"
                                  required
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="className"
                                  value={item.className}
                                  disabled
                                  placeholder="Class Name"
                                  required
                                >
                                  <option value="" disabled>Select Year</option>
                                  <option value="1stYear">1st Year</option>
                                  <option value="2ndYear">2nd Year</option>
                                  <option value="3rdYear">3rd Year</option>
                                  <option value="4thYear">4th Year</option>
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="yearlyFees"
                                  value={item.yearlyFees}
                                  disabled
                                  placeholder="Yearly Fees"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="totalIncome"
                                  value={item.totalIncome}
                                  disabled
                                  placeholder="Total Income"
                                  required
                                />
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </Table>

                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          पिछले 02 वर्षों में स्ववित्तीय/जनभागीदारी मद अंतर्गत प्राप्त आय एवं व्यय का विवरण

                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वर्ष</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्राप्त आय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>व्यय राशि</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>शेष राशि</th>

                          </tr>
                        </thead>
                        <tbody>
                          {formData.lastTwoYearRecord.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="typeName"
                                  value={item.typeName}
                                  disabled
                                  placeholder="Type Name"
                                  required
                                >
                                  <option value="" disabled>Select Type</option>
                                  <option value="janBhagidari">जनभागीदारी</option>
                                  <option value="selfFinance">स्व-वित्तीय</option>
                                  <option value="government">शासकीय </option>
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="text"
                                  name="year"
                                  value={item.year}
                                  disabled
                                  placeholder="Year"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="income"
                                  value={item.income}
                                  disabled
                                  placeholder="Income"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="expense"
                                  value={item.expense}
                                  disabled
                                  placeholder="Expense"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="balance"
                                  value={item.balance}
                                  disabled
                                  placeholder="Balance"
                                  required
                                />
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </Table>

                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          महाविद्यालय में उपलब्ध भवन/
                          अध्ययन कक्ष/फर्नीचर संख्या/प्रयोगशाला कक्ष संख्या प्रस्तावित नवीन विषय/संकाय की पुस्तक संख्या की जानकारी

                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वयं का भवन (हॉ/नही)</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>अध्ययन कक्ष संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>फर्नीचर संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रयोगशाला कक्ष संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रस्तावित नवीन
                              विषय संकाय की पुस्तक
                              संख्या
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.mahavidyalayaJankari.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="selfBuilding"
                                  value={item.selfBuilding}
                                  disabled
                                  placeholder="Self Building"
                                  required
                                >
                                  <option value="" disabled>Select</option>
                                  <option value="हाँ">हाँ</option>
                                  <option value="नहीं">नहीं</option>
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="classroomNo"
                                  value={item.classroomNo}
                                  disabled
                                  placeholder="Classroom No"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="furnitureNo"
                                  value={item.furnitureNo}
                                  disabled
                                  placeholder="Furniture No"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="labsNo"
                                  value={item.labsNo}
                                  disabled
                                  placeholder="Labs No"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="booksNo"
                                  value={item.booksNo}
                                  disabled
                                  placeholder="Books No"
                                  required
                                />
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          महाविद्यालय में पूर्व से संचालित सभी
                          संकाय/कक्षा का नाम एवं निर्धारित सीट संख्या की जानकारी। टीपः केवल प्रथम वर्ष या प्रथम सेमेस्टर हेतु निर्धारित सीट संख्या की जानकारी देवें।.
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>निर्धारित सीट संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संचालन का प्रकार (स्ववित्तीय योजना/जनभागीदारी मद)</th>

                          </tr>
                        </thead>
                        <tbody>
                          {formData.previousCourseSubjects.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="text"
                                  name="subject"
                                  value={item.subject}
                                  disabled
                                  placeholder="Subject"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="course"
                                  value={item.course}
                                  disabled
                                  placeholder="Course"
                                  required
                                >
                                  <option>
                                    No options Selected
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="className"
                                  value={item.className}
                                  disabled
                                  placeholder="Class Name"
                                  required
                                >
                                  <option value="" disabled>Select Year</option>
                                  <option value="1stYear">1st Year</option>
                                  <option value="2ndYear">2nd Year</option>
                                  <option value="3rdYear">3rd Year</option>
                                  <option value="4thYear">4th Year</option>
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="seats"
                                  value={item.seats}
                                  disabled
                                  placeholder="Seats"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="select"
                                  name="sanchalanType"
                                  value={item.sanchalanType}
                                  disabled
                                  placeholder="Sanchalan Type"
                                  required
                                >
                                  <option value="" disabled>Select Type</option>
                                  <option value="janBhagidari">जनभागीदारी</option>
                                  <option value="selfFinance">स्व-वित्तीय</option>
                                  <option value="government">शासकीय </option>
                                </Input>
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </Table>
                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          वर्तमान में महाविद्यालय हेतु शासन से
                          स्वीकृत समस्त शैक्षणिक एवं
                          अशैक्षणिक पदों की जानकारी (उदाहरण:-
                          प्राध्यापक इतिहास या सहायक प्राध्यापक हिन्दी)
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>पदनाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वीकृत </th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कार्यरत</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>रिक्त</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.currentStaff.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="designation"
                                  value={item.designation}
                                  disabled
                                  placeholder="Designation"
                                  required
                                >
                                  <option value="">Select Designation</option>
                                  {designations &&
                                    designations.length > 0 &&
                                    designations.map((type, index) => (
                                      <option key={index} value={type._id}>
                                        {type.designation}
                                      </option>
                                    ))}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="sanctioned"
                                  value={item.sanctioned}

                                  disabled
                                  placeholder="Sanctioned"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="working"
                                  value={item.working}

                                  disabled
                                  placeholder="Working"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="vacant"
                                  value={item.vacant}

                                  disabled
                                  placeholder="Vacant"
                                  required
                                />
                              </td>

                            </tr>
                          ))}
                        </tbody>
                      </Table>

                    </Row>
                    <hr />
                    <Row>
                      <Col>
                        <Label>
                          प्रस्तावित नवीन संकाय/ कक्षा प्रारंभ
                          करने का औचित्य
                          (अनिवार्य रूप से उल्लेख करें)
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Col>
                        <Input
                          type="textarea"
                          name="remarkByPrincipal"
                          maxLength={1000}
                          disabled
                          value={formData.remarkByPrincipal}
                          placeholder="Remark"
                          required
                        />
                      </Col>
                    </Row>

                  </Form>
                  <Row className="mt-3">
                    <Col>
                      <Button onClick={handleSubmit} color="primary">
                        Submit
                      </Button>
                    </Col>
                  </Row>
                </CardBody>
              </Card>
            </Col>
          </Row>
        </ModalBody>
        <ModalFooter>
          <Button color="secondary" onClick={togglePreviewModal}>
            Close
          </Button>
        </ModalFooter>
      </Modal>
      <Container className="mt--7" fluid>
        {/* Form Section */}
        <Row>
          <Col >
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col>
                    <h2 className="mb-0 text-center">शैक्षणिक सत्र {currentYear}- {(currentYear + 1).toString().slice(2)} में स्ववित्तीय/जनभागीदारी मद से नवीन विषय/संकाय/कक्षा को प्रारंभ करने हेतु ऑनलाईन  <br /> आवेदन पत्र <br />
                      (टीप:- प्रत्येक नवीन संकाय/कक्षा हेतु अलग-अलग आवेदन देना अनिवार्य है।)
                    </h2>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <Row>
                    <Col>
                      <Label>
                        स्ववित्तीय जनभागीदारी मद से प्रारंभ करने वाले प्रस्तावित नवीन संकाय कक्षा का नाम एवं वांछित सीट संख्या <b> (स्नातक या स्नातकोत्तर स्तर का उल्लेख करें)</b>
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}> संकाय का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कक्षा का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वांछित सीट संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.navinVishay.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="subject"
                                value={item.subject}
                                onChange={(e) => handleChange('navinVishay', index, e)}
                                placeholder="Subject"
                                required
                              >
                                <option>
                                  No options available
                                </option>
                                {subjectData.length > 0 ? (
                                  subjectData.map((type, index) => (
                                    <option key={index} value={type.id}>
                                      {type.subjectName}
                                    </option>
                                  ))
                                ) : (
                                  <option disabled>
                                    No options available
                                  </option>
                                )}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="course"
                                value={item.course}
                                onChange={(e) => handleChange('navinVishay', index, e)}
                                placeholder="Course"
                                required
                               >
                                  <option>
                                    No options Selected
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="className"
                                value={item.className}
                                onChange={(e) => handleChange('navinVishay', index, e)}
                                placeholder="Class Name"
                                required
                              >
                                <option value="" disabled>Select Year</option>
                                <option value="1stYear">1st Year</option>
                                <option value="2ndYear">2nd Year</option>
                                <option value="3rdYear">3rd Year</option>
                                <option value="4thYear">4th Year</option>
                              </Input>

                            </td>
                            <td>
                              <Input
                                type="number"
                                name="requiredSeat"
                                value={item.requiredSeat}
                                onChange={(e) => handleChange('navinVishay', index, e)}
                                placeholder="Required Seat"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="type"
                                value={item.type}
                                onChange={(e) => handleChange('navinVishay', index, e)}
                                placeholder="Type"
                                required
                              >
                                <option value="" disabled>Select Type</option>
                                <option value="janBhagidari">जनभागीदारी</option>
                                <option value="selfFinance">स्व-वित्तीय</option>
                              </Input>
                            </td>
                            {formData.navinVishay.length > 1 &&
                              < td >
                                <Button color="danger btn-sm mt-2" onClick={() => handleRemoveRow('navinVishay', index)}>X</Button>
                              </td>
                            }
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <Row className="mt-2 mb-2">
                      {formData.navinVishay.length < 2 && <Col>
                        <Button color="primary  ml-3 btn-sm" onClick={() => handleAddRow('navinVishay')} style={{ fontSize: "16px" }} >+</Button>
                      </Col>}
                    </Row>
                  </Row>

                  {collegeType === "UG" && <>
                    <hr />
                    <Row className="mt-2">
                      <Col>
                        <Label>
                          <b>स्नातक स्तर पर</b>  प्रस्तावित नवीन नवीन संकाय/कक्षा प्रारंभ करने हेतु
                          पोषक शालाओं में संबंधित
                          संकाय/विषय में छात्र संख्या एवं
                          गतवर्ष उत्तीर्ण छात्र संख्या ।

                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}> पोषक शाला का नाम</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}> कला</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विज्ञान</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वाणिज्य</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.poshakShalaStudent.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="text"
                                  name="school"
                                  value={item.school}
                                  onChange={(e) => handleChange('poshakShalaStudent', index, e)}
                                  placeholder="School"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="arts"
                                  value={item.arts}
                                  onChange={(e) => handleChange('poshakShalaStudent', index, e)}
                                  placeholder="Arts"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="science"
                                  value={item.science}
                                  onChange={(e) => handleChange('poshakShalaStudent', index, e)}
                                  placeholder="Science"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="commerce"
                                  value={item.commerce}
                                  onChange={(e) => handleChange('poshakShalaStudent', index, e)}
                                  placeholder="Commerce"
                                  required
                                />
                              </td>
                              {formData.poshakShalaStudent.length > 1 &&
                                <td>
                                  <Button color="danger btn-sm mt-2" className="btn-sm" onClick={() => handleRemoveRow('poshakShalaStudent', index)}>X</Button>
                                </td>
                              }
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                      <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('poshakShalaStudent')}>+</Button>

                    </Row>
                  </>}
                  {collegeType === "PG" && <>
                    <hr />

                    <Row className="mt-2">
                      <Col>
                        <Label>
                          <b> स्नातकोत्तर स्तर पर </b> प्रस्तावित नवीन
                          नवीन संकाय/कक्षा प्रारंभ करने हेतु पोषक कक्षा <b>(संबंधित विषय के स्नातक अंतिम वर्ष में अध्ययनरत छात्र संख्या एवं गत वर्षों में उत्तीर्ण छात्र संख्या।)</b>
                        </Label>
                      </Col>
                    </Row>
                    <Row>
                      <Table striped>
                        <thead>
                          <tr>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय </th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>गत वर्ष स्नातक अंतिम <br /> वर्ष में उत्तीर्ण छात्र संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>चालू सत्र में संबंधित विषय के स्नातक <br /> अंतिम वर्ष में अध्ययनरत छात्र संख्या</th>
                            <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          {formData.snatakStudent.map((item, index) => (
                            <tr key={index}>
                              <td>
                                <Input
                                  type="select"
                                  name="subject"
                                  value={item.subject}
                                  onChange={(e) => handleChange('snatakStudent', index, e)}
                                  placeholder="Subject"
                                  required
                                >
                                  <option>
                                    No options available
                                  </option>
                                  {subjectData.length > 0 ? (
                                    subjectData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.subjectName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="passedStudent"
                                  value={item.passedStudent}
                                  onChange={(e) => handleChange('snatakStudent', index, e)}
                                  placeholder="Passed Student"
                                  required
                                />
                              </td>
                              <td>
                                <Input
                                  type="number"
                                  name="lastYearStudent"
                                  value={item.lastYearStudent}
                                  onChange={(e) => handleChange('snatakStudent', index, e)}
                                  placeholder="Last Year Student"
                                  required
                                />
                              </td>

                              {formData.snatakStudent.length > 1 &&
                                <td>
                                  <Button color="danger btn-sm mt-2" onClick={() => handleRemoveRow('snatakStudent', index)}>X</Button>
                                </td>}
                            </tr>
                          ))}
                        </tbody>
                      </Table>
                      <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('snatakStudent')}>+</Button>
                    </Row>
                  </>}
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        उक्त प्रस्तावित नवीन संकाय/कक्षा की संबंद्धता किस विश्वविद्यालय से प्राप्त की जावेगी।     {"    "}
                        <b>(संबंधित विश्वविद्यालय से प्रस्तावित नवीन विषय/संकाय संचालित होने का प्रमाण पत्र अनिवार्य रूप से संलग्न करें)</b>
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>सम्बंधित विश्वविद्यालय का नाम </th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विश्वविद्यालय द्वारा जारी प्रमाण पत्र <span className="text-danger">(केवल पीडीएफ फॉरमेट में 100 kb max)</span></th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.sambandhitVishwaVidyalaya.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="university"
                                value={item.university}
                                onChange={(e) => {
                                  const updated = [...formData.sambandhitVishwaVidyalaya];
                                  updated[index].university = e.target.value;
                                  setFormData(prev => ({ ...prev, sambandhitVishwaVidyalaya: updated }));
                                }}
                                placeholder="University"
                                required
                              >
                                <option value="">Select University</option>
                                {university &&
                                  university.length > 0 &&
                                  university.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.name}
                                    </option>
                                  ))}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="file"
                                name="certificate"
                                accept=".pdf,.jpg,.png"
                                onChange={(e) => handleCertificateChange(e, index)}
                                placeholder="Certificate"
                                required
                              />
                            </td>
                            {formData.sambandhitVishwaVidyalaya.length > 1 &&

                              <td>
                                <Button color="danger  ml-3 btn-sm" onClick={() => handleRemoveRow('sambandhitVishwaVidyalaya', index)}>X</Button>
                              </td>}
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('sambandhitVishwaVidyalaya')}>+</Button>
                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        क्या उक्त प्रस्तावित नवीन संकाय कक्षा में
                        प्रवेश प्राप्त छात्रों के अध्यापन हेतु पृथक से
                        स्ववित्तीय जनभागीदारी मद से किसी
                        शैक्षणिक स्टॉफ नियुक्त किया
                        जावेगा? <b>(यदि हॉ)</b>  तो शैक्षणिक स्टॉफ के
                        अनुमानित मासिक वेतन की जानकारी (उदाहरण अनुसार)
                      </Label>
                    </Col>
                  </Row>
                  <Row className="d-block">
                    <table >
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "4px" }}>विषय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px" }}>संकाय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px" }}>कक्षा का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px", width: "300px" }}>शैक्षणिक स्टॉफ</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px", width: "60px" }}>पद संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px", width: "130px" }}>अनुमानित मासिक वेतन</th>
                          <th style={{ fontSize: "12px", paddingLeft: "4px", width: "150px" }}>वार्षिक वित्तीय भार (मासिक वेतन X पदों की संख्या X 12)
                          </th>
                          <th style={{ fontSize: "12px", }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.selfTeacherEstimate.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="subject"
                                value={item.subject}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Subject"
                                required
                                style={{ margin: 0 }} // Remove margin
                              >
                                <option>
                                  No options available
                                </option>
                                {subjectData.length > 0 ? (
                                  subjectData.map((type, index) => (
                                    <option key={index} value={type.id}>
                                      {type.subjectName}
                                    </option>
                                  ))
                                ) : (
                                  <option disabled>
                                    No options available
                                  </option>
                                )}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="course"
                                value={item.course}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Course"
                                required
                                style={{ margin: 0 }} // Remove margin
                               >
                                  <option>
                                    No options Selected
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                            </td>
                            <td>
                              <Input
                                name="className"
                                type="select"
                                value={item.className}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                required
                                style={{ margin: 0 }} // Remove margin
                              >
                                <option value="" disabled>Select Year</option>
                                <option value="1stYear">1st Year</option>
                                <option value="2ndYear">2nd Year</option>
                                <option value="3rdYear">3rd Year</option>
                                <option value="4thYear">4th Year</option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="designation"
                                value={item.designation}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Designation"
                                required
                                style={{ margin: 0 }} // Remove margin
                              >
                                <option value="">Select Designation</option>
                                {designations &&
                                  designations.length > 0 &&
                                  designations.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.designation}
                                    </option>
                                  ))}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="seats"
                                value={item.seats}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Seats"
                                required
                                style={{ margin: 0 }} // Remove margin
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="estimatedSalaryMonthly"
                                value={item.estimatedSalaryMonthly}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Estimated Salary Monthly"
                                required
                                style={{ margin: 0 }} // Remove margin
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="estimatedSalaryYearly"
                                readOnly

                                value={item.estimatedSalaryYearly = item.seats * item.estimatedSalaryMonthly * 12}
                                onChange={(e) => handleChange('selfTeacherEstimate', index, e)}
                                placeholder="Estimated Salary Yearly"
                                required
                                style={{ margin: 0 }} // Remove margin
                              />
                            </td>
                            {formData.selfTeacherEstimate.length > 1 &&

                              <td>
                                <Button color="danger btn-sm mt-2" onClick={() => handleRemoveRow('selfTeacherEstimate', index)}>X</Button>
                              </td>}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                    <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('selfTeacherEstimate')}>+</Button>

                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        उक्त प्रस्तावित नवीन संकाय कक्षा में प्रवेश हेतु प्रति छात्र कितना
                        अनुमानित वार्षिक शुल्क निर्धारित होगा तथा सीट संख्या अनुसार
                        प्रति छात्र हेतु निर्धारित वार्षिक शुल्क से कितना वार्षिक
                        आय प्राप्त होगा, की जानकारी देवें।
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>विषय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रति छात्र हेतु <br /> निर्धारित वार्षिक शुल्क
                          </th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वार्षिक आय (सीट संख्या X प्रति छात्र निर्धारित वार्षिक शुल्क)</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.incomeFromStudent.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="subject"
                                value={item.subject}
                                onChange={(e) => handleChange('incomeFromStudent', index, e)}
                                placeholder="Subject"
                                required
                              >
                                <option>
                                  No options available
                                </option>
                                {subjectData.length > 0 ? (
                                  subjectData.map((type, index) => (
                                    <option key={index} value={type.id}>
                                      {type.subjectName}
                                    </option>
                                  ))
                                ) : (
                                  <option disabled>
                                    No options available
                                  </option>
                                )}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="course"
                                value={item.course}
                                onChange={(e) => handleChange('incomeFromStudent', index, e)}
                                placeholder="Course"
                                required
                               >
                                  <option>
                                    No options Selected
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="className"
                                value={item.className}
                                onChange={(e) => handleChange('incomeFromStudent', index, e)}
                                placeholder="Class Name"
                                required
                              >
                                <option value="" disabled>Select Year</option>
                                <option value="1stYear">1st Year</option>
                                <option value="2ndYear">2nd Year</option>
                                <option value="3rdYear">3rd Year</option>
                                <option value="4thYear">4th Year</option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="yearlyFees"
                                value={item.yearlyFees}
                                onChange={(e) => handleChange('incomeFromStudent', index, e)}
                                placeholder="Yearly Fees"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="totalIncome"
                                value={item.totalIncome}
                                onChange={(e) => handleChange('incomeFromStudent', index, e)}
                                placeholder="Total Income"
                                required
                              />
                            </td>

                            {formData.incomeFromStudent.length > 1 &&

                              <td>
                                <Button color="danger  ml-3 btn-sm" onClick={() => handleRemoveRow('incomeFromStudent', index)}>X</Button>
                              </td>}
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <Button color="primary btn-sm  ml-3 btn-sm mt-2" style={{ fontSize: "16px" }} onClick={() => handleAddRow('incomeFromStudent')}>+</Button>

                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        पिछले 02 वर्षों में स्ववित्तीय/जनभागीदारी मद अंतर्गत प्राप्त आय एवं व्यय का विवरण

                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>मद नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>वर्ष</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्राप्त आय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>व्यय राशि</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>शेष राशि</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.lastTwoYearRecord.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="typeName"
                                value={item.typeName}
                                onChange={(e) => handleChange('lastTwoYearRecord', index, e)}
                                placeholder="Type Name"
                                required
                                readOnly
                              >
                                <option value="" disabled>Select Type</option>
                                <option value="janBhagidari">जनभागीदारी</option>
                                <option value="selfFinance">स्व-वित्तीय</option>
                                <option value="government">शासकीय </option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="text"
                                name="year"
                                value={item.year}
                                onChange={(e) => handleChange('lastTwoYearRecord', index, e)}
                                placeholder="Year"
                                required
                                readOnly

                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="income"
                                value={item.income}
                                onChange={(e) => handleChange('lastTwoYearRecord', index, e)}
                                placeholder="Income"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="expense"
                                value={item.expense}
                                onChange={(e) => handleChange('lastTwoYearRecord', index, e)}
                                placeholder="Expense"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="balance"
                                value={item.balance = item.income - item.expense}
                                readOnly
                                onChange={(e) => handleChange('lastTwoYearRecord', index, e)}
                                placeholder="Balance"
                                required
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>

                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        महाविद्यालय में उपलब्ध भवन/
                        अध्ययन कक्ष/फर्नीचर संख्या/प्रयोगशाला कक्ष संख्या प्रस्तावित नवीन विषय/संकाय की पुस्तक संख्या की जानकारी

                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वयं का भवन (हॉ/नही)</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>अध्ययन कक्ष संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>फर्नीचर संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रयोगशाला कक्ष संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>प्रस्तावित नवीन
                            विषय संकाय की पुस्तक
                            संख्या
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.mahavidyalayaJankari.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="selfBuilding"
                                value={item.selfBuilding}
                                onChange={(e) => handleChange('mahavidyalayaJankari', index, e)}
                                placeholder="Self Building"
                                required
                              >
                                <option value="" disabled>Select</option>
                                <option value="हाँ">हाँ</option>
                                <option value="नहीं">नहीं</option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="classroomNo"
                                value={item.classroomNo}
                                onChange={(e) => handleChange('mahavidyalayaJankari', index, e)}
                                placeholder="Classroom No"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="furnitureNo"
                                value={item.furnitureNo}
                                onChange={(e) => handleChange('mahavidyalayaJankari', index, e)}
                                placeholder="Furniture No"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="labsNo"
                                value={item.labsNo}
                                onChange={(e) => handleChange('mahavidyalayaJankari', index, e)}
                                placeholder="Labs No"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="booksNo"
                                value={item.booksNo}
                                onChange={(e) => handleChange('mahavidyalayaJankari', index, e)}
                                placeholder="Books No"
                                required
                              />
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        महाविद्यालय में पूर्व से संचालित सभी
                        संकाय/कक्षा का नाम एवं निर्धारित सीट संख्या की जानकारी। टीपः केवल प्रथम वर्ष या प्रथम सेमेस्टर हेतु निर्धारित सीट संख्या की जानकारी देवें।.
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px", width: "300px" }}>विषय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संकाय</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कक्षा का नाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>निर्धारित सीट संख्या</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>संचालन का प्रकार <br /> (स्ववित्तीय योजना <br />/जनभागीदारी मद)</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.previousCourseSubjects.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="subject"
                                value={item.subject}
                                onChange={(e) => handleChange('previousCourseSubjects', index, e)}
                                placeholder="Subject"
                                required
                              >
                                <option>
                                  No options available
                                </option>
                                {subjectData.length > 0 ? (
                                  subjectData.map((type, index) => (
                                    <option key={index} value={type.id}>
                                      {type.subjectName}
                                    </option>
                                  ))
                                ) : (
                                  <option disabled>
                                    No options available
                                  </option>
                                )}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="course"
                                value={item.course}
                                onChange={(e) => handleChange('previousCourseSubjects', index, e)}
                                placeholder="Course"
                                required
                              >
                                  <option>
                                    No options Selected
                                  </option>
                                  {courseData.length > 0 ? (
                                    courseData.map((type, index) => (
                                      <option key={index} value={type.id}>
                                        {type.courseName}
                                      </option>
                                    ))
                                  ) : (
                                    <option disabled>
                                      No options available
                                    </option>
                                  )}
                                </Input>
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="className"
                                value={item.className}
                                onChange={(e) => handleChange('previousCourseSubjects', index, e)}
                                placeholder="Class Name"
                                required
                              >
                                <option value="" disabled>Select Year</option>
                                <option value="1stYear">1st Year</option>
                                <option value="2ndYear">2nd Year</option>
                                <option value="3rdYear">3rd Year</option>
                                <option value="4thYear">4th Year</option>
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="seats"
                                value={item.seats}
                                onChange={(e) => handleChange('previousCourseSubjects', index, e)}
                                placeholder="Seats"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="select"
                                name="sanchalanType"
                                value={item.sanchalanType}
                                onChange={(e) => handleChange('previousCourseSubjects', index, e)}
                                placeholder="Sanchalan Type"
                                required
                              >
                                <option value="" disabled>Select Type</option>
                                <option value="janBhagidari">जनभागीदारी</option>
                                <option value="selfFinance">स्व-वित्तीय</option>
                                <option value="government">शासकीय </option>
                              </Input>
                            </td>
                            {formData.previousCourseSubjects.length > 1 &&

                              <td>
                                <Button color="danger  ml-3 btn-sm" onClick={() => handleRemoveRow('previousCourseSubjects', index)}>X</Button>
                              </td>}
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('previousCourseSubjects')}>+</Button>
                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        वर्तमान में महाविद्यालय हेतु शासन से
                        स्वीकृत समस्त शैक्षणिक एवं
                        अशैक्षणिक पदों की जानकारी (उदाहरण:-
                        प्राध्यापक इतिहास या सहायक प्राध्यापक हिन्दी)
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Table striped>
                      <thead>
                        <tr>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>पदनाम</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>स्वीकृत </th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>कार्यरत</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>रिक्त</th>
                          <th style={{ fontSize: "12px", paddingLeft: "40px" }}>Action</th>
                        </tr>
                      </thead>
                      <tbody>
                        {formData.currentStaff.map((item, index) => (
                          <tr key={index}>
                            <td>
                              <Input
                                type="select"
                                name="designation"
                                value={item.designation}
                                onChange={(e) => handleChange('currentStaff', index, e)}
                                placeholder="Designation"
                                required
                              >
                                <option value="">Select Designation</option>
                                {designations &&
                                  designations.length > 0 &&
                                  designations.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.designation}
                                    </option>
                                  ))}
                              </Input>
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="sanctioned"
                                value={item.sanctioned}
                                onChange={(e) => handleChange('currentStaff', index, e)}
                                placeholder="Sanctioned"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="working"
                                value={item.working}
                                onChange={(e) => handleChange('currentStaff', index, e)}
                                placeholder="Working"
                                required
                              />
                            </td>
                            <td>
                              <Input
                                type="number"
                                name="vacant"
                                value={item.vacant}
                                onChange={(e) => handleChange('currentStaff', index, e)}
                                placeholder="Vacant"
                                required
                              />
                            </td>

                            {formData.currentStaff.length > 1 &&
                              < td >
                                <Button color="danger  ml-3 btn-sm" onClick={() => handleRemoveRow('currentStaff', index)}>X</Button>
                              </td>
                            }
                          </tr>
                        ))}
                      </tbody>
                    </Table>
                    <Button color="primary btn-sm  ml-3 btn-sm mt-3" style={{ fontSize: "16px" }} onClick={() => handleAddRow('currentStaff')}>+</Button>

                  </Row>
                  <hr />
                  <Row>
                    <Col>
                      <Label>
                        प्रस्तावित नवीन संकाय/ कक्षा प्रारंभ
                        करने का औचित्य
                        (अनिवार्य रूप से उल्लेख करें)
                      </Label>
                    </Col>
                  </Row>
                  <Row>
                    <Col>
                      <Input
                        type="textarea"
                        name="remarkByPrincipal"
                        onChange={handleInputRemark}
                        value={formData.remarkByPrincipal}
                        placeholder="Remark"
                        required
                      />
                    </Col>
                  </Row>

                </Form>
                <Row className="mt-3">
                  <Col>
                    <Button onClick={togglePreviewModal} color="primary">
                      Preview
                    </Button>
                  </Col>
                </Row>
              </CardBody>
            </Card>
          </Col>
        </Row>
        {/* Table Section with Pagination */}

      </Container >
    </>
  );
};

export default JanBhagiDariCourse;
