import { createContext, useContext, useState } from "react";

// Create Context
const FontSizeContext = createContext();

// Provider Component
export const FontSizeProvider = ({ children }) => {
  const [fontSize, setFontSize] = useState(16); // Default font size

  const increaseFontSize = () => {
    setFontSize((prevSize) => Math.min(prevSize + 2, 84));
  };

  const decreaseFontSize = () => {
    setFontSize((prevSize) => Math.max(prevSize - 2, 12));
  };

  return (
    <FontSizeContext.Provider value={{ fontSize, increaseFontSize, decreaseFontSize }}>
      <div style={{ fontSize: `${fontSize}px` }}>{children}</div>
    </FontSizeContext.Provider>
  );
};

// Custom Hook for easy usage
export const useFontSize = () => useContext(FontSizeContext);
