import React, { useEffect } from "react";

const GoogleTranslate = () => {
    useEffect(() => {
        const scriptId = "google-translate-script";
    
        if (!document.getElementById(scriptId)) {
          const script = document.createElement("script");
          script.id = scriptId;
          script.src =
            "https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit";
          script.async = true;
          document.body.appendChild(script);
        }
    
        window.googleTranslateElementInit = () => {
          new window.google.translate.TranslateElement(
            { pageLanguage: "en", includedLanguages: "en,hi", autoDisplay: false },
            "google_translate_element"
          );
        };
    
        const interval = setInterval(() => {
          if (window.google && window.google.translate) {
            window.googleTranslateElementInit();
            clearInterval(interval);
          }
        }, 500);
    
        return () => clearInterval(interval);
      }, []);
    
      // 🔥 Function to Toggle Language
      const toggleLanguage = () => {
        setTimeout(() => {
          const select = document.querySelector(".goog-te-combo");
          if (select) {
            select.value = select.value === "hi" ? "en" : "hi";
            select.dispatchEvent(new Event("change"));
          }
        }, 500);
      };
    
      return (
        <div>
        
    
          {/* 🟢 Hidden Google Translate UI */}
          <div id="google_translate_element" style={{ display: "none" }}></div>
    
          {/* 🔥 Button to Toggle Language */}
          <button onClick={toggleLanguage}  style={{ display: "none" }}></button>
        </div>
      );
    }

export default GoogleTranslate;
