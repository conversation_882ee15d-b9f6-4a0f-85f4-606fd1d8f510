import { useState } from "react";
import PropTypes from "prop-types";
import {
  <PERSON>,
  CardHeader,
  CardBody,
  Container,
  Row,
  Col,
  Label,
  Button,
  Input,
} from "reactstrap";
import Swal from "sweetalert2";
import axios from "axios";
import SwalMessageAlert from "../../../utils/sweetAlertMessage";

const ACRFormClass4 = ({ employee }) => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const d = new Date();
  const year = d.getFullYear();
  const lastYear = year - 1;
  const [lastYearDate] = useState(lastYear);
  const [formData, setFormData] = useState({
    employeeId: employee._id,
    employeeName: employee.name,
    designation: employee.designationDetails.designation,
    collegeName: employee.employeeType !== "DIRECTORATE" ? employee.collegeDetails.name : "",
    collegeId: employee.employeeType !== "DIRECTORATE" ? employee.collegeDetails._id : "",
    currentSalary: employee.currentSalary,
    fatherName: "",
    dateofBirth: "",
    varishtSuchiNumber: "",
    shenikYogyata: "",
    niyamitNiyukti: "",
    avadhiAnkit: "",
  });
  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    const { name, value } = e.target;

    const regex = /^[a-zA-Z\s'-]*$/;

    // Validate the input for father's name
    if (name === "fatherName" && !regex.test(value)) {
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "Do Not Use Special Character।", // "Please do not use special characters."
      }));
      return; // Exit the function if the input is invalid
    } else {
      // Clear the error if the input is valid
      setErrors((prevErrors) => ({
        ...prevErrors,
        [name]: "",
      }));
    }

    setFormData({ ...formData, [name]: value });
    setErrors({ ...errors, [name]: "" }); // Clear error for the field being updated
  };

  const validateForm = () => {
    const newErrors = {};
    if (!formData.fatherName.trim())
      newErrors.fatherName = "This field is required.";
    if (!formData.dateofBirth)
      newErrors.dateofBirth = "This field is required.";
    if (!formData.varishtSuchiNumber.trim())
      newErrors.varishtSuchiNumber = "This field is required.";
    if (!formData.shenikYogyata)
      newErrors.shenikYogyata = "This field is required.";
    if (!formData.niyamitNiyukti)
      newErrors.niyamitNiyukti = "This field is required.";
    if (!formData.avadhiAnkit.trim())
      newErrors.avadhiAnkit = "This field is required.";
    return newErrors;
  };

  const handleFinalSubmit = async (e) => {
    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    try {
      const result = await Swal.fire({
        title: "Confirmation",
        text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#3085d6",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, submit",
        cancelButtonText: "No, cancel",
      });

      if (result.isConfirmed) {
    e.target.disabled = true;

        const response = await axios.post(
          `${endPoint}/api/acr/add`,
          { ...formData },
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          SwalMessageAlert("स्व-मतांकन सफल हुआ", "success");
          setTimeout(() => window.location.reload(), 5000);
        } else {
          SwalMessageAlert(response.data.msg, "error");
        }
      }
    } catch (error) {
      const errorMessage =
        error.response?.data?.msg ||
        "An unexpected error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    }
  };

  const currentDate = new Date();
  const currentToday = currentDate.toISOString().split('T')[0];

  const minDate = new Date();
  minDate.setFullYear(currentDate.getFullYear() - 18);

  // Calculate the maximum date (65 years ago)
  const maxDate = new Date();
  maxDate.setFullYear(currentDate.getFullYear() - 65);

  // Format dates to YYYY-MM-DD for the input
  const formattedMinDate = minDate.toISOString().split('T')[0];
  const formattedMaxDate = maxDate.toISOString().split('T')[0];


  return (
    <>
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="shadow pb-4">
              <CardHeader
                className="bg-white border-0"
                style={{ textAlign: "center" }}
              >
                <h2 className="mb-0">प्रपत्र --- दो</h2>
                <h3 className="mb-0">
                  चतुर्थ श्रेणी के शासकीय कर्मचारियों के संबंध में प्रतिवर्ष माह
                  अप्रैल के प्रथम सप्ताह में लिखी जाने वाली चरित्र पंजी का फार्म
                  31 मार्च {lastYearDate}
                  को समाप्त होने वाले वर्ष के लिये
                </h3>
              </CardHeader>
              <CardBody>
                <div className="mb-4">
                  <Row className="mb-3">
                    <Col md="3">
                      <Label>कर्मचारी का नाम</Label>
                      <Input
                        type="text"
                        name="employeeName"
                        value={formData.employeeName}
                        readOnly
                        className="form-control"
                      />
                    </Col>
                    <Col md="3">
                      <Label>पिता अथवा पति का नाम </Label>
                      <Input
                        type="text"
                        name="fatherName"
                        value={formData.fatherName}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.fatherName && (
                        <small className="text-danger">
                          {errors.fatherName}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>जन्म तिथि</Label>
                      <Input
                        type="date"
                        name="dateofBirth"
                        value={formData.dateofBirth}
                        className="form-control"
                        min={formattedMaxDate} // Set the minimum date to 65 years ago
                        max={formattedMinDate}
                        onChange={handleInputChange}
                      />
                      {errors.dateofBirth && (
                        <small className="text-danger">
                          {errors.dateofBirth}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>शैक्षणिक योग्यता, यदि कोई हो</Label>
                      <Input
                        type="select" // Change the type to "select"
                        name="shenikYogyata"
                        value={formData.shenikYogyata}
                        className="form-control"
                        onChange={handleInputChange}
                      >
                        <option value="">-- कृपया चयन करें --</option> {/* Placeholder option */}
                        <option value="12th">12th</option>
                        <option value="UG">UG</option>
                        <option value="PG">PG</option>
                        <option value="Others">Others</option>
                      </Input>
                      {errors.shenikYogyata && (
                        <small className="text-danger">
                          {errors.shenikYogyata}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-3">

                    <Col md="3">
                      <Label>वरिष्ठता सूची क्रमांक </Label>
                      <Input
                        type="number"
                        name="varishtSuchiNumber"
                        value={formData.varishtSuchiNumber}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.varishtSuchiNumber && (
                        <small className="text-danger">
                          {errors.varishtSuchiNumber}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>वेतन</Label>
                      <Input
                        type="number"
                        name="salary"
                        value={formData.currentSalary} // Use employee data here
                        className="form-control"
                        onChange={handleInputChange}
                      />
                    </Col>
                    <Col md="3">
                      <Label>पदनाम / स्थायी / अस्थायी </Label>
                      <Input
                        type="textarea"
                        name="designation"
                        value={formData.designation}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.kartavyaKaVivran && (
                        <small className="text-danger">
                          {errors.kartavyaKaVivran}
                        </small>
                      )}
                    </Col>
                    <Col md="3">
                      <Label>नियमित नियुक्ति की तिथि </Label>
                      <Input
                        type="date"
                        name="niyamitNiyukti"
                        value={formData.niyamitNiyukti}
                        className="form-control"
                        onChange={handleInputChange}
                        max={currentToday}
                      />
                      {errors.niyamitNiyukti && (
                        <small className="text-danger">
                          {errors.niyamitNiyukti}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Row className="mb-4">
                    {employee.employeeType !== "DIRECTORATE" &&
                      <Col md="6">
                        <Label>महाविद्यालय का नाम</Label>
                        <Input
                          type="text"
                          name="collegeName"
                          value={formData.collegeName} // Use employee data here
                          readOnly
                          className="form-control"
                          onChange={handleInputChange}
                        />
                      </Col>
                    }
                    <Col md="6">
                      <Label>अवधि जिसके लिए मत अंकित किया जा रहा है </Label>
                      <Input
                        type="textarea"
                        name="avadhiAnkit"
                        value={formData.avadhiAnkit}
                        maxLength={150}
                        className="form-control"
                        onChange={handleInputChange}
                      />
                      {errors.avadhiAnkit && (
                        <small className="text-danger">
                          {errors.avadhiAnkit}
                        </small>
                      )}
                    </Col>
                  </Row>
                  <Button color="success" onClick={handleFinalSubmit}>
                    Submit
                  </Button>
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

ACRFormClass4.propTypes = {
  employee: PropTypes.shape({
    _id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    designationDetails: PropTypes.shape({
      designation: PropTypes.string.isRequired,
    }).isRequired,
    collegeDetails: PropTypes.shape({
      name: PropTypes.string.isRequired,
      _id: PropTypes.string.isRequired,
    }).isRequired,
    currentSalary: PropTypes.number.isRequired,
  }).isRequired,
};

export default ACRFormClass4;