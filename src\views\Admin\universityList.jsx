import { useEffect, useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ooter,
  Container,
  Row,
  Col,
  Table,
  Pagination,
  PaginationItem,
  Input,
  PaginationLink,
  Button,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import { Link } from "react-router-dom";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const UniversityList = () => {

  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem('authToken')
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Number of items per page for pagination

  const [filterdUniversity, setFilteredUniversity] = useState([]);

  const [university, setUniversity] = useState([]);
  useEffect(() => {
    const fetchUniversity = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/university/get-all-university`, {
          headers: {
            'Content-Type': 'application/json',
            'web-url': window.location.href,
            "Authorization": `Bearer ${token}`
          }
        });

        if (response.status === 200) {
          setUniversity(response.data);
          setFilteredUniversity(response.data);
        } else {
          alert("Failed to fetch University data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchUniversity();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change


  const [division, setDivision] = useState([]);
  useEffect(() => {
    const getDivision = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/division/get-all`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data;
          setDivision(data);
        }
      } catch (error) {
        console.error("Error fetching university data:", error);
        alert("Failed to load university data.");
      }
    };

    getDivision(); // Call the function inside useEffect
  }, [endPoint, token]); // Dependencies

  const getDivisionName = (value) => {
    const divisionObj = division.find((div) => div.divisionCode === value);
    return divisionObj ? divisionObj.name : "Unknown Division";
  };
  // Handle Pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filterdUniversity.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(university.length / itemsPerPage);

  const handlePageChange = (pageNumber) => setCurrentPage(pageNumber);

  const [searchTerm, setSearchTerm] = useState("");
  

  useEffect(() => {
    const filteredItems = university.filter((item) => {
      return Object.keys(item).some((key) =>
        String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
    setFilteredUniversity(filteredItems);
    setCurrentPage(1); // Reset to first page on filter change
  }, [searchTerm, university]);
const exportAllToExcel = () => {
  try {
    const dataToExport = university.map((details, index) => ({
      "S.No": index + 1,
      "University Name": details.name,
      "Principal Name": details.contactPerson,
      "Email": details.universityEmail,
      "Principal Mobile No.": details.contactNumber || "N/A",
      "Division": division.find((d) => d.divisionCode === details.divison)?.name || "N/A",
      "District": details.districtName || "N/A",
       "Vidhan Sabha": details.vidhansabhaName || "N/A",
      "Establishment Year": details.establishYear,
      "Registeration Number": details.registerationNumber,
      "Address": details.address,
      "Website": details.universityUrl,
      "Verified": details.status ? "Yes" : "No"
    }));

    const worksheet = XLSX.utils.json_to_sheet(dataToExport);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Employees");

    const excelBuffer = XLSX.write(workbook, { bookType: "xlsx", type: "array" });
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });

    saveAs(blob, "University_Report.xlsx");
  } catch (error) {
    console.error("Excel export failed:", error);
    // alert("Failed to export data. See console for details.");
  }
};
  return (
    <>
      <Header />
      
      {/* Page content */}
      <Container className="mt--7" fluid>
        {/* Table to display institutes with pagination */}
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
                <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                <Col md={7}>
                <h3 className="mb-0">University List</h3>
                </Col>
                <Col md={2}>
                  <Button
                    color="primary"
                    onClick={exportAllToExcel}
                    size="sm"
                  >
                    Export to Excel All Report
                  </Button>
                </Col>
                <Col md={3}>
                    <Input
                      type="text"
                      placeholder="Search University.."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      style={{ marginBottom: "10px" }}
                    />
                  </Col>
              </CardHeader>
              <Table className="align-items-center table-flush" responsive>
                <thead className="thead-light">
                  <tr>
                  <th scope="col">Action</th>
                    <th scope="col">Basic Details</th>                    
                    <th scope="col">Division/District/Vidhansabha</th>
                    <th scope="col">University Type</th>
                    <th scope="col">Address</th>
                  
                  </tr>
                </thead>
                <tbody>
                  {currentItems.map((university, index) => (
                    <tr key={index}>
                    <td>
                      <Link to={`/admin/update-university/${university._id}`}>
                        <button className="btn btn-warning btn-sm">Edit</button>
                      </Link>
                      &nbsp; &nbsp;
                      {university.status === true ? <span className="btn btn-success btn-sm">Verified</span> : <span className="btn btn-danger btn-sm">Not Verified</span>}
                    </td>
                    <td>
                        Name : {university.name}
                        <br />
                        Email : {university.universityEmail}
                        <br />
                        Person : {university.contactPerson}
                        <br />
                        Mobile : {university.contactNumber}
                        <br />
                        <span className="btn btn-sm btn-primary">Reg. No. : {university.registerationNumber}</span>
                         <a href={university.universityUrl} target="_blank" className="btn btn-default btn-sm mr-2">Website Link</a>
                        <br />
                      </td>
                      <td>
                        {getDivisionName(university.divison)} /{" "}
                        {university.districtName} / {university.vidhansabhaName}
                      </td>
                      <td>{university.universityType === 0 ? 'PRIVATE' : 'GOVT.'}</td>

                      <td
                       style={{
                    wordBreak: "break-word",
                    whiteSpace: "normal",
                    maxWidth: "500px", 
                    overflowWrap: "break-word",
                  }}
                      >{university.address}</td>
                     
                    </tr>
                  ))}
                </tbody>
              </Table>
              <CardFooter className="py-4">
                <nav aria-label="...">
                  <Pagination className="pagination justify-content-end mb-0">
                    {[...Array(totalPages)].map((_, i) => (
                      <PaginationItem key={i} className={currentPage === i + 1 ? "active" : ""}>
                        <PaginationLink
                          href="#"
                          onClick={(e) => {
                            e.preventDefault();
                            handlePageChange(i + 1);
                          }}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                  </Pagination>
                </nav>
              </CardFooter>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UniversityList;
