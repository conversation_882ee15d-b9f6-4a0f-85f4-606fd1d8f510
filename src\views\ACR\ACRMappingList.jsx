import { useState, useEffect } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Container,
  CardBody,
  Col,
  Row,
  Button,
  Input,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Select from "react-select";

const ACRMappingList = () => {
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const collegeId = sessionStorage.getItem("id");
  const [employees, setEmployees] = useState([]);
  const [formData, setFormData] = useState([]);
  const [searchQuery, setSearchQuery] = useState(""); // Search query state

  const handleApiError = (error) => {
    if (error.response) {
      const errorMessage =
        error.response.data?.msg || "An error occurred. Please try again.";
      SwalMessageAlert(errorMessage, "error");
    } else if (error.request) {
      SwalMessageAlert(
        "No server response. Please check your network.",
        "error"
      );
    } else {
      SwalMessageAlert("Unexpected error occurred. Please try again.", "error");
    }
  };

  const fetchACREmployees = async () => {
    try {
      const response = await axios.get(
        `${endPoint}/api/acr/get-acr-mapping?college=${collegeId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const allEmployees = response.data || [];
        const sortedEmployees = allEmployees.sort((a, b) =>
          a.name.localeCompare(b.name)
        );
        setEmployees(sortedEmployees);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  useEffect(() => {
    fetchACREmployees();
  }, []);

  const filteredEmployees = employees.filter(
    (emp) =>
      emp.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      emp.empCode.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const columns = [
    {
      name: "S.No.",
      selector: (row, index) => index + 1,
      sortable: true,
      width: "80px",
    },
    {
      name: "Name / Employee Code",
      selector: (row) => `${row.name} (${row.empCode})`,
      sortable: true,
    },
    {
      name: "Reporting Officer Details",
      selector: (row) => `${row.reportingName} - ${row.reportingEmpCode}`,
      sortable: true,
    },
    {
      name: "Reviewing Officer Details",
      selector: (row) => `${row.reviewingName} - ${row.reviewingEmpCode}`,
      sortable: true,
    },
    {
      name: "Other Officer Details",
      selector: (row) => `${row.otherInputsName} - ${row.otherInputsEmpCode}`,
      sortable: true,
    },
  ];

  // Set pagination options
  const paginationOptions = {
    rowsPerPageText: "Rows per page",
    rangeSeparatorText: "of",
    selectAllRowsItem: true,
    selectAllRowsItemText: "All",
  };
  
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card className="shadow">
          <CardHeader className="d-flex justify-content-between align-items-center">
            <Col xs="6">
              <h3 className="mb-0">ACR Mapped List</h3>
            </Col>
            <Col className="text-right" xs="4">
              <Input
                type="text"
                placeholder="Search by Name or EmpCode"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </Col>
          </CardHeader>
          <CardBody>
            <Row>
              <Col lg="12">
                <CardBody>
                  <DataTable
                    title="Employee List"
                    columns={columns}
                    data={filteredEmployees}
                    pagination
                    paginationComponentOptions={paginationOptions}
                    highlightOnHover
                    striped
                    responsive
                  />
                </CardBody>
              </Col>
            </Row>
          </CardBody>
        </Card>
      </Container>
    </>
  );
};

export default ACRMappingList;