import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Container,
  Row,
  Col,
  Modal,
  ModalHeader,
  ModalBody,
  ModalFooter,
  Label,
  InputGroup,
  InputGroupAddon,
  InputGroupText,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
const Division = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const inputStyle = {
    color: "gray", // Set the desired gray color
    borderColor: "#ccc", // Optional: Set border color for better visibility
  };

  const [filteredDivision, setFiltereDivison] = useState([]);

  const [formData, setFormData] = useState({
    empCode: "",
    contact: "",
    name: "",
    email: "",
    classData: "",
    designation: "",
    postingLocation: "",
    address: "",
  });
  const [division, setDivision] = useState([]);
  useEffect(() => {
    const fetchDivision = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/division/get-all`, {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setDivision(response.data);
          setFiltereDivison(response.data);
        } else {
          alert("Failed to Division  data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    // Call the function
    fetchDivision();
    // Optionally add dependencies in the dependency array
  }, [endPoint, token]); // Include value and token as dependencies if they can change
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    const valuesToValidate = [formData.name, formData.divisionCode];
    const hasEmptyFields = valuesToValidate.some(
      (value) => value === null || value === "" || value === undefined
    );
    const allFieldsFilled = valuesToValidate.every(
      (value) => value !== null && value !== "" && value !== undefined
    );
    if (allFieldsFilled) {
      alert("All fields are filled. You can proceed with submission.");
    }
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    

    try {
      const body = {
        name: String(formData.name),
        divisionCode: String(formData.divisionCode),
      };
      const response = await axios.post(
        `${endPoint}/api/division/add`,
        { ...body },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setFormData({
          name: "",
          divisionCode: "",
        });
        window.location.reload();
      } else {
        alert("Division Already Exists.");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred while submitting the form:");
    }
  };

  //   const handleSubmit = async (e) => {
  //     e.preventDefault();

  //     // Create an array of the field values to validate
  //     const valuesToValidate = [formData.name, formData.divisionCode];

  //     const hasEmptyFields = valuesToValidate.some(
  //       (value) => value === null || value === "" || value === undefined
  //     );
  //     const allFieldsFilled = valuesToValidate.every(
  //       (value) => value !== null && value !== "" && value !== undefined
  //     );

  //     // Condition for empty fields
  //     if (hasEmptyFields) {
  //       alert("Please fill out all fields before submitting.");
  //       return; // Prevent form submission
  //     }

  //     // Condition for filled fields (you can implement additional logic here)
  //     if (allFieldsFilled) {
  //       alert("All fields are filled. You can proceed with submission.");
  //     }

  //     try {
  //       const body = {
  //         name: String(formData.name),
  //         divisionCode: String(formData.divisionCode),
  //       };
  //       const response = await axios.post(
  //         `${endPoint}/api/division/add`,
  //         { ...body },
  //         {
  //           headers: {
  //             "Content-Type": "application/json",
  //             'web-url': window.location.href,
  //             Authorization: `Bearer ${token}`,
  //           },
  //         }
  //       );
  //       if (response.status === 200) {
  //         setFormData({
  //           name: "",
  //           divisionCode: "",
  //         });
  //         window.location.reload();
  //       } else {
  //         alert("Division Already Exists.");
  //       }
  //     } catch (error) {
  //       console.error("An error occurred while submitting the form:", error);
  //       alert("An error occurred while submitting the form:");
  //     }
  //   };

  // Handle Pagination

  const [selectedId, setSelectedId] = useState("");
  const [selectedName, setSelectedName] = useState("");
  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = (id = "", name = "") => {
    setSelectedId(id);
    setSelectedName(name);
    setPreviewModal(!previewModal);
  };

  const [classData, setClassData] = useState([]);
  const [allDivisionEmployee, setAllDivisionEmployee] = useState([]);
  useEffect(() => {
    const fetchAllDivisionEmployee = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/division/get-all-employee`,
          {
            headers: { 
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}` },
          }
        );
        if (response.status === 200) {
          setAllDivisionEmployee(response.data);
        } else {
          SwalMessageAlert("No Division Employee Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: { 
             "Content-Type": "application/json",
              'web-url': window.location.href,
            Authorization: `Bearer ${token}` },
        });
        if (response.status === 200) {
          setClassData(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchClassData();
    fetchAllDivisionEmployee();
  }, [endPoint, token]);
  const [designationData, setDesignationData] = useState([]);
  const handleClassInputChange = async (e) => {
    const { value } = e.target;
    try {
      const response = await axios.get(
        `${endPoint}/api/degisnation-class-wise/${value}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        setDesignationData(response.data);
      } else {
        SwalMessageAlert("Designation Not Found", "error");
        // navigate('/auth/Register');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      // navigate('/auth/Register');
    }
    setFormData({
      ...formData,
      classData: value,
    });
  };
  const handleSubmitDivisionEmployee = async (e) => {
    e.target.disabled = true;

    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();

    // Validation for required fields
    const requiredFields = [
      "empCode",
      "contact",
      "name",
      "email",
      "classData",
      "designation",
      "postingLocation",
      "address",
    ];
    for (let field of requiredFields) {
      if (!formData[field]) {
        Swal.fire("Error", `Please fill out the ${field} field.`, "error");
        return;
      }
    }

    // Prepare payload
    const payload = {
      ...formData,
      divisionId: selectedId,
      divisionName: selectedName,
    };
    try {
      // API call
      const response = await axios.post(
        `${endPoint}/api/division/add-employee`,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("Division Employee added successfully!", "success");
        setTimeout(() => {
          window.location.reload();
        }, 5000);
        // Clear form
        setFormData({
          empCode: "",
          contact: "",
          name: "",
          email: "",
          classData: "",
          designation: "",
          postingLocation: "",
          address: "",
        });
      } else {
        SwalMessageAlert("Failed to add employee", "error");
      }
    } catch (error) {
      Swal.fire("Error", error.message || "Something went wrong.", "error");
    }
  };
  const changePassword = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/change-password?id=${id}&key=DivisionEmployee`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data; // Check the status code to ensure success
        copyToClipboard(data.password);
        SwalMessageAlert("Password Change Successfully","success");
      } else {
        SwalMessageAlert("Password Change Failed","error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
      alert("An error occurred. Please try again later.");
    }
  };
  const copyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => console.log("Text copied to clipboard!"))
        .catch((err) => console.error("Failed to copy text:", err));
    } else {
      copyTextFallback(text);
    }
  };
  function copyTextFallback(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand("copy");
      // console.log("Text copied using fallback!");
    } catch (err) {
      console.error("Fallback copy failed:", err);
    }
    document.body.removeChild(textArea);
  }
  const columns = [
    {
      name: "Action",
      cell: (row) => {
        // Check if the employee exists in the allDivisionEmployee array
        const isEmployeeFound = allDivisionEmployee.find(
          (employee) => employee.divisionId === row._id
        );
        return (
          <>
            {isEmployeeFound ? (
              <div>
                <Button color="success" className="btn btn-sm" disabled>
                  Employee Added
                </Button>
                <button
                  title="Change Password"
                  className="btn btn-primary btn-sm"
                  onClick={() => changePassword(isEmployeeFound._id)}
                >
                  <span className="fa fa-lock"></span>
                </button>
              </div>
            ) : (
              <Button
                color="primary"
                className="btn btn-sm"
                onClick={() => togglePreviewModal(row._id, row.name)}
              >
                Add Division Employee
              </Button>
            )}
          </>
        );
      },
    },
    {
      name: "Basic Details",
      cell: (row) => (
        <div>
          <div className="mb-2">
            <strong>Name: </strong>
            {row.name}
          </div>
          <div className="mb-2">
            <strong>Division Code: </strong>
            {row.divisionCode}
          </div>
          <div className="mb-2"></div>
        </div>
      ),
      wrap: true,
    },
    {
      name: "Employee Basic Details",
      cell: (row) => {
        // Check if the employee exists in the allDivisionEmployee array
        const isEmployeeFound = allDivisionEmployee.find(
          (employee) => employee.divisionId === row._id
        );
        return (
          <>
            {isEmployeeFound ? (
              <div>
                <div className="mt-2">
                  <div>
                    <strong>Employee Name: </strong>
                    {isEmployeeFound.name}
                  </div>
                  <div>
                    <strong>Employee Code: </strong>
                    {isEmployeeFound.empCode}
                  </div>
                  <div>
                    <strong>Contact: </strong>
                    {isEmployeeFound.contact}
                  </div>
                  <div>
                    <strong>Email: </strong>
                    {isEmployeeFound.email}
                  </div>
                  <div>
                    <strong>Class: </strong>
                    {isEmployeeFound.classDetails.className}
                  </div>
                  <div>
                    <strong>Designation: </strong>
                    {isEmployeeFound.designationDetails.designation}
                  </div>
                </div>
              </div>
            ) : (
              "No Employee Data Found"
            )}
          </>
        );
      },
      wrap: true,
    },
 
  ];
  const [filterText, setFilterText] = useState("");
  const filteredData = division.filter((item) => {
    const filterTextLower = filterText.toLowerCase();
    return item.name && item.name.toLowerCase().includes(filterTextLower);
  });
  return (
    <>
      <Header />

      {/* Page content */}
      <Container className="mt--7" fluid>
      <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Add Division</h3>
                  </Col>
                  <Col className="text-right" xs="4">
                    <Button
                      color="primary"
                      href="#"
                      onClick={(e) => e.preventDefault()}
                      size="sm"
                    >
                      Settings
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      <Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-name"
                          >
                            Name
                          </label>
                          <Input
                            name="name"
                            id="input-name"
                            placeholder="Division Name"
                            type="text"
                            value={formData.name}
                            onChange={handleInputChange}
                            autoComplete="nope" // Disable autocomplete
                            required
                          />
                        </FormGroup>
                      </Col><Col lg="3">
                        <FormGroup>
                          <label
                            className="form-control-label"
                            htmlFor="input-divisionCode"
                          >
                            Division Code
                          </label>
                          <Input
                            name="divisionCode"
                            id="input-divisionCode"
                            placeholder="Division Code"
                            type="number"
                            autoComplete="pope"
                            value={formData.divisionCode}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                    </Row>

                    <Button color="primary" onClick={handleSubmit}>
                      Submit
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader className="border-0 d-flex">
                <Col md={9}>
                  <h3 className="mb-0">Division List</h3>
                </Col>
              </CardHeader>
              <DataTable
                columns={columns}
                data={filteredData}
                pagination
                paginationPerPage={10}
                highlightOnHover
                striped
                sortable // Enable sorting
                defaultSortField="name" // Sort by the 'name' column initially
                defaultSortAsc={true} // Ascending order
                customStyles={{
                  header: {
                    style: {
                      backgroundColor: "#f8f9fa", // Light background color for header
                      fontWeight: "bold",
                    },
                  },
                  rows: {
                    style: {
                      backgroundColor: "#fff", // Row color
                      borderBottom: "1px solid #ddd",
                    },
                    // Apply hover effect through the :hover pseudo-class directly in custom CSS
                    onHoverStyle: {
                      backgroundColor: "#ffff99", // Hover color
                    },
                  },
                }}
              />
            </Card>
            <Modal
              isOpen={previewModal}
              toggle={togglePreviewModal}
              style={{
                maxWidth: "800px",
                width: "100%",
              }}
            >
              <ModalHeader toggle={togglePreviewModal}>
                <h2>Add Division ({selectedName}) Employee Details</h2>
              </ModalHeader>
              <ModalBody>
                <Col>
                  <Form>
                    <div className="pl-lg-4">
                      <Row>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="empCode" className="form-control-label">
                              Employee Code
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-hat-3" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="empCode"
                                name="empCode"
                                placeholder="Employee Code"
                                type="text"
                                maxLength={12}
                                style={inputStyle}
                                value={formData.empCode || ""}
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="contact" className="form-control-label">
                              Contact
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-mobile-button" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="contact"
                                name="contact"
                                placeholder="Contact"
                                type="tel"
                                style={inputStyle}
                                value={formData.contact || ""}
                                maxLength={10}
                                onChange={(e) => {
                                  const value = e.target.value;
                                  if (value === "") {
                                    handleInputChange(e);
                                    return;
                                  }
                                  const validStartRegex = /^[6-9]/;
                                  if (validStartRegex.test(value)) {
                                    handleInputChange(e);
                                  } else {
                                    SwalMessageAlert(
                                      " Mobile number must start with digits 6, 7, 8, or 9.",
                                      "warning"
                                    );
                                  }
                                }}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="name" className="form-control-label">
                              Name
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-hat-3" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="name"
                                name="name"
                                placeholder="Name"
                                type="text"
                                style={inputStyle}
                                value={formData.name || ""}
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="email" className="form-control-label">
                              Email
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-email-83" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="email"
                                placeholder="Email"
                                name="email"
                                type="email"
                                style={inputStyle}
                                autoComplete="new-email"
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>
                      </Row>
                      <Row>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="class" className="form-control-label">
                              Select Class
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-book-bookmark" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="class"
                                type="select"
                                name="classData"
                                style={inputStyle}
                                onChange={handleClassInputChange}
                              >
                                <option value="">Select Class</option>
                                {classData &&
                                  classData.length > 0 &&
                                  classData.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.className}
                                    </option>
                                  ))}
                              </Input>
                            </InputGroup>
                          </FormGroup>
                        </Col>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label
                              for="designation"
                              className="form-control-label"
                            >
                              Designation
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-briefcase-24" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="designation"
                                type="select"
                                name="designation"
                                style={inputStyle}
                                onChange={handleInputChange}
                              >
                                <option value="">Select Designation</option>
                                {designationData &&
                                  designationData.length > 0 &&
                                  designationData.map((type, index) => (
                                    <option key={index} value={type._id}>
                                      {type.designation}
                                    </option>
                                  ))}
                              </Input>
                            </InputGroup>
                          </FormGroup>
                        </Col>
                        <Col lg="6">
                          <FormGroup>
                            <label
                              className="form-control-label"
                              htmlFor="input-postingLocation"
                            >
                              Posting Location
                            </label>
                            <Input
                              name="postingLocation"
                              id="input-postingLocation"
                              type="textarea"
                              value={formData.postingLocation}
                              onChange={handleInputChange}
                              required
                            ></Input>
                          </FormGroup>
                        </Col>
                        <Col lg="6" md="12">
                          <FormGroup>
                            <Label for="address" className="form-control-label">
                              Address
                            </Label>
                            <InputGroup className="input-group-alternative mb-3">
                              <InputGroupAddon addonType="prepend">
                                <InputGroupText>
                                  <i className="ni ni-map-pin" />
                                </InputGroupText>
                              </InputGroupAddon>
                              <Input
                                id="address"
                                placeholder="Address"
                                name="address"
                                type="text"
                                style={inputStyle}
                                onChange={handleInputChange}
                              />
                            </InputGroup>
                          </FormGroup>
                        </Col>
                      </Row>
                    </div>
                  </Form>
                </Col>
              </ModalBody>
              <ModalFooter>
                <Button color="secondary" onClick={togglePreviewModal}>
                  Cancel
                </Button>
                <Button color="primary" onClick={handleSubmitDivisionEmployee}>
                  Submit
                </Button>
              </ModalFooter>
            </Modal>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default Division;
