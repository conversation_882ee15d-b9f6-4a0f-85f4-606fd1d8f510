import React, { useEffect, useState } from "react";
import axios from "axios";
import DataTable from "react-data-table-component";
import Select from "react-select";
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Table,
  Container,
  Row,
  Col,
  Label,
  Modal,
  ModalHeader,
  ModalBody,
} from "reactstrap";
import { FormControl } from "react-bootstrap";
import loadable from "@loadable/component";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import NavImage from "../../assets/img/theme/user-icon.png";
const Header = loadable(() => import("../../components/Headers/Header.jsx"));
import formatDate from "../../utils/formateDate.jsx";
const AttendanceList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const userType = sessionStorage.getItem("userType");
  const id = sessionStorage.getItem("id");
  const [empInput, setEmpInput] = useState("");
  const [collegeInput, setCollegeInput] = useState("");
  const [filter, setFilter] = useState("");
  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [attendanceData, setAttendanceData] = useState(null);
  const [error, setError] = useState("");
  const [college, setCollege] = useState([]);
  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/college/get-all-college`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setCollege(response.data);
        } else {
          alert("Failed to fetch College data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };

    fetchCollege();
  }, [endPoint, token]);
 
  // useEffect(() => {
  //   onLoadFetch();
  //   }, []);
  // const onLoadFetch = async () => {
  //   try {
  //     setError("");
  //     setAttendanceData(null);
  //     const queryParams = {
  //       emp: empInput,
  //       college: collegeInput,
  //       filter,
  //       ...(filter === "dateRange" && { startDate, endDate }), // Add dates only for "dateRange"
  //     };
  //     const response = await axios.get(
  //       `${endPoint}/api/attendance/get-all`,
  //       { params: queryParams },
  //       {
  //         headers: {
  //           "Content-Type": "application/json",
  //           Authorization: `Bearer ${token}`,
  //         },
  //       }
  //     );
  //     setAttendanceData(response.data);
  //   } catch (error) {
  //     console.error("An error occurred while fetching the data:", error);
  //     alert("An error occurred. Please try again later.");
  //   }
  // };
  const handleSubmit = async (e) => {

    e.preventDefault();

    try {
      setError("");
      setAttendanceData(null);

      const queryParams = {
        //emp: empInput,
        college: collegeInput,
        filter,
        ...(filter === "dateRange" && { startDate, endDate }), // Add dates only for "dateRange"
      };

      const response = await axios.get(
        `${endPoint}/api/attendance/get-all`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      setAttendanceData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || "Failed to fetch attendance.");
    }
  };
  const calculateStayDuration = (loginTime, logoutTime) => {
    if (!loginTime || !logoutTime) {
      return "N/A";
    }
    const loginDate = new Date(loginTime);
    const logoutDate = new Date(logoutTime);
    const durationMs = logoutDate - loginDate;
    if (durationMs <= 0) {
      return "Invalid times";
    }
    const hours = Math.floor(durationMs / (1000 * 60 * 60));
    const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    return `${hours}h ${minutes}m ${seconds}s`;
  };

  const [base64Image, setBase64Image] = useState("");
  const [attendanceEmployeeData, setAttendanceEmployeeData] = useState([]);
  const [employeeProfile, setEmployeeProfile] = useState([]);
  const [empId, setEmpId] = useState(null);
  const [attendanceModal, setAttendanceModal] = useState(false);
  const toggleAttendanceModal = () => setAttendanceModal(!attendanceModal);
  const [filterTextModal, setFilterTextModal] = useState("");
  const viewEmployeeWise = async (empId, empCode) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/employee/get-single-employee?empCode=${empCode}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        handleConvertAndDisplay(data[0].encodedImage);
        setEmployeeProfile(data[0]);
        setEmpId(empId);
        setAttendanceModal(true);
        setAttendanceEmployeeData(null);
      } else {
        SwalMessageAlert("Employee Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };
  function handleConvertAndDisplay(base64) {
    if (base64 === undefined) {
      setBase64Image(NavImage);
    } else {
      const sampleBase64Image = `data:image/png;base64,${base64}`;
      setBase64Image(sampleBase64Image);
    }
  }

  const handleSubmits = async (e) => {

    e.preventDefault();

    try {
      if (filter === "") {
        SwalMessageAlert("Please Select Filter", "error");
      }
      const queryParams = {
        filter,
        startDate: filter === "dateRange" ? startDate : undefined,
        endDate: filter === "dateRange" ? endDate : undefined,
      };

      const response = await axios.get(
        `${endPoint}/api/attendance/employee-wise-filter/${empId}`,
        { params: queryParams },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        console.log(data);
        setAttendanceEmployeeData(data);
      } else {
        SwalMessageAlert("Attendance Data Not Found", "error");
      }
    } catch (err) {
      console.error("An error occurred while fetching the data:", err);
      alert("An error occurred. Please try again later.");
    }
  };
  
  const columnsModal = [
    {
      name: "LogIN Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.loginTime
            ? new Date(attendanceEmployeeData.loginTime).toLocaleString()
            : "No LogIn Time"}
        </>
      ),
    },
    {
      name: "LogOUT Time",
      selector: (attendanceEmployeeData) => (
        <>
          {attendanceEmployeeData.logoutTime ? (
            new Date(attendanceEmployeeData.logoutTime).toLocaleString()
          ) : (
            <span style={{ color: "red" }}>No Logout Time</span>
          )}
        </>
      ),
    },
    {
      name: "Stay Duration",
      selector: (attendanceEmployeeData) => (
        <>
          {calculateStayDuration(
            attendanceEmployeeData.loginTime,
            attendanceEmployeeData.logoutTime
          )}
        </>
      ),
    },
  ];
  const filteredDataModal = Array.isArray(attendanceEmployeeData)
    ? attendanceEmployeeData.filter((item) => {
        const filterTextLowerModal = filterTextModal
          ? filterTextModal.toLowerCase()
          : "";
        return (
          (item.name &&
            typeof item.name === "string" &&
            item.name.toLowerCase().includes(filterTextLowerModal)) ||
          (item.empCode &&
            typeof item.empCode === "string" &&
            item.empCode.toLowerCase().includes(filterTextLowerModal))
        );
      })
    : [];

  const collegeOptions = college &&
  college.length > 0 &&
    college?.map((type) => ({
      value: type._id,
      label: `${type.name} (${type.aisheCode})`,
    })) || [];

    const today = new Date().toISOString().split("T")[0];

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        {/* Form Section */}
        <Row>
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Attendance Filter</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>
                      {/* <Col lg="3">
                        <FormGroup>
                          <Label htmlFor="input-className">
                            Employee Code / Name
                          </Label>
                          <Input
                            type="text"
                            placeholder="e.g., John Doe@EMP001"
                            value={empInput}
                            onChange={(e) => setEmpInput(e.target.value)}
                          />
                        </FormGroup>
                      </Col> */}
                      <Col lg="4">
                        <FormGroup>
                          <Label htmlFor="input-className">College</Label>
                          <Select 
                            options={collegeOptions}
                            onChange={(selectedOption) => setCollegeInput(selectedOption?.value || "")}
                            placeholder="Select College"
                          />
                          {/* <Input
                            id="college"
                            name="college"
                            type="select"
                            onChange={(e) => setCollegeInput(e.target.value)}
                          >
                            <option value="">Select College</option>
                            {college &&
                              college.length > 0 &&
                              college.map((type, index) => (
                                <option key={index} value={type._id}>
                                  {type.name} ({type.aisheCode})
                                </option>
                              ))}
                          </Input> */}
                          {/* <Input
                            type="text"
                            placeholder="e.g., ABC College@AISHE123"
                            value={collegeInput}
                            onChange={(e) => setCollegeInput(e.target.value)}
                          /> */}
                        </FormGroup>
                      </Col>
                      <Col lg="4">
                        <FormGroup>
                          <Label htmlFor="input-className">Filter</Label>
                          <Input
                            type="select"
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                          >
                            <option value="">Select Filter</option>
                            <option value="today">Today</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                            <option value="yearly">Yearly</option>
                            <option value="dateRange">Date Range</option>
                          </Input>
                        </FormGroup>
                      </Col>
                      <Col lg="4">
                        <FormGroup>
                          {filter === "dateRange" && (
                            <div>
                              <label>Start Date:</label>
                              <Input
                                type="date"
                                name="startDate"
                                // value={formatDate(startDate)}
                                value={startDate}
                                  max={today}
                                onChange={(e) => setStartDate(e.target.value)}
                              />
                              <label>End Date:</label>
                              <Input
                                type="date"
                                name="endDate"
                                // value={formatDate(endDate)}
                                value={endDate}
                                // min={startDate} 
                                 max={today}     
                                onChange={(e) => setEndDate(e.target.value)}
                              />
                            </div>
                          )}
                        </FormGroup>
                      </Col>
                     
                    </Row>
                     <button className="btn btn-sm btn-primary" onClick={handleSubmit}>
                        Get Attendance
                      </button>
                  </div>
                </Form>
                {/* {error && <p className="error">{error}</p>}
                {attendanceData && (
                  <div className="attendance-results">
                    <h3>Results:</h3>
                    <pre>{JSON.stringify(attendanceData, null, 2)}</pre>
                  </div>
                )} */}
              </CardBody>
            </Card>
          </Col>
        </Row>
        <Row className="mt-4">
          <Col lg="12">
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">Attendance List</h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Table responsive hover striped>
                  <thead>
                    <tr>
                      <th colSpan="10">College Name</th>
                    </tr>
                  </thead>
                  <tbody>
                    {error && <p className="error">{error}</p>}
                    <table>
                      <thead>
                        {attendanceData &&
                          attendanceData.map((attendance, index) => (
                            <tr key={index}>
                              <th colSpan="10">{attendance.collegeName}</th>
                            </tr>
                          ))}
                      </thead>
                      <tbody>
                        {attendanceData &&
                          attendanceData.map((attendance, index) => (
                            <td key={index}>
                              <tr>
                                <th>Sno</th>
                                <th>Employee Name</th>
                                <th>Employee Code</th>
                                <th>Designation</th>
                                <th>Login Time</th>
                                <th>Logout Time</th>
                                <th>Stay Duration</th>
                                <th>Action</th>
                              </tr>
                              {attendance.attendanceData &&
                                attendance.attendanceData.map(
                                  (details, subIndex) => (
                                    <tr key={subIndex}>
                                      <td>{subIndex + 1}</td>
                                      <td>{details.name}</td>
                                      <td>{details.empCode}</td>
                                      <td>
                                        {details.designationDetails.designation}
                                      </td>
                                      <td>
                                        {details.loginTime
                                          ? new Date(
                                              details.loginTime
                                            ).toLocaleString()
                                          : "No LogIn Time"}
                                      </td>
                                      <td>
                                        {details.logoutTime
                                          ? new Date(
                                              details.logoutTime
                                            ).toLocaleString()
                                          : "No LogOut Time"}
                                      </td>
                                      <td>
                                        {calculateStayDuration(
                                          details.loginTime,
                                          details.logoutTime
                                        )}
                                      </td>
                                      <td>
                                        <button
                                          className="btn btn-primary btn-sm"
                                          title="Change Password"
                                          onClick={() =>
                                            viewEmployeeWise(
                                              details.empId,
                                              details.empCode
                                            )
                                          }
                                        >
                                          <span className="fa fa-eye"></span>
                                        </button>
                                      </td>
                                    </tr>
                                  )
                                )}
                            </td>
                          ))}
                      </tbody>
                    </table>
                  </tbody>
                </Table>
              </CardBody>
            </Card>
          </Col>
        </Row>

        <Modal
          isOpen={attendanceModal}
          toggle={toggleAttendanceModal}
          style={{ maxWidth: "90%" }}
        >
          <ModalHeader toggle={toggleAttendanceModal}>
            {/* <h2>Attendance Filter For : {employeeProfile.name}</h2> */}
          </ModalHeader>
          <ModalBody>
            <section style={{ backgroundColor: "#eee" }}>
              <div className="container py-5">
                <div className="row">
                  <div className="col-lg-4">
                    <div className="card mb-4">
                      <div className="card-body text-center">
                        <img
                          src={base64Image}
                          alt="avatar"
                          className="rounded-circle img-fluid"
                          style={{ width: "150px", height: "150px" }}
                        />

                        <h5 className="my-3">
                          <b>{employeeProfile.name}</b>({" "}
                          {employeeProfile ? employeeProfile.empCode : ""} )
                        </h5>
                        <p className="text-muted mb-1">
                          {employeeProfile && employeeProfile.length > 0
                            ? employeeProfile.designationDetails.designation
                            : ""}
                        </p>
                        <p className="text-muted mb-4">
                          {employeeProfile ? employeeProfile.address : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="col-lg-8">
                    <div className="card mb-4">
                      <div className="card-body">
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Full Name</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.name : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Email</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.email : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                        <div className="row">
                          <div className="col-sm-3">
                            <p className="mb-0">Mobile</p>
                          </div>
                          <div className="col-sm-9">
                            <p className="text-muted mb-0">
                              <b>
                                {employeeProfile ? employeeProfile.contact : ""}
                              </b>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="row">
                      <div className="col-md-12">
                        <div className="card mb-4 mb-md-0">
                          <div className="card-body">
                            <Form >
                              <div className="pl-lg-4">
                                <Row>
                                  <Col lg="3">
                                    <FormGroup>
                                      <Label htmlFor="input-className">
                                        Filter
                                      </Label>
                                      <Input
                                        type="select"
                                        value={filter}
                                        onChange={(e) =>
                                          setFilter(e.target.value)
                                        }
                                      >
                                        <option value="">Select Filter</option>
                                        <option value="today">Today</option>
                                        <option value="weekly">
                                          Current Week
                                        </option>
                                        <option value="monthly">
                                          Current Month
                                        </option>
                                        <option value="yearly">
                                          Current Year
                                        </option>
                                        <option value="dateRange">
                                          Date Range
                                        </option>
                                      </Input>
                                    </FormGroup>
                                  </Col>
                                  <Col lg="3">
                                    <FormGroup>
                                      {filter === "dateRange" && (
                                        <div>
                                          <label>Start Date:</label>
                                          <Input
                                            type="date"
                                            value={startDate}
                                            onChange={(e) =>
                                              setStartDate(e.target.value)
                                            }
                                          />
                                        </div>
                                      )}
                                    </FormGroup>
                                  </Col>
                                  <Col lg="3">
                                    <FormGroup>
                                      {filter === "dateRange" && (
                                        <div>
                                          <label>End Date:</label>
                                          <Input
                                            type="date"
                                            value={endDate}
                                            onChange={(e) =>
                                              setEndDate(e.target.value)
                                            }
                                          />
                                        </div>
                                      )}
                                    </FormGroup>
                                  </Col>
                                </Row>
                                <Button
                                  className="btn btn-sm btn-primary"
                                  onClick={handleSubmits}
                                >
                                  Get Attendance
                                </Button>
                              </div>
                            </Form>
                          </div>
                        </div>
                      </div>
                    </div>
                    <br />
                  </div>
                  <Col lg="12">
                    <Card className="shadow">
                      <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                        <h3 className="mb-0">Attendance List</h3>
                        <FormControl
                          type="text"
                          placeholder="Search..."
                          className="ml-auto"
                          value={filterTextModal}
                          onChange={(e) => setFilterTextModal(e.target.value)}
                          style={{ width: "250px", borderRadius: "30px" }}
                        />
                      </CardHeader>
                      <CardBody>
                        <DataTable
                          columns={columnsModal}
                          data={filteredDataModal}
                          pagination
                          paginationPerPage={10}
                          highlightOnHover
                          striped
                          customStyles={{
                            header: {
                              style: {
                                backgroundColor: "#f8f9fa",
                                fontWeight: "bold",
                              },
                            },
                            rows: {
                              style: {
                                backgroundColor: "#fff",
                                borderBottom: "1px solid #ddd",
                              },
                              onHoverStyle: {
                                backgroundColor: "#ffff99",
                              },
                            },
                          }}
                        />
                      </CardBody>
                    </Card>
                  </Col>
                </div>
              </div>
            </section>
            <br />
          </ModalBody>
        </Modal>
      </Container>
    </>
  );
};
export default AttendanceList;
