import { useState, useEffect, useLayoutEffect } from "react";
import {
    <PERSON><PERSON>,
    <PERSON>,
    CardHeader,
    CardBody,
    FormGroup,
    Form,
    Input,
    Badge,
    Table,
    CardFooter,
    Container,
    Row,
    Col,
    Modal,
    ModalHeader,
    ModalBody,
    ModalFooter,
    <PERSON>,
    Spinner,
} from "reactstrap";
import axios from "axios";
import Header from "../../components/Headers/Header.jsx";
import Swal from "sweetalert2";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate.jsx";

const AddSection = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const [formData, setFormData] = useState({
        sectionName: "",
        office: "",
        // sectionOfficer: '',
    });

    const [data, setData] = useState([]);
    const [isModalOpen, setModalOpen] = useState(false);
    const [sortConfig, setSortConfig] = useState({ key: "", direction: "asc" });
    const [searchTerm, setSearchTerm] = useState("");
    const [filterOffice, setFilterOffice] = useState("");
    const [currentPage, setCurrentPage] = useState(1);
    const [directors, setDirectors] = useState([]);


    const entriesPerPage = 5;

    const toggleModal = () => setModalOpen(!isModalOpen);

    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            const response = await axios.get(`${endPoint}/api/section/getAll`, {
                headers: { 
                    "Content-Type": "application/json",
                    'web-url': window.location.href,
                    Authorization: `Bearer ${token}` },
            });
            const unverifiedData = response.data.filter(
                (item) => item.isVerified === false
            );
            setData(unverifiedData);
        } catch (error) {
            console.error("Error fetching data:", error);
        }
    };

    useEffect(() => {
        const fetchDirector = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/director/get-all-director`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'web-url': window.location.href,
                        "Authorization": `Bearer ${token}`
                    }
                });
                if (response.status === 200) {
                    //   // console.log(response.data);
                    const data = response.data;
                    const verifiedDirectors = data.filter((director) => director.verified === true)
                    setDirectors(verifiedDirectors);


                } else {
                    alert("Failed to director  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDirector();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token]);



    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };
    const handleVerification = (item) => {
        Swal.fire({
            title: "Are you sure?",
            text: "Please verify the details. If verification is not completed, the data will not be saved.",
            icon: "warning",
            showCancelButton: true,
            confirmButtonText: "Verify",
            cancelButtonText: "Cancel",
        }).then(async (result) => {
            if (result.isConfirmed) {
                // Set the formData isVarified field to "1" after verification
                setFormData((prevData) => ({ ...prevData, isVarified: true }));

                // Make the PUT API call to update the designation
                try {
                    const response = await axios.put(
                        `${endPoint}/api/section/update/${item._id}`,
                        {
                            sectionName: String(item.sectionName),
                            office: String(item.office),
                            // sectionOfficer: String(item.sectionOfficer),
                            isVerified: true,
                        },
                        {
                            headers: {
                                "Content-Type": "application/json",
                                'web-url': window.location.href,
                                Authorization: `Bearer ${token}`,
                            },
                        }
                    );

                    if (response.status === 200) {
                        Swal.fire(
                            "Success!",
                            "Section verified successfully!",
                            "success"
                        );
                    } else {
                        Swal.fire("Error", "Failed to Verify section.", "error");
                    }
                } catch (error) {
                    console.error("An error occurred while updating the data:", error);
                    Swal.fire(
                        "Error",
                        "An error occurred. Please try again later.",
                        "error"
                    );
                }
            } else {
                Swal.fire("Cancelled", "Verification was not completed.", "info");
            }
        });
    };



    const handleSubmit = async (e) => {
        e.target.disabled = true;

        setTimeout(() => {
            e.target.disabled = false;
          }, 5000);
          

        e.preventDefault();
        try {
            const body = {
                sectionName: String(formData.sectionName),
                office: String(formData.office),
                // sectionOfficer: String(formData.sectionOfficer),
            };
            const response = await axios.post(
                `${endPoint}/api/section/add`,
                body,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );

            if (response.status === 200) {
                SwalMessageAlert(response.data.msg, "success");
                setFormData({
                    sectionName: "",
                    office: "",
                    // sectionOfficer: "",
                });
                fetchData();
            } else {
               SwalMessageAlert(response.data.msg, "error");
            }
            toggleModal();
        } catch (error) {
            console.error("An error occurred while submitting the form:", error);
            alert("An error occurred. Please try again later.");
        }
    };

    const handleSort = (key) => {
        const direction =
            sortConfig.key === key && sortConfig.direction === "asc" ? "desc" : "asc";
        setSortConfig({ key, direction });
    };

    const sortedData = [...data].sort((a, b) => {
        if (sortConfig.key) {
            const order = sortConfig.direction === "asc" ? 1 : -1;
            return a[sortConfig.key] > b[sortConfig.key] ? order : -order;
        }
        return 0;
    });

    const filteredData = sortedData.filter((item) => {
        return (
            item.sectionName.toLowerCase().includes(searchTerm.toLowerCase()) &&
            (!filterOffice || String(item.office) === filterOffice)
        );
    });

    const paginatedData = filteredData.slice(
        (currentPage - 1) * entriesPerPage,
        currentPage * entriesPerPage
    );

    const totalPages = Math.ceil(filteredData.length / entriesPerPage);

    const handlePageChange = (page) => {
        setCurrentPage(page);
    };
    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                {/* Form Section */}
                <Row>

                    <Col >
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">Add Section</h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Form >
                                    <div className="pl-lg-4">
                                        <Row style={{ justifyItems: "center" }}>
                                            <Col lg="6">
                                                <FormGroup>
                                                    <Label htmlFor="input-sectionName"><div className="d-flex" >Section Name <h3 className="text-danger ml-2"  >
                                                        *
                                                    </h3></div> </Label>
                                                    <Input
                                                        type="text"
                                                        name="sectionName"
                                                        id="input-sectionName"
                                                        placeholder="Section Name"
                                                        value={formData.sectionName}
                                                        onChange={handleInputChange}
                                                        required
                                                    />
                                                </FormGroup>
                                            </Col>

                                            <Col lg="6">
                                                <FormGroup>
                                                    <Label htmlFor="input-office"><div className="d-flex" >Office <h3 className="text-danger ml-2"  >
                                                        *
                                                    </h3></div> </Label>
                                                    <Input
                                                        type="select"
                                                        name="office"
                                                        id="input-office"
                                                        value={formData.office}
                                                        onChange={handleInputChange}
                                                        required
                                                    >
                                                        <option value="">Select Office</option>
                                                        <option value="Directorate">Directorate</option>
                                                        <option value="Institute">Institute</option>
                                                    </Input>
                                                </FormGroup>
                                            </Col>
                                        </Row>
                                        {/* {formData.office === "Directorate" && <Row>
                                            <Col lg="6">
                                                <FormGroup>
                                                    <Label htmlFor="input-sectionOfficer"><div className="d-flex" >Section Officer <h3 className="text-danger ml-2"  >
                                                        *
                                                    </h3></div> </Label>
                                                    <Input
                                                        type="select"
                                                        name="sectionOfficer"
                                                        id="input-sectionOfficer"
                                                        value={formData.sectionOfficer}
                                                        onChange={handleInputChange}
                                                        required
                                                    >
                                                        <option value="">Select Section Officer</option>
                                                        {directors &&
                                                            directors.length > 0 &&
                                                            directors.map((type, index) => (
                                                                <option key={index} value={type._id}>
                                                                    {type.name}
                                                                </option>
                                                            ))}
                                                    </Input>
                                                </FormGroup>
                                            </Col>
                                        </Row>} */}

                                        <Button color="primary" onClick={toggleModal}>
                                            Preview & Submit
                                        </Button>
                                    </div>
                                </Form>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
                <Modal style={{ maxWidth: "700px" }} isOpen={isModalOpen} toggle={toggleModal}>
                    <ModalHeader toggle={toggleModal}><h3> Preview Add Section </h3><br />
                        <h5 className="text-danger">
                            Note - Check Carefully before Submit
                        </h5></ModalHeader>
                    <ModalBody>
                        <Row className="d-block">

                            <Row className="text-left m-2">
                                <Col md="6" >
                                    <FormGroup>
                                        <Label htmlFor="preview-sectionName">Section Name</Label>
                                        <Input
                                            type="text"
                                            id="preview-sectionName"
                                            value={formData.sectionName}
                                            disabled
                                            className="text-left"
                                        />
                                    </FormGroup>
                                </Col>
                                <Col md="6">
                                    <FormGroup>
                                        <Label htmlFor="preview-office">Office</Label>
                                        <Input
                                            type="text"
                                            id="preview-office"
                                            value={formData.office}
                                            disabled
                                            className="text-left"
                                        />
                                    </FormGroup>
                                </Col>

                            </Row>
                            {/* <Row className="text-left m-2">
                                {formData.office === "Directorate" && <Col xs="6">
                                    <FormGroup>
                                        <Label htmlFor="preview-sectionOfficer">Section Officer</Label>
                                        <Input
                                            type="text"
                                            id="preview-sectionOfficer"
                                            value={directors && directors.length > 0  && directors.find((type)=>type._id ===  formData.sectionOfficer)?.name}
                                            disabled
                                            className="text-left"
                                        />
                                    </FormGroup>
                                </Col>}
                            </Row> */}


                        </Row>
                    </ModalBody>
                    <ModalFooter>
                        <Button color="primary" onClick={handleSubmit}>
                            Submit
                        </Button>
                        <Button color="secondary" onClick={toggleModal}>
                            Cancel
                        </Button>
                    </ModalFooter>
                </Modal>

                {/* Table Section with Pagination */}
                <Row className="mt-4">

                    <Col lg="12">
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">
                                            Verify Updates
                                            <span style={{ color: "red", fontSize: "12px" }}>
                                                ( Unverified Data will not be finalized, so please
                                                verify. )
                                            </span>
                                        </h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <Row className="mb-3">
                                    <Col md="4">
                                        <Input
                                            type="text"
                                            placeholder="Search"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                    </Col>
                                    <Col md="4">
                                        <Input
                                            type="select"
                                            value={filterOffice}
                                            onChange={(e) => setFilterOffice(e.target.value)}
                                        >
                                            <option value="">Filter by Office</option>
                                            <option value="Directorate">Directorate</option>
                                            <option value="Institute">Institute</option>

                                        </Input>
                                    </Col>
                                </Row>
                                <Table responsive hover striped>
                                    <thead>
                                        <tr>
                                            <th>Sno</th>
                                            <th
                                                onClick={() => handleSort("sectionName")}
                                                style={{ cursor: "pointer" }}
                                            >
                                                Section Name{" "}
                                                {sortConfig.key === "sectionName"
                                                    ? sortConfig.direction === "asc"
                                                        ? "▲"
                                                        : "▼"
                                                    : ""}
                                            </th>
                                            <th>Section Officer</th>
                                            <th
                                                onClick={() => handleSort("office")}
                                                style={{ cursor: "pointer" }}
                                            >
                                                Office{" "}
                                                {sortConfig.key === "office"
                                                    ? sortConfig.direction === "asc"
                                                        ? "▲"
                                                        : "▼"
                                                    : ""}
                                            </th>
                                            <th>Last Updated Date</th>
                                            <th>Verify</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {paginatedData.map((item, index) => (

                                            <tr key={item.id}>
                                                <td>
                                                    {index + 1 + (currentPage - 1) * entriesPerPage}
                                                </td>
                                                <td>{item.sectionName}</td>
                                                <td>{item.sectionOfficerName === '' || item.sectionOfficerName === undefined &&   <strong> <Badge style={{fontWeight:"bold",fontSize:"12px"}} className="bg-warning text-white "> Not Mapped </Badge></strong> }</td>
                                                <td>{item.office}</td>
                                                <td>{formatDate(item.updatedAt)}</td>
                                                <td>
                                                    <Button
                                                        className="btn btn-warning btn-sm"
                                                        type="button"
                                                        onClick={() => handleVerification(item)}
                                                    >
                                                        <Spinner
                                                            size="sm"
                                                            color="white"
                                                            style={{ marginRight: "8px" }}
                                                        />
                                                        Verify
                                                    </Button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </Table>
                            </CardBody>
                            <CardFooter>
                                <div className="pagination">
                                    {[...Array(totalPages)].map((_, index) => (
                                        <Button
                                            key={index}
                                            color={
                                                currentPage === index + 1 ? "primary" : "secondary"
                                            }
                                            onClick={() => handlePageChange(index + 1)}
                                            size="sm"
                                            className="mr-2"
                                        >
                                            {index + 1}
                                        </Button>
                                    ))}
                                </div>
                            </CardFooter>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default AddSection;
