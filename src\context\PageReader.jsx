import { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useSpeech } from "./SpeechContext.jsx";

const PageReader = () => {
  const { isSpeechOn, speak, stop } = useSpeech();
  const location = useLocation();

  useEffect(() => {
    if (!isSpeechOn) return;

    stop?.();
    const timeout = setTimeout(() => {
      const walker = document.createTreeWalker(
        document.body,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: (node) => {
            const parent = node.parentElement;
            if (!parent) return NodeFilter.FILTER_REJECT;

            const tag = parent.tagName.toUpperCase();
            if (
              ["SCRIPT", "STYLE", "NOSCRIPT", "META", "TITLE", "HEAD", "OPTION", "SELECT"].includes(tag)
            ) return NodeFilter.FILTER_REJECT;

            const style = window.getComputedStyle(parent);
            const isHidden =
              parent.offsetParent === null ||
              parent.getAttribute("aria-hidden") === "true" ||
              style.display === "none" ||
              style.visibility === "hidden";

            if (isHidden || !node.nodeValue.trim()) {
              return NodeFilter.FILTER_REJECT;
            }
            return NodeFilter.FILTER_ACCEPT;
          },
        }
      );

      let text = "";
      let node;
      while ((node = walker.nextNode())) {
        const value = node.nodeValue.trim();
        if (value.length > 500 || value.includes("{") || value.includes(";")) continue;
        text += value + " ";
      }

      if (text.trim()) speak(text.trim());
    }, 200);

    return () => {
      clearTimeout(timeout);
      stop?.();
    };
  }, [location, isSpeechOn, speak, stop]);

  useEffect(() => {
    if (!isSpeechOn) return;

    const handleHover = (e) => {
      const el = e.target;
      if (!el || el.tagName === "OPTION" || el.tagName === "SELECT") return;

      // Skip inputs, buttons, and selects on hover
      const skipTags = ["INPUT", "SELECT", "TEXTAREA", "BUTTON"];
      if (skipTags.includes(el.tagName)) return;

      const text = el.innerText?.trim();
      if (text && text.length <= 300) {
        speak(text);
      }
    };

    document.body.addEventListener("mouseover", handleHover);

    return () => {
      document.body.removeEventListener("mouseover", handleHover);
    };
  }, [isSpeechOn, speak]);

  useEffect(() => {
    if (!isSpeechOn) return;

    const handleFocus = (e) => {
      const el = e.target;
      if (!el) return;

      const tag = el.tagName.toUpperCase();
      if (tag === "INPUT" || tag === "TEXTAREA" || tag === "SELECT") {
        const label = el.getAttribute("aria-label") || el.getAttribute("placeholder") || "";
        if (label) {
          speak(label);
        }
      }
    };

    document.body.addEventListener("focusin", handleFocus);

    return () => {
      document.body.removeEventListener("focusin", handleFocus);
    };
  }, [isSpeechOn, speak]);

  return null;
};

export default PageReader;
