import React, { useState, useEffect } from "react";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Button,
  Container,
  Row,
  Col,
  Label,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";

const ACRMapping = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");

  const [acrMappings, setAcrMappings] = useState([]);
  const [levels, setLevels] = useState([]);
  const [visibleSelects, setVisibleSelects] = useState({});

  const labels = [
    { id: 1, name: "महाविद्यालय (क्लास 3 - 4) (प्रतिवेदक)" },
    { id: 2, name: "स्नातक प्राचार्य (प्रतिवेदक)" },
    { id: 3, name: "स्नातकोत्तर प्राचार्य (प्रतिवेदक)" },
    {
      id: 4,
      name: "प्राध्यापक/ सहायक प्राध्यापक/ ग्रंथपाल क्रीडा अधिकारी/ रजिस्ट्रार (प्रतिवेदक)",
    },
    // {
    //   id: 5,
    //   name: "संचालनालय अधिकारी/ कर्मचारी (स्नातक प्राचार्य एवं स्नातकोत्तर प्राचार्य को छोड़कर )",
    // },
    { id: 5, name: "संयुक्त संचालक , अपर संचालक" },
    { id: 6, name: "आयुक्त की पदस्थापना" },
  ];

  const selectOptions = [
    "Institute",
    "Division Office",
    "Additional Director",
    "Commissioner",
    "Secretary",
  ];

  useEffect(() => {
    fetchAcrMappings();
  }, []);

  const fetchAcrMappings = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/acr/get-all`, {
        headers: {
          "Content-Type": "application/json",
          "web-url": window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });

      const data = response.data;

      if (data && data.length > 0) {
        setAcrMappings(data);

        const initialLevels = labels.map((label) => {
          const mapping = data.find((item) => item.id === label.id); // Use id for matching
          return mapping ? mapping.levels : [];
        });
        setLevels(initialLevels);

        const initialVisibleSelects = {};
        data.forEach((item) => {
          item.levels.forEach((level) => {
            initialVisibleSelects[`${item.id}-${level.levelName}`] =
              level.selectOption || "Institute";
          });
        });
        setVisibleSelects(initialVisibleSelects);
      } else {
        setAcrMappings([]);
        setLevels(labels.map(() => []));
        setVisibleSelects({});
      }
    } catch (error) {
      console.error("Error fetching ACR mappings:", error.message);
    }
  };

  const addLevel = (labelId) => {
    setLevels((prevLevels) => {
      const newLevels = [...prevLevels];
      const labelIndex = labels.findIndex((label) => label.id === labelId);
      const nextLevel = `Level${newLevels[labelIndex]?.length + 1 || 1}`;
      newLevels[labelIndex] = [
        ...(newLevels[labelIndex] || []),
        { levelName: nextLevel, selectOption: "Institute", employeeOption: "" },
      ];
      return newLevels;
    });
  };

  const removeLevel = (labelId, levelIndex) => {
    setLevels((prevLevels) => {
      const newLevels = [...prevLevels];
      const labelIndex = labels.findIndex((label) => label.id === labelId);

      // Remove the specific level using the levelIndex
      newLevels[labelIndex] = newLevels[labelIndex].filter(
        (_, index) => index !== levelIndex
      );

      // Re-index the remaining levels to maintain Level1, Level2, Level3, etc.
      newLevels[labelIndex] = newLevels[labelIndex].map((level, index) => ({
        ...level,
        levelName: `Level${index + 1}`, // Update the level name to reflect the correct order
      }));

      return newLevels;
    });
  };

  const handleSelectChange = (labelId, levelName, value) => {
    setVisibleSelects((prevState) => {
      const newVisibleSelects = {
        ...prevState,
        [`${labelId}-${levelName}`]: value,
      };
      const newLevels = [...levels];
      const labelIndex = labels.findIndex((label) => label.id === labelId);
      newLevels[labelIndex] = newLevels[labelIndex].map((levelItem) =>
        levelItem.levelName === levelName
          ? { ...levelItem, selectOption: value }
          : levelItem
      );
      setLevels(newLevels);
      return newVisibleSelects;
    });
  };

  // const renderEmployeeOptions = (type) => {
  //   switch (type) {
  //     // case "Division Office":
  //     //   return [
  //     //     "Division Employee 1",
  //     //     "Division Employee 2",
  //     //     "Division Employee 3",
  //     //   ];
  //     // case "Additional Director":
  //     //   return [
  //     //     "Directorate Employee 1",
  //     //     "Directorate Employee 2",
  //     //     "Directorate Employee 3",
  //     //   ];
  //     // case "Commissioner":
  //     //   return [
  //     //     "Commissioner Employee 1",
  //     //     "Commissioner Employee 2",
  //     //     "Commissioner Employee 3",
  //     //   ];
  //     // case "Secretary":
  //     //   return [
  //     //     "Secretary Employee 1",
  //     //     "Secretary Employee 2",
  //     //     "Secretary Employee 3",
  //     //   ];
  //     default:
  //       return [];
  //   }
  // };

  // const handleEmployeeOptionChange = (labelId, levelIndex, value) => {
  //   const updatedLevels = [...levels];
  //   const labelIndex = labels.findIndex((label) => label.id === labelId);
  //   updatedLevels[labelIndex][levelIndex].employeeOption = value;
  //   setLevels(updatedLevels);
  // };

  const handleSubmit = async (e) => {
    const formData = labels
      .map((label) => ({
        id: label.id, // Use the label's id
        label: label.name,
        levels: levels[labels.findIndex((l) => l.id === label.id)].map(
          (level) => {
            const selectOption =
              visibleSelects[`${label.id}-${level.levelName}`] || "Institute";
            return {
              ...level,
              selectOption,
              // employeeOption:
              //   renderEmployeeOptions(selectOption).find(
              //     (option) => option === level.employeeOption
              //   ) || "",
            };
          }
        ),
      }))
      .sort((a, b) => a.id - b.id); // Sort the formData array by id in ascending order

    try {
    e.target.disabled = true;

      const response = await axios.post(
        `${endPoint}/api/acr/acr-mapping`,
        { formData },
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        SwalMessageAlert("ACR Mapping Successfully Updated", "success");
        fetchAcrMappings(); // Reload data
      } else {
        SwalMessageAlert("ACR Mapping Update Failed", "error");
      }
    } catch (error) {
      console.error("Error submitting form:", error.message);
    }
  };
  const getRandomColor = () => {
    const r = Math.floor(Math.random() * 156) + 100; // Range: 100 to 255
    const g = Math.floor(Math.random() * 156) + 100; // Range: 100 to 255
    const b = Math.floor(Math.random() * 156) + 100; // Range: 100 to 255
    return `rgb(${r}, ${g}, ${b})`;
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Form>
          {labels.map((label) => (
            <Card key={label.id} className="mb-2">
              <CardHeader>
                <Row>
                  <Col md="9">
                    <h4>{label.name}</h4>
                  </Col>
                  <Col className="text-right">
                    <Button
                      color="primary"
                      type="button"
                      className="btn btn-sm"
                      onClick={() => addLevel(label.id)}
                    >
                      Add Level
                    </Button>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Row>
                  {levels[labels.findIndex((l) => l.id === label.id)]?.map(
                    (level, levelIndex) => (
                      <Col key={levelIndex} md="3">
                        <Card style={{ backgroundColor: getRandomColor() }}>
                          {" "}
                          {/* Apply random background color */}
                          <CardBody>
                            <FormGroup>
                              <Label>{level.levelName}:</Label>
                              <Input
                                type="select"
                                value={
                                  visibleSelects[
                                    `${label.id}-${level.levelName}`
                                  ] || "Institute"
                                }
                                onChange={(e) =>
                                  handleSelectChange(
                                    label.id,
                                    level.levelName,
                                    e.target.value
                                  )
                                }
                              >
                                <option value="">Select</option>
                                {selectOptions.map((option, optionIndex) => (
                                  <option key={optionIndex} value={option}>
                                    {option}
                                  </option>
                                ))}
                              </Input>
                            </FormGroup>
                            {visibleSelects[
                              `${label.id}-${level.levelName}`
                            ] && (
                              <FormGroup>
                                {/* <Label>Employee Option:</Label>
                                <Input
                                  type="select"
                                  value={level.employeeOption || ""}
                                  onChange={(e) =>
                                    handleEmployeeOptionChange(
                                      label.id,
                                      levelIndex,
                                      e.target.value
                                    )
                                  }
                                >
                                  {renderEmployeeOptions(
                                    visibleSelects[
                                      `${label.id}-${level.levelName}`
                                    ]
                                  ).map((employee, employeeIndex) => (
                                    <option
                                      key={employeeIndex}
                                      value={employee}
                                    >
                                      {employee}
                                    </option>
                                  ))}
                                </Input> */}
                              </FormGroup>
                            )}
                            <Button
                              color="danger"
                              type="button"
                              className="btn btn-sm"
                              onClick={() => removeLevel(label.id, levelIndex)}
                            >
                              Remove Level
                            </Button>
                          </CardBody>
                        </Card>
                      </Col>
                    )
                  )}
                </Row>
              </CardBody>
            </Card>
          ))}
          <Button color="success" type="button" onClick={handleSubmit}>
            Submit
          </Button>
        </Form>
      </Container>
    </>
  );
};

export default ACRMapping;