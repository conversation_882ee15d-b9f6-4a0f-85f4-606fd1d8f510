import axios from "axios";
import { useState, useEffect } from "react";
import { utils, write } from "xlsx";
import { saveAs } from "file-saver";
import {
  Container,
  Card,
  CardBody,
  CardHeader,
  Row,
  Col,
  Button,
  FormControl,
} from "react-bootstrap";
import { <PERSON>dal, ModalHeader, ModalBody, Badge, Input } from "reactstrap";
import { Link, useLocation } from "react-router-dom";
import NavImage from "../../assets/img/theme/user-icon.png";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import Swal from "sweetalert2";
import "../../assets/css/paginationPage.css";

const EmployeeList = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [employee, setEmployee] = useState([]);

  useEffect(() => {
    const fetchCollege = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          const data = response.data.getAllEmployeeCollegeWise;

          
          // console.log(data, "Getting Data");
          const filterd = data.filter(
            (item) =>
              item.designation_details.designation === "UG Principal" ||
              item.designation_details.designation === "PG Principal" ||
              item.isInchargePrincipal === true
          );
          setEmployee(filterd);

          // console.log(filterd, "filterdfilterdfilterd");
        } else {
          alert("Failed to fetch Employee data. Please try again.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchCollege();
  }, []);

  const [data, setData] = useState([]);
  const [images, setImages] = useState({});
  const [college, setCollege] = useState({});
  const [classData, setClassData] = useState({});
  const [designation, setDesigantion] = useState({});
  const [divisions, setDivisions] = useState({});
  const [profiles, setProfiles] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const [status, setStatus] = useState("");
  const [workType, setWorkType] = useState("");
  const [imageStatus, setImageStatus] = useState("");
  const [profileStatus, setProfileStatus] = useState("");

  const [filterText, setFilterText] = useState("");
  const [totalItems, setTotalitems] = useState("");
  const [classDatas, setClassDatas] = useState({});
  const [designations, setDesigantions] = useState({});
  const [filterDesignation, setFilterDesignation] = useState("");
  const [filterClass, setFilterClass] = useState("");
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);

  const forProfile = queryParams.get("status");
  const [isLoading, setIsLoading] = useState(false);
  useEffect(() => {
    const fetchClassData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/class/getAll`, {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setClassDatas(response.data);
        } else {
          SwalMessageAlert("No Class Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    const fetchDesignation = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/designation/getAll`, {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });
        if (response.status === 200) {
          setDesigantions(response.data);
        } else {
          SwalMessageAlert("No Designation Data Found", "error");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };

    fetchDesignation();
    fetchClassData();
  }, [endPoint, token]);

  useEffect(() => {
   
    const fetchData = async () => {
       setIsLoading(true);
      try {
        const response = await axios.get(`${endPoint}/api/employees-details`, {
          params: {
            collegeId: collegeId,
            page: currentPage,
            limit: itemsPerPage,
            status: status !== "" ? status : undefined,
            workType: workType !== "" ? workType : undefined,
            imageStatus: imageStatus !== "" ? imageStatus : undefined,
            profileStatus: profileStatus !== "" ? profileStatus : undefined,
            query: filterText !== "" ? filterText : undefined,
            designation: filterDesignation || undefined,
            classData: filterClass || undefined,
          },
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        });

        setTotalitems(response.data.totalItems);
        setTotalPages(response.data.totalPages);
        setData(response.data.data);
        setImages(response.data.images || {});
        setCollege(response.data.colleges || {});
        setClassData(response.data.classDatass || {});
        setDesigantion(response.data.designationss || {});
        setDivisions(response.data.divisions || {});
        setProfiles(response.data.profiles || {});
      } catch (error) {
        console.error("Error fetching data:", error);
      }finally {
      setIsLoading(false); //
    }
    };

    fetchData();
  }, [
    currentPage,
    itemsPerPage,
    status,
    workType,
    imageStatus,
    profileStatus,
    filterText,
    filterDesignation,
    filterClass,
  ]);
useEffect(() => {
  if (forProfile) {
    setProfileStatus(forProfile);
  }
}, [forProfile]);

  const changePassword = async (id) => {
    try {
      const response = await axios.get(
        `${endPoint}/api/change-password?id=${id}&key=Employee`,
        {
          headers: {
            "Content-Type": "application/json",
            "web-url": window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        copyToClipboard(data.password);
        SwalMessageAlert("Password Change Successfully", "success");
      } else {
        SwalMessageAlert("Password Change Failed", "error");
      }
    } catch (error) {
      console.error("An error occurred while fetching the data:", error);
    }
  };

  const deleteEmployee = async (employee) => {
    const result = await Swal.fire({
      title: `Are you sure you want to delete ${employee.name}?`,
      html: `<strong>Emp Code: ${employee.empCode}</strong>`,
      imageUrl:
        employee.encodedImage &&
        employee.faceVerified !== false &&
        employee.encodedImage !== ""
          ? `data:image/png;base64,${employee.encodedImage}`
          : NavImage,
      imageWidth: 100,
      imageHeight: 100,
      imageAlt: "Employee Photo",
      input: "text",
      inputPlaceholder: "Enter reason for deletion...",
      inputAttributes: { "aria-label": "Reason for deletion" },
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, keep it",
      preConfirm: (reason) => {
        if (!reason) {
          Swal.showValidationMessage("Reason for delete is required");
        }
        return reason;
      },
    });

    if (result.isConfirmed) {
      const reason = result.value;
      try {
        const response = await axios.put(
          // `${endPoint}/api/delete-employee?id=${employee._id}&key=Employee`,
          
           `${endPoint}/api/delete-employee?id=${employee._id}&key=Employee&college=${employee.college}`,{ reason },
          {
            headers: {
              "Content-Type": "application/json",
              "web-url": window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          SwalMessageAlert("Employee Deleted Successfully", "success");
          setTimeout(() => {
            window.location.reload();
          }, 1000);
        } else {
          SwalMessageAlert(
            "Delete Employee Failed. Please try again!",
            "error"
          );
        }
      } catch (error) {
        SwalMessageAlert(
          "An error occurred while Deleting Employee. Please try again later!",
          "error"
        );
        console.error("An error occurred while fetching the data:", error);
      }
    }
  };

  const copyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard
        .writeText(text)
        .then(() => console.log("Text copied to clipboard!"))
        .catch((err) => console.error("Failed to copy text:", err));
    }
  };

  const [base64Image, setBase64Image] = useState("");
  const [isImageModalOpen, setImageIsModalOpen] = useState(false);
  function handleConvertAndDisplay(base64) {
    const sampleBase64Image = base64;
    setBase64Image(sampleBase64Image);
    setImageIsModalOpen(true);
  }
  const toggleImageModal = () => {
    setImageIsModalOpen(!isImageModalOpen);
  };

  const exportToExcel = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/employees-details`, {
        params: {
          collegeId: collegeId,
          limit: totalItems, // Fetch all data
          status: status !== "" ? status : undefined,
          workType: workType !== "" ? workType : undefined,
          imageStatus: imageStatus !== "" ? imageStatus : undefined,
          profileStatus: profileStatus !== "" ? profileStatus : undefined,
          query: filterText !== "" ? filterText : undefined,
        },
        headers: {
          "Content-Type": "application/json",
          "web-url": window.location.href,
          Authorization: `Bearer ${token}`,
        },
      });
      const allData = response.data.data;
      if (!allData || allData.length === 0) {
        SwalMessageAlert("No data available for export.", "warning");
        return;
      }
      // Prepare data for Excel export
      const dataToExport = allData.map((item, index) => ({
        "S.No": index + 1,
        "Employee Name": item.name,
        "Employee Code": item.empCode,
        Email: item.email,
        Contact: item.contact,
        Division: divisions?.[item.divison],
        District: item.districtName,
        Vidhansabha: item.vidhansabhaName,
        "Work Type": item.workType,
        Class: classData?.[item.classData] || "N/A",
        Designation: designation?.[item.designation] || "N/A",
        Address: item.address,
      }));
      const worksheet = utils.json_to_sheet(dataToExport);
      const workbook = utils.book_new();
      utils.book_append_sheet(workbook, worksheet, "Employee Data");
      const excelBuffer = write(workbook, { bookType: "xlsx", type: "array" });
      const blob = new Blob([excelBuffer], {
        type: "application/octet-stream",
      });
      saveAs(blob, "Employee_Report.xlsx");
      SwalMessageAlert("Report generated successfully!", "success");
    } catch (error) {
      console.error("Error exporting data:", error);
      SwalMessageAlert("Failed to generate report. Please try again.", "error");
    }
  };
  const handleClearFilters = () => {
    setFilterClass("");
    setFilterDesignation("");
    setProfileStatus("");
    setImageStatus("");
    setWorkType("");
    setStatus("");
  };
  return (
    <>
      <Header />
      <Container style={{ marginTop: "-10rem" }} fluid>
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardHeader>
                <h2>Principal</h2>
              </CardHeader>
              <CardBody>
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>S.No.</th>
                        <th>Action</th>
                        <th>Photo</th>
                        {/* <th>Working Status</th> */}
                        <th>Employee Code</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Contact</th>
                        <th>Class</th>
                      </tr>
                    </thead>
                    <tbody style={{ height: "20px", lineHeight: "1" }}>
                      {employee.map((item, index) => {
                        const imageSrc = images?.[item.empCode]?.uploadPath;
                        const imageStatus = images?.[item.empCode]?.verified;
                        const profileStatus = profiles?.[item._id];
                        const classDatas = classData?.[item.classData];
                        const designationDatas =
                          designation?.[item.designation];
                        // const divisionData = divisions?.[item.divison];
                        return (
                          <tr key={item._id}>
                            <td>{startIndex + index + 1}</td>
                            <td>                             
                                  <span
                                    className="btn btn-sm"
                                    style={{
                                      backgroundColor: "red",
                                      color: "white",
                                    }}
                                  >
                                    Edited Only By Pracharya Section
                                  </span>
                                  <br />
                                  <br />                               
                              <Button
                                className="btn-sm btn-primary fa fa-lock"
                                title="Change Password"
                                onClick={() => changePassword(item._id)}
                              ></Button>
                              
                              <Link to={`/admin/update-employee/${item._id}`}>
                                <Button
                                  className="btn-sm btn-warning fa fa-edit"
                                  title="Edit Basic Details"
                                ></Button>
                              </Link>{" "}
                              <Link to={`/admin/profile-print/${item._id}`}>
                                <Button
                                  className=" btn btn-sm mr-2"
                                  style={{ backgroundColor: "orange" }}
                                >
                                  <i className="fas fa-print"></i>
                                </Button>
                              </Link>
                              {(() => {
                                const transfer =
                                  item.transferDetails &&
                                  item.transferDetails[0];

                                if (
                                  transfer &&
                                  transfer.isTransfered === true
                                ) {
                                  if (
                                    transfer.isChargeReleasingDone === false &&
                                    transfer.isJoiningDone === false
                                  ) {
                                    return (
                                      <Badge
                                        color="warning"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Awaiting Relieving & Joining
                                        </strong>
                                      </Badge>
                                    );
                                  } else if (
                                    transfer.isChargeReleasingDone === true &&
                                    transfer.isJoiningDone === false
                                  ) {
                                    return (
                                      <Badge
                                        color="info"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Relieving Done Awaiting Joining
                                        </strong>
                                      </Badge>
                                    );
                                  } else if (
                                    transfer.isChargeReleasingDone === true &&
                                    transfer.isJoiningDone === true
                                  ) {
                                    return (
                                      <Badge
                                        color="primary"
                                        className="text-uppercase"
                                      >
                                        <strong>Transferred & Joined</strong>
                                      </Badge>
                                    );
                                  } else {
                                    return (
                                      <Badge
                                        color="primary"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Transfer Status Incomplete
                                        </strong>
                                      </Badge>
                                    );
                                  }
                                } else {
                                  return (
                                    // <Badge color="success" className="text-uppercase">
                                    //   <strong>Working Now Here</strong>
                                    // </Badge>
                                    null
                                  );
                                }
                              })()}
                            </td>
                            <td>
                              <div
                                style={{
                                  position: "relative",
                                  display: "inline-block",
                                  width: "70px",
                                }}
                              >
                                {imageSrc ? (
                                  <img
                                    src={`https://heonline.cg.nic.in/lmsbackend/${imageSrc}`}
                                    alt={item.name}
                                    className="img-thumbnail"
                                    onClick={() =>
                                      handleConvertAndDisplay(
                                        `https://heonline.cg.nic.in/lmsbackend/${imageSrc}`
                                      )
                                    }
                                  />
                                ) : (
                                  <img
                                    src={NavImage}
                                    alt="Default Placeholder"
                                    className="img-thumbnail"
                                  />
                                )}
                                <div
                                  style={{
                                    position: "absolute",
                                    top:
                                      imageSrc === undefined ||
                                      imageSrc === null
                                        ? "50%"
                                        : "80%",
                                    left: "50%",
                                    transform: "translate(-50%, -50%)",
                                    color: "white",
                                    padding: "5px 10px",
                                    borderRadius: "5px",
                                    fontSize: "12px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  {imageStatus ? (
                                    <span
                                      className="fa fa-check"
                                      style={{
                                        fontSize: "25px",
                                        color: "greenyellow",
                                      }}
                                    ></span>
                                  ) : imageSrc === undefined ||
                                    imageSrc === null ? (
                                    <span style={{ color: "red" }}>
                                      No Photo
                                    </span>
                                  ) : (
                                    <span
                                      className="fa fa-times"
                                      style={{ fontSize: "25px", color: "red" }}
                                    ></span>
                                  )}
                                </div>
                              </div>
                            </td>

                            <td>
                              <b>
                                {item.verified ? (
                                  <i
                                    className="fa fa-check-circle"
                                    style={{ color: "yellowgreen" }}
                                    title="Employee Verified"
                                  ></i>
                                ) : (
                                  <i
                                    className="fa fa-times-circle"
                                    style={{ color: "red" }}
                                    title="Employee Not Verified"
                                  ></i>
                                )}{" "}
                                {item.empCode}
                              </b>
                            </td>
                            <td>
                              {item.name}
                              <br />
                              <br />
                              <b>( {item.designation_details.designation} )</b>
                            </td>
                            <td>{item.email}</td>
                            <td>{item.contact}</td>
                            <td>{item.class_details.className}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardBody>
            </Card>
          </Col>
        </Row>
        <Row className="mt-5">
          <Col>
            <Card className="shadow">
              <CardBody>
                <b style={{ fontSize: "18px" }}>Employee List</b>

                <div className="d-flex justify-content-between align-items-center m-3">
                  <Col xs="3">
                    <select
                      value={filterDesignation}
                      onChange={(e) => setFilterDesignation(e.target.value)}
                      className="form-control"
                    >
                      <option value="">Select Designation</option>
                      {designations &&
                        designations.length > 0 &&
                        designations.map((type, index) => (
                          <option key={index} value={type._id}>
                            {type.designation}
                          </option>
                        ))}
                    </select>
                  </Col>
                  <Col xs="3">
                    <Input
                      id="filterClass"
                      type="select"
                      value={filterClass}
                      onChange={(e) => setFilterClass(e.target.value)}
                      placeholder="Enter class"
                    >
                      <option value="">Select Class</option>
                      {classDatas &&
                        classDatas.length > 0 &&
                        classDatas.map((type, index) => (
                          <option key={index} value={type._id}>
                            {type.className}
                          </option>
                        ))}
                    </Input>
                  </Col>
                  <Col xs="2">
                    <Button
                      onClick={handleClearFilters}
                      size="sm"
                      className="btn btn-danger mt-1"
                    >
                      Clear Filters
                    </Button>
                  </Col>

                  <Button
                    color="primary"
                    href="#"
                    size="sm"
                    onClick={(e) => {
                      e.preventDefault();
                      exportToExcel();
                    }}
                  >
                    Generate Report
                  </Button>
                  <FormControl
                    type="text"
                    placeholder="Search By Name / Employee Code"
                    className="ml-auto"
                    value={filterText}
                    onChange={(e) => setFilterText(e.target.value)}
                    style={{ width: "250px", borderRadius: "30px" }}
                  />
                </div>

                <div className="d-flex justify-content-between align-items-center mt-1">
                  <table className="table">
                    <thead>
                      <tr className="filters">
                        <th>
                          <label>
                            Rows per page:
                            <select
                              value={itemsPerPage}
                              onChange={(e) =>
                                setItemsPerPage(Number(e.target.value))
                              }
                            >
                              <option value={5}>5</option>
                              <option value={10}>10</option>
                              <option value={20}>20</option>
                              <option value={50}>50</option>
                            </select>
                          </label>
                        </th>
                        <th>
                          Status
                          <select
                            id="statusFilter"
                            value={status}
                            onChange={(e) => {
                              setStatus(e.target.value);
                              setCurrentPage(1); // Reset to first page when filter changes
                            }}
                            className="form-control"
                          >
                            <option value="">Select Status</option>
                            <option value="true">Verified</option>
                            <option value="false">Not Verified</option>
                          </select>
                        </th>
                        <th>
                          Work Type
                          <select
                            id="workType"
                            value={workType}
                            onChange={(e) => {
                              setWorkType(e.target.value);
                              setCurrentPage(1); // Reset to first page when filter changes
                            }}
                            className="form-control"
                          >
                            <option value="">Select Work Type</option>
                            <option value="TEACHING">TEACHING</option>
                            <option value="NON TEACHING">NON TEACHING</option>
                          </select>
                        </th>
                        <th>
                          Photo Status
                          <select
                            id="photoFilter"
                            value={imageStatus}
                            onChange={(e) => {
                              setImageStatus(e.target.value);
                              setCurrentPage(1); // Reset to first page when filter changes
                            }}
                            className="form-control"
                          >
                            <option value="">Select Photo Status</option>
                            <option value="Verified">Verified</option>
                            <option value="Not Verified">Not Verified</option>
                            <option value="Not Submitted">Not Submitted</option>
                          </select>
                        </th>
                        <th>
                          Profile Status
                          <select
                            id="profileFilter"
                            value={profileStatus}
                            onChange={(e) => {
                              setProfileStatus(e.target.value);
                              setCurrentPage(1); // Reset to first page when filter changes
                            }}
                            className="form-control"
                          >
                            <option value="">Select Profile Status</option>
                            <option value="Verified">Verified</option>
                            <option value="Not Verified">Not Verified</option>
                            <option value="Not Submitted">Not Submitted</option>
                          </select>
                        </th>
                        <th>
                          <Button
                            className="btn btn-sm"
                            disabled={currentPage === 1}
                            onClick={() =>
                              setCurrentPage((prev) => Math.max(prev - 1, 1))
                            }
                          >
                            Previous
                          </Button>
                          <span className="mx-2">
                            Page {currentPage} of {totalPages}
                          </span>
                          <Button
                            className="btn btn-sm"
                            disabled={currentPage === totalPages}
                            onClick={() =>
                              setCurrentPage((prev) =>
                                Math.min(prev + 1, totalPages)
                              )
                            }
                          >
                            Next
                          </Button>
                        </th>
                      </tr>
                    </thead>
                  </table>
                </div>
                <div className="table-responsive">
                  <table className="table">
                    <thead>
                      <tr>
                        <th>S.No.</th>
                        <th>Action</th>
                        <th>Photo</th>
                        {/* <th>Working Status</th> */}
                        <th>Employee Code</th>
                        <th>Name</th>
                        <th>Email</th>
                        <th>Contact</th>
                        <th>Class</th>
                      </tr>
                    </thead>
                    <tbody style={{ height: "20px", lineHeight: "1" }}>
                      {data.map((item, index) => {
                        const imageSrc = images?.[item.empCode]?.uploadPath;
                        const imageStatus = images?.[item.empCode]?.verified;
                        const profileStatus = profiles?.[item._id];
                        const classDatas = classData?.[item.classData];
                        const designationDatas =
                          designation?.[item.designation];
                        // const divisionData = divisions?.[item.divison];
                        return (
                          <tr key={item._id}>
                            <td>{startIndex + index + 1}</td>
                            <td>
                              {designationDatas === "UG Principal" ||
                              designationDatas === "PG Principal" ||
                              item.isInchargePrincipal === true ? (
                                <>
                                  <span
                                    className="btn btn-sm"
                                    style={{
                                      backgroundColor: "red",
                                      color: "white",
                                    }}
                                  >
                                    Edited Only By Pracharya Section
                                  </span>
                                  <br />
                                  <br />

                                </>
                              ) : (<>
                                <Link
                                  to={`/admin/profile-employee/${item._id}`}
                                >
                                  <Button
                                    className={
                                      profileStatus == true
                                        ? "btn-sm btn-success"
                                        : "btn-sm btn-warning"
                                    }
                                    title="Edit Profile Details"
                                  >
                                    Edit Profile
                                  </Button>
                                </Link>
                                <Button
                                className="btn-sm btn-danger fa fa-trash ml-2"
                                title="Delete Employee"
                                onClick={() => deleteEmployee(item)}
                              ></Button>
                              </>
                              )}{" "}
                              <Button
                                className="btn-sm btn-primary fa fa-lock"
                                title="Change Password"
                                onClick={() => changePassword(item._id)}
                              ></Button>
                              
                              <Link to={`/admin/update-employee/${item._id}`}>
                                <Button
                                  className="btn-sm btn-warning fa fa-edit"
                                  title="Edit Basic Details"
                                ></Button>
                              </Link>{" "}
                              <Link to={`/admin/profile-print/${item._id}`}>
                                <Button
                                  className=" btn btn-sm mr-2"
                                  style={{ backgroundColor: "orange" }}
                                >
                                  <i className="fas fa-print"></i>
                                </Button>
                              </Link>
                              {(() => {
                                const transfer =
                                  item.transferDetails &&
                                  item.transferDetails[0];

                                if (
                                  transfer &&
                                  transfer.isTransfered === true
                                ) {
                                  if (
                                    transfer.isChargeReleasingDone === false &&
                                    transfer.isJoiningDone === false
                                  ) {
                                    return (
                                      <Badge
                                        color="warning"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Awaiting Relieving & Joining
                                        </strong>
                                      </Badge>
                                    );
                                  } else if (
                                    transfer.isChargeReleasingDone === true &&
                                    transfer.isJoiningDone === false
                                  ) {
                                    return (
                                      <Badge
                                        color="info"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Relieving Done Awaiting Joining
                                        </strong>
                                      </Badge>
                                    );
                                  } else if (
                                    transfer.isChargeReleasingDone === true &&
                                    transfer.isJoiningDone === true
                                  ) {
                                    return (
                                      <Badge
                                        color="primary"
                                        className="text-uppercase"
                                      >
                                        <strong>Transferred & Joined</strong>
                                      </Badge>
                                    );
                                  } else {
                                    return (
                                      <Badge
                                        color="primary"
                                        className="text-uppercase"
                                      >
                                        <strong>
                                          Transfer Status Incomplete
                                        </strong>
                                      </Badge>
                                    );
                                  }
                                } else {
                                  return (
                                    // <Badge color="success" className="text-uppercase">
                                    //   <strong>Working Now Here</strong>
                                    // </Badge>
                                    null
                                  );
                                }
                              })()}
                            </td>
                            <td>
                              <div
                                style={{
                                  position: "relative",
                                  display: "inline-block",
                                  width: "70px",
                                }}
                              >
                                {imageSrc ? (
                                  <img
                                    src={`https://heonline.cg.nic.in/lmsbackend/${imageSrc}`}
                                    alt={item.name}
                                    className="img-thumbnail"
                                    onClick={() =>
                                      handleConvertAndDisplay(
                                        `https://heonline.cg.nic.in/lmsbackend/${imageSrc}`
                                      )
                                    }
                                  />
                                ) : (
                                  <img
                                    src={NavImage}
                                    alt="Default Placeholder"
                                    className="img-thumbnail"
                                  />
                                )}
                                <div
                                  style={{
                                    position: "absolute",
                                    top:
                                      imageSrc === undefined ||
                                      imageSrc === null
                                        ? "50%"
                                        : "80%",
                                    left: "50%",
                                    transform: "translate(-50%, -50%)",
                                    color: "white",
                                    padding: "5px 10px",
                                    borderRadius: "5px",
                                    fontSize: "12px",
                                    fontWeight: "bold",
                                  }}
                                >
                                  {imageStatus ? (
                                    <span
                                      className="fa fa-check"
                                      style={{
                                        fontSize: "25px",
                                        color: "greenyellow",
                                      }}
                                    ></span>
                                  ) : imageSrc === undefined ||
                                    imageSrc === null ? (
                                    <span style={{ color: "red" }}>
                                      No Photo
                                    </span>
                                  ) : (
                                    <span
                                      className="fa fa-times"
                                      style={{ fontSize: "25px", color: "red" }}
                                    ></span>
                                  )}
                                </div>
                              </div>
                            </td>

                            <td>
                              <b>
                                {item.verified ? (
                                  <i
                                    className="fa fa-check-circle"
                                    style={{ color: "yellowgreen" }}
                                    title="Employee Verified"
                                  ></i>
                                ) : (
                                  <i
                                    className="fa fa-times-circle"
                                    style={{ color: "red" }}
                                    title="Employee Not Verified"
                                  ></i>
                                )}{" "}
                                {item.empCode}
                              </b>
                            </td>
                            <td>
                              {item.name}
                              <br />
                              <br />
                              <b>( {designationDatas} )</b>
                            </td>
                            <td>{item.email}</td>
                            <td>{item.contact}</td>
                            <td>{classDatas}</td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </CardBody>
            </Card>
          </Col>
          <Modal
            isOpen={isImageModalOpen}
            toggle={toggleImageModal}
            style={{ textAlign: "center" }}
          >
            <ModalHeader toggle={toggleImageModal}>Image Preview</ModalHeader>
            <ModalBody>
              <img src={base64Image} alt="Preview" style={{ width: "50%" }} />
            </ModalBody>
          </Modal>
        </Row>
      </Container>
    </>
  );
};
export default EmployeeList;
