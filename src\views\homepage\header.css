.custom-header {
    width: 100%;
    background: rgb(237, 235, 250);
    padding: 4px 10px;
    text-align: center;
  }
  
  .header-top {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
  
 /* Container for the header-top */
.header-top {
    display: flex;
    justify-content: flex-end; /* Align items to the right */
    gap: 15px; /* Add space between buttons */
    margin: 1px;
  }
  
  /* Button style for all buttons */
  .font-btn, .lang-btn, .skip-content, .screen-reader {
    padding: 2px 5px; /* Add padding for better button size */
    font-size: 12px; /* Slightly larger font size */
    border: 1px solid #ccc; /* Border for the button */
    cursor: pointer;
    background-color: #f0f0f0; /* Light background for a clean look */
    color: rgb(185, 55, 15); /* Formal color */
    border-radius: 5px; /* Rounded corners */
    transition: background-color 0.3s, color 0.3s; /* Smooth transition */
  }
  
  /* Button hover effect for better interactivity */
  .font-btn:hover, .lang-btn:hover, .skip-content:hover, .screen-reader:hover {
    background-color: #e0e0e0; /* Slightly darker on hover */
    color: #000000; /* Change text color on hover */
  }
  
  /* Active button effect */
  .font-btn:active, .lang-btn:active, .skip-content:active, .screen-reader:active {
    background-color: #ddd; /* Even darker background when active */
    color: #333; /* Slightly darker text */
  }
  
  /* Style for buttons when focused (e.g., keyboard navigation) */
  .font-btn:focus, .lang-btn:focus, .skip-content:focus, .screen-reader:focus {
    outline: none; /* Remove default focus outline */
    box-shadow: 0 0 5px rgba(185, 55, 15, 0.5); /* Add custom focus shadow */
  }
  
  /* Style for specific button types (if needed) */
  .skip-content {
    background-color: #4CAF50; /* Green for Skip Content */
    color: white;
  }
  
  .screen-reader {
    background-color: #2196F3; /* Blue for Screen Reader Access */
    color: white;
  }
  
  .font-btn {
    background-color: #FFEB3B; /* Yellow for font size buttons */
    color: black;
  }
  
  .lang-btn {
    background-color: #9C27B0; /* Purple for language toggle */
    color: white;
  }
  
  .font-btn:hover, .lang-btn:hover, .skip-content:hover, .screen-reader:hover {
    background: #95c0f1;
  }
  
  
  
  .header-main {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align to left */
    /* padding: 10px 20px; */
    /* background-color: #f8f9fa; Light background */
  }
  
  .logo {
    height: 110px; /* Increase logo size */
    width: auto; /* Maintain aspect ratio */
    margin-right: 20px; /* Space between logo and text */
  }
  
  .department-name {
    font-size: 36px; /* Bigger font size */
    font-weight: bold;
    color: #2c3e50; /* Dark blue shade */
    white-space: nowrap;
  }
  
  