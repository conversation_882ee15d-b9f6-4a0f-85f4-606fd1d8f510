// BY RX200
import { useEffect, useState } from "react";
import DataTable from "react-data-table-component";
import {
    Container,
    Card,
    CardHeader,
    CardBody,
    FormControl,
    Col,
    Row,
    FormCheck,
    FormGroup,
    <PERSON>ner,
    <PERSON><PERSON>
} from "react-bootstrap";
import { Input, } from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import formatDate from "../../utils/formateDate";
import Swal from "sweetalert2";

const GuestFacultyList = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const id = sessionStorage.getItem("id");

    const [guestFaculty, setGuestFaculty] = useState([]);
    const [totalCount, setTotalCount] = useState(0);
    const [subjectData, setSubjectData] = useState([]);
    const todaydate = new Date().toISOString().split("T")[0]; // "YYYY-MM-DD"
    const [otrFilter, setOtrFilter] = useState("");
    const [nameFilter, setNameFilter] = useState("");
    const [emailFilter, setEmailFilter] = useState("");
    const [mobileFilter, setMobileFilter] = useState("");
    const [fromJoiningDateFilter, setFromJoiningDateFilter] = useState("");
    const [tojoiningDateFilter, setToJoiningDateFilter] = useState(""); // Default to today
    const [phdFilter, setPhdFilter] = useState(false);
    const [netFilter, setNetFilter] = useState(false);
    const [setFilter, setSetFilter] = useState(false);
    const [mphilFilter, setMphilFilter] = useState(false);
    const [experienceYearsFilter, setExperienceYearsFilter] = useState("");
    const [researchPapersFilter, setResearchPapersFilter] = useState("");
    const [isVerifiedFilter, setIsVerifiedFilter] = useState("");
    const [totalCountData, setTotalCountData] = useState(0);

    const handleApiError = (error) => {
        const errorMessage =
            error.response?.data?.msg ||
            (error.request
                ? "No server response. Please check your network."
                : "Unexpected error occurred.");
        SwalMessageAlert(errorMessage, "error");
    };

    useEffect(() => {
        const queryParams = new URLSearchParams(location.search);
        const value = queryParams.get("isVerified");

        // Set initial filter from URL
        if (value === "true" || value === "false") {
            setIsVerifiedFilter(value);
        } else {
            setIsVerifiedFilter(""); // default for no param
        }
    }, []); // runs only once when component mounts

    const handleVerificationChange = (e) => {
        const value = e.target.value; // "true", "false", or ""
        setIsVerifiedFilter(value);
    };



    useEffect(() => {
        const fetchGuestFaculty = async () => {
            try {
                const params = {};

                if (otrFilter.trim() !== "") params.OTRCode = otrFilter.trim();
                if (nameFilter.trim() !== "") params.name = nameFilter.trim();
                if (emailFilter.trim() !== "") params.email = emailFilter.trim();
                if (mobileFilter.trim() !== "") params.mobile = mobileFilter.trim();


                if (fromJoiningDateFilter) {
                    params.fromJoiningDate = fromJoiningDateFilter;
                }
                if (tojoiningDateFilter) {
                    params.toJoiningDate = tojoiningDateFilter;
                }
                if (isVerifiedFilter === "true" || isVerifiedFilter === "false" || isVerifiedFilter !== "") {
                    params.isVerified = isVerifiedFilter === "true" ? true : isVerifiedFilter === "false" ? false : undefined;
                }
                if (netFilter) params.net = "yes";
                if (setFilter) params.set = "yes";
                if (mphilFilter) params.mphil = "yes";
                if (phdFilter) params.phd = "yes";
                if (experienceYearsFilter)
                    params.experienceYears = experienceYearsFilter;
                if (researchPapersFilter.trim() !== "")
                    params.researchPapers = researchPapersFilter.trim();

                const response = await axios.get(
                    `${endPoint}/api/fetch/guest-faculty/college-wise-list/${id}`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                        params,
                    }
                );
                if (response.status === 200) {
                    const data = response.data || {};
                    setGuestFaculty(data.results || []);
                    setTotalCountData(data.totalCountData || 0);
                    setTotalCount((data.results || []).length);
                } else {
                    setGuestFaculty([]);
                    setTotalCount(0);
                }
            } catch (error) {
                handleApiError(error);
            }
        };

        fetchGuestFaculty();
    }, [
        experienceYearsFilter,
        endPoint,
        token,
        id,
        otrFilter,
        nameFilter,
        emailFilter,
        mobileFilter,
        fromJoiningDateFilter,
        tojoiningDateFilter,
        phdFilter,
        netFilter,
        setFilter,
        mphilFilter,
        researchPapersFilter,
        isVerifiedFilter,
    ]);

    // For filter counted with PHD/NET/SET/MPhil
    const selectedQualifications = [
        phdFilter ? "PhD+" : "",
        netFilter ? "NET+" : "",
        setFilter ? "SET+" : "",
        mphilFilter ? "MPhil+" : "",
        experienceYearsFilter ? `Experience: ${experienceYearsFilter} Years+` : "",
        researchPapersFilter ? `Research Papers: ${researchPapersFilter} +` : "",
        isVerifiedFilter === "true" ? "Verified" : isVerifiedFilter === "false" ? "Not Verified" : "",
    ]
        .filter(Boolean) // removes empty strings
        .join(" "); // joins with space


    useEffect(() => {
        const fetchSubjectList = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/get-subjects`, {
                    headers: {
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`
                    },
                });
                if (response.status === 200) {
                    setSubjectData(response.data);
                } else {
                    SwalMessageAlert("No Subject Data Found", "error");
                }
            } catch (error) {
                console.error("Error fetching data:", error);
            }
        };
        fetchSubjectList();
    }, [endPoint, token]);

    const finalSubmit = async (id) => {
        try {
            const response = await axios.get(
                `${endPoint}/api/final-submit/guest-faculty/${id}`,
                {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                }
            );
            if (response.status === 200) {
                setTimeout(() => window.location.reload(), 1000);
                SwalMessageAlert("Final Submitted Successfully", "success");
            } else {
                setTimeout(() => window.location.reload(), 2000);
                SwalMessageAlert("Final Submission Failed", "error");
            }
        } catch (error) {
            handleApiError(error);
        }
    };


    const updateStatus = async (facultyId) => {

        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप Verify करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, Verify",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
                const response = await axios.put(
                    `${endPoint}/api/guest-faculty/update-status/${facultyId}`,
                    {}, // Pass empty object if no data needs to be sent in the body
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) { // Check the status code to ensure success
                    // window.location.reload();
                    setGuestFaculty((prev) =>
                        prev.map((item) =>
                            item._id === facultyId ? { ...item, isVerified: true } : item
                        )
                    );
                    SwalMessageAlert("Verification Sucessfully किया गया .", "success")

                    setTimeout(() => window.location.reload(), 2000);
                } else {
                    SwalMessageAlert("Failed to fetch College data. Please try again.", "error")

                }
            } else {
                SwalMessageAlert("Verification रद्द किया गया .", "success")
            }
        } catch (error) {
            console.error("An error occurred while fetching the data:", error);
            alert("An error occurred. Please try again later.");
        }
    };

    const columns = [
        {
            name: "Action",
            style: { minWidth: "140px" },
            cell: (row) => (
                <div style={{ display: "flex", flexWrap: "wrap", gap: "6px", margin: "2px" }}>
                    <a href={`/admin/update/guest-lecture?guestId=${row._id}`}>
                        <button className="btn btn-warning btn-sm " title="Edit Details">
                            Edit
                        </button>
                    </a>
                    <Button
                        className="btn-sm btn-danger fa fa-trash"
                        title="Delete Employee"
                        onClick={() => deleteFaculty(row)}
                    ></Button>
                    {!row.isFinalSubmit && (
                        <button
                            //  style={{ display: row.isFinalSubmit ? "none" : "" }}
                            onClick={() => finalSubmit(row._id)}
                            className="btn btn-primary btn-sm "
                            title="Final Submit"
                        >
                            Final Submit
                        </button>
                    )}

                    {!row.isVerified && (
                        <button
                            className="btn btn-warning btn-sm "
                            onClick={() => updateStatus(row._id)}
                        >
                            <Spinner size="sm" color="white" style={{ marginRight: "8px" }} />
                            Verify
                        </button>
                    )}

                    {row.isVerified === true && (
                        <strong className="btn-success btn-sm " disabled>Verified</strong>

                    )}

                </div>
            ),
        },

        {
            name: "OTR Number",
            cell: (row) => <div>{row.OTRCode}</div>,
        },
        {
            name: "Basic Details", wrap: true, 

            cell: (row) => (
                <div style={{ lineHeight: "1.6" }}>
                    <div><strong>Name:</strong> {row.name}</div>
                    <div style={{
                        whiteSpace: "nowrap",       // keeps email on one line
                        overflow: "hidden",         // hides overflow
                        textOverflow: "ellipsis",   // adds ... if too long

                    }}>
                        <strong>Email:</strong> {row.email}</div>
                    <div><strong>Contact:</strong> {row.mobile}</div>
                    <div><strong>DOB:</strong> {formatDate(row.dob)}</div>
                </div>
            ),
        },
        {
            name: "Joining Date",
            style: { marginLeft: "120px", minWidth: "100px", maxWidth: "200px" },
            cell: (row) => <div>{formatDate(row.joiningDate)}</div>,
        },
        {
            name: "Subject",
            selector: (row) =>
                subjectData.find((a) => String(a._id) === String(row.subject))?.subjectName || "",
        },
        {
            name: "PHD/NET/SET/Mphil",
            style: { minWidth: "230px", maxWidth: "240px" },
            selector: (row) => (
                <div style={{ lineHeight: "1.4", whiteSpace: "normal", wordBreak: "break-word" }}>
                    {row.phd === "yes" && <div>PHD Date: {formatDate(row.phdNotificationDate)}</div>}
                    {row.net === "yes" && <div>NET Date: {formatDate(row.netNotificationDate)}</div>}
                    {row.set === "yes" && <div>SET Date: {formatDate(row.setNotificationDate)}</div>}
                    {row.mphil === "yes" && <div>MPhil Date: {formatDate(row.mphilNotificationDate)}</div>}
                </div>
            ),
        },
        {
            name: "Experience / Research Papers",
            style: {   minWidth:"90px" },
            selector: (row) => (
                <div>
                   {row.experienceYears === 0 && row.experienceMonths === 0 && row.experienceDays === 0 ? (
  <div><strong className="text-danger">No Experience</strong></div>
) : (
  <>
    <div><strong>Years:</strong> {row.experienceYears}</div>
    <div><strong>Months:</strong> {row.experienceMonths}</div>
    <div><strong>Days:</strong> {row.experienceDays}</div>
  </>
)}

                     <span > Research Paper : <b className="text-primary"> {row.researchPapers}</b></span>
                </div>
            ),
        },
        // {
        //     name: "Research Papers",
        //     selector: (row) => <b>{row.researchPapers}</b>,
        // },
    ];

    const resetFilters = () => {
        setOtrFilter("");
        setNameFilter("");
        setEmailFilter("");
        setMobileFilter("");
        setFromJoiningDateFilter("");
        setToJoiningDateFilter("");
        setPhdFilter(false);
        setNetFilter(false);
        setSetFilter(false);
        setMphilFilter(false);
        setExperienceYearsFilter("");
        setResearchPapersFilter("");
        setIsVerifiedFilter("");

        // Optional: trigger fetch manually (if you're not relying on useEffect)
        // fetchGuestFaculty(); // uncomment if needed
    };

    const deleteFaculty = async (faculty) => {
        const result = await Swal.fire({
            title: `Are you sure you want to delete ${faculty.name}?`,
            html: `<strong>OTR Code: ${faculty.OTRCode}</strong>`,
            input: "text",
            inputPlaceholder: "Enter reason for deletion...",
            inputAttributes: { "aria-label": "Reason for deletion" },
            showCancelButton: true,
            confirmButtonColor: "#3085d6",
            cancelButtonColor: "#d33",
            confirmButtonText: "Yes, delete it!",
            cancelButtonText: "No, keep it",
            preConfirm: (reason) => {
                if (!reason) {
                    Swal.showValidationMessage("Reason for delete is required");
                }
                return reason;
            },
        });

        if (result.isConfirmed) {
            const reason = result.value;
            try {
                const response = await axios.put(
                    // `${endPoint}/api/delete-faculty?id=${faculty._id}&key=faculty`,

                    `${endPoint}/api/delete-faculty?id=${faculty._id}&key=Faculty&collegeId=${faculty.collegeId}`, { reason },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            "web-url": window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    SwalMessageAlert("Guest Faculty Deleted Successfully", "success");
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    SwalMessageAlert(
                        "Delete Guest Faculty Failed. Please try again!",
                        "error"
                    );
                }
            } catch (error) {
                SwalMessageAlert(
                    "An error occurred while Deleting Guest Faculty. Please try again later!",
                    "error"
                );
                console.error("An error occurred while fetching the data:", error);
            }
        }
    };
    return (
        <>
            <Header />
            <Container className="mt--7" fluid>
                <Card className="shadow">
                    <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                        <Col xs="12">
                            <b style={{ fontSize: "18px" }}>Guest Faculty List</b>
                        </Col>

                    </CardHeader>
                    <CardBody className="bg-secondary">
                        <Row className="mb-3">
                            <Col md={3}>
                                <label>Filter by OTR</label>
                                <FormControl placeholder="Filter by OTR" value={otrFilter} onChange={(e) => setOtrFilter(e.target.value)} />
                            </Col>
                            <Col md={3}>
                                <label>Filter by Name</label>
                                <FormControl placeholder="Filter by Name" value={nameFilter} onChange={(e) => setNameFilter(e.target.value)} />
                            </Col>
                            <Col md={3}>
                                <label>Filter by Email</label>
                                <FormControl placeholder="Filter by Email" value={emailFilter} onChange={(e) => setEmailFilter(e.target.value)} />
                            </Col>
                            <Col md={3}>
                                <label>Filter by Mobile</label>
                                <FormControl placeholder="Filter by Mobile" value={mobileFilter} onChange={(e) => setMobileFilter(e.target.value)} />
                            </Col>
                        </Row>
                        <Row className="mb-3">
                            <Col md={3}>
                                <label>Filter by Experience Years</label>
                                <FormControl
                                    type="number"
                                    placeholder="Experience Years"
                                    min={0}
                                    value={experienceYearsFilter}
                                    onChange={(e) => setExperienceYearsFilter(e.target.value)}
                                />
                            </Col>
                            <Col md={3}>
                                <label>Filter by Research Papers</label>
                                <FormControl
                                    type="number"
                                    min={0}
                                    placeholder="Research Papers"
                                    value={researchPapersFilter}
                                    onChange={(e) => setResearchPapersFilter(e.target.value)}
                                />
                            </Col>
                            <Col md={3}>
                                <label>From Joining Date</label>
                                <FormControl
                                    type="date"
                                    value={fromJoiningDateFilter}
                                    max={todaydate}
                                    onChange={(e) => setFromJoiningDateFilter(e.target.value)}
                                />
                            </Col>
                            <Col md={3}>
                                <label>To Joining Date</label>
                                <FormControl
                                    type="date"
                                    max={todaydate}
                                    value={tojoiningDateFilter}
                                    onChange={(e) => setToJoiningDateFilter(e.target.value)}
                                />
                            </Col>
                        </Row>

                        <Row className="mb-3 text-center">
                            <Col md={1}>

                                <FormCheck label="PHD" checked={phdFilter} onChange={(e) => setPhdFilter(e.target.checked)} />
                            </Col>
                            <Col md={1}>
                                <FormCheck label="NET" checked={netFilter} onChange={(e) => setNetFilter(e.target.checked)} />
                            </Col>
                            <Col md={1}>
                                <FormCheck label="SET" checked={setFilter} onChange={(e) => setSetFilter(e.target.checked)} />
                            </Col>
                            <Col md={1}>
                                <FormCheck label="MPhil" checked={mphilFilter} onChange={(e) => setMphilFilter(e.target.checked)} />
                            </Col>
                            <Col md={2}>
                                <FormGroup>
                                    <Input
                                        id="isVerifiedSelect"
                                        type="select"
                                        value={isVerifiedFilter}
                                        onChange={handleVerificationChange}
                                    >
                                        <option value=""><strong>All</strong></option>
                                        <option value="true"><strong>Verified</strong></option>
                                        <option value="false"><strong>Not Verified</strong></option>
                                    </Input>
                                </FormGroup>
                            </Col>
                            <Col md={2}>
                                <button onClick={resetFilters} className="btn btn-sm btn-danger">
                                    Reset Filters
                                </button>
                            </Col>
                            <Col md={4}>
                                <strong className="mb-0">
                                    Total Guest Faculty : <span className="text-danger">{totalCountData}</span>
                                </strong>
                                <br />
                                {selectedQualifications && selectedQualifications !== "" && (
                                    <strong className="mb-0">
                                        {selectedQualifications} :={" "}
                                        <span className="text-primary">{totalCount}</span>
                                    </strong>
                                )}
                            </Col>
                        </Row>
                        <hr />
                        <div style={{ width: "100%", overflowX: "auto"  ,border: "1px solid #ccc", borderRadius: "4px", padding: "8px" }}>
                            <DataTable
                                columns={columns}
                                data={guestFaculty.length > 0 ? guestFaculty : []}
                                pagination
                                highlightOnHover
                                striped
                                responsive
                                persistTableHead
                                dense
                            />
                        </div>
                    </CardBody>
                </Card>
            </Container>
        </>
    );
};

export default GuestFacultyList;
