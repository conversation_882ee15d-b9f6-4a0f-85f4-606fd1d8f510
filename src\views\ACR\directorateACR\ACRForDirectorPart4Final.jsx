import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,
    Col,
    Label,
    Input,
} from "reactstrap";



const ACRForDirectorPart4Final = (employee) => {
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);
    const [errors, setErrors] = useState({});




    return (
        <>
            <Container className="mt--7" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2">प्रपत्र - तीन </h2>
                                <h2> प्रथम श्रेणी, द्वितीय श्रेणी एवं कार्यपालिक तृतीय श्रेणी</h2>
                                <h2>
                                    अधिकारी का गोपनीय प्रतिवेदन
                                </h2>
                                <h3>31 मार्च {year} को समाप्त होने वाली अवधि</h3>
                                <br /><h2><u>भाग – एक</u></h2>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row>
                                        <Col>
                                            <Row className="mb-3 ">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong>अधिकारी का नाम </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={employee.employee.basicDetails[0].employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पदनाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="designation"
                                                        value={employee.employee.basicDetails[0].designation} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.designation && (
                                                        <small className="text-danger">
                                                            {errors.designation}
                                                        </small>
                                                    )}

                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong> पदस्थापना जिला</Label>
                                                    <Input
                                                        type="text"
                                                        name="padSthapnaJila"
                                                        value={employee.employee.basicDetails[0].padSthapnaJila} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.padSthapnaJila && (
                                                        <small className="text-danger">
                                                            {errors.padSthapnaJila}
                                                        </small>
                                                    )}
                                                </Col>

                                            </Row>

                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row>
                                        <Col>
                                            <Row className="mt-4" style={{ textAlign: "center", display: "block" }}>
                                                <Col>
                                                    <h2>
                                                        <u >भाग- दो</u>
                                                    </h2>
                                                </Col>
                                                <Col>
                                                    <h3>
                                                        (प्रतिवेदित अधिकारी द्वारा भरा जाए)

                                                    </h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>1.</strong> कार्य का संक्षिप्त विवरण :-</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKaSanchhiptVivran"
                                                        value={employee.employee.basicDetails[0].karyaKaSanchhiptVivran} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.karyaKaSanchhiptVivran && (
                                                        <small className="text-danger">
                                                            {errors.karyaKaSanchhiptVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt--4">
                                                    <Label><strong>2. </strong>कृपया आपके लिये निर्धारित गुणात्मक /
                                                        भौतिक / वित्तीय लक्ष्यों को प्राथमिकता क्रम से और
                                                        प्रत्येक लक्ष्य के विरूद्ध उपलब्धि का उल्लेख करें ।
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="uplabdhiKaUllekh"
                                                        value={employee.employee.basicDetails[0].uplabdhiKaUllekh} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.uplabdhiKaUllekh && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhiKaUllekh}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-5">
                                                <Col md="6">
                                                    <Label>
                                                        <strong>3.</strong> कृपया, कॉलम 2 के सन्दर्भ में लक्ष्यों /
                                                        उद्देश्यों की पूर्ति में कमी का संक्षिप्त विवरण दें।
                                                        यदि लक्ष्यों की पूर्ति में कोई कठिनाई
                                                        (बाधा) आई हो तो उसको भी बतायें  :</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="kathinayiBadhaKaUllekh"
                                                        value={employee.employee.basicDetails[0].kathinayiBadhaKaUllekh} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.kathinayiBadhaKaUllekh && (
                                                        <small className="text-danger">
                                                            {errors.kathinayiBadhaKaUllekh}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6" className="mt-4">
                                                    <Label><strong>4. </strong>कृपया उन मदों को भी दर्शाएं जिनमें
                                                        अति महत्वपूर्ण उपलब्धियों में आपका सहयोग रहा हो -
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="uplabdhiMeSahyog"
                                                        value={employee.employee.basicDetails[0].uplabdhiMeSahyog} // Use employee data here
                                                        readOnly
                                                        className="form-control"

                                                    />
                                                    {errors.uplabdhiMeSahyog && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhiMeSahyog}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>






                                    <hr />
                                    <Row>
                                        <Col>


                                            {employee.employee.levelIndex >= 0 && employee.employee.preDefinedLevels[0].status === true && <>

                                                <Row>
                                                    <Col>
                                                        <Row className="mt-4" style={{ textAlign: "center", display: "block" }}>
                                                            <Col>
                                                                <h2>
                                                                    <u >भाग - तीन
                                                                    </u>
                                                                </h2>
                                                            </Col>
                                                            <Col>
                                                                <h3>
                                                                    (प्रतिवेदक अधिकारी द्वारा भरा जाए)
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col>
                                                                <h3>
                                                                    (अ)कार्य का स्वरूप एवं प्रकार:-
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3">
                                                            <Col md="6">
                                                                <Label>
                                                                    <strong>1.</strong> कृपया अधिकारी द्वारा भरे गये भाग
                                                                    दो पर विशेष रूप से साक्ष्यो और उद्देश्यों उपलब्धियों, कमियों से
                                                                    सम्बन्धित उत्तरों से सहमति
                                                                    सम्बन्धी टीप दें । यदि किसी उददेश्य की
                                                                    पूर्ति  में कोई बाधा है तो उसका भी उल्लेख करें :-</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="sahmatiSambandhitTip"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.sahmatiSambandhitTip} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.sahmatiSambandhitTip && (
                                                                    <small className="text-danger">
                                                                        {errors.sahmatiSambandhitTip}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" ><Label><strong>2.
                                                                किये गये कार्य की गुणवत्ता:- <br />
                                                            </strong>कृपया, अधिकारी द्वारा किये गये कार्य की
                                                                गुणवत्ता, स्तर और कार्यक्षमता का उददेश्य
                                                                और बाधाएं यदि कोई हो, के संबंध में टीप दे -
                                                            </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="karyaKiGudwatta"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.karyaKiGudwatta} // Use employee data here
                                                                    maxLength={400}
                                                                    className="form-control"

                                                                />
                                                                {errors.karyaKiGudwatta && (
                                                                    <small className="text-danger">
                                                                        {errors.karyaKiGudwatta}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 mt-5">
                                                            <Col md="6">
                                                                <Label>
                                                                    <strong>3. कार्यक्षेत्र का ज्ञानः- <br />
                                                                    </strong> कृपया, विशिष्ठ रूप से इनमें से
                                                                    प्रत्येक पर टीप दें कार्यों के ज्ञान का
                                                                    स्तर संबंधित अनुदेश और उनका लागू किया जाना - :</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="karyaChhetraKaGyan"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.karyaChhetraKaGyan} // Use employee data here
                                                                    maxLength={400}
                                                                    className="form-control"

                                                                />
                                                                {errors.karyaChhetraKaGyan && (
                                                                    <small className="text-danger">
                                                                        {errors.karyaChhetraKaGyan}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col>
                                                                <h3>
                                                                    (ब) कार्य के प्रति दृष्टिकोण :-
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>1.
                                                                </strong>अधिकारी द्वारा किस हद तक कार्य समर्पण,
                                                                    प्रेरणा उसकी इच्छा और पहल कर
                                                                    व्यवस्थित रूप से किया गया. पर टीप दे -
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="karyaSamparpanPrerna"
                                                                    maxLength={400}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.karyaSamparpanPrerna} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.karyaSamparpanPrerna && (
                                                                    <small className="text-danger">
                                                                        {errors.karyaSamparpanPrerna}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" >
                                                                <Label><strong>2. निर्णय लेने की योग्यता <br />
                                                                </strong>निर्णय लेने के गुण, पक्ष-विपक्ष को
                                                                    देखते हुए वैकल्पिक योग्यता पर टीप -
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="nirnayaLeneKiYogyta"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.nirnayaLeneKiYogyta} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.nirnayaLeneKiYogyta && (
                                                                    <small className="text-danger">
                                                                        {errors.nirnayaLeneKiYogyta}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>3. पहल <br />
                                                                </strong>अधिकारी की अप्रत्याशित परिस्थितियों से निपटने
                                                                    की क्षमता और उपाय और कार्य
                                                                    के नवीन क्षेत्रों में स्वेच्छा से अतिरिक्त
                                                                    उत्तरदायित्व लेने के सम्बन्ध में टीप
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="pahal"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.pahal} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.pahal && (
                                                                    <small className="text-danger">
                                                                        {errors.pahal}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" >
                                                                <Label><strong>4. प्रोत्साहन और प्रेरणा की योग्यता-<br />
                                                                </strong>कृपया, अधिकारी की प्रेरणा देने,
                                                                    स्वयं के आचरण और विश्वास से
                                                                    सहयोग प्राप्त करने की क्षमता पर टीप दें-
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="prernakaYogyta"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.prernakaYogyta} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.prernakaYogyta && (
                                                                    <small className="text-danger">
                                                                        {errors.prernakaYogyta}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>5. संसूचना कौशल (लिखित और मौखिक)<br />
                                                                </strong>अधिकारी की संसूचना एवं तवं
                                                                    प्रस्तुत करने की योग्यता के सम्बन्ध में टीप
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="sansuchnaKaushal"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.sansuchnaKaushal} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.sansuchnaKaushal && (
                                                                    <small className="text-danger">
                                                                        {errors.sansuchnaKaushal}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" >
                                                                <Label><strong>6. व्यक्तिगत सम्बन्ध एवं समूह कार्य (टीम वर्क)<br />
                                                                </strong>उच्च अधिकारियों सहयोगियों एवं अधीनस्थों
                                                                    से सम्बन्ध, दूसरों के विचारों की सराहना एवं
                                                                    सदभावना से ली गई सलाह की योग्यता का
                                                                    उल्लेख करें। कृपया टीम के सदस्य के रूप
                                                                    में कार्यक्षमता और टीम भावना को बढ़ाने और टीम
                                                                    द्वारा किये गये कार्यो की उत्तमता पर भी टीप दें -
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="vyaktigatSambandhAndTeamWork"
                                                                    value={employee.employee.levelDetails[0].data.vyaktigatSambandhAndTeamWork} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.vyaktigatSambandhAndTeamWork && (
                                                                    <small className="text-danger">
                                                                        {errors.vyaktigatSambandhAndTeamWork}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>(7) आम जनता के साथ सम्बन्ध -<br />
                                                                </strong>अधिकारी की आम जनता तक
                                                                    पहुंच और उनकी आवश्यकताओं
                                                                    के प्रति संवेदनशीलता
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="aamJantaKeSathSambandh"
                                                                    value={employee.employee.levelDetails[0].data.aamJantaKeSathSambandh} // Use employee data here
                                                                    className="form-control"
                                                                    readOnly

                                                                />
                                                                {errors.aamJantaKeSathSambandh && (
                                                                    <small className="text-danger">
                                                                        {errors.aamJantaKeSathSambandh}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row>
                                                            <Col>
                                                                <h3>
                                                                    (स) अतिरिक्त गुण (विशेषताएं)
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="12" >
                                                                <Label><strong>(1).  योजना बनाने की योग्यता-<br />
                                                                </strong>क्या अधिकारी से समस्याओं, कार्यों की
                                                                    आवश्यकताओं का पूर्व अनुमान
                                                                    लगाकर तदनुसार योजना बनाने
                                                                    और सम्भावनी हल उपलब्ध
                                                                    कराने की क्षमता है?
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="yojnaBananeKiYogyta"
                                                                    maxLength={400}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.yojnaBananeKiYogyta} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.yojnaBananeKiYogyta && (
                                                                    <small className="text-danger">
                                                                        {errors.yojnaBananeKiYogyta}
                                                                    </small>
                                                                )}
                                                            </Col>

                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>(2) निरीक्षण की योग्यता -
                                                                </strong>
                                                                </Label>
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>(1).</strong>कार्य का समुचित बंटवारा</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="samuchitBatwara"
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.samuchitBatwara} // Use employee data here
                                                                    className="form-control"
                                                                    maxLength={400}

                                                                />
                                                                {errors.samuchitBatwara && (
                                                                    <small className="text-danger">
                                                                        {errors.samuchitBatwara}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" className="">
                                                                <Label><strong>(2).</strong>कार्य करवाने के लिये उचित कर्मियों का चुनाव</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="uchitKarmiyoKaChunav"
                                                                    readOnly
                                                                    maxLength={400}
                                                                    value={employee.employee.levelDetails[0].data.uchitKarmiyoKaChunav} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.uchitKarmiyoKaChunav && (
                                                                    <small className="text-danger">
                                                                        {errors.uchitKarmiyoKaChunav}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>(3).</strong>कार्य करने में मार्गदर्शन</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="karyaKarneMeMargdarshan"
                                                                    maxLength={400}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.karyaKarneMeMargdarshan} // Use employee data here
                                                                    className="form-control"


                                                                />
                                                                {errors.karyaKarneMeMargdarshan && (
                                                                    <small className="text-danger">
                                                                        {errors.karyaKarneMeMargdarshan}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                            <Col md="6" >
                                                                <Label><strong>(4).</strong>कार्य की समीक्षा</Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="karyaKiSamiksha"
                                                                    maxLength={400}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.karyaKiSamiksha} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.karyaKiSamiksha && (
                                                                    <small className="text-danger">
                                                                        {errors.karyaKiSamiksha}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <hr />
                                                        <Row className="mt-4" style={{ textAlign: "center", display: "block" }}>
                                                            <Col>
                                                                <h2>
                                                                    भाग - चार
                                                                </h2>
                                                            </Col>
                                                            <Col>
                                                                <h3>
                                                                    सामान्य
                                                                </h3>
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="6" >
                                                                <Label><strong>(1) निष्ठा-<br />
                                                                </strong>
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="nishtha"
                                                                    maxLength={400}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.nishtha} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.nishtha && (
                                                                    <small className="text-danger">
                                                                        {errors.nishtha}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="3" >
                                                                <Label><strong>(2) श्रेणी-<br />
                                                                </strong>
                                                                </Label>
                                                                <Input
                                                                    name="shreniKaran1"
                                                                    type="select"
                                                                    readOnly
                                                                    id="recordsPerPage"
                                                                    value={employee.employee.levelDetails[0].data.shreniKaran1}


                                                                >
                                                                    <option value="">चुनें</option>
                                                                    <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                                    <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                                    <option value="अच्छा">अच्छा</option>
                                                                    <option value="औसत">औसत</option>
                                                                    <option value="औसत से कम">औसत से कम</option>
                                                                </Input>
                                                                {errors.shreniKaran1 && (
                                                                    <small className="text-danger">
                                                                        {errors.shreniKaran1}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>
                                                        <Row className="mb-3 ">
                                                            <Col md="12" >
                                                                <Label><strong>(3) रिमार्क -<br />
                                                                </strong>
                                                                </Label>
                                                                <Input
                                                                    type="textarea"
                                                                    name="remark"
                                                                    style={{ height: "100px" }}
                                                                    maxLength={500}
                                                                    readOnly
                                                                    value={employee.employee.levelDetails[0].data.remark} // Use employee data here
                                                                    className="form-control"

                                                                />
                                                                {errors.remark && (
                                                                    <small className="text-danger">
                                                                        {errors.remark}
                                                                    </small>
                                                                )}
                                                            </Col>
                                                        </Row>

                                                    </Col>
                                                </Row>

                                            </>}






                                            {employee.employee.levelIndex >= 1 && employee.employee.preDefinedLevels[1].status === true && <>

                                                <hr />
                                                <Row className="mb-3">
                                                    <Col>
                                                        <h2 style={{ textAlign: "center" }}>
                                                            <u>समीक्षक अधिकारी की टिप्पणी</u>
                                                        </h2>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="3">
                                                        <Label>श्रेणीकरण</Label>
                                                        <Input
                                                            name="shreniKaran2"
                                                            type="select"
                                                            id="recordsPerPage"
                                                            value={employee.employee?.levelDetails[1].data.shreniKaran2}

                                                            disabled
                                                        >
                                                            <option value="">चुनें</option>
                                                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                            <option value="अच्छा">अच्छा</option>
                                                            <option value="साधारण">साधारण</option>
                                                            <option value="घटिया">घटिया</option>
                                                        </Input>

                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="12">
                                                        <Label>रिमार्क</Label>
                                                        <Input
                                                            name="remark2"
                                                            type="textarea"
                                                            style={{ height: "100px" }}

                                                            value={employee.employee?.levelDetails[1].data.remark2}

                                                            disabled
                                                        />

                                                    </Col>
                                                </Row>
                                            </>}

                                            {employee.employee.levelIndex >= 2 && employee.employee.preDefinedLevels[2].status === true && <>


                                                <hr />
                                                <Row className="mb-3">
                                                    <Col>
                                                        <h2 style={{ textAlign: "center" }}>
                                                            <u>स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                        </h2>
                                                    </Col>
                                                </Row>
                                                <Row className="mb-4">
                                                    <Col md="4">
                                                        <Label>श्रेणीकरण</Label>
                                                        <Input
                                                            name="shreniKaran3"
                                                            type="select"
                                                            id="recordsPerPage"
                                                            readOnly
                                                            value={employee.employee?.levelDetails[2]?.data.shreniKaran3}

                                                        >
                                                            <option value="">चुनें</option>
                                                            <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                            <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                            <option value="अच्छा">अच्छा</option>
                                                            <option value="साधारण">साधारण</option>
                                                            <option value="घटिया">घटिया</option>
                                                        </Input>
                                                        {errors.shreniKaran3 && (
                                                            <small className="text-danger">
                                                                {errors.shreniKaran3}
                                                            </small>
                                                        )}
                                                    </Col>
                                                    <Col md="4">
                                                        <Label>रिमार्क</Label>
                                                        <Input
                                                            name="remark3"
                                                            style={{ height: "100px" }}
                                                            type="textarea"
                                                            readOnly
                                                            value={employee.employee?.levelDetails[2]?.data.remark3}

                                                        />
                                                        {errors.remark3 && (
                                                            <small className="text-danger">{errors.remark3}</small>
                                                        )}
                                                    </Col>
                                                </Row>

                                            </>}
                                        </Col>
                                    </Row>

                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

ACRForDirectorPart4Final.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRForDirectorPart4Final;
