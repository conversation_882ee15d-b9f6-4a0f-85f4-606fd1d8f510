import { useState } from "react";
import PropTypes from "prop-types";
import {
    Card,
    CardHeader,
    CardBody,
    Container,
    Row,

    Col,
    Label,
    Button,
    Input,
} from "reactstrap";

import SwalMessageAlert from "../../../utils/sweetAlertMessage";
import axios from "axios";
import Swal from "sweetalert2";


const ACRformForRegistrarPart4 = ({ employee }) => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");
    const d = new Date();
    const year = d.getFullYear();
    const lastYear = year - 1;
    const [lastYearDate] = useState(lastYear);




    const [formData, setFormData] = useState({
        applicationId: employee[0].applicationId,
        employeeId: employee[0].employeeId,
        employeeName: employee[0].employeeName,
        collegeName: employee[0].collegeName,
        collegeId: employee[0].collegeId,
        shreniKaran2: "",
        remark2: "",
    })


    const [errors, setErrors] = useState({});

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
        setErrors({ ...errors, [name]: "" });
    };

    const handleCheckboxChange = (e) => {
        const { name, checked } = e.target;
        // Update formData state
        setFormData((prevData) => ({
            ...prevData,
            [name]: checked,
        }));
    };


    const handleFinalSubmit = async (e) => {

        // Validation function to check for required fields
        const validateForm = () => {
            const newErrors = {};
            const requiredFields = {
                shreniKaran2: "",
                remark2: "",
            };

            // Iterate over each field in the requiredFields object
            for (const field in requiredFields) {
                if (requiredFields.hasOwnProperty(field)) {
                    // Check if the field is empty or not
                    if (!formData[field] || (typeof formData[field] === 'string' && !formData[field].trim())) {
                        newErrors[field] = "This Field is Required.";

                    }
                }
            }

            return newErrors;
        };



        const validationErrors = validateForm();
        if (Object.keys(validationErrors).length > 0) {
            setErrors(validationErrors);
            return;
        }

        try {
            const result = await Swal.fire({
                title: "Confirmation",
                text: "क्या आप सुनिश्चित हैं कि आप यह सबमिट करना चाहते हैं ?",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#3085d6",
                cancelButtonColor: "#d33",
                confirmButtonText: "Yes, submit",
                cancelButtonText: "No, cancel",
            });

            if (result.isConfirmed) {
        e.target.disabled = true;

                const response = await axios.post(
                    `${endPoint}/api/acr/add`,
                    { ...formData },
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    SwalMessageAlert("अंतिम मतांकन सफल हुआ", "success");
                    setTimeout(() => window.location.reload(), 5000);
                } else {
                    SwalMessageAlert(response.data.msg, "error");
                }
            }
        } catch (error) {
            const errorMessage =
                error.response?.data?.msg ||
                "An unexpected error occurred. Please try again.";
            SwalMessageAlert(errorMessage, "error");
        }
    };


    return (
        <>
            <Container className="mt--6" fluid>
                <Row>
                    <Col>
                        <Card className="shadow pb-4">
                            <CardHeader
                                className="bg-white border-0"
                                style={{ textAlign: "center" }}
                            >
                                <h2 className="mb-2"> <u>प्रपत्र - तीन</u></h2>
                                <h2>प्रथम श्रेणी, द्वितीय श्रेणी, कार्यपालिक श्रेणी के अधिकारी का</h2>
                                <br /><h2> <u>गोपनीय प्रतिवेदन</u></h2>
                                <h3>|| 31 मार्च {year} को समाप्त होने होने वाली अवधि ||</h3>

                            </CardHeader>
                            <CardBody>
                                <div className="mb-4">
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - एक</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="3">
                                                    <Label> <strong>1.</strong>अधिकारी का नाम

                                                    </Label>
                                                    <Input
                                                        type="text"
                                                        name="employeeName"
                                                        value={employee[0].basicDetails[0].employeeName} // Use employee data here
                                                        readOnly
                                                        className="form-control"
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.employeeName && (
                                                        <small className="text-danger">
                                                            {errors.employeeName}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>2.</strong>पदनाम</Label>
                                                    <Input
                                                        type="text"
                                                        name="designation"
                                                        value={employee[0].basicDetails[0].designation} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.designation && (
                                                        <small className="text-danger">
                                                            {errors.designation}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>3.</strong>नियोजन का प्रकार</Label>
                                                    <Input
                                                        type="text"
                                                        name="niyojanKaPrakar"
                                                        value={employee[0].basicDetails[0].niyojanKaPrakar} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.niyojanKaPrakar && (
                                                        <small className="text-danger">
                                                            {errors.niyojanKaPrakar}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="3">
                                                    <Label><strong>4.</strong>पदस्थापना का जिला</Label>
                                                    <Input
                                                        type="text"
                                                        name="padSthapnaKaJila"
                                                        value={employee[0].basicDetails[0].padSthapnaKaJila} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.padSthapnaKaJila && (
                                                        <small className="text-danger">
                                                            {errors.padSthapnaKaJila}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - दो ( प्रतिवेदित अधिकारी द्वारा जाये ).</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col md="12">
                                                    <Label> <strong>1.</strong>कार्य का संक्षिप्त विवरण</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKaVivran"
                                                        value={employee[0].basicDetails[0].karyaKaVivran} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.karyaKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <Label> <strong>2.</strong>कृपया आपके लिये निर्धारित गुणात्मक / भौतिक/वित्तीय लक्ष्यों को प्राथमिकता क्रम में और प्रत्येक लक्ष्य के विरूद्ध उपलब्धि का उल्लेख करें ।
                                                    </Label>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>लक्ष्य : </strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        name="lakshya"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={employee[0].basicDetails[0].lakshya} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.lakshya && (
                                                        <small className="text-danger">
                                                            {errors.lakshya}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label><strong>उपलब्धियाँ :</strong></Label>
                                                    <Input
                                                        type="textarea"
                                                        name="uplabdhi"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={employee[0].basicDetails[0].uplabdhi} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.uplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.uplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label><strong>3. </strong>
                                                        कृपया कालम 2 के संदर्भ में लक्ष्यों /
                                                        उद्देश्यों की पूर्ति में कमी का संक्षिप्त विवरण दें ।
                                                        यदि लक्ष्यों की पूर्ति में कोई कठिनाई
                                                        (बाधा) आई हो तो उसको भी बतायें ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="purtiKeKamiKaVivran"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={employee[0].basicDetails[0].purtiKeKamiKaVivran} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.purtiKeKamiKaVivran && (
                                                        <small className="text-danger">
                                                            {errors.purtiKeKamiKaVivran}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col className="mt-4" md="6">
                                                    <Label><strong>4. </strong>
                                                        कृपया उन मदों को भी दर्शाये
                                                        जिनमें अति महत्वपूर्ण उपलब्धियां
                                                        और आपका सहयोग रहा हो ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="atiMahatvaPurnUplabdhi"
                                                        style={{ height: "150px" }}
                                                        maxLength={400}
                                                        value={employee[0].basicDetails[0].atiMahatvaPurnUplabdhi} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.atiMahatvaPurnUplabdhi && (
                                                        <small className="text-danger">
                                                            {errors.atiMahatvaPurnUplabdhi}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <hr />
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <h3><i> <u>भाग - तीन (प्रतिवेदन अधिकारी द्वारा भरा जावे).</u></i></h3>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <h3><strong>(अ) कार्य का स्परूप एवं प्रकार-</strong></h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col md="6">
                                                    <Label> <strong>1.</strong>कृपया अधिकारी द्वारा भरे गये भाग-दो पर विशेष रूप से लक्ष्यों और उद्देश्यों, उपलब्धियों, कर्मियों से संबंधित उत्तरों से सहमति संबंधी टीप दें, यदि किसी उद्देश्यों की पूर्ति में कोई बाधा न हो तो इनका भी उल्लेख करें ।
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="sahmatiSambandhiTeep"
                                                        value={employee[0].levelDetails[0].data.sahmatiSambandhiTeep} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.sahmatiSambandhiTeep && (
                                                        <small className="text-danger">
                                                            {errors.sahmatiSambandhiTeep}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label> <strong>2 . किये गये कार्य की गुणवत्ता -
                                                    </strong> <br /> कृपया अधिकारी द्वारा किये गये
                                                        कार्य की गुणवत्ता, स्तर और
                                                        कार्यक्रम का उद्देश्य और बाधाएं
                                                        यदि कोई हो, के संबंध में टीप दें !</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKiGunwatta"
                                                        value={employee[0].levelDetails[0].data.karyaKiGunwatta} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaKiGunwatta && (
                                                        <small className="text-danger">
                                                            {errors.karyaKiGunwatta}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-3">
                                                <Col md="6">
                                                    <Label> <strong>3 .कार्य क्षेत्र का नाम- </strong> <br />कृपया विशिष्ट रूप से इनमें
                                                        से प्रत्येक पर टीप दें, कार्यों के
                                                        ज्ञान कांस्तर संबंधित अनुदेश और
                                                        उनका लागू किया जाना ।</Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaChhetraKaNaam"
                                                        value={employee[0].levelDetails[0].data.karyaChhetraKaNaam} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaChhetraKaNaam && (
                                                        <small className="text-danger">
                                                            {errors.karyaChhetraKaNaam}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <h3><strong>(ब) विशेष -</strong></h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col md="6">
                                                    <Label> <strong>1.  कार्य के प्रति दृष्टिकोण
                                                    </strong> <br /> अधिकारी द्वारा किस हद तक
                                                        कार्य समर्पण, प्रेरणा, उसकी
                                                        इच्छा और पहल कर व्यवस्थित
                                                        रूप से किया गया पर टीप दें,
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="karyaKePratiDrishtikon"
                                                        value={employee[0].levelDetails[0].data.karyaKePratiDrishtikon} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.karyaKePratiDrishtikon && (
                                                        <small className="text-danger">
                                                            {errors.karyaKePratiDrishtikon}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label> <strong> 2.निर्णय लेने की योग्यता  <br />
                                                    </strong>निर्णय लेने के गुण,
                                                        पक्ष-विपक्ष को देखते हुए <br />
                                                        वैकल्पिक योग्यता पर टीप दें-
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="nirnayLeneKiYogyta"
                                                        value={employee[0].levelDetails[0].data.nirnayLeneKiYogyta} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.nirnayLeneKiYogyta && (
                                                        <small className="text-danger">
                                                            {errors.nirnayLeneKiYogyta}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-3">
                                                <Col md="6">
                                                    <Label> <strong>3  पहल <br />
                                                    </strong>अधिकारी की अप्रत्यक्ष परिस्थितयों
                                                        से निपटने की क्षमता और
                                                        उपाय और कार्य के नवीन
                                                        क्षेत्रों में स्वेच्छा से अतिरिक्त
                                                        उत्तरदायित्व लेने के संबंध में टीप.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="pahal"
                                                        value={employee[0].levelDetails[0].data.pahal} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.pahal && (
                                                        <small className="text-danger">
                                                            {errors.pahal}
                                                        </small>
                                                    )}
                                                </Col>
                                                <Col md="6">
                                                    <Label> <strong>4. प्रोत्साहन और प्रेरणा की योग्यता <br />
                                                    </strong>कृपया अधिकारी की प्रेरणा देने,
                                                        स्वयं के आचरण और विश्वास से
                                                        सहयोग प्राप्त करने की क्षमता पर टीप दें.

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="prenaKiYogyta"
                                                        value={employee[0].levelDetails[0].data.prenaKiYogyta} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.prenaKiYogyta && (
                                                        <small className="text-danger">
                                                            {errors.prenaKiYogyta}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-3">
                                                <Col md="6">
                                                    <Label> <strong>5. संसूचना कौशल (लिखित और मौखिक)
                                                    </strong><br /> अधिकारी संसुचना एवं तर्क प्रस्तुत .
                                                        करने की योग्यता के संबंध में टीप.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="sansuchnaKaushal"
                                                        value={employee[0].levelDetails[0].data.sansuchnaKaushal} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.sansuchnaKaushal && (
                                                        <small className="text-danger">
                                                            {errors.sansuchnaKaushal}
                                                        </small>
                                                    )}
                                                </Col>

                                                <Col md="6">
                                                    <Label> <strong>6. व्यक्तिगत संबंध एवं समूह कार्य (टीम वर्क)
                                                    </strong> <br /> उच्च अधिकारियों, सहयोगियों एवं
                                                        अघनस्थों से संबंध दूसरों के विचारों
                                                        का सराहना एवं सदभावना से ली
                                                        गयी सलाह की योग्यता का उल्लेख
                                                        करें, कृपया टीम के सदस्य के रूप
                                                        में कार्यक्षमता और टीम भावना को
                                                        बढ़ाने और टीम द्वारा किये गये
                                                        कार्य की उत्तरमता पर भी टीप दें ।
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="vyaktiGatSambandh"
                                                        value={employee[0].levelDetails[0].data.vyaktiGatSambandh} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.vyaktiGatSambandh && (
                                                        <small className="text-danger">
                                                            {errors.vyaktiGatSambandh}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-1">
                                                <Col md="6">
                                                    <Label> <strong>7. आम जनता के साथ संबंध <br />
                                                    </strong> अधिकारी की आम जनता
                                                        तक पहुंच और उनका
                                                        आवश्यकताओं के प्रति संवेदनशीलता.

                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="aamJantaSeSambandh"
                                                        value={employee[0].levelDetails[0].data.aamJantaSeSambandh} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.aamJantaSeSambandh && (
                                                        <small className="text-danger">
                                                            {errors.aamJantaSeSambandh}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-4">
                                                <Col>
                                                    <h3><strong>(स)अतिरिक्त गुण (विशेषताएँ) -</strong></h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-3">
                                                <Col md="6">
                                                    <Label> <strong>1. योजना बनाने की योग्यता <br />
                                                    </strong>क्या अधिकारी में समस्याओं,
                                                        कार्य की आवश्यकताओं का पूर्व
                                                        अनुमान लगाकर तदनुसार योजना बनाना
                                                        और संभावित व्यय उपलब्ध कराने की - योग्यता है ।
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="yojnaBananeYogyta"
                                                        value={employee[0].levelDetails[0].data.yojnaBananeYogyta} // Use employee data here
                                                        className="form-control"

                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.yojnaBananeYogyta && (
                                                        <small className="text-danger">
                                                            {errors.yojnaBananeYogyta}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row>
                                                <Col>
                                                    <Label><strong>2.</strong> निरीक्षण की योग्यता
                                                    </Label>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 ml-4">
                                                <Col md="3">
                                                    <Label>
                                                        <Input
                                                            type="checkbox"
                                                            name="samuchitBatwara"
                                                            value={employee[0].levelDetails[0].data.samuchitBatwara}
                                                            disabled
                                                            readOnly
                                                            checked={employee[0].levelDetails[0].data.samuchitBatwara === true}
                                                            onChange={handleCheckboxChange}
                                                        />
                                                        <strong>1.</strong> कार्य की समूचित बंटवारा
                                                    </Label>
                                                </Col>
                                                <Col md="4">
                                                    <Label>
                                                        <Input
                                                            type="checkbox"
                                                            name="kamiyoKaChunav"
                                                            value={employee[0].levelDetails[0].data.kamiyoKaChunav}
                                                            disabled
                                                            checked={employee[0].levelDetails[0].data.kamiyoKaChunav === true}
                                                            onChange={handleCheckboxChange}
                                                        />
                                                        <strong>2.</strong> कार्य करवाने के लिये उचित कर्मियों का चुनाव
                                                    </Label>
                                                </Col>
                                                <Col md="3">
                                                    <Label>
                                                        <Input
                                                            type="checkbox"
                                                            name="margDarshan"
                                                            value={employee[0].levelDetails[0].data.margDarshan}
                                                            checked={employee[0].levelDetails[0].data.margDarshan === true}
                                                            disabled
                                                            onChange={handleCheckboxChange}
                                                        />
                                                        <strong>3.</strong> कार्य करने में मार्गदर्शन
                                                    </Label>
                                                </Col>
                                                <Col md="2">
                                                    <Label>
                                                        <Input
                                                            type="checkbox"
                                                            name="karyaKiSamiksha"
                                                            disabled
                                                            value={employee[0].levelDetails[0].data.karyaKiSamiksha}
                                                            checked={employee[0].levelDetails[0].data.karyaKiSamiksha === true}
                                                            onChange={handleCheckboxChange}
                                                        />
                                                        <strong>4.</strong> कार्य की समीक्षा
                                                    </Label>
                                                </Col>
                                            </Row>
                                            <hr />
                                            <Row className="pl-2 pr-4">
                                                <Col>
                                                    <h3><i> <u>भाग-चार (सामान्य)</u></i></h3>
                                                </Col>
                                            </Row>
                                            <Row className="mb-3 mt-2">
                                                <Col md="6">
                                                    <Label> <strong></strong>निष्ठा
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="nishtha"
                                                        value={employee[0].levelDetails[0].data.nishtha} // Use employee data here
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.nishtha && (
                                                        <small className="text-danger">
                                                            {errors.nishtha}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="3">
                                                    <Label> <strong></strong>श्रेणी
                                                    </Label>
                                                    <Input
                                                        type="select"
                                                        id="ratingSelect"
                                                        name="shreni"
                                                        value={employee[0].levelDetails[0].data.shreni}
                                                        className="form-control"
                                                        readOnly
                                                        onChange={handleInputChange}

                                                        required
                                                    >
                                                        <option value="">विकल्प चुने </option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="औसत">औसत</option>
                                                        <option value="औसत से कम">औसत से कम</option>

                                                    </Input>
                                                    {errors.shreni && (
                                                        <small className="text-danger">
                                                            {errors.shreni}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="12" >
                                                    <Label><strong>
                                                    </strong>उत्कृष्ट श्रेणीकरण तक न
                                                        किया जाये जब तक कि
                                                        अपवाद स्वरूप गुण और
                                                        कार्य संपादन न देखा गया हो,
                                                        ऐसी श्रेणी का आधार भी
                                                        स्पष्ट बताया जाना चाहिए.
                                                    </Label>
                                                    <Input
                                                        type="textarea"
                                                        name="remark"
                                                        value={employee[0].levelDetails[0].data.remark} // Use employee data here
                                                        className="form-control"
                                                        style={{ height: "100px" }}
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.remark && (
                                                        <small className="text-danger">
                                                            {errors.remark}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Row className="pl-4 pr-4">
                                        <Col>
                                            <Row className="mb-3">
                                                <Col>
                                                    <h2 style={{ textAlign: "center" }}>
                                                        समीक्षक अधिकारी की टिप्पणी
                                                    </h2>
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="3">
                                                    <Label>श्रेणीकरण</Label>
                                                    <Input
                                                        name="shreniKaran1"
                                                        type="select"
                                                        id="recordsPerPage"
                                                        value={employee[0].levelDetails[1].data.shreniKaran1}
                                                        readOnly
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="">चुनें</option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="साधारण">साधारण</option>
                                                        <option value="घटिया">घटिया</option>
                                                    </Input>
                                                    {errors.shreniKaran1 && (
                                                        <small className="text-danger">
                                                            {errors.shreniKaran1}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="12">
                                                    <Label>रिमार्क</Label>
                                                    <Input
                                                        name="remark1"
                                                        readOnly
                                                        type="textarea"
                                                        style={{ height: "100px" }}

                                                        value={employee[0].levelDetails[1].data.remark1}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.remark1 && (
                                                        <small className="text-danger">{errors.remark1}</small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <hr />
                                            <Row className="mb-3">
                                                <Col>
                                                    <h2 style={{ textAlign: "center" }}>
                                                        <u>स्वीकृतकर्ता अधिकारी की टिप्पणी</u>
                                                    </h2>
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="3">
                                                    <Label>श्रेणीकरण</Label>
                                                    <Input
                                                        name="shreniKaran2"
                                                        type="select"
                                                        id="recordsPerPage"
                                                        value={formData.shreniKaran2}
                                                        onChange={handleInputChange}
                                                    >
                                                        <option value="">चुनें</option>
                                                        <option value="उत्कृष्ट">उत्कृष्ट</option>
                                                        <option value="बहुत अच्छा">बहुत अच्छा</option>
                                                        <option value="अच्छा">अच्छा</option>
                                                        <option value="साधारण">साधारण</option>
                                                        <option value="घटिया">घटिया</option>
                                                    </Input>
                                                    {errors.shreniKaran2 && (
                                                        <small className="text-danger">
                                                            {errors.shreniKaran2}
                                                        </small>
                                                    )}
                                                </Col>
                                            </Row>
                                            <Row className="mb-4">
                                                <Col md="12">
                                                    <Label>रिमार्क</Label>
                                                    <Input
                                                        name="remark2"
                                                        type="textarea"
                                                        value={formData.remark2}
                                                        style={{ height: "100px" }}
                                                        maxLength={500}
                                                        onChange={handleInputChange}
                                                    />
                                                    {errors.remark2 && (
                                                        <small className="text-danger">{errors.remark2}</small>
                                                    )}
                                                </Col>
                                            </Row>
                                        </Col>
                                    </Row>
                                    <Button color="success" onClick={handleFinalSubmit}>
                                        Submit
                                    </Button>
                                </div>
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};


ACRformForRegistrarPart4.propTypes = {
    employee: PropTypes.shape({
        _id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        designationDetails: PropTypes.shape({
            designation: PropTypes.string.isRequired,
        }).isRequired,
        collegeDetails: PropTypes.shape({
            name: PropTypes.string.isRequired,
            _id: PropTypes.string.isRequired,
        }).isRequired,
        currentSalary: PropTypes.number.isRequired,
    }).isRequired,
};

export default ACRformForRegistrarPart4;
