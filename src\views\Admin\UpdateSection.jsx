import { useState, useEffect } from "react";
import {
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  Button,
  Container,
  Row,
  Col,
  Label,
} from "reactstrap";
import { useParams, useNavigate } from "react-router-dom";
import axios from "axios";
import Swal from "sweetalert2";
import Header from "../../components/Headers/Header.jsx";

const UpdateSection = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const [directors, setDirectors] = useState([]);

  const [data, setData] = useState([]);
  const [formData, setFormData] = useState({
    sectionName: "",
    office: "",
    sectionOfficer: '',
    isVarified: false,
  });

  useEffect(() => {
    const fetchSection = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/section/getSingle/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setFormData(response.data);
        } else {
          alert("Failed to fetch designation details.");
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        alert("An error occurred. Please try again later.");
      }
    };
    fetchSection();
  }, [endPoint,token,id]);

  useEffect(() => {
    if (data && data.class) {
      setFormData({
        sectionName: String(data.sectionName),
        office: String(data.office),
        sectionOfficer: String(data.sectionOfficer),
      });
    }
  }, [data]);

  useEffect(() => {
    const fetchDirector = async () => {
        try {
            const response = await axios.get(`${endPoint}/api/director/get-all-director`, {
                headers: {
                    'Content-Type': 'application/json',
                    'web-url': window.location.href,
                    "Authorization": `Bearer ${token}`
                }
            });
            if (response.status === 200) {
                //   // console.log(response.data);
                const data = response.data;
                const verifiedDirectors = data.filter((director) => director.verified === true)
                setDirectors(verifiedDirectors);


            } else {
                alert("Failed to director  data. Please try again.");
            }
        } catch (error) {
            console.error("An error occurred while fetching the data:", error);
            alert("An error occurred. Please try again later.");
        }
    };
    // Call the function
    fetchDirector();
    // Optionally add dependencies in the dependency array
}, [endPoint, token]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  const handleSubmit = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);
    
    e.preventDefault();
    Swal.fire({
      title: "Are you sure?",
      text: "Please verify the details. If verification is not completed, the data will not be saved.",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Verify",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        setFormData((prevData) => ({ ...prevData, isVerified: true }));
        try {
          const response = await axios.put(
            `${endPoint}/api/section/update/${id}`,
            { ...formData },
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 200) {
            Swal.fire(
              "Success!",
              "Section updated successfully!",
              "success"
            );
            navigate("/admin/list-section");
          } else {
            Swal.fire("Error", "Failed to update Section.", "error");
          }
        } catch (error) {
          console.error("An error occurred while updating the data:", error);
          Swal.fire(
            "Error",
            "An error occurred. Please try again later.",
            "error"
          );
        }
      } else {
        Swal.fire("Cancelled", "Verification was not completed.", "info");
      }
    });
  };


  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="bg-secondary shadow">
              <CardHeader className="bg-white border-0">
                <Row className="align-items-center">
                  <Col xs="8">
                    <h3 className="mb-0">
                      Update Section{" "}
                      <span style={{ color: "red", fontSize: "12px" }}>
                        ( Last Updated{" "}
                        {new Date(data.updatedAt).toLocaleDateString()} )
                      </span>
                    </h3>
                  </Col>
                </Row>
              </CardHeader>
              <CardBody>
                <Form >
                  <div className="pl-lg-4">
                    <Row>

                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-designation">Section Name</Label>
                          <Input
                            type="text"
                            name="sectionName"
                            id="input-sectionName"
                            placeholder="Section Name"
                            value={formData.sectionName}
                            onChange={handleInputChange}
                            required
                          />
                        </FormGroup>
                      </Col>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-class">Office</Label>
                          <Input
                            type="select"
                            name="office"
                            id="input-office"
                            value={formData.office}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select Office</option>
                            <option value="Directorate">Directorate</option>
                            <option value="Institute">Institute</option>
                          </Input>
                        </FormGroup>
                      </Col>
                    </Row>
                    <Row>
                      <Col lg="6">
                        <FormGroup>
                          <Label htmlFor="input-sectionOfficer"><div className="d-flex" >Section Officer <h3 className="text-danger ml-2"  >
                            *
                          </h3></div> </Label>
                          <Input
                            type="select"
                            name="sectionOfficer"
                            id="input-sectionOfficer"
                            value={formData.sectionOfficer}
                            onChange={handleInputChange}
                            required
                          >
                            <option value="">Select Section Officer</option>
                            {directors &&
                              directors.length > 0 &&
                              directors.map((type, index) => (
                                <option key={index} value={type._id}>
                                  {type.name}
                                </option>
                              ))}
                          </Input>
                        </FormGroup>
                      </Col>
                    </Row>
                    <Button color="primary" onClick={handleSubmit}>
                      Update
                    </Button>
                  </div>
                </Form>
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default UpdateSection;
