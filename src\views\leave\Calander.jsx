import { useState, useEffect } from 'react';
import { Calendar, momentLocalizer } from 'react-big-calendar';
import moment from 'moment';
import "../../../node_modules/react-big-calendar/lib/css/react-big-calendar.css";
import Swal from 'sweetalert2'; // Import SweetAlert2
import { Modal, ModalHeader, ModalBody, ModalFooter, Button, Form, FormGroup, Label, Col,Input } from 'reactstrap';
// import 'bootstrap/dist/css/bootstrap.min.css';
import axios from 'axios';




moment.updateLocale('en', {
    week: {
        dow: 1,
    },
});

const localizer = momentLocalizer(moment);

const MyCalendar = () => {
    const token = sessionStorage.getItem('authToken');
    const endPoint = import.meta.env.VITE_API_URL;
    const empId = sessionStorage.getItem('id')
    const userType = sessionStorage.getItem('type')

    const [holiday, setHoliday] = useState([]);
    const [restrictedHoliday, setRestrictedHoliday] = useState([])
    const [applications, setApplications] = useState([]);
    const [totalHolidays, setTotalHolidays] = useState(0);

    useEffect(() => {
        const fetchHoliday = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/holiday/get-all-holiday`,
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                if (response.status === 200) {
                    const govtHoliday = response.data.filter(item => item.type === 'Govt')
                    const restrictedHoliday = response.data.filter(item => item.type === 'Restricted')
                    setHoliday(govtHoliday);
                    setRestrictedHoliday(restrictedHoliday);


                } else {
                    alert("Data Not Fetched.");
                }
            } catch (error) {
                console.error("An error occurred while Getting Data:", error);
                alert("An error occurred. Please try again later.");
            }
        }
        fetchHoliday();
    }, [token, endPoint]);

    useEffect(() => {
        if (userType !== '1') {
            const fetchApplications = async () => {
                try {
                    const response = await axios.get(`${endPoint}/api/leave/applied_Leaves/${empId}`, {
                        headers: {
                            'Content-Type': 'application/json',
                            'web-url': window.location.href,
                            "Authorization": `Bearer ${token}`
                        }
                    });
                    if (response.status === 200) {
                        const data = response.data;
                        const filterdData = data.filter(item => item.leaveStatus === 3);
                        setApplications(filterdData);
                        // console.log(filterdData, "Getting Leave in Calander");

                    } else {
                        alert("Data Not Fetched.");
                    }
                } catch (error) {
                    console.error("An error occurred while Getting Data:", error);
                    alert("An error occurred. Please try again later.");
                }
            };
            fetchApplications();
        }
    }, [endPoint, empId, token, userType]);




    const [events, setEvents] = useState([

    ]);

    const [showModal, setShowModal] = useState(false);
    const [newEvent, setNewEvent] = useState({ title: '', start: null, end: null });




    const handleSelectEvent = (event) => {
        Swal.fire(`${event.title}`);
    };

    // const handleSelectSlot = ({ start }) => {
    //     setNewEvent({ title: '', start, end: start });
    //     setShowModal(true);
    // };

    const handleAddEvent = () => {
        if (newEvent.title) {
            setEvents([...events, { ...newEvent }]);
            setShowModal(false);
        } else {
            alert('Please enter an event title');
        }
    };

    const handleInputChange = (e) => {
        const { name, value } = e.target;
        setNewEvent({ ...newEvent, [name]: value });
    };

    const dayPropGetter = (date) => {
        const day = date.getDay();
        const isWeekend = day === 0 || day === 6;

        // Check for holidays
        const redDate = holiday.find((redDate) => {
            const holidayDate = new Date(redDate.date); // Convert string to Date object
            return (
                holidayDate.getDate() === date.getDate() &&
                holidayDate.getMonth() === date.getMonth() &&
                holidayDate.getFullYear() === date.getFullYear()
            );
        });

        // Check for restricted holidays
        const greenDate = restrictedHoliday.find((greenDate) => {
            const holidayDate = new Date(greenDate.date); // Convert string to Date object
            return (
                holidayDate.getDate() === date.getDate() &&
                holidayDate.getMonth() === date.getMonth() &&
                holidayDate.getFullYear() === date.getFullYear()
            );
        });

        // Check for leave dates
        const leaveDate = applications.find((leave) => {
            const leaveFromDate = new Date(leave.fromDate);
            const leaveTillDate = new Date(leave.tillDate);
            leaveFromDate.setHours(0, 0, 0, 0);
            leaveTillDate.setHours(23, 59, 59, 999);
            return date >= leaveFromDate && date <= leaveTillDate;
        });


        if (leaveDate) {
            return {
                style: {
                    backgroundColor: 'purple',
                    color: 'white',
                    cursor: 'pointer',
                },
                title: leaveDate.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : leaveDate.leaveType,
            };
        }


        if (isWeekend || redDate) {
            return {
                style: {
                    backgroundColor: 'red',
                    color: 'white',
                    cursor: 'pointer',
                },
                title: redDate ? redDate.name : 'Weekend',
            };
        } else if (greenDate) {
            return {
                style: {
                    backgroundColor: 'lightgreen',
                    color: 'black',
                    cursor: 'pointer',
                },
                title: greenDate.name,
            };
        }

        return {};
    };


    const leaveEvents = applications.map((leaveDate) => ({
        title: leaveDate.leaveType === 'Restricted Holiday' ? 'Optional Holiday' : leaveDate.leaveType,
        start: leaveDate.fromDate,
        end: leaveDate.tillDate,
        allDay: true,
    }));

    const holidayEvents = holiday.map((redDate) => ({
        title: redDate.name,
        start: redDate.date,
        end: redDate.date,
        allDay: true,
    }));

    const restrictedHolidayEvents = restrictedHoliday.map((greenDate) => ({
        title: greenDate.name,
        start: greenDate.date,
        end: greenDate.date,
        allDay: true,
    }));


     const getTotalHolidays = (startDate, endDate, holidays) => {
        let totalHolidays = 0;
        const start = moment(startDate);
        const end = moment(endDate);
        const workType = "NONTEACHING"

        while (start <= end) {
            const isSunday = start.day() === 0; // Check if the day is Sunday (0 = Sunday)
            const isSaturday = start.day() === 6; // Check if the day is Saturday (6 = Saturday)
            const isHoliday = holidays.some((holiday) => {
                const holidayDate = moment(holiday.date); // Parse holiday date
                return holidayDate.isSame(start, 'day'); // Check if holiday matches the current date
            });

            if (workType === 'TEACHING') {
                if (isSunday || isHoliday) {
                    totalHolidays += 1;
                }
            } else if (workType === 'NONTEACHING') {
                if (isSunday || isSaturday || isHoliday) {
                    totalHolidays += 1;
                }
            }

            start.add(1, 'day'); // Move to the next day
        }

        return totalHolidays;
    };

    const calculateHolidays = () => {
        const startDate = new Date(2025, 2, 7); // Example start date
        const endDate = new Date(2025, 2, 17); // Example end date
        const total = getTotalHolidays(startDate, endDate, holiday);
        setTotalHolidays(total);
        // console.log(`Total holidays between ${startDate.toLocaleDateString()} and ${endDate.toLocaleDateString()}: ${total}`);
    };

    // calculateHolidays();

    return (
        <div style={{ padding: '20px', textAlign: 'center' }}>
            <Calendar
                localizer={localizer}
                events={[...events, ...holidayEvents, ...restrictedHolidayEvents, ...leaveEvents]}
                startAccessor="start"
                endAccessor="end"
                style={{ height: 500, margin: '0 auto' }}
                onSelectEvent={handleSelectEvent}
                selectable
                views={['month', 'week', 'day']}
                defaultView="month"
                popup
                dayPropGetter={dayPropGetter}
            />

            <Modal isOpen={showModal} toggle={() => setShowModal(false)}>
                <ModalHeader toggle={() => setShowModal(false)}>Add New Event</ModalHeader>
                <ModalBody>
                    <Form>
                        <FormGroup>
                            <Label for="eventTitle">Event Title</Label>
                            <Input
                                type="text"
                                name="title"
                                id="eventTitle"
                                placeholder="Enter event title"
                                value={newEvent.title}
                                onChange={handleInputChange}
                            />
                        </FormGroup>
                        <FormGroup>
                            <Label for="eventStart">Start Date</Label>
                            <Input
                                type="text"
                                id="eventStart"
                                readOnly
                                value={moment(newEvent.start).format('MMMM Do YYYY')}
                            />
                        </FormGroup>
                    </Form>
                </ModalBody>
                <ModalFooter>
                    <Button color="secondary" onClick={() => setShowModal(false)}>
                        Close
                    </Button>
                    <Button color="primary" onClick={handleAddEvent}>
                        Add Event
                    </Button>
                </ModalFooter>
            </Modal>
        </div>
    );
};
export default MyCalendar;
