import React, { useState, useEffect, useRef } from "react";
import {
  Navbar,
  Nav,
  Container,
  Carousel,
  Card,
  Button,
  Form,
  FormControl,
  FormGroup,
  Badge,
  Row,
  Col,
  InputGroup,
  Toast,
  Modal,
  Alert,
} from "react-bootstrap";
import "../homepage/PageLayout.css"; // Import the external CSS file
import "bootstrap/dist/css/bootstrap.min.css";
import { Swiper, SwiperSlide } from "swiper/react"; // Swiper imports
import "swiper/swiper-bundle.css"; // Correct Swiper CSS import
import LogoSlider from "../homepage/LogoSlider";
import Testimonial from "../homepage/Testimonials";
import LeftImage from "../homepage/images/images/img1.jpg";
import indravatiImg from "../homepage/images/images/ind2.jpg";
import bannhp from "../homepage/images/images/bannerhp.jpeg";
import banner2 from "../homepage/images/images/banner2.jpeg";
import cglogo from '../homepage/images/images/cglg.png';
import "../homepage/toggle.css";
import "../homepage/customHome.css";
import "../homepage/header.css";
import "../homepage/marquee.css";
import "../homepage/custom.css";
import {
  FaInfoCircle,
  FaArrowUp,
  FaHandsHelping,
  FaPager,
  FaUser,
  FaLink,
  FaTrophy,
  FaCalendarAlt
} from "react-icons/fa";
import { MdAssignment } from "react-icons/md";
import { motion } from "framer-motion";
import { Link } from "react-router-dom";
import { useNavigate } from "react-router-dom";
import Help from "../Help.jsx";
import axios from "axios";
import SwalMessageAlert from "../homepage/sweetAlertMessage.jsx";
import Swal from "sweetalert2";
import "sweetalert2/dist/sweetalert2.min.css";
import GoogleTranslate from "../homepage/GoogleTanslate.jsx";
import { useFontSize } from "../FontSizeContext.jsx";

const HomePage = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [loading, setLoading] = useState(true);
  const [expanded, setExpanded] = useState(false);
  const currentYear = new Date().getFullYear();
  const navigate = useNavigate(); // Navigation ka use karna hai
  const endPoint = import.meta.env.VITE_API_URL;
  const [userType, allUserType] = useState([]);
  const [formData, setFormData] = useState({
    userType: "",
    username: "",
    password: "",
  });

  const [show, setShow] = useState(false); // State to control modal visibility
  const handleShow = () => setShow(true);
  const { increaseFontSize, decreaseFontSize } = useFontSize();
  const handleClose = () => setShow(false);
  const [CaptchaText, setCaptchaText] = useState(""); // Captcha input value
  const [generatedCaptcha, setGeneratedCaptcha] = useState(""); // Captcha display text
  const [captchaImage, setCaptchaImage] = useState(""); // Image data URL
  const [captchaError, setCaptchaError] = useState("");
  const [isSidebarOpen, setSidebarOpen] = useState(false);
  const sidebarRef = useRef(null);
  const [isTranslateEnabled, setIsTranslateEnabled] = useState(false);
  const usernameRef = useRef(null);
  const timeoutRef = useRef(null);
  const targetRowRef = useRef(null);
 
  const links = [
    { title: "Coming soon" },
  ];

  const awards = [
    { title: "Coming soon" },
  ];

  const events = [
    { title: "Coming soon" },
  ];

  useEffect(() => {
    const savedScrollPosition = sessionStorage.getItem("scrollPosition");
    if (savedScrollPosition && targetRowRef.current) {
      setTimeout(() => {
        window.scrollTo({
          top: parseInt(savedScrollPosition, 10),
          behavior: "smooth",
        });
      }, 100); // Delay to allow DOM rendering
    }
  }, []);

  // ✅ Save scroll position before page unload or refresh
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (targetRowRef.current) {
        const scrollPosition = targetRowRef.current.offsetTop;
        sessionStorage.setItem("scrollPosition", scrollPosition);
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
    };
  }, []);



  const handleTranslateClick = () => {
    setIsTranslateEnabled(true);
  };

  const navbarRef = useRef(null);

  const handleNavbarToggle = () => {
    setExpanded((prev) => !prev);
  };
  const [news, setNews] = useState([]);
  useEffect(() => {
    const latestNews = [
      {
        text: "📱✨ Watch Now! How to Log In to Institute and Complete Employee Registration | Step-by-Step Guide",
        url: "https://www.youtube.com/embed/oGXi69J9KK8?si=Df2_TNqG-_VgA6_h"
      },
      {
        text: "🎥📚 Watch Now! FAS Video Tutorial is available to guide you step-by-step.",
        url: "https://www.youtube.com/embed/2Fcn7HERyzA?si=bvFNTDUMP-DYP5QV"
      },

    ];

    setTimeout(() => {
      setNews(latestNews);
    }, 9000);
  }, []);



  const handleClickOutsideNavBar = (event) => {
    if (navbarRef.current && !navbarRef.current.contains(event.target)) {
      setExpanded(false);
    }
  };

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutsideNavBar);
    return () => {
      document.removeEventListener("mousedown", handleClickOutsideNavBar);
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen((prevState) => !prevState);
  };

  const handleClickOutside = (event) => {
    if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {
      setSidebarOpen(false);
    }
  };



  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    const generateCaptcha = () => {
      const captcha = Math.floor(Math.random() * 900000) + 100000;
      setGeneratedCaptcha(captcha.toString());
    };

    generateCaptcha();
    const fetchData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/userType`, {
          headers: { "Content-Type": "application/json",
            'web-url': window.location.href,
           },
        });
        allUserType(response.data.getAllUserType);
      } catch (error) {
        console.error("An error occurred while fetching user types:", error);
      }
    };
    fetchData();
  }, [endPoint]);
  const [placeHolder, setPlaceHolder] = useState("");

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === "userType") {
      switch (value) {
        case "State Admin":
          setPlaceHolder("Enter your Username");
          break;
        case "Employee":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Lead College":
        case "Institute":
          setPlaceHolder("Enter your AISHE Code");
          break;
        case "Director":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Commissioner":
          setPlaceHolder("Enter your Employee Code");
          break;
        default:
          setPlaceHolder("");
      }
      const resetData = {
        userType: value,
        username: "",
        password: "", // Explicitly clear password          
      };

      setFormData(resetData);
      setCaptchaText("");
      setCaptchaError("");
      return;
    }
    resetSessionTimer();
    if (name === "Captcha") {
      setCaptchaText(value);
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };


  const handleInputChange2 = (e) => {


    const { name, value } = e.target;

    setPasswordFormData({ ...PasswordFormData, [name]: value });


    if (name === "userType") {
      switch (value) {
        case "State Admin":
          setPlaceHolder("Enter your Username");
          break;
        case "Employee":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Lead College":
        case "Institute":
          setPlaceHolder("Enter your AISHE Code");
          break;
        case "Director":
          setPlaceHolder("Enter your Employee Code");
          break;
        case "Commissioner":
          setPlaceHolder("Enter your Employee Code");
          break;
        default:
          setPlaceHolder("");
      }

      return;
    }
    resetSessionTimer();


    if (name === "mobile") {
      if (PasswordFormData.userType === "") {
        SwalMessageAlert("Please Select User Type!", "error");
      }
    }
    if (name === "otp") {
      setPasswordFormData((prev) => ({ ...prev, [name]: value }));
    }
    if (name === "newPassword") {
      validatePassword(value);
    }
  };

  useEffect(() => {
    if (performance.navigation.type === 1) {
      // Page was reloaded, clear all fields
      clearFields();
    }
  }, []);
  const clearFields = () => {
    setFormData({
      userType: "",
      username: "",
      password: "",
    });
    setCaptchaText(""); // Clear CAPTCHA if needed
    setCaptchaError("");
    if (document.querySelector("input[name='password']")) {
      document.querySelector("input[name='password']").value = "";
    }
  };

  const handleUserActivity = () => {
    resetSessionTimer();

  };
  const resetSessionTimer = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    timeoutRef.current = setTimeout(() => {
      clearFields();
      handleSessionTimeout();
    }, 900000); // 15 min timeout
  };
  const handleSessionTimeout = () => {
    alert('Session expired. Please log in again.');
    sessionStorage.clear();
    window.location.replace("/login");

  };

  useEffect(() => {
    resetSessionTimer();
    document.addEventListener('mousemove', handleUserActivity);
    document.addEventListener('keydown', handleUserActivity);
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      document.removeEventListener('mousemove', handleUserActivity);
      document.removeEventListener('keydown', handleUserActivity);
    };
  }, []);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic form validation
    if (!formData.userType) {
      SwalMessageAlert("Please select a user type!", "error");
      clearFields();
      return;
    }
    if (!formData.username) {
      SwalMessageAlert("Please enter your User Name!", "error");
      clearFields();
      return;
    }
    if (!formData.password) {
      SwalMessageAlert("Please enter your password!", "error");
      clearFields();
      return;
    }
    if (!CaptchaText) {
      setCaptchaError("CAPTCHA is required!");
      generateCaptchaText();
      return;
    } else if (CaptchaText !== generatedCaptcha) {
      setCaptchaError("Incorrect CAPTCHA, please try again.");
      generateCaptchaText();
      return;
    } else {
      setCaptchaError("");
    }

    try {
      const response = await axios.post(`${endPoint}/api/login`, formData, {
        headers: { "Content-Type": "application/json",
          'web-url': window.location.href,
         },
      });
      if (response.status === 200) {
        const token = response.data.token;
        const id = response.data.id;
        sessionStorage.setItem("id", id);
        sessionStorage.setItem("authToken", token);
        sessionStorage.setItem("name", response.data.username);
        sessionStorage.setItem("userType", response.data.userType);
        sessionStorage.setItem("type", response.data.type);
        sessionStorage.setItem("status", response.data.status);
        sessionStorage.setItem("empId", response.data.empCode);
       sessionStorage.setItem("college", response.data.college);
       sessionStorage.setItem("employeeName", response.data.employeeName);
       sessionStorage.setItem("director", response.data.director);

        // // console.log(response.data.designation, "Getting Designation");
        console.log( response.data.director,"collegename");

        sessionStorage.setItem("designation", response.data.designation);
        sessionStorage.setItem(
          "designationName",
          response.data.designationName
        );


        sessionStorage.setItem("sectionName", response.data.sectionName);
        if (response.data.type === 4) {
          window.location.replace("admin/leave-dashboard");
        } else {
          window.location.replace("admin/dashboard");
        }
        // window.location.reload();
        clearFields();
      } else if (response.status === 201) {
        generateCaptchaText();
        SwalMessageAlert(response.data.msg, "error");
        // clearFields();
      } else {
        generateCaptchaText();
        SwalMessageAlert(response.data.msg, "error");
        window.location.reload();
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      window.location.reload();
    } finally {
      resetSessionTimer();
      clearFields();

    }
  };
  const renderCaptchaImage = (captcha) => {
    const canvas = document.createElement("canvas");
    canvas.width = 150;
    canvas.height = 50;
    const ctx = canvas.getContext("2d");

    const gradient = ctx.createLinearGradient(
      0,
      0,
      canvas.width,
      canvas.height
    );
    gradient.addColorStop(0, "#007BFF");
    gradient.addColorStop(1, "#28A745");
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < 5; i++) {
      ctx.beginPath();
      ctx.arc(
        Math.random() * canvas.width,
        Math.random() * canvas.height,
        Math.random() * 10 + 5, // Random size
        0,
        2 * Math.PI
      );
      ctx.fillStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255
        }, 0.3)`;
      ctx.fill();
    }
    ctx.font = "bold 30px Arial, sans-serif";
    ctx.textAlign = "center";
    ctx.textBaseline = "middle";
    const textX = canvas.width / 2;
    const textY = canvas.height / 2;

    captcha.split("").forEach((char, index) => {
      const charX = textX - (captcha.length / 2 - index) * 20 + 10;
      const charRotation = (Math.random() - 0.5) * 0.4;

      ctx.save();
      ctx.translate(charX, textY);
      ctx.rotate(charRotation);
      ctx.fillStyle = "white";
      ctx.fillText(char, 0, 0);
      ctx.restore();
    });
    for (let i = 0; i < 8; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.lineTo(Math.random() * canvas.width, Math.random() * canvas.height);
      ctx.strokeStyle = `rgba(${Math.random() * 255}, ${Math.random() * 255}, ${Math.random() * 255
        }, 0.4)`;
      ctx.lineWidth = Math.random() * 2 + 1;
      ctx.stroke();
    }
    setCaptchaImage(canvas.toDataURL());
  };
  const generateCaptchaText = () => {
    const chars = "1234567890";
    let captcha = "";
    for (let i = 0; i < 6; i++) {
      captcha += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setGeneratedCaptcha(captcha);
    renderCaptchaImage(captcha);
  };

  useEffect(() => {
    generateCaptchaText(); // Generate CAPTCHA when the component loads
  }, []);

  //Forget Password
  const [PasswordFormData, setPasswordFormData] = useState({
    userType: "",
    username: "",
    otp: "",
    newPassword: "",
    confirmPassword: "",
  });
  const [isOtpVisible, setIsOtpVisible] = useState(false);
  const [isOtpInputVisible, setIsOtpInputVisible] = useState(false);
  const [previewModal, setPreviewModal] = useState(false);
  const togglePreviewModal = () => setPreviewModal(!previewModal);
  const [previewPasswordModal, setPreviewPasswordModal] = useState(false);
  const togglePreviewPasswordModal = () =>
    setPreviewPasswordModal(!previewPasswordModal);
  const handleSubmitOpt = async (e) => {
    e.target.disabled = true;
    setTimeout(() => {
      e.target.disabled = false;
    }, 5000);


    try {
      //alert(PasswordFormData.mobile)
      const response = await axios.get(
        `${endPoint}/api/send-message?username=${PasswordFormData.username}&userType=${PasswordFormData.userType}`,
        {
          headers: { "Content-Type": "application/json",
            'web-url': window.location.href,
           },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        if (data.msg === "OTP sent successfully") {
          SwalMessageAlert(`OTP sent successfully`, "success");
          setIsOtpVisible(true);
          setIsOtpInputVisible(true);
        }
      } else {
        SwalMessageAlert(
          `${response.data.msg}! Please Try After Some Time`,
          "error"
        );
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    }
  };

  const handleCheckOpt = async () => {
    const body = {
      userType: PasswordFormData.userType,
      username: PasswordFormData.username,
      otp: PasswordFormData.otp,
    };
    try {
      const response = await axios.post(`${endPoint}/api/validate-opt`, body, {
        headers: { "Content-Type": "application/json",
          'web-url': window.location.href,
         },
      });
      if (response.status === 200) {
        //const data = response.data;
        setPreviewPasswordModal(true);
        setPreviewModal(false);
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      setTimeout(() => {
        window.location.reload();
      }, 5000);
    }
  };

  const handleSubmitPassword = async () => {
    const allValid = Object.values(validationRules).every((rule) => rule);

    if (!allValid) {
      SwalMessageAlert("Password does not meet the requirements.", "error");
      return;
    }

    if (PasswordFormData.newPassword !== PasswordFormData.confirmPassword) {
      SwalMessageAlert("Passwords do not match.", "error");
      return;
    }
    const body = {
      userType: PasswordFormData.userType,
      username: PasswordFormData.username,
      password: PasswordFormData.newPassword,
      confirmPassword: PasswordFormData.confirmPassword,
      otp: PasswordFormData.otp,
    };
    try {
      const response = await axios.put(
        `${endPoint}/api/forget-password`,
        body,
        {
          headers: { "Content-Type": "application/json",
            'web-url': window.location.href,
           },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        SwalMessageAlert(data.msg, "success");
        setPreviewPasswordModal(false);
        setTimeout(() => {
          window.location.reload();
        }, 5000);
      } else {
        SwalMessageAlert(`An error occurred. ${response.data.msg}`, "error");
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      SwalMessageAlert(`An error occurred. ${error}`, "error");
      // setTimeout(() => {
      //   window.location.reload();
      // }, 5000);
    }
  };

  const [showPassword, setShowPassword] = useState({
    password: false,
    newPassword: false,
    confirmPassword: false,
  });

  const toggleShowPassword = (field) => {
    setShowPassword((prevState) => ({
      ...prevState,
      [field]: !prevState[field],
    }));
  };
  const [validationRules, setValidationRules] = useState({
    minLength: false,
    uppercase: false,
    lowercase: false,
    number: false,
    specialChar: false,
  });
  const validatePassword = (password) => {
    const rules = {
      minLength: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      lowercase: /[a-z]/.test(password),
      number: /\d/.test(password),
      specialChar: /[@$!%*?&]/.test(password),
    };

    setValidationRules(rules);
  };

  // Handle loading effect separately
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Scroll event listener
  useEffect(() => {
    const handleScroll = () => {
      setShowScrollTop(window.scrollY > 100);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []); // No need for dependencies here, it just adds/removes an event listener

  // Debugging state updates
  useEffect(() => {

  }, [showScrollTop]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  if (loading) {
    return (
      <div className="loading-overlay">
        <div className="spinner-border text-primary" role="status">
          <span className="visually-hidden"></span>
        </div>
      </div>
    );
  }

  const handleRegisterClick = () => {
    sessionStorage.setItem("reloadOnce", "true");
    navigate("/register", { replace: true });
  };


  // Function to handle Proceed action
  const proceedAction = () => {
    window.open("http://highereducation.cg.gov.in", "_blank"); // Open the link
    handleClose(); // Close modal after proceeding
  };



  const toggleLanguage = () => {
    setTimeout(() => {
      const select = document.querySelector(".goog-te-combo");
      if (select) {
        select.value = select.value === "hi" ? "en" : "hi";
        select.dispatchEvent(new Event("change"));
      }
    }, 500);
  };
  return (
    <>


      <Row className="d-flex p-2" style={{ backgroundColor: "rgb(237, 246, 247)" }}>

        <Col md={10} style={{
          display: "flex",
          color: "blue",
          textAlign: "center",
          justifyContent: "left"
        }}>
          <img src="/logo.png" alt="Government Logo" className="logo" />
          <span className="text-center" style={{ marginTop: "30px" }} > <strong className="text-center" style={{ fontSize: "30px" }}>
            उच्च शिक्षा विभाग  </strong> <br></br>
            <strong className="text-warning"> छत्तीसगढ़ शासन</strong></span>



        </Col>
        <Col md={2}>
          <div>
            {/* <button className="font-btn m-1" onClick={increaseFontSize}>A+</button>
            <button className="font-btn m-1" onClick={decreaseFontSize}>A-</button> */}

            {!isTranslateEnabled ? (
              <button onClick={handleTranslateClick} >Translate</button>
            ) : (
              <div>
                <select onChange={toggleLanguage}>
                  <option value="fr">choose</option>
                  <option value="hi">Hindi</option>
                  <option value="en">English</option>
                </select>
                <GoogleTranslate />
              </div>
            )}
          </div>

        </Col>
      </Row>


      <Navbar
        style={{ backgroundColor: "#0056b3", zIndex: 1000, height: "40px" }}
        expand="md"
        className=""
        variant="dark"
        expanded={expanded}
        onToggle={handleNavbarToggle}
        ref={navbarRef}
      >
        <Container>
          {/* Logo & Branding */}
          <Navbar.Brand
            href="#home"
            style={{ display: "flex", alignItems: "left" }}
          >
            {/* <img
              src={cglogo}
              alt="Logo"
              style={{ height: "90px", marginRight: "10px" ,marginTop:"5px"}}
            /> */}
            {/* <div
              style={{
                display: "flex",
                flexDirection: "column",
                color: "white",
              }}
            >
              <span style={{ fontSize: "20px", fontWeight: "bold" }}>
                उच्च शिक्षा विभाग
              </span>
              <span style={{ fontSize: "15px", fontWeight: "bold" }}>
                Department of Higher Education
              </span>
            </div> */}
          </Navbar.Brand>

          {/* Toggle Button for Mobile */}
          <Navbar.Toggle aria-controls="basic-navbar-nav" />

          {/* Collapsible Nav Links */}
          <Navbar.Collapse id="basic-navbar-nav">

            <Nav className="ms-auto navbar-links text-left">
              <Nav.Link as={Link} to="/privacy-policy">
                <MdAssignment /> Terms & Condition
              </Nav.Link>
              <Nav.Link as={Link} to="/register" onClick={handleRegisterClick}>
                <FaInfoCircle /> Register
              </Nav.Link>
              <Nav.Link onClick={toggleSidebar}>
                <FaHandsHelping /> Help
              </Nav.Link>
              <Nav.Link as={Link} to="/attendance-node">
                <FaPager /> Attendance
              </Nav.Link>
              {/* <Nav.Link as={Link} to="/attendance-node">
                <FaPager /> Node Attendance
              </Nav.Link> */}
            </Nav>
          </Navbar.Collapse>
        </Container>
      </Navbar>

      <Carousel
        // interval={3000}
        pause="hover"
        indicators={false}
        controls={false}
        style={{ backgroundColor: "#003d82" }}
      >
        <Carousel.Item>
          <div
            style={{
              // marginTop: "80px" /* Adjust to avoid navbar overlap */,
              width: "100vw" /* Ensure full viewport width */,
              textAlign: "center" /* Center the image */,
            }}
          >
            <img
              src={bannhp}
              alt="Banner 1"
              className="d-block w-100 img-fluid"
              style={{ width: "100%", height: "auto" }}
            />
          </div>
        </Carousel.Item>

      </Carousel>
      <Container fluid className="news-marquee-container m-0">
        {news.length > 0 && (
          <>
            <div className="marquee">
              <div className="marquee-content">


                {news.map((item, index) => (

                  <a
                    key={index}
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    style={{
                      textDecoration: "none",
                      color: "white",
                      marginRight: "50px",
                      fontWeight: "bold"
                    }}
                  >
                    {item.text}
                  </a>

                ))}


                {/* {news.map((item, index) => (
              <span key={index} className={`news-item color-${index % 5}`}>
                {item}
              </span>
            ))} */}
              </div>
            </div>
          </>
        )}
      </Container>

      {/* <Container fluid className="px-0"> */}
      <Container>
        <div className="heading-container">

          <h2 className="welcome-heading animate-heading">
            Web Application Portal of Higher Education Department
          </h2>
        </div>




        <Row style={{ backgroundColor: "#f5f5f5" }} ref={targetRowRef}   >
          <Col ></Col>

          <Col md={4} style={{ display: "flex" }} className=" " >
            <motion.div
              initial={{ opacity: 0, y: -50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="flex-grow-1"
            >
              <Card
                className="shadow-lg border gradient-custom-2  "
                style={{ width: "100%", borderRadius: "5px", color: "white" }}
              >
                <Card.Body className="">
                  <div className="text-center ">
                    <FaUser
                      style={{
                        height: "40px",
                        width: "30px",
                        color: "#003ca7",
                        marginTop: "1px",
                      }}
                    />
                  </div>
                  <div className="text-center">
                    <h1
                      className="mt-1 pb-1"
                      style={{ fontWeight: "bold", letterSpacing: "1px" }}
                    >
                      Login
                    </h1>
                    <h4 className="text-center text-secondary">
                      Sign in with credentials
                    </h4>
                  </div>

                  <Form autoComplete="off">
                    {/* User Type Dropdown */}
                    <Form.Group className="mb-3">
                      <Form.Label className="mb-1">User Type</Form.Label>
                      <Form.Control
                        as="select"
                        name="userType"
                        value={formData.userType}
                        className="py-2 px-3"
                        style={{ color: "#404446" }}
                        onChange={handleInputChange}
                      >
                        <option value="" disabled>
                          Select User Type
                        </option>
                        {userType.map((type, index) => (
                          <option key={index} value={type.UserType}>
                            {type.UserType} ({type.UserTypeHin})
                          </option>
                        ))}
                      </Form.Control>
                    </Form.Group>

                    {/* Username Field */}
                    <Form.Group className="mb-3">
                      <Form.Label className="mb-1">Username</Form.Label>
                      <InputGroup>
                        {/* Dummy input to trick the browser */}
                        <input
                          type="text"
                          name="fakeUsername"
                          style={{ display: "none" }}
                          autoComplete="username"
                        />
                        <InputGroup.Text className="border-end-0">
                          <i className="fas fa-user text-muted" />
                        </InputGroup.Text>
                        <Form.Control
                          name="username"
                          placeholder="Enter Username"
                          type="text"
                          className="py-2 px-3 border-start-0"
                          style={{ color: "#404446" }}
                          value={formData.username}
                          ref={usernameRef}
                          onChange={handleInputChange}
                          autoComplete="new-username"
                          readOnly
                          onFocus={(e) => e.target.removeAttribute("readOnly")}
                        />
                      </InputGroup>
                    </Form.Group>

                    {/* <Form.Group className="mb-3">
                      <Form.Label className="mb-1">Username</Form.Label>
                      <InputGroup>
                        <InputGroup.Text className=" border-end-0">
                          <i className="fas fa-user text-muted" />
                        </InputGroup.Text>
                        <Form.Control
                          name="username"
                          placeholder="Enter Username"
                          type="text"
                          className="py-2 px-3 border-start-0"
                          style={{ color: "#404446" }}
                          // value={formData.username}
                          onChange={handleInputChange}
                            autoComplete="off"
                        />
                      </InputGroup>
                    </Form.Group> */}

                    {/* Password Field */}
                    <Form.Group className="mb-3">
                      <Form.Label className="mb-1">Password</Form.Label>

                      <InputGroup>
                        <input
                          type="password"
                          name="fake_password"
                          style={{ display: "none" }}
                          autoComplete="off"
                        />
                        <InputGroup.Text className=" border-end-0">
                          <i className="fas fa-lock text-muted" />
                        </InputGroup.Text>
                        <Form.Control
                          name="password"
                          id="password"
                          placeholder="Enter Password"
                          type={showPassword.password ? "text" : "password"}
                          className="py-2 px-3 border-start-0"
                          style={{
                            color: "#404446",
                          }}
                          autoComplete="new-password"
                          onFocus={(event) => {
                            if (event.target.autocomplete) {
                              event.target.autocomplete = "off";
                            }
                          }}
                          onCopy={(e) => e.preventDefault()}
                          onCut={(e) => e.preventDefault()}
                          onPaste={(e) => e.preventDefault()}
                          onContextMenu={(e) => e.preventDefault()}
                          onChange={handleInputChange}
                          value={formData.password}
                        />
                        <InputGroup.Text
                          onClick={() => toggleShowPassword("password")}
                          style={{ cursor: "pointer" }}
                        >
                          <i
                            className={
                              showPassword.password
                                ? "fas fa-eye-slash"
                                : "fas fa-eye"
                            }
                          />
                        </InputGroup.Text>
                      </InputGroup>
                    </Form.Group>

                    {/* CAPTCHA Section */}
                    <Row className="align-items-center mb-3">
                      <Col xs={4} className="text-center">
                        {captchaImage && (
                          <img
                            src={captchaImage}
                            alt="CAPTCHA"
                            className="img-fluid p-0"
                            style={{ maxWidth: "100px", height: "40px" }}
                          />
                        )}
                      </Col>
                      <Col xs={2} className="text-center">
                        <Button
                          variant="link"
                          onClick={generateCaptchaText}
                          title="Refresh CAPTCHA"
                        >
                          <i className="fas fa-sync-alt text-secondary" />
                        </Button>
                      </Col>

                      <Col xs={6}>
                        <Form.Group>
                          <Form.Control
                            name="Captcha"
                            placeholder="Enter CAPTCHA"
                            type="text"
                            className="py-2 px-3"
                            value={CaptchaText}
                            onChange={(e) => {
                              const inputValue = e.target.value.trim();
                              if (/^\d{0,6}$/.test(inputValue)) {
                                setCaptchaText(inputValue);
                              }
                            }}
                            style={{
                              width: "100%",
                              maxWidth: "600px",
                              color: "#404446",
                              display: "inline-block",
                            }}
                            autoComplete="off"

                          />
                        </Form.Group>
                      </Col>
                      {captchaError && (
                        <small className="text-danger mt-1">
                          {captchaError}
                        </small>
                      )}
                    </Row>

                    {/* Submit Button */}
                    <div className="text-center">
                      <Button
                        className="btn btn-success"
                        variant="success"
                        onClick={handleSubmit}
                        autoComplete="off"
                        style={{ fontWeight: "bold", color: "#404446" }}
                      >
                        Sign in
                      </Button>
                    </div>
                  </Form>

                  <div className="d-flex flex-row align-items-center justify-content-center ">
                    <Button
                      variant="link"
                      className="text-white"
                      onClick={togglePreviewModal}
                      style={{ textDecoration: "none" }}
                    >
                      <strong>Forgot Password?</strong>
                    </Button>
                  </div>
                </Card.Body>
              </Card>
            </motion.div>
          </Col>
          <Col></Col>
        </Row>
        <Row>
          <Col md={4}></Col>
          <Col md={8}>
            <section className="">

              <strong className="text-primary">
                उच्च शिक्षा विभाग की मूल वेबसाइट पर जाने के लिए यहाँ क्लिक करें :{" "}
                <a href="#" onClick={(e) => { e.preventDefault(); handleShow(); }}>
                  Click here
                </a>
              </strong>
              <Modal
                show={show}
                onHide={handleClose}
                aria-labelledby="contained-modal-title-vcenter"
                dialogClassName="custom-modal"
              >
                <Modal.Header >

                  <Modal.Title>Higher-Education Says</Modal.Title>
                  <button
                    type="button"
                    className="btn-close btn-sm "
                    onClick={handleClose}
                    aria-label="Close"
                  >
                    <i className="fas fa-times"></i>
                  </button>
                </Modal.Header>
                <Modal.Body>
                  <span> You are being redirected to an application outside Higher Education</span>
                </Modal.Body>
                <Modal.Footer>
                  <Button variant="success" onClick={proceedAction}>Proceed</Button>
                  <Button variant="danger" onClick={handleClose}>Cancel</Button>
                </Modal.Footer>
              </Modal>

            </section>
          </Col>

        </Row>

        <Modal show={previewModal} onHide={togglePreviewModal} centered>
          <Modal.Header>
            <Modal.Title>Forget Password</Modal.Title>
            <button
              type="button"
              className="btn-close btn-danger btn-sm"
              onClick={togglePreviewModal}
              aria-label="Close"
            >
              <i className="fas fa-times"></i>
            </button>
          </Modal.Header>
          <Modal.Body>
            <Form autoComplete="off">
              <FormGroup className="mb-3">
                <Form.Control
                  as="select"
                  name="userType"
                  value={PasswordFormData.userType}
                  style={{ color: "#404446" }}
                  onChange={handleInputChange2}
                  disabled={isOtpInputVisible}
                >
                  <option value="" disabled>
                    Select User Type
                  </option>
                  {userType?.map((type, index) => (
                    <option key={index} value={type.UserType}>
                      {type.UserType} ({type.UserTypeHin})
                    </option>
                  ))}
                </Form.Control>
              </FormGroup>

              <FormGroup className="mb-3">
                <InputGroup>
                  <InputGroup.Text>
                    <i className="ni ni-single-02" />
                  </InputGroup.Text>
                  <FormControl
                    name="username"
                    placeholder={placeHolder}
                    type="text"
                    value={PasswordFormData.mobile}
                    style={{ color: "#404446" }}
                    onChange={handleInputChange2}
                    disabled={isOtpInputVisible}
                  />
                </InputGroup>
              </FormGroup>

              {isOtpVisible && (
                <FormGroup className="mb-3">
                  <InputGroup>
                    <InputGroup.Text>
                      <i className="ni ni-single-02" />
                    </InputGroup.Text>
                    <FormControl
                      name="otp"
                      placeholder="Enter OTP"
                      type="text"
                      value={PasswordFormData.otp}
                      style={{ color: "#404446" }}
                      autoComplete="off"
                      onChange={handleInputChange2}
                    />
                  </InputGroup>
                </FormGroup>
              )}

              <Row className="text-center">
                {!isOtpVisible && (
                  <Button variant="primary" size="sm" onClick={handleSubmitOpt}>
                    Get OTP
                  </Button>
                )}
                {isOtpVisible && (
                  <>
                    <Button
                      variant="warning"
                      size="sm"
                      className="mx-2"
                      onClick={handleSubmitOpt}
                    >
                      Resend OTP
                    </Button>
                    <Button
                      variant="success"
                      size="sm"
                      onClick={handleCheckOpt}
                    >
                      Submit
                    </Button>
                  </>
                )}
              </Row>
            </Form>
          </Modal.Body>
        </Modal>

        {/* Change Password Modal */}
        <Modal
          show={previewPasswordModal}
          onHide={togglePreviewPasswordModal}
          centered
        >
          <Modal.Header>
            <Modal.Title>Change Password</Modal.Title>
            <button
              type="button"
              className="btn-close btn-sm"
              onClick={togglePreviewPasswordModal}
              aria-label="Close"
            >
              <i className="fas fa-times"></i>
            </button>
          </Modal.Header>
          <Modal.Body>
            <Form>
              <FormGroup className="mb-3">
                <InputGroup>
                  <InputGroup.Text>
                    <i className="ni ni-lock-circle-open" />
                  </InputGroup.Text>
                  <FormControl
                    name="newPassword"
                    placeholder="New Password"
                    type={showPassword.newPassword ? "text" : "password"}
                    value={PasswordFormData.newPassword}
                    style={{ color: "#404446" }}
                    autoComplete="off"
                    onChange={handleInputChange2}
                  />
                  <InputGroup.Text
                    style={{ cursor: "pointer" }}
                    onClick={() => toggleShowPassword("newPassword")}
                  >
                    <i
                      className={
                        showPassword.newPassword
                          ? "fas fa-eye-slash"
                          : "fas fa-eye"
                      }
                    />
                  </InputGroup.Text>
                </InputGroup>
              </FormGroup>

              {/* Password Validation Rules */}
              <ul className="ps-3">
                <li
                  style={{ color: validationRules.minLength ? "green" : "red" }}
                >
                  {validationRules.minLength ? "✔" : "✖"} Minimum 8 characters
                </li>
                <li
                  style={{ color: validationRules.uppercase ? "green" : "red" }}
                >
                  {validationRules.uppercase ? "✔" : "✖"} At least 1 uppercase
                  letter
                </li>
                <li
                  style={{ color: validationRules.lowercase ? "green" : "red" }}
                >
                  {validationRules.lowercase ? "✔" : "✖"} At least 1 lowercase
                  letter
                </li>
                <li style={{ color: validationRules.number ? "green" : "red" }}>
                  {validationRules.number ? "✔" : "✖"} At least 1 number
                </li>
                <li
                  style={{
                    color: validationRules.specialChar ? "green" : "red",
                  }}
                >
                  {validationRules.specialChar ? "✔" : "✖"} At least 1 special
                  character
                </li>
              </ul>

              <FormGroup className="mb-3">
                <InputGroup>
                  <InputGroup.Text>
                    <i className="ni ni-lock-circle-open" />
                  </InputGroup.Text>
                  <FormControl
                    name="confirmPassword"
                    placeholder="Confirm New Password"
                    type={showPassword.confirmPassword ? "text" : "password"}
                    value={PasswordFormData.confirmPassword}
                    style={{ color: "#404446", userSelect: "none" }}
                    autoComplete="new-password"
                    readOnly
                    onFocus={(e) => e.target.removeAttribute("readOnly")}
                    onCopy={(e) => e.preventDefault()}
                    onCut={(e) => e.preventDefault()}
                    onPaste={(e) => e.preventDefault()}
                    onContextMenu={(e) => e.preventDefault()}
                    onChange={handleInputChange2}
                  />
                  <InputGroup.Text
                    style={{ cursor: "pointer" }}
                    onClick={() => toggleShowPassword("confirmPassword")}
                  >
                    <i
                      className={
                        showPassword.confirmPassword
                          ? "fas fa-eye-slash"
                          : "fas fa-eye"
                      }
                    />
                  </InputGroup.Text>
                </InputGroup>
              </FormGroup>

              <Row className="text-center">
                <Button
                  variant="primary"
                  size="sm"
                  onClick={handleSubmitPassword}
                >
                  Submit
                </Button>
              </Row>
            </Form>
          </Modal.Body>
        </Modal>
      </Container>

      <Container>
        <div>
          {/* Include the sidebar and pass the open/close state */}
          <div ref={sidebarRef}>
            <Help isOpen={isSidebarOpen} />

            {/* Add sidebar content here */}
          </div>
        </div>


        <Row>
          <div className="sections-wrapper mt-4">

          </div>

          {showScrollTop && (
            <button
              onClick={scrollToTop}
              className="scroll-to-top"
              aria-label="Scroll to top"
            >
              <FaArrowUp className="arrow-icon" />
            </button>
          )}
          <style jsx>{`
            .scroll-to-top {
              position: fixed;
              bottom: 20px;
              right: 20px;
              width: 50px;
              height: 50px;
              border-radius: 50%;
              background: linear-gradient(45deg, #ff416c, #ff4b2b);
              box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.2);
              border: none;
              display: flex;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              transition: all 0.3s ease-in-out;
              opacity: 0.8;
              margin-bottom: 60px;
            }

            .scroll-to-top:hover {
              opacity: 1;
              transform: scale(1.1);
            }

            .arrow-icon {
              color: black;
              font-size: 24px;
            }
          `}</style>
        </Row>
      </Container>

      <footer className=" mt-4">
        <Container fluid className="bg-dark text-white py-5">
          <Row className="justify-content-center">
            <Col xs="auto">
              <p className="text-center">
                <a href="http://highereducation.cg.gov.in/hi/accessibility-statement" target="_blank"> अभिगम्यता विवरण</a>  |
                <a href="http://highereducation.cg.gov.in/hi/copyright-policy" target="_blank"> सर्वाधिकार नीति</a> |
                <a href="http://highereducation.cg.gov.in/hi/disclaimer" target="_blank"> खंडन</a> |
                <a href="http://highereducation.cg.gov.in/hi/sitemap" target="_blank">  साइट मानचित्र</a>|
                <a href="http://highereducation.cg.gov.in/hi/feedback" target="_blank"> प्रतिक्रिया </a> | <a href="http://highereducation.cg.gov.in/hi/hyperlink-policy" target="_blank"> हाइपरलिंक नीति</a>
                |<a href="http://highereducation.cg.gov.in/hi/privacy-policy" target="_blank"> गोपनीयता नीति </a>|<a href="http://highereducation.cg.gov.in/hi/terms-condition" target="_blank"> नियम और शर्तें</a> |
                <a href="http://highereducation.cg.gov.in/hi/terms-use" target="_blank"> उपयोग की शर्तें</a> |<a href="http://highereducation.cg.gov.in/hi/help" target="_blank"> मदद</a>
                <br></br>
                सर्वाधिकार सुरक्षित - उच्च शिक्षा विभाग की
                आधिकारिक वेबसाइट, छत्तीसगढ़ सरकार, भारत<br></br>
                नोट: इस वेबसाइट पर सामग्री प्रकाशित और प्रबंधन उच्च शिक्षा
                विभाग, छत्तीसगढ़ सरकार द्वारा की गई है<br></br>
                {/* इस वेबसाइट के बारे में किसी भी प्रश्न के लिए, कृपया वेब सूचना
                प्रबंधक आनंद चरपे (सहायक कंप्यूटर प्रोग्रामर) से संपर्क करें।
                <br></br>
                ई-मेल आईडी: <EMAIL>, संपर्क: + 91-9425258918 */}
              </p>
              <p className="mt-4 text-center" style={{ padding: 10 }}>
                © {currentYear} <a href="https://www.nic.in/" target="_blank">National Informatics Centre <img src="/carousel/nic.png" style={{ height: "40px" }}></img></a>
              </p>
            </Col>
          </Row>
        </Container>
      </footer>

      {/* </Container> */}
      {/* </Container> */}
    </>
  );
};

export default HomePage;
