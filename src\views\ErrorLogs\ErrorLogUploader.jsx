import React, { useState, useEffect, useRef } from "react";
import axios from "axios";
import {
  Container,
  Col,
  <PERSON>,
  <PERSON><PERSON>,
  Row,
  Card,
  CardHeader,
  Card<PERSON>ody,
  Modal,
  ModalHeader,
  <PERSON>dal<PERSON><PERSON>,
  <PERSON>dal<PERSON>ooter,
  Uncontrolled<PERSON><PERSON>down,
  DropdownToggle,
  DropdownMenu,
  Dropdown<PERSON>tem,
  Badge,
  Spinner,
  Input,
} from "reactstrap";
import Header from "../../components/Headers/Header";
import SwalMessageAlert from "../../utils/sweetAlertMessage";
import DataTable from "react-data-table-component";
import formatDate from "../../utils/formateDate.jsx";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
const ErrorLogUploader = () => {
  const [files, setFiles] = useState([]); // Change to an array to hold multiple files
  const [title, setTitle] = useState("");
  const [message, setMessage] = useState("");
  const [responseMessage, setResponseMessage] = useState("");
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState(null);
  const [errors, setErrors] = useState(null);
  const [totalRows, setTotalRows] = useState(0);
  const type = sessionStorage.getItem("userType");
  const token = sessionStorage.getItem("authToken");
  const endPoint = import.meta.env.VITE_API_URL;
  const id = sessionStorage.getItem("id");
  const [loading, setLoading] = useState(true);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState("");
  const messagesEndRef = useRef(null);
  const [selectedTicket, setSelectedTickets] = useState("");
  const [selectedTicketNo, setSelectedTicketsNo] = useState("");
  const [modal, setModal] = useState(false);
  const [errorLogs, setErrorLogs] = useState([]);
  const toggleChatBox = () => setModal(!modal);
  const [status, setStatus] = useState("");
  const [ticketNo, setTicketNo] = useState("");
  const [transferErrorList, setTransferErrorList] = useState([]);
  const [showTransferLogs, setShowTransferLogs] = useState(false);
  useEffect(() => {
    const fetchErrorList = async () => {
      try {
        setLoading(true); // Add loading state
        const response = await axios.get(
          `${endPoint}/api/error-logs-list/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200 && response.data.success) {
          const ticketsArray = response.data.data;

          if (Array.isArray(ticketsArray) && ticketsArray.length > 0) {
            const userName = ticketsArray[0]?.submittedById?.name;
            if (userName) {
              sessionStorage.setItem("username", userName);
            }
            setErrorLogs(ticketsArray.reverse());
            const transferLogs = response.data.allErrorLogs.filter(
              (item) => item.TransferStatus?.toUpperCase() === "DEPARTMENT"
            );
            setTransferErrorList(transferLogs);
            setTotalRows(ticketsArray.length);
          } else {
            setErrorLogs([]);
            setTotalRows(0);
          }
        } else {
          alert("Failed to fetch Errorlist. Please try again.");
        }
      } catch (error) {
        console.error("Failed to Load Data:", error);
        alert("Failed to Load Data.");
      } finally {
        setLoading(false);
      }
    };

    if (id && token) {
      fetchErrorList();
    }
  }, [endPoint, token, id]);

  const [sentByUserMessage, setSentByUserMessage] = useState(null);
  const handleChatBox = (ticketId, ticketNo) => {
    setModal(true);
    setSelectedTickets(ticketId);
    const message = errorLogs.find((a) => String(a._id) === ticketId).message;
    setSentByUserMessage(message);
    setSelectedTicketsNo(ticketNo);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (modal && selectedTicket) {
      const fetchChatMessages = async () => {
        try {
          const response = await axios.get(
            `${endPoint}/api/error-logs-chat/${selectedTicket}`,
            {
              headers: {
                "Content-Type": "application/json",
                'web-url': window.location.href,
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.data.success) {
            const chatData = response.data.chat || [];
            setMessages(chatData);
          } else {
            console.log("Error:", response.data.message);
          }
        } catch (error) {
          console.error("API Error:", error);
        }
      };
      fetchChatMessages();
    }
  }, [modal, selectedTicket]);

  useEffect(() => {
    if (messages.length > 0) {
      localStorage.setItem(
        `chatMessages_${selectedTicket}`,
        JSON.stringify(messages)
      );
    }
  }, [messages, selectedTicket]);

  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [messages]);

  const handleMessageSent = async () => {
    if (!newMessage.trim()) return;

    try {
      const body = {
        message: newMessage,
        submittedById: sessionStorage.getItem("id"),
        submittedBy: sessionStorage.getItem("name"),
        receiverId: "7987342537",
      };
      const response = await axios.post(
        `${endPoint}/api/error-logs/sendMessage/${selectedTicket}`,
        body,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.data.success) {
        setMessages((prevMessages) => [
          ...prevMessages,
          {
            message: newMessage,
            submittedById: sessionStorage.getItem("id"),
            submittedBy: sessionStorage.getItem("name"),
            receiverId: "",
            createdAt: new Date().toISOString(),
          },
        ]);
        setNewMessage("");
      }
    } catch (error) {
      console.error("Failed to send message:", error);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === "Enter") {
      handleMessageSent(); // Call the button click handler
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const handleFileChange = (e) => {
    const files = e.target.files;
    const allowedTypes = ["image/jpeg", "image/png", "image/jpg"];
    const maxSize = 100 * 1024;

    if (files.length > 0) {
      const validFiles = Array.from(files).filter((file) => {
        if (!allowedTypes.includes(file.type)) {
          setErrors("Only JPG, JPEG, and PNG files are allowed!");
          return false;
        }
        if (file.size > maxSize) {
          setErrors(`File "${file.name}" exceeds the size limit of 100KB!`);
          return false;
        }
        return true;
      });

      if (validFiles.length === 0) {
        e.target.value = "";
        return;
      }

      setFiles(validFiles);
      setErrors("");
    }
  };
  const handleStatusChange = async (ticketNo, newStatus) => {
    try {
      // Correcting status format
      let correctedStatus =
        newStatus === "inprogress"
          ? "In Progress"
          : newStatus === "pending"
          ? "Pending"
          : newStatus === "resolved"
          ? "Resolved"
          : newStatus;
      if (!showTransferLogs) {
        SwalMessageAlert(
          "Status change is only allowed in Transferred Error List.",
          "warning"
        );
        return;
      }
      const response = await axios.post(
        `${endPoint}/api/error-update-status`,
        {
          ticketNo,
          status: correctedStatus,
        },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );
      if (response.status === 200) {
        const data = response.data;
        // setTransferErrorList(data);
        SwalMessageAlert(response.data.msg, "success");
        setTransferErrorList((prevList) =>
          prevList.map((item) =>
            item.ticketNo === ticketNo
              ? { ...item, status: correctedStatus }
              : item
          )
        );
      } else {
        SwalMessageAlert(response.data.msg || "Error updating status", "error");
      }
    } catch (err) {
      console.error(
        "Status update error:",
        err.response ? err.response.data : err
      );
      SwalMessageAlert("Failed to update status", "error");
    }
  };
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (files.length === 0) {
      setError("Please select at least one file to upload");
      return;
    }
    setUploading(true);
    setError(null);
    const body = new FormData();
    body.append("title", title);
    body.append("message", message);

    for (let i = 0; i < files.length; i++) {
      body.append("files", files[i]);
    }

    try {
      const response = await axios.post(
        `${endPoint}/api/error-logs/add/${id}?type=${type}`,
        body,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        setResponseMessage("Files uploaded successfully!");
        SwalMessageAlert("Added successfully.", "success");
        setUploading(false);
        window.setTimeout(function () {
          location.reload();
        }, 3000);
      }
    } catch (err) {
      console.error("Error uploading files:", err);
      setError("Error uploading files");
      setUploading(false);
    }
  };
  const columns = [
    {
      name: "S. No",
      selector: (row, index) => index + 1,
      sortable: true,
      width: "70px",
    },
    {
      name: "Ticket No",
      selector: (row) => row.ticketNo,
      sortable: true,
    },
    {
      name: "Date of Raise Ticket",
      selector: (row) =>
        `${formatDate(row.createdAt)} (${new Date(
          row.createdAt
        ).toLocaleTimeString()})`,
      sortable: true,
    },
    {
      name: "Submitted By",
      selector: (row) => row.submittedBy,
      sortable: true,
    },
    {
      name: "Title",
      selector: (row) => row.title,
      sortable: true,
    },
    // {
    //   name: "Message",
    //   selector: (row) => row.message,
    //   style: { maxWidth: "200px", whiteSpace: "normal" },
    // },
    {
      name: "Attachments",
      cell: (row) => (
        <>
          <span>
            Download Images <br />
          </span>
          {row.image && row.image.length > 0 ? (
            row.image.map((image, index) => (
              <a
                key={index}
                target="_blank"
                href={`https://heonline.cg.nic.in/${image}`}
                download
              >
                <Button className="btn btn-sm m-2 btn-primary">
                  {index + 1}
                </Button>
              </a>
            ))
          ) : (
            <Button className="btn btn-sm m-2 btn-secondary" disabled>
              No Files
            </Button>
          )}
        </>
      ),
    },
    {
      name: "Remarks",
      cell: (row) => (
        <Button
          onClick={() => handleChatBox(row._id, row.ticketNo)}
          className="btn-sm bg-success text-white"
        >
          Chat
        </Button>
      ),
    },

    {
      name: "Status",
      cell: (row) => {
        const isStateAdmin = row.submittedBy === "StateAdmin";
        const getButtonColor = () => {
          const normalizedStatus = (row.status || "").toLowerCase();
          switch (normalizedStatus) {
            case "resolved":
              return "success";
            case "in progress":
            case "inprogress":
              return "info";
            case "pending":
            default:
              return "warning";
          }
        };

        const displayStatus =
          row.status || (row.isResolved ? "Resolved" : "Pending");
        if (isStateAdmin) {
          return (
            <UncontrolledDropdown>
              <DropdownToggle
                caret
                color={getButtonColor()}
                size="sm"
                className="text-white"
                disabled={!showTransferLogs}
              >
                {displayStatus}
              </DropdownToggle>
              <DropdownMenu>
                {["Resolved", "In Progress", "Pending"]
                  .filter((item) => item !== displayStatus)
                  .map((item) => (
                    <DropdownItem
                      key={item}
                      onClick={() =>
                        showTransferLogs
                          ? handleStatusChange(row.ticketNo, item)
                          : alert(
                              "Status change is only allowed in Transferred Error List."
                            )
                      }
                    >
                      {item}
                    </DropdownItem>
                  ))}
              </DropdownMenu>
            </UncontrolledDropdown>
          );
        } else {
          return (
            <Button
              caret
              color={getButtonColor()}
              size="sm"
              className="text-white"
            >
              {displayStatus}
            </Button>
          );
        }
      },
    },

    // {
    //   name: "Status",
    //   cell: (row) => {
    //     const isStateAdmin = row.submittedBy === "StateAdmin";
    //     const getButtonColor = () => {
    //       // Normalize status case for consistent comparison
    //       const normalizedStatus = (row.status || "").toLowerCase();

    //       switch (normalizedStatus) {
    //         case "resolved":
    //           return "success";
    //         case "in progress":
    //         case "inprogress":
    //           return "info";
    //         case "pending":
    //         default:
    //           return "warning";
    //       }
    //     };

    //     // Ensure correct display format of status
    //     const displayStatus =
    //       row.status || (row.isResolved ? "Resolved" : "Pending");

    //     // Debug the current row status
    //     console.log(
    //       `Rendering status for ticket ${row.ticketNo}: ${displayStatus}`
    //     );

    //     return (
    //       <Button
    //         caret
    //         color={getButtonColor()}
    //         size="sm"
    //         className="text-white"
    //       >
    //         {displayStatus}
    //       </Button>
    //     );
    //   },
    // },
  ];
  useEffect(() => {
    if (modal) {
      const interval = setInterval(() => {
        handleReloadChat();
      }, 5000);
      return () => clearInterval(interval);
    }
  });
  const handleReloadChat = async () => {
    try {
      const receiverId = sessionStorage.getItem("id");
      const response = await axios.get(
        `${endPoint}/api/error-logs-chat/${selectedTicket}?receiverId=${receiverId}`,
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        const chatData = response.data.chat || [];
        setMessages(chatData);
      } else {
        console.log("Error:", response.data.message);
      }
    } catch (error) {
      console.error("API Error:", error);
    }
  };
  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Card
          className="bg-secondary shadow mb-8"
          style={{ marginRight: "10%", marginLeft: "10%" }}
        >
          <CardHeader className="bg-white border-0">
            <Row className="align-items-center">
              <Col xs="8">
                <h3 className="mb-0">Request Error Solution</h3>
              </Col>
            </Row>
          </CardHeader>
          <CardBody>
            <Form onSubmit={handleSubmit}>
              <Row>
                <Col>
                  <label
                    style={{ fontWeight: "700", color: "#004388" }}
                    htmlFor="formTitle"
                  >
                    Title:
                  </label>
                  <select
                    id="formTitle"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    required
                    className="form-control"
                  >
                    <option value="">Select an error type</option>
                    <option value="File Upload Error">File Upload Error</option>
                    <option value="Form Submission Error">
                      Form Submission Error
                    </option>
                    <option value="Data Load Error">Data Load Error</option>
                    <option value="Authentication Error">
                      Authentication Error
                    </option>
                    <option value="Authorization Error">
                      Authorization Error
                    </option>
                    <option value="Network Error">Network Error</option>
                    <option value="Server Error">Server Error</option>
                    <option value="Timeout Error">Timeout Error</option>
                    <option value="Unexpected Error">Unexpected Error</option>
                  </select>
                </Col>

                <Col>
                  <div className="form-group">
                    <label
                      style={{ fontWeight: "700", color: "#004388" }}
                      htmlFor="formFile"
                    >
                      Upload File :
                      <small className="text-warning"> (Max 100 KB*)</small>
                    </label>

                    <input
                      type="file"
                      id="formFile"
                      onChange={handleFileChange}
                      multiple
                      required
                      accept=".jpg, .jpeg, .png"
                      className="form-control"
                    />

                    {errors && (
                      <p style={{ color: "red", fontSize: "14px" }}>{errors}</p>
                    )}
                  </div>
                </Col>
              </Row>
              <Row>
                <Col>
                  <label
                    style={{ fontWeight: "700", color: "#004388" }}
                    htmlFor="formMessage"
                  >
                    Message:
                  </label>
                  <textarea
                    id="formMessage"
                    rows={4}
                    placeholder="Enter message"
                    value={message}
                    onKeyPress={handleKeyPress}
                    onChange={(e) => setMessage(e.target.value)}
                    required
                    className="form-control"
                  />
                </Col>
              </Row>

              {error && <p style={{ color: "red" }}>{error}</p>}
              {responseMessage && (
                <p style={{ color: "green" }}>{responseMessage}</p>
              )}
              <br />

              <Button
                className="bg-primary text-white"
                style={{ border: "none" }}
                type="submit"
                disabled={uploading}
              >
                {uploading ? "Uploading..." : "Submit"}
              </Button>
            </Form>
          </CardBody>
        </Card>
        <Card>
          <CardHeader>
            <h2
              className={`mr-2 ${
                showTransferLogs && type === "1" ? "text-primary" : "text-info"
              }`}
            >
              {showTransferLogs && type === "1"
                ? "Transferred Error List"
                : "Error List"}
            </h2>

            <div className="mb-3 ">
              {type === "1" && (
                <Button
                  color={showTransferLogs ? "primary" : "success"}
                  onClick={() => setShowTransferLogs(!showTransferLogs)}
                  className="btn-sm mb-3"
                >
                  {showTransferLogs ? "Error List" : "Transferred Error List"}
                </Button>
              )}
            </div>
          </CardHeader>

          <div className="error-logs-table">
            <DataTable
              columns={columns}
              data={showTransferLogs ? transferErrorList : errorLogs}
              progressPending={loading}
              progressComponent={
                <div className="p-4">
                  <Spinner animation="border" />
                </div>
              }
              pagination
              paginationTotalRows={
                showTransferLogs ? transferErrorList.length : errorLogs.length
              }
              persistTableHead
              responsive
              highlightOnHover
              noDataComponent={<div className="p-4">No error logs found</div>}
            />

            {/* <DataTable
              columns={columns}
              data={errorLogs}
              progressPending={loading}
              progressComponent={
                <div className="p-4">
                  <Spinner animation="border" />
                </div>
              }
              pagination
              paginationTotalRows={totalRows}
              persistTableHead
              responsive
              highlightOnHover
              noDataComponent={<div className="p-4">No error logs found</div>}
            /> */}
          </div>
        </Card>
        <Modal isOpen={modal} toggle={toggleChatBox} size="lg">
          <ModalHeader toggle={toggleChatBox}>
            Chat for Ticket No.
            <b>( {selectedTicketNo} )</b>
          </ModalHeader>
          <ModalBody
            style={{
              height: "480px",

              overflowY: "auto",
              display: "flex",
              flexDirection: "column",
              backgroundColor: "#f0f0f0",
            }}
          >
            <div style={{ flexGrow: 1, overflowY: "auto", padding: "10px" }}>
              <div className="text-center text-muted mt-4">
              {sentByUserMessage ? (
                  <div
                    style={{
                      textAlign: "left",
                      marginBottom: "10px",
                    }}
                  >
                    <div
                      style={{
                        display: "inline-block",
                        backgroundColor: "#fff",
                        borderRadius: "8px",
                        padding: "5px 10px",
                        boxShadow: "0 0 5px rgba(0, 0, 0, 0.1)",
                        maxWidth: "70%",
                      }}
                    >
                      <strong style={{ color: "#555", fontSize: "12px" }}>
                        {selectedTicketNo}
                      </strong>
                      <p style={{ margin: "2px 0", wordBreak: "break-word" }}>
                        {sentByUserMessage}
                      </p>
                    </div>
                  </div>
                ) : (
                  "No messages yet. Start a conversation!"
                )}
              </div>
              {messages.length > 0
                ? messages.map((msg, index) => (
                    <div
                      key={index}
                      style={{
                        textAlign:
                          msg.submittedBy === "Tech Team" ? "right" : "left",
                        marginBottom: "10px",
                      }}
                    >
                      <div
                        style={{
                          display: "inline-block",
                          backgroundColor:
                            msg.submittedBy === "Tech Team"
                              ? "#DCF8C6"
                              : "#fff",
                          borderRadius: "8px",
                          padding: "5px 10px",
                          boxShadow: "0 0 5px rgba(0, 0, 0, 0.1)",
                          maxWidth: "70%",
                        }}
                      >
                        <strong style={{ color: "#555", fontSize: "12px" }}>
                          {msg.submittedBy}
                        </strong>
                        <p style={{ margin: "2px 0", wordBreak: "break-word" }}>
                          {msg.message}
                        </p>
                        <small style={{ fontSize: "10px", color: "#888" }}>
                          {new Date(msg.createdAt).toLocaleTimeString()}
                        </small>
                      </div>
                    </div>
                  ))
                : ""}
              <div ref={messagesEndRef} />
            </div>
          </ModalBody>

          <ModalFooter className="p-2">
            <div className="d-flex w-100">
              <Input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder="Type your message..."
                onKeyDown={handleKeyPress}
                className="mr-2"
                style={{
                  borderRadius: "20px",
                  flexGrow: 1,
                }}
              />
              <Button
                color="primary"
                onClick={handleMessageSent}
                disabled={!newMessage.trim()}
                style={{
                  borderRadius: "50%",
                  width: "40px",
                  height: "40px",
                  padding: 0,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <i className="fas fa-paper-plane"></i>
              </Button>
            </div>
          </ModalFooter>
        </Modal>
      </Container>
    </>
  );
};

export default ErrorLogUploader;
