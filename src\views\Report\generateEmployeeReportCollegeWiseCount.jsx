import { useEffect, useState } from "react";
import {
    Card,
    CardHeader,
    CardBody,
    FormGroup,
    Form,
    Input,
    Container,
    Row,
    Col,
    Label,
    Button,
    Table,
} from "reactstrap";
import Select from "react-select";
import DataTable from "react-data-table-component";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
const GenerateEmployeeReportCount = () => {
    const endPoint = import.meta.env.VITE_API_URL;
    const token = sessionStorage.getItem("authToken");

    const [data, setData] = useState("");



    useEffect(() => {
        const fetchAllCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/getCollegeWiseEmployeeCount`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );

                if (response.status === 200) {
                    setData(response.data);
                    // console.log(response.data, "response.data Getting Data");

                } else {
                    alert("Failed to fetch Employee data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        // Call the function
        fetchAllCollege();

        // Optionally add dependencies in the dependency array
    }, [endPoint, token]); // Include value and token as dependencies if they can change


    // console.log(data, "Getting Data here ");


    const [college, setCollege] = useState([]);
    useEffect(() => {
        const fetchCollege = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/college/get-all-college`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    setCollege(response.data);
                } else {
                    alert("Failed to fetch College data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };

        fetchCollege();
    }, [endPoint, token]);



    const [district, setDistrict] = useState([]);
    useEffect(() => {
        const fetchDistrict = async () => {
            try {
                const response = await axios.get(
                    `${endPoint}/api/district/get-all-district`,
                    {
                        headers: {
                            "Content-Type": "application/json",
                            'web-url': window.location.href,
                            Authorization: `Bearer ${token}`,
                        },
                    }
                );
                if (response.status === 200) {
                    //   // console.log(response.data);

                    setDistrict(response.data);
                } else {
                    alert("Failed to District  data. Please try again.");
                }
            } catch (error) {
                console.error("An error occurred while fetching the data:", error);
                alert("An error occurred. Please try again later.");
            }
        };
        // Call the function
        fetchDistrict();
        // Optionally add dependencies in the dependency array
    }, [endPoint, token]);


    const [division, setDivision] = useState([]);
    useEffect(() => {
        const getDivision = async () => {
            try {
                const response = await axios.get(`${endPoint}/api/division/get-all`, {
                    headers: {
                        "Content-Type": "application/json",
                        'web-url': window.location.href,
                        Authorization: `Bearer ${token}`,
                    },
                });
                if (response.status === 200) {
                    const data = response.data;
                    setDivision(data);
                }
            } catch (error) {
                console.error("Error fetching university data:", error);
                alert("Failed to load university data.");
            }
        };

        getDivision();
    }, [endPoint, token]);



    const getDivisionName = (value) => {
        const divisionObj = division.find((div) => div.divisionCode === value);
        return divisionObj ? divisionObj.name : "Unknown Division";
    };


    const getDistrictName = (value) => {
        const districtObj = district.find((div) => div.LGDCode === value);
        return districtObj ? districtObj.districtNameEng : "Unknown Division";
    };


    const mergedData = college.map(col => {
        const matchedCollege = data && data.length > 0 && data.find(d => d.collegeId === col._id); 
        return {
            collegeId: col._id,
            collegeName: col.name,
            district: col.district,
            division: col.divison,
            employeeCount: matchedCollege ? matchedCollege.employeeCount : 0, 
            verifiedEmployeeCount: matchedCollege ? matchedCollege.verifiedEmployeeCount : 0
        };
    });
    
    // console.log(mergedData,"get Full Data");
    



    const exportToExcel = async () => {
        try {
            if (data.length > 0) {  // Check if data is available
                let tableHTML = `
                  <table border="1">
                    <thead>
                      <tr>
                        <th>S.No</th>
                        <th>Division</th>
                        <th>District</th>
                        <th>College Name</th>
                        <th>Registerd Employees</th>
                        <th>Verified Employees</th>
                      </tr>
                    </thead>
                    <tbody>
                `;

                mergedData.forEach((college, index) => {
                    tableHTML += `            
                      <tr>
                        <td>${index + 1}</td>
                        <td>${getDivisionName(college.division)}</td>
                        <td>${getDistrictName(college.district)}</td>
                        <td>${college.collegeName}</td>
                        <td>${college.employeeCount}</td>
                        <td>${college.verifiedEmployeeCount}</td>
                      </tr>
                    `;
                });

                tableHTML += "</tbody></table>";

                const excelFileContent = `
                  <html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">
                    <head><!--[if gte mso 9]><xml>
                      <x:ExcelWorkbook>
                        <x:ExcelWorksheets>
                          <x:ExcelWorksheet>
                            <x:Name>Employees</x:Name>
                            <x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions>
                          </x:ExcelWorksheet>
                        </x:ExcelWorksheets>
                      </x:ExcelWorkbook>
                    </xml><![endif]--></head>
                    <body>${tableHTML}</body>
                  </html>
                `;

                const blob = new Blob([excelFileContent], { type: "application/vnd.ms-excel;charset=utf-8;" });
                const date = new Date().toLocaleDateString();
                const downloadLink = document.createElement("a");
                downloadLink.href = URL.createObjectURL(blob);
                downloadLink.download = `Employee_Report_${date}.xls`;
                downloadLink.click();
            } else {
                SwalMessageAlert("No data available for export.", "warning");
            }
        } catch (error) {
            SwalMessageAlert(`Data Not Found: ${error.message}`, "error");
        }
    };

    const columns = [
        {
            name: "S.No",
            selector: (row, index) => index + 1, 
            sortable: false,
        },
        {
            name: "Division",
            selector: (row) => getDivisionName(row.division),
            sortable: true,
        },
        {
            name: "District",
            selector: (row) => getDistrictName(row.district),
            sortable: true,
        },
        {
            name: "College Name",
            selector: (row) => row.collegeName,
            sortable: true,
            cell: (row) => (
                <span style={{ color: row.employeeCount === 0 ? "red" : "black",  }}>
                    {row.collegeName}
                </span>
            ),
        },
        {
            name: "Registered Employees",
            selector: (row) => row.employeeCount,
            sortable: true,
            cell: (row) => (
                <span style={{ color: row.employeeCount === 0 ? "red" : "black"}}>
                    {row.employeeCount}
                </span>
            ),
        },
        {
            name: "Verified Employees",
            selector: (row) => row.verifiedEmployeeCount,
            sortable: true,
        },
    ];

    return (
        <>
            <Header />

            {/* Page content */}
            <Container className="mt--7" fluid>
                {/* Table to display institutes with pagination */}
                <Row className="mt-5">
                    <Col>
                        <Card className="shadow">
                            <CardHeader className="border-0 d-flex justify-content-between align-items-center">
                                <Col xs="6">
                                    <h3 className="mb-0">
                                        Generate Employee Report Count{" "}


                                    </h3>
                                </Col>
                                <Col className="text-right" xs="4">
                                    <Button
                                        color="primary"
                                        size="sm"
                                        onClick={exportToExcel}
                                    >
                                        Export To Excel
                                    </Button>
                                </Col>
                            </CardHeader>

                        </Card>
                    </Col>
                </Row>

                <Row className="mt-4">
                    <Col lg="12">
                        <Card className="bg-secondary shadow">
                            <CardHeader className="bg-white border-0">
                                <Row className="align-items-center">
                                    <Col xs="8">
                                        <h3 className="mb-0">Employees Counts List</h3>
                                    </Col>
                                </Row>
                            </CardHeader>
                            <CardBody>
                                <DataTable
                                    columns={columns}
                                    data={mergedData}
                                    pagination
                                    highlightOnHover
                                    striped
                                />
                            </CardBody>
                        </Card>
                    </Col>
                </Row>
            </Container>
        </>
    );
};

export default GenerateEmployeeReportCount;
