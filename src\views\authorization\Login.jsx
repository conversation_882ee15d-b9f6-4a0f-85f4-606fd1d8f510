import { Link, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import '../../assets/css/Login.css'
import { FaUser } from 'react-icons/fa';
import {
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardBody,
  FormGroup,
  Form,
  Input,
  InputGroupAddon,
  InputGroupText,
  InputGroup,
  Toast,
  Row,
  ToastBody, ToastHeader,
  Col,
  Container
} from "reactstrap";
import axios from "axios";

const Login = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const navigate = useNavigate();
  const [userType, allUserType] = useState([]);
  const [formData, setFormData] = useState({
    userType: '',
    username: '',
    password: '',
  });

  const [CaptchaText, setCaptchaText] = useState(''); // Captcha input value
  const [generatedCaptcha, setGeneratedCaptcha] = useState(''); // Captcha display text
  const [toastVisible, setToastVisible] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState(''); // 'success' or 'error'

  // Generate a random Captcha when component mounts

  const generateCaptcha = () => {
    const captcha = Math.floor(Math.random() * 900000) + 100000; // Generates a random 6-digit number
    setGeneratedCaptcha(captcha.toString());
  };


  useEffect(() => {

    generateCaptcha(); // Call the function to set the initial Captcha

    // Get User Types from the API
    const fetchData = async () => {
      try {
        const response = await axios.get(`${endPoint}/api/userType`, {
          headers: { 'Content-Type': 'application/json', 
                      'web-url': window.location.href,

          }
        });
        allUserType(response.data.getAllUserType);
      } catch (error) {
        console.error("An error occurred while fetching user types:", error);
      }
    };
    fetchData();
  }, [endPoint]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'Captcha') {
      setCaptchaText(value);
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Basic form validation
    if (!formData.userType) {
      alert("Please select a user type.");
      return;
    }
    if (!formData.username) {
      alert("Please enter your User Name.");
      return;
    }
    if (!formData.password) {
      alert("Please enter your password.");
      return;
    }
    const expectedCaptcha = generatedCaptcha;
    if (CaptchaText !== expectedCaptcha) {
      setToastMessage("Captcha does not match!");
      setToastType('error');
      setToastVisible(true);
      return; // Stop further processing
    }

    try {
      const response = await axios.post(`${endPoint}/api/login`, formData, {
        headers: { 'Content-Type': 'application/json',
          'web-url': window.location.href,
         }
      });
      if (response.status === 200) {
        const token = response.data.token;
        const id = response.data.id;
        sessionStorage.setItem('id', id);
        sessionStorage.setItem('authToken', token);
        sessionStorage.setItem('name', response.data.username);
        sessionStorage.setItem('userType', response.data.userType);
        sessionStorage.setItem('type', response.data.type);
        sessionStorage.setItem('status', response.data.status);
        sessionStorage.setItem('empId', response.data.empCode);
        sessionStorage.setItem('sectionName', response.data.sectionName);
        // alert("Logged in Success")
        if (response.data.type === 3) {
          window.location.replace("admin/leave-dashboard");
        } else {
          window.location.replace("admin/dashboard");
        }
        //navigate('admin/Dashboard');
        // window.location.reload();
      } else {
        generateCaptcha();
        alert(response.data.msg);
        navigate('/auth/login');
      }
    } catch (error) {
      console.error("An error occurred while submitting the form:", error);
      alert("An error occurred. Please try again later.");
      navigate('/auth/login');
    }
  };

  return (
    <>
      <Col lg="5" md="7">
        <Card className="bg-secondary shadow border-0">
          <CardHeader className="bg-transparent pb-5">
            <div className="text-center mt-2 mb-3">
              <FaUser style={{ height: '80px', width: '60px', color: '#003ca7', marginTop: "10px" }} />
            </div>
            <Container>
              <div className="header-body text-center mb-3">
                <Row className="justify-content-center">
                  <Col lg="10" md="0">
                    <p className="WelcomeText">WELCOME</p>
                  </Col>
                </Row>
              </div>
            </Container>
            <div className="text-center text-muted" style={{ marginBottom: "-40px" }}>
              <small>Sign in with credentials</small>
            </div>
          </CardHeader>
          <CardBody className="px-lg-5 py-lg-5">
            <Form role="form" onSubmit={handleSubmit}>
              <FormGroup className="mb-3">
                <Input
                  name="userType"
                  type="select"
                  value={formData.userType}
                  style={{ color: '#404446' }}
                  onChange={handleInputChange}
                >
                  <option value="" disabled>Select User Type</option>
                  {userType && userType.length > 0 && userType.map((type, index) => (
                    <option key={index} value={type.UserType}>
                      {type.UserType} ({type.UserTypeHin})
                    </option>
                  ))}
                </Input>
              </FormGroup>
              <FormGroup className="mb-3">
                <InputGroup className="input-group-alternative">
                  <InputGroupAddon addonType="prepend">
                    <InputGroupText>
                      <i className="ni ni-single-02" />
                    </InputGroupText>
                  </InputGroupAddon>
                  <Input
                    name="username"
                    placeholder="User Name"
                    type="text"
                    style={{ color: '#404446' }}
                    value={formData.username}
                    onChange={handleInputChange}
                    autoComplete="off"
                  />
                </InputGroup>
              </FormGroup>
              <FormGroup>
                <InputGroup className="input-group-alternative">
                  <InputGroupAddon addonType="prepend">
                    <InputGroupText>
                      <i className="ni ni-lock-circle-open" />
                    </InputGroupText>
                  </InputGroupAddon>
                  <Input
                    name="password"
                    placeholder="Password"
                    type="password"
                    value={formData.password}
                    style={{ color: '#404446' }}
                    onChange={handleInputChange}
                  />
                </InputGroup>
              </FormGroup>
              <Row><Col >

                {toastVisible && (
                  <Toast>
                    <ToastHeader toggle={() => setToastVisible(false)}>
                    </ToastHeader>
                    <ToastBody style={{ color: toastType === 'error' ? 'red' : 'green', fontSize: "12px" }}>
                      {toastMessage}
                    </ToastBody>
                  </Toast>
                )}

              </Col>
              </Row>
              <Row>
                <Col xs="6">
                  <FormGroup style={{ color: '#404446', backgroundColor: 'green' }}>
                    <Input
                      name="captchaDisplay"
                      placeholder="Captcha"
                      type="text"
                      value={generatedCaptcha}
                      disabled
                      readOnly
                      style={{
                        color: 'white',
                        backgroundColor: 'green',
                        border: "none",
                        userSelect: "none",
                        pointerEvents: "none",
                        fontFamily: "Lucida Handwriting, cursive"
                      }}
                    />
                  </FormGroup>
                </Col>
                <Col xs="6">
                  <FormGroup>
                    <InputGroup className="input-group-alternative">


                      <Input
                        name="Captcha"
                        placeholder="Enter Captcha"
                        type="text"
                        value={CaptchaText}
                        style={{ color: '#404446' }}
                        onChange={handleInputChange}
                      />
                    </InputGroup>
                  </FormGroup>
                </Col>
              </Row>
              <div className="text-center">
                <Button className="my-4" color="primary" type="submit">
                  Sign in
                </Button>
              </div>
            </Form>

          </CardBody>
        </Card>
        <Row className="mt-3">
          <Col xs="6">
            <a className="text-light" href="#" onClick={(e) => e.preventDefault()}>
              <small>Forgot password?</small>
            </a>
          </Col>
          <Col className="text-right" xs="6">
            <Link className="text-light" to="/auth/register">
              <small>Create new account</small>
            </Link>
          </Col>
        </Row>
      </Col>
    </>
  );
};

export default Login;
