import  { createContext, useState, useContext } from "react";

// Create the Context
const SidebarContext = createContext();

// Provide the Context
export const SidebarProvider = ({ children }) => {
  const [isSidebarVisible, setIsSidebarVisible] = useState(true);

  // // console.log(isSidebarVisible,"isSidefjkaljdsklfjls");

  return (
    <SidebarContext.Provider value={{ isSidebarVisible, setIsSidebarVisible }}>
      {children}
    </SidebarContext.Provider>
  );
};




// Custom Hook for Convenience
export const useSidebar = () => {
  return useContext(SidebarContext);
};
