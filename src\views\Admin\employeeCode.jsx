import { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardHeader,
  CardBody,
  Container,
  Input,
  Row,
  Col,
} from "reactstrap";
import Header from "../../components/Headers/Header.jsx";
import axios from "axios";
import SwalMessageAlert from "../../utils/sweetAlertMessage.jsx";
import DataTable from "react-data-table-component";
import Swal from "sweetalert2";
import { useParams } from 'react-router-dom';
const EmployeeCode = () => {
  const endPoint = import.meta.env.VITE_API_URL;
  const token = sessionStorage.getItem("authToken");
  const collegeId = sessionStorage.getItem("id");
  const [employeeCode, setEmployeeCode] = useState([]);
  const [editableRow, setEditableRow] = useState(null);
  const { empCode } = useParams();
  const [formData, setFormData] = useState({
    empCode: "",
    name: "",
    contact: "",
  });
  const [newRow, setNewRow] = useState({
    empCode: "",
    name: "",
    contact: "",
  }); // For adding a new employee

  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 10;

  const [searchTerm, setSearchTerm] = useState("");
  const [filteredEmployeeCode, setFiltereEmployeeCode] = useState([]);
const [EmpName, setEmpName] = useState([]);
const [statusFilter, setStatusFilter] = useState("");
  // Fetch employee code list
  useEffect(() => {
    const fetchEmployeeCode = async () => {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee-code/college-wise/${collegeId}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );
        if (response.status === 200) {
          setEmployeeCode(response.data);
          setFiltereEmployeeCode(response.data);
        } else {
          SwalMessageAlert(
            "Failed to fetch Employee data. Please try again.",
            "error"
          );
        }
      } catch (error) {
        console.error("An error occurred while fetching the data:", error);
        SwalMessageAlert("An error occurred. Please try again later.", "error");
      }
    };

    fetchEmployeeCode();
  }, [endPoint, token, collegeId]);

  useEffect(() => {
    let filtered = employeeCode;
    if (searchTerm) {
      filtered = filtered.filter((item) =>
        Object.keys(item).some((key) =>
          String(item[key]).toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
    }
    if (statusFilter) {
      filtered = filtered.filter((item) =>
        statusFilter === "active" ? item.status === true : item.status !== true
      );
    }
    setFiltereEmployeeCode(filtered);
    setCurrentPage(1);
  }, [searchTerm, employeeCode, statusFilter]);

  // Handle input changes for new and edit rows
  const [empCodeformData, setEmpCodeformData] = useState({
    empCode: "",contact: "",
  });
  const [employeeCodeerrors, setEmployeeCodeErrors] = useState(formData);
  const [proceedButton, setProceedButton] = useState(true);
  const handleInputChange = (e, isNewRow = false) => {
    const { name, value } = e.target;
    if (isNewRow) {
      setNewRow({ ...newRow, [name]: value });
    } else {
      setFormData({ ...formData, [name]: value });
    }
    if (name === "empCode") {
      if (value.length !== 11) {
        setEmployeeCodeErrors({
          ...employeeCodeerrors,
          empCode: "Employee Code must be exactly 11 digits",
        });
        setProceedButton(false);
      } else {
        setEmployeeCodeErrors({ ...employeeCodeerrors, empCode: "" });
        setProceedButton(true);
      }
    }
    if (name === "contact") {
      if (value.length !== 10 || !/^[6-9]\d{9}$/.test(value)) {
        setEmployeeCodeErrors({
          ...employeeCodeerrors,
          contact:
            "Mobile number must be 10 digits and start with 6, 7, 8, or 9",
        });
        setProceedButton(false);
      } else {
        setEmployeeCodeErrors({ ...employeeCodeerrors, contact: "" });
        setProceedButton(true);
      }
    }
  };

  // Add new employee
  const handleAddNewRow = async () => {
    try {
      const response = await axios.post(
        `${endPoint}/api/employee-code/add`,
        { ...newRow, college: collegeId },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("Employee added successfully!", "success");
        setEmployeeCode((prev) => [...prev, response.data]);
        setNewRow({
          empCode: "",
          name: "",
          contact: "",
        });
      } else {
        SwalMessageAlert(response.data.msg, "error");
      }
    } catch (error) {
      console.error("An error occurred while adding the data:", error);
      SwalMessageAlert("An error occurred. Please try again later.", "error");
    }
  };

  const handleEdit = (row) => {
    setEditableRow(row._id); // Set the row as editable
    setFormData({
      empCode: row.empCode || "",
      name: row.name || "",
      contact: row.contact || "",
    }); // Populate formData with row data
  };

  // Handle cancel edit
  const handleCancelEdit = () => {
    setEditableRow(null); // Exit edit mode
    setFormData({
      empCode: "",
      name: "",
      contact: "",
    }); // Clear the form data
  };

  // // Handle input changes for editable row
  // const handleInputChange = (e) => {
  //   const { name, value } = e.target;
  //   setFormData((prevData) => ({
  //     ...prevData,
  //     [name]: value,
  //   }));
  // };
  // Columns configuration
useEffect(() => {
  const fetchEmpName = async () => {
    try {
      const response = await axios.get(`${endPoint}/api/emp-name`, {
        headers: {
          "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
        },
      });

      if (response.status === 200 ) {
        setEmpName(response.data); 
        console.log("Fetched employee names:", response.data);
      } else {
        console.warn("Unexpected response format", response.data);
        setEmpName([]);
      }
    } catch (err) {
      console.error("Error fetching employee names:", err);
      setEmpName([]);
    }
  };

  fetchEmpName();
}, [endPoint, token]);

  const columns = [
    {
      name: "S.No.",
      selector: (_, index) => (currentPage - 1) * rowsPerPage + index + 1,
      sortable: false,
      width: "80px",
    },

   {
      name: "Actions",
      cell: (row) =>
        editableRow === row._id ? (
          <>
            <Button color="success" size="sm" onClick={handleUpdate}>
              Save
            </Button>
            <Button
              color="secondary"
              size="sm"
              onClick={handleCancelEdit}
              className="ms-2"
            >
              Cancel
            </Button>
          </>
        ) : row.status !== true ? (
          <>
            <Button color="warning" size="sm" onClick={() => handleEdit(row)}>
              Edit
            </Button>
            <Button
              color="danger"
              size="sm"
              onClick={() => handleDelete(row._id)}
            >
              Delete
            </Button>
          </>
        ) : (
          <>
            <Button color="success" size="sm">
              Activated
            </Button>
          </>
        ),
    },

    {
      name: "Emp Code",
      selector: (row) =>
        editableRow === row._id ? (
          <input
            type="text"
            name="empCode"
            value={formData.empCode}
            onChange={handleInputChange}
            className="form-control"
          />
        ) : (
          row.empCode
        ),
      sortable: true,
    },
    // {
    //   name: "Name",
    //   selector: (row) =>
    //     editableRow === row._id ? (
    //       <input
    //         type="text"
    //         name="name"
    //         value={formData.name}
    //         onChange={handleInputChange}
    //         className="form-control"
    //       />
         
    //     ) : (
    //       row.name
    //     ),
    //   sortable: true,
    // },
   {
  name: "Name",
  selector: (row) => {
    const matchedEmployee = EmpName?.find(emp => emp.empCode === row.empCode);
    const value = matchedEmployee?.name || row.name;

    if (editableRow === row._id) {
      return (
        <input
          type="text"
          name="name"
          value={value}
          onChange={handleInputChange}
          className="form-control"
        />
      );
    } else {
      return (
        <span>{value}</span>
      );
    }
  },
  sortable: true,
},

    {
      name: "Contact",
      selector: (row) =>
        editableRow === row._id ? (
          <input
            type="text"
            name="contact"
            value={formData.contact}
            onChange={handleInputChange}
            className="form-control"
          />
        ) : (
          row.contact
        ),
      sortable: true,
    },


   {
  name: "Created At",
  selector: (row) => row.createdAt 
    ? (() => { 
        const d = new Date(row.createdAt); 
        const [day, month, year] = [d.getDate(), d.getMonth() + 1, d.getFullYear()].map(v => String(v).padStart(2, "0"));
        const [h, m, s] = [d.getHours() % 12 || 12, d.getMinutes(), d.getSeconds()].map(v => String(v).padStart(2, "0"));
        const ampm = d.getHours() >= 12 ? "PM" : "AM";
        return `${day}/${month}/${year}, ${h}:${m}:${s} ${ampm}`;
      })() 
    : "-", 
  sortable: true,
},
{
  name: "Updated At",
  selector: (row) => row.updatedAt 
    ? (() => { 
        const d = new Date(row.updatedAt); 
        const [day, month, year] = [d.getDate(), d.getMonth() + 1, d.getFullYear()].map(v => String(v).padStart(2, "0"));
        const [h, m, s] = [d.getHours() % 12 || 12, d.getMinutes(), d.getSeconds()].map(v => String(v).padStart(2, "0"));
        const ampm = d.getHours() >= 12 ? "PM" : "AM";
        return `${day}/${month}/${year}, ${h}:${m}:${s} ${ampm}`;
      })() 
    : "-", 
  sortable: true,
},








  ];

  const handleDelete = async (id) => {
    const result = await Swal.fire({
      title: "Are you sure you want to delete?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#3085d6",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "No, keep it",
    });

    if (result.isConfirmed) {
      try {
        const response = await axios.get(
          `${endPoint}/api/employee-code/delete/${id}`,
          {
            headers: {
              "Content-Type": "application/json",
              'web-url': window.location.href,
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200) {
          SwalMessageAlert("Employee Code Deleted successfully!", "success");
          setTimeout(() => {
            window.location.reload();
          }, 5000);
        } else {
          SwalMessageAlert(
            "Failed to delete Employee Code. Please try again.",
            "error"
          );
        }
      } catch (error) {
        console.error("An error occurred while updating the data:", error);
        SwalMessageAlert(`An error occurred. ${error}`, "error");
      }
    }
  };

  const handleUpdate = async () => {
    try {
      const body = {
        empCode: String(formData.empCode),
        name: String(formData.name),
        contact: String(formData.contact),
      };
      const response = await axios.put(
        `${endPoint}/api/employee-code/update/${editableRow}`,
        { ...body, college: collegeId },
        {
          headers: {
            "Content-Type": "application/json",
            'web-url': window.location.href,
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.status === 200) {
        SwalMessageAlert("Employee Code updated successfully!", "success");
        // Update the local state with the updated row
        setEmployeeCode((prev) =>
          prev.map((emp) =>
            emp._id === editableRow ? { ...emp, ...formData } : emp
          )
        );
        handleCancelEdit(); // Exit edit mode
      } else {
        SwalMessageAlert(
          "Failed to update Employee Code. Please try again.",
          "error"
        );
      }
    } catch (error) {
      console.error("An error occurred while updating the data:", error);
      SwalMessageAlert("An error occurred. Please try again later.", "error");
    }
  };
  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <>
      <Header />
      <Container className="mt--7" fluid>
        <Row>
          <Col>
            <Card className="shadow pb-4">
              <CardHeader className="bg-white border-0">
                <h3 className="mb-0">Add Employee Code</h3>
              </CardHeader>
              <CardBody>
                {/* Add New Employee Row */}
                <div className="mb-4">
                  <Row>
                   
                    <Col md="3">
                      <input
                        type="number"
                        name="empCode"
                        placeholder="Emp Code"
                        value={newRow.empCode}
                        maxLength="11" // Now enforced by JavaScript
                        onInput={(e) => {
                          const value = e.target.value;
                          // Ensure only numeric input and limit to 11 characters
                          if (!/^\d*$/.test(value)) {
                            e.preventDefault();
                          }
                          if (value.length > 11) {
                            e.target.value = value.slice(0, 11); // Limit to 11 characters
                          }
                        }}
                        onChange={(e) => handleInputChange(e, true)}
                        className="form-control"
                      />
                      {employeeCodeerrors.empCode && (
                        <p style={{ color: "red" }}>
                          {employeeCodeerrors.empCode}
                        </p>
                      )}
                    </Col>
                    <Col md="3">
                      <input
                        type="text"
                        name="name"
                        placeholder="Name"
                        value={newRow.name}
                        onChange={(e) => handleInputChange(e, true)}
                        className="form-control"
                      />
                    </Col>
                    <Col md="3">
                      <input
                        type="text"
                        name="contact"
                        placeholder="Contact"
                        value={newRow.contact}
                        pattern="[6-9][0-9]*"
                        maxLength={10}
                        onChange={(e) => {
                          const value = e.target.value;

                          // Check if the input is empty
                          if (value === "") {
                            handleInputChange(e, true);
                            return;
                          }

                          // Regex to match numbers starting with 6, 7, 8, or 9
                          const validStartRegex = /^[6-9]/;

                          if (validStartRegex.test(value)) {
                            // If valid, pass 'true' as the second argument to handleInputChange
                            handleInputChange(e, true);
                          }
                        }}
                        className="form-control"
                      />
                      {employeeCodeerrors.contact && (
                        <p style={{ color: "red" }}>
                          {employeeCodeerrors.contact}
                        </p>
                      )}
                    </Col>
                    <Col md="3">
                      <Button
                        color="primary"
                        onClick={handleAddNewRow}
                        style={{ display: proceedButton ? "" : "none" }}
                      >
                        Submit
                      </Button>
                    </Col>
                  </Row>
                </div>
              </CardBody>
            </Card>
            <Card className="shadow mt-6">
              <CardHeader className="bg-white border-0 d-flex">
                <Col md={6}>
                  <h3 className="mb-0">Employee Code List</h3>
                </Col>
                   <Col md={3}>
                  <Input
                    type="select"
                    value={statusFilter}
                    onChange={e => setStatusFilter(e.target.value)}
                    style={{ marginBottom: "10px" }}
                  >
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Not Active</option>
                  </Input>
                </Col>
                <Col md={3}>
                  <Input
                    type="text"
                    placeholder="Search Employee Code Or Contact"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    style={{ marginBottom: "10px" }}
                  />
                </Col>
              </CardHeader>
              <CardBody>
                {/* Data Table */}
                <span className="badge badge-success">

</span>

                <DataTable
                  columns={columns}
                  data={filteredEmployeeCode}
                  pagination
                  highlightOnHover
                  striped
                  responsive
                  onChangePage={handlePageChange}
                />
              </CardBody>
            </Card>
          </Col>
        </Row>
      </Container>
    </>
  );
};

export default EmployeeCode;
