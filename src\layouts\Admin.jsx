import React, { useEffect } from "react";
import { useLocation, Route, Routes, Navigate } from "react-router-dom";
import { Container } from "reactstrap";
import AdminNavbar from "../components/Navbars/AdminNavbar.jsx";
import AdminFooter from "../components/Footers/AdminFooter.jsx";
import Sidebar from "../components/Sidebar/Sidebar.jsx";
import routes from "../routers/routes.jsx";
import OtherRoutes from "../routers/AdminOtherRoutes.jsx";
import { useSidebar } from "../components/Sidebar/SidebarContext.jsx";

// Speech synthesis
import { useSpeech } from "../context/SpeechContext.jsx";


const Admin = (props) => {
  const mainContent = React.useRef(null);
  const location = useLocation();

  // Speech synthesis  Text-to-Speech functionality RX
const { isSpeechOn, speak } = useSpeech();
useEffect(() => {
  if (!isSpeechOn) return;
  const timeout = setTimeout(() => {
    const text = document.body?.innerText || "";
    speak(text);
  }, 150);
  return () => clearTimeout(timeout);
}, [location, isSpeechOn, speak]);
useEffect(() => {
  if (!isSpeechOn) return;
  const handleHoverSpeak = (e) => {
    const target = e.target;
    const text = target?.innerText?.trim();
    if (!text || text.length > 300) return;
    const style = window.getComputedStyle(target);
    const isHidden =
      style.display === "none" ||
      style.visibility === "hidden" ||
      target.offsetParent === null ||
      target.getAttribute("aria-hidden") === "true";
    if (isHidden) return;
    window.speechSynthesis.cancel();
    speak(text);
  };
  document.body.addEventListener("mouseover", handleHoverSpeak);
  return () => {
    document.body.removeEventListener("mouseover", handleHoverSpeak);
  };
}, [isSpeechOn, speak]);
// Rx Page Reader Speech Synthesis Close  

  const { isSidebarVisible } = useSidebar();
  React.useEffect(() => {
    document.documentElement.scrollTop = 0;
    document.scrollingElement.scrollTop = 0;
    mainContent.current.scrollTop = 0;
  }, [location]);

  const getRoutes = (routes) => {
    return routes.map((prop, key) => {
      if (prop.layout === "/admin") {
        if (prop.children) {
          // Render child routes if present
          return prop.children.map((child, childKey) => (
            <Route path={child.path} element={child.component} key={`${key}-${childKey}`} exact />
          ));
        }
        return (
          <Route path={prop.path} element={prop.component} key={key} exact />
        );
      }
      return null;
    });
  };

  const getBrandText = (path) => {
    for (let i = 0; i < routes.length; i++) {
      if (
        props?.location?.pathname.indexOf(routes[i].layout + routes[i].path) !==
        -1
      ) {
        return routes[i].name;
      }
    }
    return "Brand";
  };

  return (
    <>

      {isSidebarVisible === true && <Sidebar
        {...props}
        routes={routes}
      // logo={{
      //   innerLink: "/admin/index",
      //   imgSrc: require("../assets/img/brand/argon-react.png"),
      //   imgAlt: "...",
      // }}
      />}
      <div className="main-content" ref={mainContent}>
        <AdminNavbar
          {...props}
          brandText={getBrandText(props?.location?.pathname)}
        />

        <Routes>
          {getRoutes(routes)}
          {getRoutes(OtherRoutes)}
          <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
        </Routes>
        <Container fluid>
          <AdminFooter />
        </Container>
      </div>
    </>
  );
};

export default Admin;
